package com.ql.dto.pay;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 外部直接
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ThirdPayResult {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 支付状态
     */
    private Byte payStatus;

    /**
     * 实际支付金额
     */
    private Long actualPayAmount;

    /**
     * 交易单号
     */
    private Long payNo;

    /**
     * 外部支付单号
     */
    private String thirdSourceNo;

    /**
     * 外部失败原因
     */
    private String sourceFailedReason;

    /**
     * 订单支付码
     */
    private String qrCode;

    /**
     * 预支付交易会话标识
     */
    private String prepayId;

    /**
     * 随机字符串（UUID）
     */
    private String nonceStr;

    /**
     * 时间戳（毫秒）
     */
    private String timeStamp;

    public static ThirdPayResult failedResult() {
        return ThirdPayResult.builder().success(false).build();
    }


    public static ThirdPayResult failedResult(String sourceFailedReason) {
        return ThirdPayResult.builder().success(false).sourceFailedReason(sourceFailedReason).build();
    }

    public static boolean isSuccess(ThirdPayResult result) {
        return result != null && Boolean.TRUE.equals(result.success);
    }
}
