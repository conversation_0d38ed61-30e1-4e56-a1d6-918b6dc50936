package com.ql.dto.fundauth;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FundAuthFreezeResult {
    /**
     * 是否成功
     */
    private boolean success;
    /**
     * 外部失败原因
     */
    private String sourceFailedReason;
    /**
     * 签名字符串
     */
    private String orderStr;
}
