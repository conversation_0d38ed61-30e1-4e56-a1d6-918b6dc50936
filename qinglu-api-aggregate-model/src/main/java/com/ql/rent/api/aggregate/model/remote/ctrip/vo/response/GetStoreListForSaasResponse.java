package com.ql.rent.api.aggregate.model.remote.ctrip.vo.response;

import com.ql.rent.api.aggregate.model.remote.ctrip.dto.CtripStoreDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/7/26 10:45
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetStoreListForSaasResponse extends BaseCtripResponse {

    private List<CtripStoreDTO> storeInfoList;

}
