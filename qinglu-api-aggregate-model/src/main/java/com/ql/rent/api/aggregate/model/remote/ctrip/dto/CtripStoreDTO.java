package com.ql.rent.api.aggregate.model.remote.ctrip.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 携程门店
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/07/26 10:43
 */
@Data
@ApiModel(description = "携程门店信息接口对象")
public class CtripStoreDTO implements Serializable {

    private static final long serialVersionUID = -260226781629566211L;

    /**
     * 携程门店id
     */
    private Long storeId;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 门店类型 1 实体店 2 服务点 3 接送虚拟点 4 送取虚拟点
     */
    private Byte storeType;
    /**
     * 父门店id (门店类型为虚拟点)
     */
    private Long parentStoreId;
    /**
     * 供应商id
     */
    private Long supplierId;
    /**
     * 服务商id
     */
    private Long vendorId;
    /**
     * 服务商门店编码
     */
    private String vendorStoreCode;
    /**
     * 门店状态（0 下线 1 上线）
     */
    private Byte storeStatus;
    /**
     * 门店面积 非必填
     */
    private Integer storeArea;
    /**
     * 门店装修级别（1 精装修 2 标准装修 3 普通装修 4 低档装修) 直连非必填，PMS必填；
     */
    private Byte storeDecorateLevel;

    private List<String> storeCounterImgList;
    private List<String> storeFrontImgList;
    private List<String> treetViewImgList;

    /**
     * 位置信息
     */
    private CtripLocationDTO locationInfo;

    @Data
    public static class CtripLocationDTO implements Serializable {
        /**
         * 城市id
         */
        private Long cityId;
        /**
         * 详细地址
         */
        private String address;
        /**
         * 邮编
         */
        private String postalCode;
        /**
         * 经度（国内高德，国外谷歌坐标系）
         */
        private Double longitude;
        /**
         * 纬度（国内高德，国外谷歌坐标系）
         */
        private Double latitude;
        /**
         * 枢纽站店类型（1 枢纽站内  2枢纽站外 3 非枢纽站）
         */
        private Byte stationType;
        /**
         * 枢纽站id
         */
        private Long stationId;
        /**
         * 枢纽站名称 直连枢纽站门店必填
         */
        private String stationName;
        /**
         * 三字码
         */
        private String threeCode;
        /**
         * 最近的枢纽站出口 非必填
         */
        private String stationExitName;
        /**
         * 指引信息
         */
        private List<CtripGuideDTO> guideInfoList;

        @Data
        public static class CtripGuideDTO implements Serializable {
            /**
             * 指引类型 1 取车指引 2 还车指引 3 门店指引 必填
             */
            private Byte guideType;
            /**
             * 步骤编号 必填
             */
            private Integer stepNum;
            /**
             * 文字描述 必填
             */
            private String guideDesc;

            private List<String> guideImg;
        }
    }

    /**
     * 门店联系人信息
     */
    private List<CtripContactDTO> contactInfoList;

    @Data
    public static class CtripContactDTO implements Serializable {
        /**
         * 联系人名称 非必填
         */
        private String contactName;
        /**
         * 联系人类型
         */
        private List<Integer> contactType;
        /**
         * 手机-区号 非必填
         */
        private String mobilePhoneAreaCode;
        /**
         * 手机号 与 fixedPhone 字段选填其中一个
         */
        private String mobilePhone;
        /**
         * 备用手机-区号 非必填
         */
        private String backupMobilePhoneAreaCode;
        /**
         * 备用手机号 非必填
         */
        private String backupMobilePhone;
        /**
         * 座机-国家号 非必填
         */
        private String fixedPhoneCountryCode;
        /**
         * 座机-区号 非必填
         */
        private String fixedPhoneAreaCode;
        /**
         * 座机号 与 mobilePhone 字段选填其中一个
         */
        private String fixedPhone;
        /**
         * 分机号 非必填
         */
        private String fixedPhoneExtensionNumber;
        /**
         * 邮箱 非必填
         */
        private String email;
        /**
         * 排序号 必填 从 0 开始自增
         */
        private Integer sortNum;
    }

    /**
     * 常规营业时间信息
     */
    private List<CtripBusinessHoursDTO> normalBusinessHoursList;

    @Data
    public static class CtripBusinessHoursDTO implements Serializable {

        /**
         * 门店服务日期id 非必填
         */
        private Long businessHoursId;

        private List<Long> businessHoursCycle;

        /**
         * 常规营业时间段 必填
         */
        private List<CtripBusinessTimeDTO> periodTime;

        /**
         * 夜间收费服务时间段 非必填
         */
        private List<CtripNightDTO> nightPeriodTime;

        @Data
        public static class CtripBusinessTimeDTO implements Serializable {
            /**
             * 开始时间 例如 8:00 必填
             */
            private String businessHoursStartTime;
            /**
             * 结束时间 例如 21:00 必填
             */
            private String businessHoursEndTime;
            /**
             * 金额 非必填
             */
            private BigDecimal chargeFee;
            /**
             * "收费类型 1：每次  非必填
             */
            private Byte chargeType;
            /**
             * 货币单位 非必填
             */
            private String chargeUnit;
        }

        @Data
        public static class CtripNightDTO implements Serializable {
            /**
             * 开始时间 例如 8:00 必填
             */
            private String businessHoursStartTime;
            /**
             * 结束时间 例如 21:00 必填
             */
            private String businessHoursEndTime;
            /**
             * 金额 非必填
             */
            private BigDecimal chargeFee;
            /**
             * 收费类型 1：每次  非必填
             */
            private Byte chargeType;
            /**
             * 货币单位 非必填
             */
            private String chargeUnit;
        }
    }

    /**
     * 特殊营业时间信息
     */
    private List<CtripSpecialTimeDTO>  specialBusinessHoursList;

    @Data
    public static class CtripSpecialTimeDTO implements Serializable {
        /**
         * 门店服务日期id 非必填
         */
        private Long businessHoursId;

        private String businessHoursName;
        /**
         * 营业开始日期 必填
         */
        private String startDate;
        /**
         * 营业结束日期 必填
         */
        private String endDate;
        /**
         * 常规营业时间段 必填
         */
        private List<CtripPeriodDTO> periodTime;

        @Data
        public static class CtripPeriodDTO implements Serializable {
            /**
             * 开始时间 例如 8:00 必填
             */
            private String businessHoursStartTime;
            /**
             * 结束时间 例如 21:00 必填
             */
            private String businessHoursEndTime;
            /**
             * 金额 非必填
             */
            private BigDecimal chargeFee;
            /**
             * 收费类型 1：每次  非必填
             */
            private Byte chargeType;
            /**
             * 货币单位 非必填
             */
            private String chargeUnit;
        }
    }

    /**
     * 暂停营业时间信息
     */
    private List<CtripSuspendTimeDTO> suspendBusinessHoursList;

    @Data
    public static class CtripSuspendTimeDTO implements Serializable {
        /**
         * 门店服务日期id 非必填
         */
        private Long businessHoursId;

        private String businessHoursName;
        /**
         * 营业开始日期 必填
         */
        private String startDate;
        /**
         * 营业结束日期 必填
         */
        private String endDate;
        /**
         * 常规营业时间段 必填
         */
        private List<CtripPeriodDTO> periodTime;

        @Data
        public static class CtripPeriodDTO implements Serializable {
            /**
             * 开始时间 例如 8:00 必填
             */
            private String businessHoursStartTime;
            /**
             * 结束时间 例如 21:00 必填
             */
            private String businessHoursEndTime;
            /**
             * 金额 非必填
             */
            private BigDecimal chargeFee;
            /**
             * 收费类型 1：每次  非必填
             */
            private Byte chargeType;
            /**
             * 货币单位 非必填
             */
            private String chargeUnit;
        }
    }

    /**
     * 门店服务圈信息列表
     */
    private List<CtripCircleDTO> serviceCircleList;

    @Data
    public static class CtripCircleDTO implements Serializable {
        /**
         * 状态 0 下线 1 上线
         */
        private Byte status;
        /**
         * 门店id
         */
        private Long storeId;
        /**
         * 携程服务圈id
         */
        private Long deliveryServiceId;
        /**
         * 供应商服务圈 code
         */
        private Long vendorServiceCircleCode;
        /**
         * 接送服务类型 1-免费接送服务（门店支持在5公里范围内，为顾客提供免费接送服务），2-上门送取车服务
         */
        private Byte deliveryServiceType;
        /**
         * 服务圈/点 名称
         */
        private String deliveryServiceName;
        /**
         * 提前预定期 decimal
         */
        private Double advancePeriod;
        /**
         * 提前预定期单位; 0：分钟 1：小时 number
         */
        private Byte advancePeriodUnit;
        /**
         * 收费金额：元/次 decimal
         */
        private BigDecimal chargeFee;

        /**
         * 服务圈poi (免费接送服务没有poi信息)
         */
        private List<CtripLngLatDTO> circleRangePoiList;

        @Data
        public static class CtripLngLatDTO implements Serializable {
            /**
             * 经度
             */
            private Double longitude;;
            /**
             * 纬度
             */
            private Double latitude;
        }

        /**
         * 常规营业时间信息
         */
        private List<CtripBusinessHoursDTO> normalBusinessHoursList;

        /**
         * 特殊营业时间信息
         */
        private List<CtripSpecialTimeDTO> specialBusinessHoursList;

        /**
         * 暂停营业时间信息
         */
        private List<CtripSuspendTimeDTO> suspendBusinessHoursList;

    }
}
