package com.ql.rent.open.service.trade;

import com.ql.dto.ResultResp;
import com.ql.dto.open.request.trade.ContractInspectionRequest;
import com.ql.dto.open.request.trade.ContractSmsRequest;
import com.ql.dto.open.request.trade.ContractStatusRequest;
import com.ql.dto.open.response.trade.ContractStatusResponse;
import com.ql.rent.enums.ElectronicContractStatusEnum;
import com.ql.rent.service.trade.IThirdOrderContractService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 开放平台电子合同服务测试类
 *
 * <AUTHOR>
 */
class OrderContractOpenServiceImplTest {

    @InjectMocks
    private OrderContractOpenServiceImpl orderContractOpenService;

    @Mock
    private IThirdOrderContractService thirdOrderContractService;

    private ContractInspectionRequest mockInspectionRequest;
    private ContractSmsRequest mockSmsRequest;
    private ContractStatusRequest mockStatusRequest;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 初始化测试数据
        mockInspectionRequest = new ContractInspectionRequest();
        mockInspectionRequest.setOrderId(1001L);
        mockInspectionRequest.setMerchantId(100L);
        mockInspectionRequest.setMileage(1000);
        mockInspectionRequest.setOilLiter(50);

        mockSmsRequest = new ContractSmsRequest();
        mockSmsRequest.setOrderId(1001L);
        mockSmsRequest.setMerchantId(100L);
        mockSmsRequest.setPrType((byte) 1);

        mockStatusRequest = new ContractStatusRequest();
        mockStatusRequest.setOrderId(1001L);
        mockStatusRequest.setMerchantId(100L);
    }

    @Test
    void testUploadInspectionData_Success() {
        // Given
        when(thirdOrderContractService.processInspectionData(any(ContractInspectionRequest.class)))
                .thenReturn(ResultUtil.successResult(true));

        // When
        ResultResp<Boolean> result = orderContractOpenService.uploadInspectionData(mockInspectionRequest);

        // Then
        assertTrue(result.isSuccess());
        assertTrue(result.getData());
        verify(thirdOrderContractService).processInspectionData(mockInspectionRequest);
    }

    @Test
    void testUploadInspectionData_Failed() {
        // Given
        when(thirdOrderContractService.processInspectionData(any(ContractInspectionRequest.class)))
                .thenReturn(ResultUtil.failResult("上传失败"));

        // When
        ResultResp<Boolean> result = orderContractOpenService.uploadInspectionData(mockInspectionRequest);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("上传失败", result.getMsg());
        verify(thirdOrderContractService).processInspectionData(mockInspectionRequest);
    }

    @Test
    void testUploadInspectionData_Exception() {
        // Given
        when(thirdOrderContractService.processInspectionData(any(ContractInspectionRequest.class)))
                .thenThrow(new RuntimeException("系统异常"));

        // When
        ResultResp<Boolean> result = orderContractOpenService.uploadInspectionData(mockInspectionRequest);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("系统异常，请稍后重试", result.getMsg());
        verify(thirdOrderContractService).processInspectionData(mockInspectionRequest);
    }

    @Test
    void testSendContractMessage_Success() {
        // Given
        when(thirdOrderContractService.sendContractSms(any(ContractSmsRequest.class)))
                .thenReturn(ResultUtil.successResult(true));

        // When
        ResultResp<Boolean> result = orderContractOpenService.sendContractMessage(mockSmsRequest);

        // Then
        assertTrue(result.isSuccess());
        assertTrue(result.getData());
        verify(thirdOrderContractService).sendContractSms(mockSmsRequest);
    }

    @Test
    void testSendContractMessage_Failed() {
        // Given
        when(thirdOrderContractService.sendContractSms(any(ContractSmsRequest.class)))
                .thenReturn(ResultUtil.failResult("短信发送失败"));

        // When
        ResultResp<Boolean> result = orderContractOpenService.sendContractMessage(mockSmsRequest);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("短信发送失败", result.getMsg());
        verify(thirdOrderContractService).sendContractSms(mockSmsRequest);
    }

    @Test
    void testSendContractMessage_Exception() {
        // Given
        when(thirdOrderContractService.sendContractSms(any(ContractSmsRequest.class)))
                .thenThrow(new RuntimeException("网络异常"));

        // When
        ResultResp<Boolean> result = orderContractOpenService.sendContractMessage(mockSmsRequest);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("系统异常，请稍后重试", result.getMsg());
        verify(thirdOrderContractService).sendContractSms(mockSmsRequest);
    }

    @Test
    void testGetContractStatus_Success() {
        // Given
        ContractStatusResponse mockResponse = new ContractStatusResponse();
        mockResponse.setOrderId(1001L);
        mockResponse.setStatus(ElectronicContractStatusEnum.SMS_SIGNING.getStatus());
        mockResponse.setStatusDesc(ElectronicContractStatusEnum.SMS_SIGNING.getDesc());
        mockResponse.setContractUrl("http://example.com/contract.pdf");

        when(thirdOrderContractService.queryContractStatus(any(ContractStatusRequest.class)))
                .thenReturn(ResultUtil.successResult(mockResponse));

        // When
        ResultResp<ContractStatusResponse> result = orderContractOpenService.getContractStatus(mockStatusRequest);

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(1001L, result.getData().getOrderId());
        assertEquals(ElectronicContractStatusEnum.SMS_SIGNING.getStatus(), result.getData().getStatus());
        assertEquals("http://example.com/contract.pdf", result.getData().getContractUrl());
        verify(thirdOrderContractService).queryContractStatus(mockStatusRequest);
    }

    @Test
    void testGetContractStatus_Failed() {
        // Given
        when(thirdOrderContractService.queryContractStatus(any(ContractStatusRequest.class)))
                .thenReturn(ResultUtil.failResult("查询失败"));

        // When
        ResultResp<ContractStatusResponse> result = orderContractOpenService.getContractStatus(mockStatusRequest);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("查询失败", result.getMsg());
        verify(thirdOrderContractService).queryContractStatus(mockStatusRequest);
    }

    @Test
    void testGetContractStatus_Exception() {
        // Given
        when(thirdOrderContractService.queryContractStatus(any(ContractStatusRequest.class)))
                .thenThrow(new RuntimeException("数据库异常"));

        // When
        ResultResp<ContractStatusResponse> result = orderContractOpenService.getContractStatus(mockStatusRequest);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("系统异常，请稍后重试", result.getMsg());
        verify(thirdOrderContractService).queryContractStatus(mockStatusRequest);
    }
}