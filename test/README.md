# 测试目录脚手架说明

本目录提供通用的测试脚手架与约定，涵盖测试用例、提示词、自动化脚本与测试报告等资源，适用于功能测试与接口测试场景。

## 目录结构

```
/test
 ├── test_cases/                 # 测试用例与测试数据
 │   ├── functional/             # 功能测试用例（支持按版本/模块分目录）
 │   ├── api/                    # 接口测试用例
 │   └── data/                   # 测试数据（模拟数据、入参、字典表等）
 │
 ├── prompts/                    # 测试用例生成提示词
 │   ├── functional/             # 功能测试提示词（支持按版本/模块分目录）
 │   └── api/                    # 接口测试提示词
 │
 ├── scripts/                    # 自动化测试脚本
 │   └── api/                    # API 测试脚本
 │
 ├── reports/                    # 测试报告与记录
 │   ├── api/                    # 接口测试报告
 │   ├── functional/             # 功能测试报告
 │   └── history/                # 历史报告归档
 │
 ├── requirements.txt            # 测试依赖声明
 ├── .gitignore                  # 测试目录忽略规则
 └── README.md                   # 本说明文档
```

> 说明：功能测试与提示词目录支持按版本/模块创建子目录（如 `functional/vX.Y.Z/`），以便区分不同迭代产物。

## 约定与命名

- 用例文档命名：`[模块名称]-功能测试用例.md`
- 用例清单命名：`[模块名称]-功能测试用例清单.md`
- 提示词命名：`[模块名称]-测试提示词.md`
- 脚本命名：清晰表达用途与范围（如 `illegal_transfer_test.py`）
- 目录组织：优先按模块，其次按版本；同一模块多版本并存时使用 `functional/vX.Y.Z/` 归档

## 使用流程（推荐）

1. 在 `prompts/functional/` 中创建提示词（可按版本分目录），沉淀需求背景、核心业务点与用例要求。
2. 基于提示词在 `test_cases/functional/` 产出功能用例与用例清单（Markdown），覆盖正常/边界/异常场景。
3. 准备必要的测试数据到 `test_cases/data/`（如 JSON、CSV、SQL 片段等）。
4. 编写或复用 `scripts/` 下自动化脚本执行接口/功能验证（如 API 层脚本）。
5. 将执行结果与报告输出到 `reports/` 对应子目录，必要时同步归档到 `reports/history/`。

## 环境与依赖

- 依赖安装：
  - 使用虚拟环境或隔离环境管理依赖
  - 安装命令：`pip install -r test/requirements.txt`
- 运行方式：
  - 依据脚本约定执行（例如：`python test/scripts/api/<script>.py`）
  - 或集成到统一的测试框架/流水线中执行

## 规范与检查

- 测试用例需包含：用例编号、模块名称、优先级、前置条件、测试数据、步骤、预期结果、实际结果
- 用例清单以表格形式列出关键场景并标记验证结果
- 产物统一使用 Markdown 保存，便于评审与变更管理
- 新增测试类型或目录结构调整时，请同步更新本 README 说明