package com.ql.rent.component.mq;

import com.ql.rent.component.mq.config.RocketMqProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.acl.common.AclClientRPCHook;
import org.apache.rocketmq.acl.common.SessionCredentials;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.remoting.RPCHook;

import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * RocketMQ 消费者抽象基类，支持阿里云 ACL 和业务自定义 Consumer 配置，
 * handleMessage 由业务返回消费状态。
 * 
 * 实现 InitializingBean 确保在 Spring 容器启动后自动启动消费者
 * 
 * 替代方案：也可以使用 @PostConstruct 注解替代 InitializingBean
 * 例如：
 * @PostConstruct
 * public void init() throws Exception {
 *     // 启动逻辑
 * }
 */
@Slf4j
public abstract class AbstractRocketMqListener implements InitializingBean, DisposableBean {

    private static final int MAX_RETRY_TIMES = 3;
    private static final long RETRY_INTERVAL_MS = 2000L;
    private static final long INITIAL_DELAY_MS = 1000L;

    @Autowired
    private RocketMqProperties rocketMqProperties;

    private DefaultMQPushConsumer consumer;

    /**
     * 子类实现：返回消费的 topic
     */
    protected abstract String getTopic();

    /**
     * 子类实现：返回消费组
     */
    protected abstract String getConsumerGroup();

    /**
     * 子类实现：处理消息并返回消费状态
     */
    protected abstract ConsumeConcurrentlyStatus handleMessage(MessageExt message);

    /**
     * 子类可重写：自定义 Consumer 配置（如线程数、tag、消费模式等）
     */
    protected void customizeConsumer(DefaultMQPushConsumer consumer) throws MQClientException {
        consumer.subscribe(getTopic(), "*");
    }

    /**
     * 获取 Namesrv 地址
     */
    protected String getNamesrvAddr() {
        return rocketMqProperties.getNameServer();
    }

    /**
     * 获取阿里云 ACL 鉴权钩子
     */
    protected RPCHook getAclHook() {
        return new AclClientRPCHook(
                new SessionCredentials(
                        rocketMqProperties.getAccessKey(),
                        rocketMqProperties.getSecretKey()
                )
        );
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("Starting RocketMQ Consumer for topic: {}, group: {}", getTopic(), getConsumerGroup());
        
        // 延迟启动，等待配置和网络准备就绪
        TimeUnit.MILLISECONDS.sleep(INITIAL_DELAY_MS);
        
        // 带重试机制的消费者启动
        startConsumerWithRetry();
    }

    /**
     * 带重试机制的消费者启动
     */
    private void startConsumerWithRetry() throws Exception {
        Exception lastException = null;
        
        for (int retryCount = 0; retryCount < MAX_RETRY_TIMES; retryCount++) {
            try {
                // 验证配置
                validateConfiguration();
                
                // 创建并启动消费者
                createAndStartConsumer();
                
                log.info("RocketMQ Consumer started successfully: topic={}, group={}", getTopic(), getConsumerGroup());
                return; // 启动成功，退出重试循环
                
            } catch (Exception e) {
                lastException = e;
                log.warn("RocketMQ Consumer startup failed, retry {}/{}: {}", 
                        retryCount + 1, MAX_RETRY_TIMES, e.getMessage());
                
                if (retryCount < MAX_RETRY_TIMES - 1) {
                    TimeUnit.MILLISECONDS.sleep(RETRY_INTERVAL_MS);
                }
            }
        }
        
        // 所有重试都失败了
        log.error("Failed to start RocketMQ Consumer after {} retries: topic={}, group={}", 
                MAX_RETRY_TIMES, getTopic(), getConsumerGroup(), lastException);
        throw new RuntimeException("Failed to start RocketMQ Consumer after " + MAX_RETRY_TIMES + " retries", lastException);
    }

    /**
     * 验证配置
     */
    private void validateConfiguration() {
        if (rocketMqProperties == null) {
            throw new IllegalStateException("RocketMqProperties is not initialized");
        }
        
        if (getNamesrvAddr() == null || getNamesrvAddr().trim().isEmpty()) {
            throw new IllegalStateException("NameServer address is not configured");
        }
        
        if (getConsumerGroup() == null || getConsumerGroup().trim().isEmpty()) {
            throw new IllegalStateException("Consumer group is not configured");
        }
        
        if (getTopic() == null || getTopic().trim().isEmpty()) {
            throw new IllegalStateException("Topic is not configured");
        }
        
        log.debug("RocketMQ configuration validated: namesrv={}, group={}, topic={}", 
                getNamesrvAddr(), getConsumerGroup(), getTopic());
    }

    /**
     * 创建并启动消费者
     */
    private void createAndStartConsumer() throws MQClientException {
        consumer = new DefaultMQPushConsumer(getConsumerGroup(), getAclHook());
        consumer.setNamesrvAddr(getNamesrvAddr());

        if (StringUtils.isNotBlank(rocketMqProperties.getNamespace())){
            consumer.setNamespaceV2(rocketMqProperties.getNamespace());
        }

        // 自定义配置
        customizeConsumer(consumer);
        
        // 注册消息监听器
        consumer.registerMessageListener(new MessageListenerConcurrently() {
            @Override
            public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
                for (MessageExt msg : msgs) {
                    ConsumeConcurrentlyStatus status = handleMessage(msg);
                    if (status != ConsumeConcurrentlyStatus.CONSUME_SUCCESS) {
                        return status;
                    }
                }
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
        });
        
        // 启动消费者
        consumer.start();
    }

    @Override
    public void destroy() throws Exception {
        if (consumer != null) {
            consumer.shutdown();
            log.info("RocketMQ Consumer shutdown: topic={}, group={}", getTopic(), getConsumerGroup());
        }
    }
}
