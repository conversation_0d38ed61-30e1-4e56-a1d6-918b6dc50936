package com.ql.rent.component.mq.config;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.acl.common.AclClientRPCHook;
import org.apache.rocketmq.acl.common.SessionCredentials;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "rocketmq")
@Data
public class RocketMqProperties {
    private String nameServer;
    private String accessKey;
    private String secretKey;
    private String namespace;

    @Bean
    public DefaultMQProducer defaultMQProducer() throws Exception {
        DefaultMQProducer producer = new DefaultMQProducer(
            "saas",
            new AclClientRPCHook(new SessionCredentials(accessKey, secretKey))
        );
        producer.setNamesrvAddr(nameServer);
        if (StringUtils.isNotBlank(namespace)){
            producer.setNamespaceV2(namespace);
        }
        producer.start();
        return producer;
    }
}
