package com.ql.rent.converter.pay;

import com.ql.dto.pay.RefundResult;
import com.ql.dto.pay.ThirdPayResult;
import com.ql.rent.api.aggregate.remote.platform.vo.request.ThirdPayCreateOrderReq;
import com.ql.rent.api.aggregate.remote.platform.vo.request.ThirdPayRefundReq;
import com.ql.rent.api.aggregate.remote.platform.vo.response.ThirdPayCreateOrderResp;
import com.ql.rent.api.aggregate.remote.platform.vo.response.ThirdPayRefundResp;
import com.ql.rent.param.pay.RefundParam;
import com.ql.rent.param.pay.ThirdPayCreateParam;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 微信支付转换器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "jsr330")
public interface WxPayConverter {

    WxPayConverter INSTANCE = Mappers.getMapper(WxPayConverter.class);

    /**
     * 转换创建订单请求参数
     */
    ThirdPayCreateOrderReq toCreateOrderRequest(ThirdPayCreateParam param);

    /**
     * 转换退款请求参数
     */
    ThirdPayRefundReq toRefundRequest(RefundParam param);

    /**
     * 转换创建订单响应结果
     */
    @Mapping(target = "prepayId", source = "prepay_id")
    ThirdPayResult toThirdPayResult(ThirdPayCreateOrderResp response);

    /**
     * 转换退款响应结果
     */
    RefundResult toRefundResult(ThirdPayRefundResp response);
}
