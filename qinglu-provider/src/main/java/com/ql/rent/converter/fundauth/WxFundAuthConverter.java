package com.ql.rent.converter.fundauth;

import com.ql.dto.fundauth.FundAuthFreezeResult;
import com.ql.dto.fundauth.FundAuthUnfreezeResult;
import com.ql.dto.fundauth.RefundForFreezeResult;
import com.ql.dto.fundauth.TradeCloseForFreezeResult;
import com.ql.dto.fundauth.TradePayForFreezeResult;
import com.ql.rent.api.aggregate.remote.platform.vo.request.*;
import com.ql.rent.api.aggregate.remote.platform.vo.response.*;
import com.ql.rent.param.fundauth.FundAuthFreezeParam;
import com.ql.rent.param.fundauth.FundAuthUnfreezeParam;
import com.ql.rent.param.fundauth.RefundForFreezeParam;
import com.ql.rent.param.fundauth.TradeCloseForFreezeParam;
import com.ql.rent.param.fundauth.TradePayForFreezeParam;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 微信预授权转换器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "jsr330")
public interface WxFundAuthConverter {

    WxFundAuthConverter INSTANCE = Mappers.getMapper(WxFundAuthConverter.class);

    /**
     * 转换冻结请求参数
     */
    ThirdFundAuthFreezeReq toFreezeRequest(FundAuthFreezeParam param);

    /**
     * 转换解冻请求参数
     */
    ThirdFundAuthUnfreezeReq toUnfreezeRequest(FundAuthUnfreezeParam param);

    /**
     * 转换预授权支付请求参数
     */
    ThirdFundAuthPayReq toPayRequest(TradePayForFreezeParam param);

    /**
     * 转换预授权退款请求参数
     */
    ThirdFundAuthRefundReq toRefundRequest(RefundForFreezeParam param);

    /**
     * 转换交易关闭请求参数
     */
    ThirdFundAuthCloseReq toCloseRequest(TradeCloseForFreezeParam param);

    /**
     * 转换冻结响应结果
     */
    FundAuthFreezeResult toFreezeResult(ThirdFundAuthFreezeResp response);

    /**
     * 转换解冻响应结果
     */
    FundAuthUnfreezeResult toUnfreezeResult(ThirdFundAuthUnfreezeResp response);

    /**
     * 转换预授权支付响应结果
     */
    TradePayForFreezeResult toPayResult(ThirdFundAuthPayResp response);

    /**
     * 转换预授权退款响应结果
     */
    RefundForFreezeResult toRefundResult(ThirdFundAuthRefundResp response);

    /**
     * 转换交易关闭响应结果
     */
    TradeCloseForFreezeResult toCloseResult(ThirdFundAuthCloseResp response);
}
