package com.ql.rent.provider.pay;

import com.alibaba.fastjson2.JSON;
import com.ql.dto.pay.*;
import com.ql.enums.ThirdPayEnum;
import com.ql.rent.common.IRedisService;
import com.ql.rent.dao.bill.ThirdPayMapper;
import com.ql.rent.dao.bill.ThirdPayRoyaltyMapper;
import com.ql.rent.dao.bill.ThirdRefundPayMapper;
import com.ql.rent.dao.bill.ThirdRefundPayRoyaltyMapper;
import com.ql.rent.dao.bill.ex.ThirdPayMapperEx;
import com.ql.rent.dao.bill.ex.ThirdPayRoyaltyMapperEx;
import com.ql.rent.dao.trade.PayMapper;
import com.ql.rent.entity.bill.*;
import com.ql.rent.entity.trade.Pay;
import com.ql.rent.entity.trade.PayExample;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.common.ResultEnum;
import com.ql.rent.enums.trade.PayFeeTypeEnum;
import com.ql.rent.enums.trade.PaySourceEnum;
import com.ql.rent.enums.trade.PayStatusEnum;
import com.ql.rent.multipledatasource.TransactionManagerName;
import com.ql.rent.param.merchant.RoyaltyRelationParam;
import com.ql.rent.param.merchant.RoyaltyRelationQuery;
import com.ql.rent.param.pay.*;
import com.ql.rent.service.merchant.IRoyaltyRelationService;
import com.ql.rent.service.pay.IThirdPayService;
import com.ql.rent.service.trade.IOrderService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.SpanEnhancer;
import com.ql.rent.share.utils.UuidUtil;
import com.ql.rent.vo.bill.ThirdPayExtraVO;
import com.ql.rent.vo.bill.ThirdPayVO;
import com.ql.rent.vo.bill.ThirdRefundExtraVO;
import com.ql.rent.vo.bill.ThirdRefundPayVO;
import com.ql.rent.vo.merchant.RoyaltyRelationVO;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 三方支付处理
 * <AUTHOR>
 */
@Service
@Slf4j
public class ThirdPayServiceImpl implements IThirdPayService, ApplicationContextAware {


    @Resource
    private ThirdPayMapper thirdPayMapper;
    @Resource
    private PayMapper payMapper;
    @Resource
    private ThirdRefundPayMapper thirdRefundPayMapper;
    @Resource
    private IRedisService redisService;
    @Resource
    private IOrderService orderService;
    @Resource
    private IRoyaltyRelationService royaltyRelationService;
    @Resource
    private ThirdPayRoyaltyMapper thirdPayRoyaltyMapper;
    @Resource
    private ThirdPayMapperEx thirdPayMapperEx;
    @Resource
    private ThirdRefundPayRoyaltyMapper thirdRefundPayRoyaltyMapper;
    @Resource
    private ThirdPayRoyaltyMapperEx thirdPayRoyaltyMapperEx;

    @Value("${bill.pay.timeout.minutes}")
    private Integer payTimeoutMinutes;

    private static Map<Integer, ThirdPayHandler> THIRD_FACTORY = Collections.emptyMap();

    @Override
    @WithSpan("创建支付单")
    public Result<ThirdPayResult> createThirdPay(ThirdPayCreateParam thirdPayCreateParam) {
        if (thirdPayCreateParam == null || thirdPayCreateParam.getAmount() == null
            || thirdPayCreateParam.getRelationId() == null || thirdPayCreateParam.getThirdPaySource() == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }
        log.info("创建支付订单 thirdPayCreateParam={}", JSON.toJSONString(thirdPayCreateParam));
        if (thirdPayCreateParam.getAmount() <= 0) {
            return ResultUtil.failResult(ResultEnum.e002, "支付金额需要大于0");
        }

        // 防重
        String redisLockKey = String.format("pay_lock:%s_%s",
            thirdPayCreateParam.getRelationId(), thirdPayCreateParam.getRelationType());
        if (redisService.setnx(redisLockKey, 2L) > 1) {
            return ResultUtil.failResult(ResultEnum.e008);
        }

        // 获取对应的支付处理对象
        ThirdPayHandler thirdPayHandler = this.getThirdPayHandler(thirdPayCreateParam.getThirdPaySource());

        // 查支付单
        ThirdPayExample example = new ThirdPayExample();
        example.createCriteria().andPayNoEqualTo(thirdPayCreateParam.getPayNo())
                .andThirdSourceEqualTo(thirdPayCreateParam.getThirdPaySource())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<ThirdPay> thirdPays = thirdPayMapper.selectByExample(example);
        ThirdPay thirdPay;
        if (!thirdPays.isEmpty()) {
            thirdPay = thirdPays.get(0);
            if (thirdPay.getPayStatus().equals(ThirdPayEnum.ThirdPayStatus.ALL_PAID.getStatus())) {
                return ResultUtil.failResult("交易单已支付");
            }
            if (!thirdPay.getPayStatus().equals(ThirdPayEnum.ThirdPayStatus.UNPAID.getStatus())
                    && !thirdPay.getPayStatus().equals(ThirdPayEnum.ThirdPayStatus.EXCEPTION_PAID.getStatus())) {
                return ResultUtil.failResult("交易单状态错误");
            }
        } else {
            // 写入支付单
            thirdPay = new ThirdPay();
            thirdPay.setPayNo(thirdPayCreateParam.getPayNo());
            thirdPay.setRelationId(thirdPayCreateParam.getRelationId());
            thirdPay.setRelationType(thirdPayCreateParam.getRelationType());
            thirdPay.setStoreId(ObjectUtils.defaultIfNull(thirdPayCreateParam.getStoreId(), 0L));
            thirdPay.setPayAmount(thirdPayCreateParam.getAmount());
            thirdPay.setActualPayAmount(0L);
            thirdPay.setQuantity(ObjectUtils.defaultIfNull(thirdPayCreateParam.getQuantity(), 1));
            thirdPay.setPayStatus(ThirdPayEnum.ThirdPayStatus.UNPAID.getStatus());
            thirdPay.setThirdSouceNo("");
            thirdPay.setThirdSource(thirdPayCreateParam.getThirdPaySource());
            thirdPay.setSourceFailedReason("");
            thirdPay.setMerchantId(thirdPayCreateParam.getMerchantId());
            thirdPay.setMerchantPay(thirdPayCreateParam.getMerchantPay());
            thirdPay.setDeleted(YesOrNoEnum.NO.getValue());
            thirdPay.setLastVer(1);
            thirdPay.setOpUserId(ObjectUtils.defaultIfNull(thirdPayCreateParam.getOpUserId(), 0L));
            thirdPay.setPayTime(0L);
            thirdPay.setPayOpTime(0L);
            long nowTime = System.currentTimeMillis();
            thirdPay.setCreateTime(nowTime);
            thirdPay.setOpTime(nowTime);
            if (thirdPayCreateParam.getThirdPayExtraVO() != null) {
                thirdPay.setExtra(JSON.toJSONString(thirdPayCreateParam.getThirdPayExtraVO()));
            }
            // todo 目前支付宝来源全部需要分账
            if (thirdPayCreateParam.getThirdPaySource().equals(ThirdPayEnum.ThirdPaySource.ALI_PAY.getSource())) {
                thirdPay.setRoyaltyStatus(ThirdPayEnum.PayRoyaltyStatus.WAIT_ROYALTY.getStatus());
            } else {
                thirdPay.setRoyaltyStatus(ThirdPayEnum.PayRoyaltyStatus.NO_ROYALTY.getStatus());
            }
            thirdPay.setRoyaltyAmount(0L);
            thirdPayMapper.insertSelective(thirdPay);
        }

        ThirdPayResult createPayResult = thirdPayHandler.createThirdPayOrder(thirdPayCreateParam);
        this.setPayStatus(thirdPay, createPayResult);
        if (!createPayResult.getSuccess()) {
            thirdPay.setSourceFailedReason(StringUtils.defaultString(createPayResult.getSourceFailedReason()));
        }
        thirdPay.setThirdSouceNo(StringUtils.defaultString(createPayResult.getThirdSourceNo()));
        thirdPayMapper.updateByPrimaryKeySelective(thirdPay);
        return ResultUtil.successResult(createPayResult);
    }

    @Override
    @WithSpan("三方退款")
    public Result<RefundResult> refund(RefundParam refundParam) {
        if (refundParam == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }
        ThirdPay thirdPay = thirdPayMapper.selectByPrimaryKey(refundParam.getPayId());
        if (thirdPay == null) {
            return ResultUtil.failResult(ResultEnum.e003);
        }
        log.info("退款 refundParam={}, thirdPay={}", JSON.toJSONString(refundParam), JSON.toJSONString(thirdPay));

//        // 防重
//        String redisLockKey = String.format("refund_lock:%s_%s",
//                refundParam.getRelationId(), refundParam.getRelationType());
//        if (redisService.setnx(redisLockKey, 2L) > 1) {
//            return ResultUtil.failResult(ResultEnum.e008);
//        }

        if (StringUtils.isBlank(refundParam.getRefundNo())) {
            refundParam.setRefundNo(UuidUtil.getUUID());
        }

        // 获取对应的支付处理对象
        ThirdPayHandler thirdPayHandler = this.getThirdPayHandler(refundParam.getThirdPaySource());

        ThirdRefundPay thirdRefundPay = new ThirdRefundPay();
        thirdRefundPay.setPayId(thirdPay.getId());
        thirdRefundPay.setRefundNo(refundParam.getRefundNo());
        thirdRefundPay.setAmount(refundParam.getAmount());
        thirdRefundPay.setRelationId(ObjectUtils.defaultIfNull(refundParam.getRelationId(), thirdPay.getRelationId()));
        thirdRefundPay.setRelationType(ObjectUtils.defaultIfNull(refundParam.getRelationType(), thirdPay.getRelationType()));
        thirdRefundPay.setStoreId(ObjectUtils.defaultIfNull(refundParam.getStoreId(), 0L));
        thirdRefundPay.setThirdSource(refundParam.getThirdPaySource());
        thirdRefundPay.setOpUserId(ObjectUtils.defaultIfNull(refundParam.getOpUserId(), 0L));
        long timeMillis = System.currentTimeMillis();
        thirdRefundPay.setOpTime(timeMillis);
        thirdRefundPay.setCreateTime(timeMillis);
        thirdRefundPay.setPayOpTime(0L);
        thirdRefundPay.setLastVer(1);
        thirdRefundPay.setDeleted(YesOrNoEnum.NO.getValue());
        thirdRefundPay.setMerchantId(refundParam.getMerchantId());
        thirdRefundPay.setStatus(ThirdPayEnum.ThirdRefundPayStatus.CANCEL.getStatus());
        if (refundParam.getThirdRefundExtraVO() != null) {
            thirdRefundPay.setExtra(JSON.toJSONString(refundParam.getThirdRefundExtraVO()));
        }

        Result<Integer> checkResult = this.checkRefundParam(refundParam, thirdPay);
        if (!checkResult.isSuccess()) {
            thirdRefundPay.setSourceFailedReason(checkResult.getMessage());
            thirdRefundPay.setStatus(ThirdPayEnum.ThirdRefundPayStatus.FAIL.getStatus());
            thirdRefundPayMapper.insertSelective(thirdRefundPay);
            return ResultUtil.failResult(checkResult.getMessage());
        } else {
            thirdRefundPayMapper.insertSelective(thirdRefundPay);
        }

        refundParam.setThirdSourceNo(thirdPay.getThirdSouceNo());
        refundParam.setMerchantPay(thirdPay.getMerchantPay());

        RefundResult refundResult = thirdPayHandler.refund(refundParam);

        if (!refundResult.getSuccess()) {
            thirdRefundPay.setSourceFailedReason(StringUtils.defaultString(refundResult.getSourceFailedReason()));
        } else {
            if (refundResult.getFundChange()) {
                // 退款成功后维护third_pay
                timeMillis = System.currentTimeMillis();
                thirdPayMapperEx.updateRefundAmount(thirdPay.getId(), refundResult.getActualAmount(), timeMillis);
                thirdRefundPay.setActualAmount(refundResult.getActualAmount());
                thirdRefundPay.setPayTime(timeMillis);
                thirdRefundPay.setPayOpTime(timeMillis);
            }
        }

        thirdRefundPay.setStatus(getRefundStatus(refundResult));
        thirdRefundPayMapper.updateByPrimaryKeySelective(thirdRefundPay);

        PostRefundParam postRefundParam = new PostRefundParam();
        postRefundParam.setRefundPayId(thirdRefundPay.getId());
        postRefundParam.setRefundResult(refundResult);
        thirdPayHandler.postToRefund(postRefundParam);

        return ResultUtil.successResult(refundResult);
    }

    @Override
    public void confirmRefund(PostRefundParam postRefundParam) {
        // 获取对应的支付处理对象
        ThirdPayHandler thirdPayHandler = this.getThirdPayHandler(postRefundParam.getThirdPaySource());
        thirdPayHandler.postToRefund(postRefundParam);
    }

    private Byte getRefundStatus(RefundResult refundResult) {
        if (!refundResult.getSuccess()) {
            return ThirdPayEnum.ThirdRefundPayStatus.FAIL.getStatus();
        }
        if (refundResult.getFundChange()) {
            return ThirdPayEnum.ThirdRefundPayStatus.SUCCESS.getStatus();
        } else {
            return ThirdPayEnum.ThirdRefundPayStatus.REFUNDING.getStatus();
        }
    }

    private ThirdRefundPayRoyalty buildThirdRefundPayRoyalty(RefundParam refundParam, ThirdRefundPay thirdRefundPay,
                                                                    ThirdPayRoyalty thirdPayRoyalty, TradeQueryResult.SettleDetail settleDetail) {
        ThirdRefundPayRoyalty thirdRefundPayRoyalty = new ThirdRefundPayRoyalty();
        thirdRefundPayRoyalty.setRefundPayId(thirdRefundPay.getId());
        thirdRefundPayRoyalty.setPayRoyaltyId(thirdPayRoyalty.getId());
        thirdRefundPayRoyalty.setRufundAmount(settleDetail.getAmount());
        thirdRefundPayRoyalty.setActualRufundAmount(settleDetail.getAmount());
        thirdRefundPayRoyalty.setStatus(ThirdPayEnum.ThirdRefundPayRoyaltyStatus.SUCCESS.getStatus());
        thirdRefundPayRoyalty.setThirdSource(thirdPayRoyalty.getThirdSource());
        thirdRefundPayRoyalty.setType(thirdPayRoyalty.getType());
        thirdRefundPayRoyalty.setAccount(thirdPayRoyalty.getAccount());
        thirdRefundPayRoyalty.setMerchantId(thirdPayRoyalty.getMerchantId());
        thirdRefundPayRoyalty.setDeleted(YesOrNoEnum.NO.getValue());
        thirdRefundPayRoyalty.setLastVer(1);
        thirdRefundPayRoyalty.setCreateTime(System.currentTimeMillis());
        thirdRefundPayRoyalty.setOpTime(System.currentTimeMillis());
        thirdRefundPayRoyalty.setOpUserId(refundParam.getOpUserId());

        return thirdRefundPayRoyalty;
    }

    @Override
    public Result<Boolean> updateThirdRefundExtraVO(Long payId, ThirdPayExtraVO thirdPayExtraVO) {
        if (payId == null || thirdPayExtraVO == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }
        ThirdPay thirdPay = thirdPayMapper.selectByPrimaryKey(payId);

        ThirdPay updThirdPay = new ThirdPay();
        updThirdPay.setId(thirdPay.getId());
        updThirdPay.setOpTime(System.currentTimeMillis());
        updThirdPay.setExtra(JSON.toJSONString(thirdPayExtraVO));
        thirdPayMapper.updateByPrimaryKeySelective(updThirdPay);
        return ResultUtil.successResult(true);
    }

    private Result<Integer> checkRefundParam(RefundParam refundParam, ThirdPay thirdPay) {
        Long amount = refundParam.getAmount();
        if (amount <= 0) {
            log.info("退款金额小于等于0, 无需退款 refundParam={}, thirdPay={}", JSON.toJSONString(refundParam), JSON.toJSONString(thirdPay));
            return ResultUtil.failResult("退款金额小于等于0");
        }
        if (amount > thirdPay.getActualPayAmount()) {
            log.info("退款金额大于剩余支付金额 refundParam={}, thirdPay={}", JSON.toJSONString(refundParam), JSON.toJSONString(thirdPay));
            return ResultUtil.failResult("退款金额大于剩余支付金额");
        }

        return ResultUtil.successResult(1);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, transactionManager = TransactionManagerName.BILL)
    @WithSpan("支付回调处理")
    public Result<PayNotifyResult> payNotify(PayNotifyParam payNotifyParam) {
        if (payNotifyParam == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }
        log.info("支付回调参数：{}", JSON.toJSONString(payNotifyParam));
        ThirdPayExample thirdPayExample = new ThirdPayExample();
        thirdPayExample.createCriteria().andPayNoEqualTo(payNotifyParam.getPayNo())
                .andThirdSourceEqualTo(payNotifyParam.getThirdPaySource()).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<ThirdPay> thirdPayList = thirdPayMapper.selectByExample(thirdPayExample);
        if (ObjectUtils.isEmpty(thirdPayList)) {
            return ResultUtil.failResult(ResultEnum.e004);
        }
        ThirdPay thirdPay = thirdPayList.get(0);

        if (thirdPay.getPayStatus().equals(ThirdPayEnum.ThirdPayStatus.ALL_PAID.getStatus())) {
            PayNotifyResult payNotifyResult = buildPayNotifyResult(thirdPay);
            return ResultUtil.successResult(payNotifyResult);
        }

        if (thirdPay.getPayAmount() < payNotifyParam.getTotalAmount()) {
            thirdPay.setPayStatus(ThirdPayEnum.ThirdPayStatus.PART_PAID.getStatus());
        } else {
            thirdPay.setPayStatus(ThirdPayEnum.ThirdPayStatus.ALL_PAID.getStatus());
        }
        thirdPay.setThirdSouceNo(payNotifyParam.getThirdSouceNo());
        thirdPay.setActualPayAmount(payNotifyParam.getTotalAmount());
        thirdPay.setPayTime(payNotifyParam.getPayTime());
        thirdPay.setPayOpTime(payNotifyParam.getPayOpTime());
        thirdPay.setOpTime(System.currentTimeMillis());
        thirdPay.setRemainPayAmount(payNotifyParam.getTotalAmount());
        thirdPayMapper.updateByPrimaryKeySelective(thirdPay);

        PayNotifyResult payNotifyResult = buildPayNotifyResult(thirdPay);

        handleBizCallBack(thirdPay);

        return ResultUtil.successResult(payNotifyResult);
    }

    @NotNull
    private static PayNotifyResult buildPayNotifyResult(ThirdPay thirdPay) {
        PayNotifyResult payNotifyResult = new PayNotifyResult();
        payNotifyResult.setPayId(thirdPay.getId());
        payNotifyResult.setPayNo(thirdPay.getPayNo());
        payNotifyResult.setPayAmount(thirdPay.getPayAmount());
        payNotifyResult.setActualPayAmount(thirdPay.getActualPayAmount());
        payNotifyResult.setPayStatus(thirdPay.getPayStatus());
        payNotifyResult.setThirdSouceNo(thirdPay.getThirdSouceNo());
        payNotifyResult.setThirdSource(thirdPay.getThirdSource());
        payNotifyResult.setMerchantId(thirdPay.getMerchantId());
        payNotifyResult.setRelationId(thirdPay.getRelationId());
        payNotifyResult.setRelationType(thirdPay.getRelationType());
        return payNotifyResult;
    }

    @Override
    @WithSpan("关闭交易单")
    public Result<ClosePayResult> closePay(ClosePayParam closePayParam) {
        if (closePayParam == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }
        ThirdPay thirdPay = thirdPayMapper.selectByPrimaryKey(closePayParam.getPayId());
        if (thirdPay == null) {
            return ResultUtil.failResult(ResultEnum.e003);
        }
        if (thirdPay.getPayStatus().equals(ThirdPayEnum.ThirdPayStatus.CLOSED.getStatus())) {
            return ResultUtil.successResult(ClosePayResult.builder().success(true).build());
        }
        if (!thirdPay.getPayStatus().equals(ThirdPayEnum.ThirdPayStatus.UNPAID.getStatus())
                && !thirdPay.getPayStatus().equals(ThirdPayEnum.ThirdPayStatus.EXCEPTION_PAID.getStatus())) {
            return ResultUtil.failResult("交易单状态异常");
        }
        closePayParam.setPayNo(thirdPay.getPayNo());

        // 获取对应的支付处理对象
        ThirdPayHandler thirdPayHandler = this.getThirdPayHandler(closePayParam.getThirdPaySource());
        ClosePayResult closePayResult = thirdPayHandler.closePay(closePayParam);

        if (!closePayResult.getSuccess()) {
            return ResultUtil.failResult(closePayResult.getSourceFailedReason());
        }

        // 更新pay表
        ThirdPay updThirdPay = new ThirdPay();
        updThirdPay.setId(thirdPay.getId());
        updThirdPay.setPayStatus(ThirdPayEnum.ThirdPayStatus.CLOSED.getStatus());
        updThirdPay.setOpTime(System.currentTimeMillis());
        thirdPayMapper.updateByPrimaryKeySelective(updThirdPay);
        return ResultUtil.successResult(closePayResult);
    }

    @Override
    @WithSpan("超时关闭交易单任务")
    public void closePayByJob(boolean hasAll) {
        ThirdPayExample thirdPayExample = new ThirdPayExample();
        Long payTimeOutMillis = System.currentTimeMillis() - 1000L * 60 * payTimeoutMinutes;
        ThirdPayExample.Criteria criteria = thirdPayExample.createCriteria().andPayStatusEqualTo(ThirdPayEnum.ThirdPayStatus.UNPAID.getStatus())
                .andCreateTimeLessThanOrEqualTo(payTimeOutMillis)
                .andThirdSourceEqualTo(ThirdPayEnum.ThirdPaySource.ALI_PAY.getSource())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        if (BooleanUtils.isFalse(hasAll)) {
            criteria.andCreateTimeGreaterThan(payTimeOutMillis - 1000L * 60 * 60 * 3); // todo 简单设置3小时阈值 避免任务一直执行下去;
        }
        List<ThirdPay> thirdPayList = thirdPayMapper.selectByExample(thirdPayExample);


        PayExample payExample = new PayExample();
        PayExample.Criteria payCriteria = payExample.createCriteria().andPayStatusEqualTo(PayStatusEnum.UNPAID.getStatus())
                .andPaySourceEqualTo(PaySourceEnum.BAOFU.getSource())
                .andCreateTimeLessThanOrEqualTo(payTimeOutMillis);
        if (BooleanUtils.isFalse(hasAll)) {
            payCriteria.andCreateTimeGreaterThan(payTimeOutMillis - 1000L * 60 * 60 * 3); // todo 简单设置3小时阈值 避免任务一直执行下去;
        }
        List<Pay> pays = payMapper.selectByExample(payExample);
        log.info("超时关闭交易单 任务，查询到宝付超时交易单：{}", JSON.toJSONString(pays));


        if (CollectionUtils.isEmpty(thirdPayList) && CollectionUtils.isEmpty(pays)) {
            log.info("超时关闭交易单 任务，无超时交易单");
            return;
        }
        if (CollectionUtils.isNotEmpty(thirdPayList)) {
            for (ThirdPay thirdPay : thirdPayList) {
                try {
                    // 更新业务
                    Boolean result = handleBizClosePay(thirdPay);
                    if (!result) {
                        continue;
                    }
                    ClosePayParam closePayParam = new ClosePayParam();
                    closePayParam.setPayId(thirdPay.getId());
                    closePayParam.setThirdPaySource(thirdPay.getThirdSource());
                    closePayParam.setPayNo(thirdPay.getPayNo());
                    Result<ClosePayResult> closePayResultResult = this.closePay(closePayParam);
                    if (!closePayResultResult.isSuccess()) {
                        log.error("超时关闭交易单 失败，交易单号：{}, thirdPay:{}， closePayResultResult：{}", thirdPay.getPayNo(),
                                JSON.toJSONString(thirdPay), JSON.toJSONString(closePayResultResult));
                    } else {
                        log.info("超时关闭交易单 成功，交易单号：{} closePayResultResult={}", thirdPay.getPayNo(), JSON.toJSONString(closePayResultResult));
                    }
                } catch (Exception e) {
                    log.error("超时关闭交易单 异常，交易单号：{}, thirdPay:{}", thirdPay.getPayNo(),
                            JSON.toJSONString(thirdPay), e);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(pays)) {
            for (Pay pay : pays) {
                try {
                    // 更新业务
                    Boolean result = handleBizClosePay(pay);
                    if (!result) {
                        continue;
                    }
                    pay.setPayStatus(ThirdPayEnum.ThirdPayStatus.CLOSED.getStatus());
                    pay.setOpTime(System.currentTimeMillis());
                    payMapper.updateByPrimaryKeySelective(pay);
                } catch (Exception e) {
                    log.error("超时关闭交易单 异常, thirdPay:{}", JSON.toJSONString(pay), e);
                }
            }
        }
    }

    /**
     * 超时关闭业务处理
     * @param thirdPay
     */
    private Boolean handleBizClosePay(ThirdPay thirdPay) {
        ThirdPayExtraVO thirdPayExtraVO;
        if (StringUtils.isNotBlank(thirdPay.getExtra())) {
            thirdPayExtraVO = JSON.parseObject(thirdPay.getExtra(), ThirdPayExtraVO.class);
            if (thirdPay.getRelationType().equals(ThirdPayEnum.RelationType.RENT_ORDER.getType())) {
                Result<Boolean> closeResult = orderService.closeOrder(thirdPay.getRelationId());
                if (!closeResult.isSuccess()) {
                    throw new BizException("关闭订单失败");
                }
            } else if (thirdPay.getRelationType().equals(ThirdPayEnum.RelationType.RERENT.getType())) {
                Result<Boolean> closeResult = orderService.closeRerentOrder(thirdPay.getRelationId());
                if (!closeResult.isSuccess()) {
                    throw new BizException("关闭续租订单失败");
                }
            }
        } else {
            return false;
        }

        return true;
    }

    private Boolean handleBizClosePay(Pay pay) {
        if (Objects.equals(pay.getFeeType(), PayFeeTypeEnum.ORDER.getType())) {
            Result<Boolean> closeResult = orderService.closeOrder(pay.getOrderId());
            if (!closeResult.isSuccess()) {
                throw new BizException("关闭订单失败");
            }
        } else if (Objects.equals(pay.getFeeType(), PayFeeTypeEnum.RERENT.getType())) {
            Result<Boolean> closeResult = orderService.closeRerentOrder(pay.getSubOrderId());
            if (!closeResult.isSuccess()) {
                throw new BizException("关闭续租订单失败");
            }
        }
        return true;
    }


    /**
     * 回调处理业务
     * @param thirdPay
     */
    private void handleBizCallBack(ThirdPay thirdPay) {
        if (thirdPay.getRelationType().equals(ThirdPayEnum.RelationType.RENT_ORDER.getType())) {
            Result<Boolean> payStatusResult = orderService.updateOrderPayStatus(thirdPay.getRelationId(),
                    PayStatusEnum.ALL_PAID.getStatus().intValue(), thirdPay.getActualPayAmount().intValue(), thirdPay.getPayNo());
            if (!payStatusResult.isSuccess()) {
                throw new BizException("更新订单支付状态失败");
            }
        }
        if (thirdPay.getRelationType().equals(ThirdPayEnum.RelationType.RERENT.getType())) {
            Result<Boolean> payStatusResult = orderService.updateRerentOrderPayStatus(thirdPay.getRelationId(),
                    PayStatusEnum.ALL_PAID.getStatus().intValue(), thirdPay.getActualPayAmount().intValue(), thirdPay.getPayNo());
            if (!payStatusResult.isSuccess()) {
                throw new BizException("更新订单支付状态失败");
            }
        }
    }

    @Override
    @WithSpan("绑定分账关系")
    public Result<RoyaltyResult> royaltyBind(RoyaltyRelationParam royaltyRelationParam) {
        if (royaltyRelationParam == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }

        // 获取对应的支付处理对象
        ThirdPayHandler thirdPayHandler = this.getThirdPayHandler(royaltyRelationParam.getThirdSource());
        RoyaltyResult result = thirdPayHandler.royaltyBind(royaltyRelationParam);

        return ResultUtil.successResult(result);
    }

    @Override
    @WithSpan("解绑分账关系")
    public Result<RoyaltyResult> royaltyUnbind(RoyaltyRelationParam royaltyRelationParam) {
        if (royaltyRelationParam == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }

        // 获取对应的支付处理对象
        ThirdPayHandler thirdPayHandler = this.getThirdPayHandler(royaltyRelationParam.getThirdSource());
        RoyaltyResult result = thirdPayHandler.royaltyUnbind(royaltyRelationParam);

        return ResultUtil.successResult(result);
    }

    @Override
    @WithSpan("分账结算")
    public Result<OrderSettleResult> orderSettle(OrderSettleParam orderSettleParam) {
        if (orderSettleParam == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }
        Span span = Span.current();
        SpanEnhancer.of(span).withJson("参数", orderSettleParam);

        // 查支付订单
        ThirdPayExample thirdPayExample = new ThirdPayExample();
        thirdPayExample.createCriteria().andIdEqualTo(orderSettleParam.getPayId())
                .andThirdSourceEqualTo(orderSettleParam.getThirdSource()).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<ThirdPay> thirdPayList = thirdPayMapper.selectByExample(thirdPayExample);
        if (ObjectUtils.isEmpty(thirdPayList)) {
            return ResultUtil.failResult(ResultEnum.e004);
        }
        ThirdPay thirdPay = thirdPayList.get(0);

        if (!thirdPay.getPayStatus().equals(ThirdPayEnum.ThirdPayStatus.ALL_PAID.getStatus())) {
            return ResultUtil.failResult("该订单未支付");
        }
        if (thirdPay.getRoyaltyStatus().equals(ThirdPayEnum.PayRoyaltyStatus.NO_ROYALTY.getStatus())) {
            return ResultUtil.failResult("该订单无需分账");
        }
        if (thirdPay.getRoyaltyStatus().equals(ThirdPayEnum.PayRoyaltyStatus.SUCCESS.getStatus())) {
            return ResultUtil.failResult("该订单已分账");
        }
        if (thirdPay.getRemainPayAmount() <= 0L) {
            return ResultUtil.failResult("该订单剩余金额不足");
        }

        // 查商家分账账号
        RoyaltyRelationQuery query = new RoyaltyRelationQuery();
        query.setStatus(YesOrNoEnum.YES.getValue());
        query.setMerchantId(thirdPay.getMerchantId());
        query.setThirdSource(orderSettleParam.getThirdSource());
        Result<List<RoyaltyRelationVO>> royaltyRelationResult = royaltyRelationService.getRoyaltyRelationList(query);
        if (royaltyRelationResult == null || CollectionUtils.isEmpty(royaltyRelationResult.getModel())) {
            return ResultUtil.failResult("分账关系未开通或不存在");
        }
        // todo 分账账号只有一个
        RoyaltyRelationVO royaltyRelation = royaltyRelationResult.getModel().get(0);

        orderSettleParam.setPayNo(thirdPay.getPayNo());
        orderSettleParam.setThirdSouceNo(thirdPay.getThirdSouceNo());
        orderSettleParam.setTransIn(royaltyRelation.getAccount());
        orderSettleParam.setTransInType(royaltyRelation.getType());
        orderSettleParam.setName(royaltyRelation.getName());
        if (orderSettleParam.getAmount() == null) {
            // 分给商家的金额向下取整
            long royaltyAmount = BigDecimal.valueOf(thirdPay.getRemainPayAmount()).multiply(royaltyRelation.getRoyaltyRate())
                    .setScale(0, RoundingMode.DOWN)
                    .divide(BigDecimal.valueOf(100), 0, RoundingMode.DOWN).longValue();
            orderSettleParam.setAmount(royaltyAmount);
        }

        // 获取对应的支付处理对象
        ThirdPayHandler thirdPayHandler = this.getThirdPayHandler(orderSettleParam.getThirdSource());

        long time = System.currentTimeMillis();
        ThirdPayRoyalty payRoyalty = new ThirdPayRoyalty();
        payRoyalty.setDeleted(YesOrNoEnum.NO.getValue());
        payRoyalty.setMerchantId(thirdPay.getMerchantId());
        payRoyalty.setReqNo(orderSettleParam.getRequestNo());
        payRoyalty.setRoyaltyAmount(orderSettleParam.getAmount());
        payRoyalty.setActualRoyaltyAmount(0L);
        payRoyalty.setRemindRoyaltyAmount(0L);
        payRoyalty.setStatus(ThirdPayEnum.ThirdRoyaltyStatus.UN_ROYALTY.getStatus());
        payRoyalty.setOpUserId(orderSettleParam.getOpUserId());
        payRoyalty.setPayId(orderSettleParam.getPayId());
        payRoyalty.setThirdSource(orderSettleParam.getThirdSource());
        payRoyalty.setRoyaltyRate(royaltyRelation.getRoyaltyRate());
        payRoyalty.setAccount(royaltyRelation.getAccount());
        payRoyalty.setType(royaltyRelation.getType());
        payRoyalty.setCreateTime(time);
        payRoyalty.setOpTime(time);
        payRoyalty.setLastVer(1);
        thirdPayRoyaltyMapper.insertSelective(payRoyalty);

        OrderSettleResult result = thirdPayHandler.orderSettle(thirdPay.getMerchantId(), orderSettleParam);
        if (!result.isSuccess()) {
            payRoyalty.setStatus(ThirdPayEnum.ThirdRoyaltyStatus.FAIL.getStatus());
            payRoyalty.setSourceFailedReason(result.getSourceFailedReason());
            thirdPayRoyaltyMapper.updateByPrimaryKeySelective(payRoyalty);
            return ResultUtil.failResult(result.getSourceFailedReason());
        }

        payRoyalty.setStatus(ThirdPayEnum.ThirdRoyaltyStatus.SUCCESS.getStatus());
        payRoyalty.setThirdSouceNo(result.getSettleNo());
        payRoyalty.setActualRoyaltyAmount(orderSettleParam.getAmount());
        payRoyalty.setRemindRoyaltyAmount(orderSettleParam.getAmount());
        thirdPayRoyaltyMapper.updateByPrimaryKeySelective(payRoyalty);

        ThirdPay updThirdPay = new ThirdPay();
        updThirdPay.setRoyaltyAmount(payRoyalty.getActualRoyaltyAmount());
        updThirdPay.setId(thirdPay.getId());
        updThirdPay.setOpTime(System.currentTimeMillis());
        updThirdPay.setRoyaltyStatus(ThirdPayEnum.PayRoyaltyStatus.SUCCESS.getStatus());
        thirdPayMapper.updateByPrimaryKeySelective(updThirdPay);

        return ResultUtil.successResult(result);
    }

    @Override
    @WithSpan("分账结算任务")
    public void orderRoyaltySettleByJob() {
        ThirdPayExample thirdPayExample = new ThirdPayExample();
        thirdPayExample.createCriteria()
                .andPayTimeLessThan(System.currentTimeMillis())
                .andRoyaltyStatusEqualTo(ThirdPayEnum.PayRoyaltyStatus.WAIT_ROYALTY.getStatus())
                .andPayStatusEqualTo(ThirdPayEnum.ThirdPayStatus.ALL_PAID.getStatus())
                .andRemainPayAmountGreaterThan(0L)
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<ThirdPay> thirdPayList = thirdPayMapper.selectByExample(thirdPayExample);
        if (CollectionUtils.isNotEmpty(thirdPayList)) {
            for (ThirdPay thirdPay : thirdPayList) {
                try {
                    OrderSettleParam orderSettleParam = new OrderSettleParam();
                    orderSettleParam.setPayId(thirdPay.getId());
                    orderSettleParam.setRequestNo(UuidUtil.getUUID());
                    orderSettleParam.setThirdSource(thirdPay.getThirdSource());
                    Result<OrderSettleResult> resultResult = this.orderSettle(orderSettleParam);
                    log.info("分账结算结果 merchantId:{},thirdPay:{}, resultResult:{}",
                            thirdPay.getMerchantId(), JSON.toJSONString(thirdPay), JSON.toJSONString(resultResult));
                } catch (Exception e) {
                    log.error("分账结算异常 merchantId:{},thirdPay:{}", thirdPay.getMerchantId(), JSON.toJSONString(thirdPay), e);
                }
            }
        }
    }


    private ThirdPayHandler getThirdPayHandler(Integer thirdPaySource) {
        ThirdPayHandler thirdPayHandler = THIRD_FACTORY.get(thirdPaySource);
        if (thirdPayHandler == null) {
            throw new BizException("暂不支持的支持方式");
        }
        return thirdPayHandler;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ObjectProvider<ThirdPayHandler> provider = applicationContext.getBeanProvider(ThirdPayHandler.class);
        Map<Integer, ThirdPayHandler> tempMap = new HashMap<>();
        provider.forEach(e -> tempMap.put(e.thirdPaySource(), e));
        THIRD_FACTORY = Collections.unmodifiableMap(tempMap);
    }

    /**
     * 根据支付结果 设置支付单的支付状态
     * @param payResult
     * @param thirdPay
     */
    private void setPayStatus(ThirdPay thirdPay, ThirdPayResult payResult) {
        // 本次支付成功
        if (payResult.getSuccess()) {
            Long actualPayAmount = payResult.getActualPayAmount();
            if (actualPayAmount != null && actualPayAmount > 0) {
                long paidAmount = ObjectUtils.defaultIfNull(thirdPay.getActualPayAmount(), 0L);
                paidAmount = actualPayAmount + paidAmount;
                if (paidAmount == thirdPay.getPayAmount()) {
                    thirdPay.setPayStatus(ThirdPayEnum.ThirdPayStatus.ALL_PAID.getStatus());
                } else {
                    thirdPay.setPayStatus(ThirdPayEnum.ThirdPayStatus.PART_PAID.getStatus());
                }
            }

        } else {
            // 支付异常
            thirdPay.setPayStatus(ObjectUtils.defaultIfNull(payResult.getPayStatus(),
                ThirdPayEnum.ThirdPayStatus.EXCEPTION_PAID.getStatus()));
        }
    }


    @Override
    public Result<List<ThirdPayVO>> getPayDetailList(ThirdPayQuery query) {
        if (query == null) {
            return ResultUtil.failResult("参数为空");
        }
        ThirdPayExample example = new ThirdPayExample();
        ThirdPayExample.Criteria criteria = example.createCriteria();

        if (query.getId() != null) {
            criteria.andIdEqualTo(query.getId());
        }
        if (query.getIds() != null && !query.getIds().isEmpty()) {
            criteria.andIdIn(query.getIds());
        }
        if (query.getRelationId() != null) {
            criteria.andRelationIdEqualTo(query.getRelationId());
        }
        if (query.getRelationType() != null) {
            criteria.andRelationTypeEqualTo(query.getRelationType());
        }
        if (StringUtils.isNotBlank(query.getPayNo())) {
            criteria.andPayNoEqualTo(query.getPayNo());
        }
        if (query.getPayStatus() != null) {
            criteria.andPayStatusEqualTo(query.getPayStatus());
        }
        if (query.getPayStatusList() != null && !query.getPayStatusList().isEmpty()) {
            criteria.andPayStatusIn(query.getPayStatusList());
        }
        if (query.getThirdSource() != null) {
            criteria.andThirdSourceEqualTo(query.getThirdSource());
        }
        if (query.getMerchantPay() != null) {
            criteria.andMerchantPayEqualTo(query.getMerchantPay());
        }
        if (query.getMerchantId() != null) {
            criteria.andMerchantIdEqualTo(query.getMerchantId());
        }
        criteria.andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<ThirdPay> thirdPays = thirdPayMapper.selectByExample(example);
        if (thirdPays.isEmpty()) {
            return ResultUtil.successResult(new ArrayList<>());
        }
        List<Long> payIds = thirdPays.stream().map(ThirdPay::getId).collect(Collectors.toList());

        ThirdRefundPayExample refundExample = new ThirdRefundPayExample();
        refundExample.createCriteria().andPayIdIn(payIds).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        Map<Long, List<ThirdRefundPay>> refundPayMap =
                thirdRefundPayMapper.selectByExample(refundExample).stream().collect(Collectors.groupingBy(ThirdRefundPay::getPayId));

        List<ThirdPayVO> voList = new ArrayList<>();
        for (ThirdPay thirdPay : thirdPays) {
            ThirdPayVO vo = new ThirdPayVO();
            BeanUtils.copyProperties(thirdPay, vo);
            vo.setRefundedAmount(thirdPay.getActualPayAmount() - thirdPay.getRemainPayAmount());
            if (StringUtils.isNotBlank(thirdPay.getExtra())) {
                vo.setExtraVO(JSON.parseObject(thirdPay.getExtra(), ThirdPayExtraVO.class));
            }
            List<ThirdRefundPay> thirdRefundPays = refundPayMap.get(thirdPay.getId());
            setRefundPay(thirdRefundPays, vo);
            voList.add(vo);
        }
        return ResultUtil.successResult(voList);
    }

    private void setRefundPay(List<ThirdRefundPay> thirdRefundPays, ThirdPayVO vo) {
        List<ThirdRefundPayVO> thirdRefundPayVOList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(thirdRefundPays)) {
            for (ThirdRefundPay refundPay : thirdRefundPays) {
                ThirdRefundPayVO refundPayVO = new ThirdRefundPayVO();
                BeanUtils.copyProperties(refundPay, refundPayVO);
                if (StringUtils.isNotBlank(refundPay.getExtra())) {
                    refundPayVO.setExtraVO(JSON.parseObject(refundPay.getExtra(), ThirdRefundExtraVO.class));
                }
                thirdRefundPayVOList.add(refundPayVO);
            }
        }
        vo.setThirdRefundPayVOList(thirdRefundPayVOList);
    }
}
