package com.ql.rent.provider.trade;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qinglusaas.logutil.QingluLogger;
import com.qinglusaas.logutil.QingluLoggerFactory;
import com.qinglusaas.logutil.annotation.EventLog;
import com.qinglusaas.logutil.constant.BusinessCode;
import com.ql.dto.open.request.PlatformBaseRequest;
import com.ql.dto.open.request.trade.CheckCarPickReturnRequest;
import com.ql.dto.open.response.trade.CheckCarPickReturnResponse;
import com.ql.rent.api.aggregate.dto.OrderExtraDTO;
import com.ql.rent.api.aggregate.model.contant.FeeEnum;
import com.ql.rent.api.aggregate.model.contant.PickReturnErrorEnum;
import com.ql.dto.price.ExpenseRecordDTO;
import com.ql.rent.api.aggregate.model.remote.ctrip.dto.RemainingEnergyDTO;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.request.CarImageReq;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.request.PushPickReturnToVendorRequest;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.response.CtripCheckCarPickReturnResponse;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.response.PushPickReturnToVendorResponse;
import com.ql.rent.api.aggregate.remote.api.APIClient;
import com.ql.rent.api.aggregate.remote.vo.request.OrderInfoCallbackScheduledReq;
import com.ql.rent.api.aggregate.remote.vo.request.OrderInfoStatusCallBackReq;
import com.ql.rent.api.aggregate.remote.vo.response.OrderInfoCallbackV2Resp;
import com.ql.rent.common.FileUploader;
import com.ql.rent.component.OssComponent;
import com.ql.rent.dao.trade.*;
import com.ql.rent.entity.trade.*;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.common.ResultEnum;
import com.ql.rent.enums.trade.SelfPrStatusEnum;
import com.ql.rent.enums.trade.VehiclePickReturnEnum;
import com.ql.rent.enums.trade.*;
import com.ql.rent.enums.vehicle.FuelFormEnum;
import com.ql.rent.multipledatasource.TransactionManagerName;
import com.ql.rent.open.config.OpenMerchantConfig;
import com.ql.rent.param.trade.ConfirmReturnParam;
import com.ql.rent.param.trade.SelfPickReturnConfirmVO;
import com.ql.rent.param.trade.VehiclePickReturnParam;
import com.ql.rent.param.vehicle.VehicleDeviceUpdateRequest;
import com.ql.rent.provider.store.StoreInfoServiceImpl;
import com.ql.rent.service.store.IStoreInfoService;
import com.ql.rent.service.trade.*;
import com.ql.rent.service.vehicle.*;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.third.dto.VehicleDeviceStateDTO;
import com.ql.rent.util.GisUtils;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.store.LongLatVo;
import com.ql.rent.vo.store.StoreInfoVo;
import com.ql.rent.vo.trade.DepositVO;
import com.ql.rent.vo.trade.OrderSelfOperationExtraVo;
import com.ql.rent.vo.trade.VehicleReturnExpenseItemPropVO;
import com.ql.rent.vo.trade.VehicleReturnExpenseItemVO;
import com.ql.rent.vo.vehicle.VehicleFuelFormSettingVO;
import com.ql.rent.vo.vehicle.VehicleSelfSettingDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @desc 自助取还履约
 */
@Service
@Slf4j
public class ThirdSelfPickReturnServiceImpl implements IThirdSelfPickReturnService {

    QingluLogger logger = QingluLoggerFactory.getLogger(ThirdSelfPickReturnServiceImpl.class);

    @Resource
    private OrderSelfPickReturnOperationMapper orderSelfPickReturnOperationMapper;
    @Resource
    private OrderSelfPickReturnOperationAttMapper orderSelfPickReturnOperationAttMapper;
    @Resource
    private VehiclePickReturnMapper vehiclePickReturnMapper;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private IOrderDepositService orderDepositService;
    @Resource
    private APIClient apiClient;
    @Resource
    private OssComponent ossComponent;
    @Resource
    private IVehicleDeviceSettingService vehicleDeviceSettingService;
    @Resource
    private IVehicleFuelFormSettingService vehicleFuelFormSettingService;
    @Resource
    private IVehicleSelfSettingService vehicleSelfSettingService;
    @Resource
    private VehicleReturnExpenseItemMapper vehicleReturnExpenseItemMapper;
    @Resource
    private IVehicleReturnExpenseItemService vehicleReturnExpenseItemService;
    @Resource
    private PayMapper payMapper;
    @Resource
    private OrderComponent orderComponent;
    @Resource
    private IOrderService orderService;
    @Resource
    private IVehicleInfoService vehicleInfoService;
    @Resource
    private IOrderBillService orderBillService;
    @Resource
    private IVehicleReturnExpenseItemPropService vehicleReturnExpenseItemPropService;
    @Resource
    private OrderMsgProducer orderMsgProducer;
    @Resource
    private Executor asyncPromiseExecutor;
    @Resource
    private IVehicleIllegalContractService vehicleIllegalContractService;
    @Resource
    private IStoreInfoService iStoreInfoService;
    @Resource
    private IOrderReconciliationService orderReconciliationService;
    @Resource
    private OpenMerchantConfig openMerchantConfig;

    @Override
    @EventLog(businessCode = BusinessCode.ORDER_SELF_CHECK_PICK_RETURN)
    public Result<CtripCheckCarPickReturnResponse> checkCarPickReturn(Long channelId, Long merchantId,
                                                                 CheckCarPickReturnRequest request) {
        logger.startLog().businessCode(BusinessCode.ORDER_SELF_CHECK_PICK_RETURN).with("param", request)
                .ext(request.getOrderId()).channel(channelId).log("自助取还车校验");
        OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(request.getOrderId());
        if (orderInfo == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }
//        if (!orderInfo.getSelfPrOrder().equals(SelfPrOrderEnum.SELF_PR.getValue()) && StringUtils.isBlank(orderInfo.getSourceOrderId())) {
//            return ResultUtil.failResult("非自助取还订单");
//        }
        Long vehicleId = orderInfo.getVehicleId();
        boolean isPickReturn = true;
        List<CtripCheckCarPickReturnResponse.FailInfo> failInfos = new ArrayList<>();
        CtripCheckCarPickReturnResponse response = new CtripCheckCarPickReturnResponse();
        VehicleDeviceStateDTO vehicleDeviceStateDTO = vehicleDeviceSettingService.deviceState(merchantId, vehicleId, null);
        if (Objects.isNull(vehicleDeviceStateDTO)) {
            log.info("自助取还,取还车校验车机状态merchantId:{} orderId:{}  is null", merchantId, request.getOrderId());
            CtripCheckCarPickReturnResponse.FailInfo failInfo = new CtripCheckCarPickReturnResponse.FailInfo();
            failInfo.setCode(PickReturnErrorEnum.OTHER.getCode());
            failInfo.setMessage(PickReturnErrorEnum.OTHER.getMessage());
            failInfos.add(failInfo);
            return ResultUtil.failResult("车机状态获取失败");
        }
        LongLatVo vehicleGPS = new LongLatVo();
        vehicleGPS.setLongitude(vehicleDeviceStateDTO.getLng().doubleValue());
        vehicleGPS.setLatitude(vehicleDeviceStateDTO.getLat().doubleValue());

        // 取车校验，取车时间有限制
        if (request.getOperateType() == 1) {
            if (!SelfPrStatusEnum.PREPARED.getStatus().equals(orderInfo.getSelfPrStatus())) {
                isPickReturn = false;
                CtripCheckCarPickReturnResponse.FailInfo failInfo = new CtripCheckCarPickReturnResponse.FailInfo();
                failInfo.setCode(PickReturnErrorEnum.ORDER_STATUS_NOT_SATISFIED.getCode());
                failInfo.setMessage(PickReturnErrorEnum.ORDER_STATUS_NOT_SATISFIED.getMessage());
                failInfos.add(failInfo);
            }
            if (orderInfo.getFreeDepositDegree() != FreeDepositTypeEnum.FREE_ALL.getType().intValue()) {
                isPickReturn = false;
                CtripCheckCarPickReturnResponse.FailInfo failInfo = new CtripCheckCarPickReturnResponse.FailInfo();
                failInfo.setCode(PickReturnErrorEnum.ORDER_NOT_DEPOSIT.getCode());
                failInfo.setMessage(PickReturnErrorEnum.ORDER_NOT_DEPOSIT.getMessage());
                failInfos.add(failInfo);
            }
            response.setPickReason(failInfos);
        }

        if (2 == request.getOperateType()) {
            List<VehicleSelfSettingDTO> vehicleSelfSettingDTOS = vehicleSelfSettingService.selfSetting(merchantId, null, vehicleId);
            // 11 未在可还车距离
            // 门店开启自助取还服务。还车也可以在门店还车范围内
            boolean inPoint = false;
            Long returnStoreId = orderInfo.getReturnStoreId();
            Result<StoreInfoVo> storeInfoVoResult = iStoreInfoService.storeInfoBaseFind(returnStoreId);
            if (storeInfoVoResult.isSuccess() && Objects.nonNull(storeInfoVoResult.getModel())) {
                try {
                    if (BooleanUtils.isTrue(storeInfoVoResult.getModel().getSelfServiceReturn())) {
                        LongLatVo storeLongLat = storeInfoVoResult.getModel().getLongLat();
                        log.info("vehicleGPS:{} {}  storeGPS:{} {}", vehicleGPS.getLatitude(), vehicleGPS.getLongitude(),
                                storeLongLat.getLatitude(), storeLongLat.getLongitude());
                        double distance = GisUtils.getDistance(vehicleGPS.getLatitude(), vehicleGPS.getLongitude(),
                                storeLongLat.getLatitude(), storeLongLat.getLongitude());
                        if (distance < storeInfoVoResult.getModel().getSelfServiceDistance() * 1000) {
                            inPoint = true;
                        }
                    }
                } catch (Exception e) {
                    log.error("自助取还,取还车校验 门店范围内还车异常", e);
                }
            }
            if (CollectionUtils.isNotEmpty(vehicleSelfSettingDTOS) && BooleanUtils.isFalse(inPoint)) {
                for (VehicleSelfSettingDTO selfSettingDTO : vehicleSelfSettingDTOS) {
                    if (inPoint) {
                        continue;
                    }
                    byte[] selfCircle = selfSettingDTO.getSelfCircle();
                    try {
                        Geometry geometry = StoreInfoServiceImpl.getGeometryByBytes(selfCircle);
                        if (Objects.nonNull(geometry)) {
                            List<LongLatVo> selfSetting = Lists.newArrayList();
                            for (Coordinate coordinate : geometry.getCoordinates()) {
                                LongLatVo longLatVo = new LongLatVo();
                                longLatVo.setLongitude(coordinate.getX());
                                longLatVo.setLatitude(coordinate.getY());
                                selfSetting.add(longLatVo);
                            }
                            inPoint = GisUtils.isPointInPolygon(selfSetting, vehicleGPS);
                        }
                    } catch (Exception e) {
                        CtripCheckCarPickReturnResponse.FailInfo failInfo = new CtripCheckCarPickReturnResponse.FailInfo();
                        failInfo.setCode(PickReturnErrorEnum.OTHER.getCode());
                        failInfo.setMessage(PickReturnErrorEnum.OTHER.getMessage());
                        failInfos.add(failInfo);
                        isPickReturn = false;
                    }
                }
                if (BooleanUtils.isFalse(inPoint)) {
                    CtripCheckCarPickReturnResponse.FailInfo failInfo = new CtripCheckCarPickReturnResponse.FailInfo();
                    failInfo.setCode(PickReturnErrorEnum.NOT_IN_RETURNABLE_DISTANCE.getCode());
                    failInfo.setMessage(PickReturnErrorEnum.NOT_IN_RETURNABLE_DISTANCE.getMessage());
                    failInfos.add(failInfo);
                    response.setDistance(100);
                    isPickReturn = false;
                }
            }
            // 12 未熄火 校验
            if (BooleanUtils.isTrue(vehicleDeviceStateDTO.getEngineStatus())) {
                CtripCheckCarPickReturnResponse.FailInfo failInfo = new CtripCheckCarPickReturnResponse.FailInfo();
                failInfo.setCode(PickReturnErrorEnum.NOT_TURNED_OFF_ENGINE.getCode());
                failInfo.setMessage(PickReturnErrorEnum.NOT_TURNED_OFF_ENGINE.getMessage());
                failInfos.add(failInfo);
                isPickReturn = false;
            }
            // 13 未关闭车灯 校验
            if (BooleanUtils.isTrue(vehicleDeviceStateDTO.getLightOnStatus())) {
                CtripCheckCarPickReturnResponse.FailInfo failInfo = new CtripCheckCarPickReturnResponse.FailInfo();
                failInfo.setCode(PickReturnErrorEnum.NOT_TURNED_OFF_LIGHT.getCode());
                failInfo.setMessage(PickReturnErrorEnum.NOT_TURNED_OFF_LIGHT.getMessage());
                failInfos.add(failInfo);
                isPickReturn = false;
            }
            // 14 未关闭车门 校验 仅校验车门是否关闭
            if (BooleanUtils.isTrue(vehicleDeviceStateDTO.getDoorOnStatus())) {
                CtripCheckCarPickReturnResponse.FailInfo failInfo = new CtripCheckCarPickReturnResponse.FailInfo();
                failInfo.setCode(PickReturnErrorEnum.NOT_TURNED_OFF_DOOR.getCode());
                failInfo.setMessage(PickReturnErrorEnum.NOT_TURNED_OFF_DOOR.getMessage());
                failInfos.add(failInfo);
                isPickReturn = false;
            }
            // 15 押金未支付 校验
            Result<DepositVO> depositResult = orderDepositService.getByOrderId(orderInfo.getId());
            if (depositResult.isSuccess() && Objects.nonNull(depositResult.getModel())
                    && depositResult.getModel().getDepositStatus() == 0) {
                CtripCheckCarPickReturnResponse.FailInfo failInfo = new CtripCheckCarPickReturnResponse.FailInfo();
                failInfo.setCode(PickReturnErrorEnum.ORDER_NOT_DEPOSIT.getCode());
                failInfo.setMessage(PickReturnErrorEnum.ORDER_NOT_DEPOSIT.getMessage());
                failInfos.add(failInfo);
                isPickReturn = false;
            }
            // 16 当前订单已还车成功，请勿重复提交
            if (orderInfo.getOrderStatus().intValue() == OrderStatusEnum.RETURNED.getStatus().intValue()) {
                CtripCheckCarPickReturnResponse.FailInfo failInfo = new CtripCheckCarPickReturnResponse.FailInfo();
                failInfo.setCode(PickReturnErrorEnum.ORDER_IS_RETURNED.getCode());
                failInfo.setMessage(PickReturnErrorEnum.ORDER_IS_RETURNED.getMessage());
                failInfos.add(failInfo);
                isPickReturn = false;
            }
            // 18 未关闭车窗 校验。 车窗状态读取不到,不返回该错误给携程
            if (Objects.nonNull(vehicleDeviceStateDTO.getWindowOnStatus())
                    && BooleanUtils.isTrue(vehicleDeviceStateDTO.getWindowOnStatus())) {
                CtripCheckCarPickReturnResponse.FailInfo failInfo = new CtripCheckCarPickReturnResponse.FailInfo();
                failInfo.setCode(PickReturnErrorEnum.NOT_TURNED_OFF_WINDOW.getCode());
                failInfo.setMessage(PickReturnErrorEnum.NOT_TURNED_OFF_WINDOW.getMessage());
                failInfos.add(failInfo);
                isPickReturn = false;
            }
            // 19 本订单用车中更换车辆，请联系门店工作人员线下支付油电费后还车
            boolean updatePlanTag = orderInfo.getSelfPrOrder().equals(SelfPrOrderEnum.SELF_PR.getValue());
            if (BooleanUtils.isFalse(updatePlanTag)) {
                CtripCheckCarPickReturnResponse.FailInfo failInfo = new CtripCheckCarPickReturnResponse.FailInfo();
                failInfo.setCode(PickReturnErrorEnum.ORDER_CHANGED.getCode());
                failInfo.setMessage(PickReturnErrorEnum.ORDER_CHANGED.getMessage());
                failInfos.add(failInfo);
                isPickReturn = false;
            }
            response.setReturnReason(failInfos);
            // 如果车辆变更，费用项不计算
            if (BooleanUtils.isTrue(updatePlanTag)) {
                this.buildVehicleItem(orderInfo, vehicleDeviceStateDTO, response, request.getRecordList());
                // 需补款则不允许还车，但是不影响携程侧唤醒支付
                if (Objects.nonNull(response.getFeeAmount()) && response.getFeeAmount().compareTo(BigDecimal.ZERO) > 0) {
                    isPickReturn = false;
                }
            }

        }

        response.setIsPickReturn(isPickReturn);
        log.info("自助取还,取还车校验 merchantId={} ctripOrderId:{} orderId:{} response:{} ",
                merchantId, request.getSourceOrderId(), request.getOrderId(), JSON.toJSONString(response));
        // 检查订单和车机状态
        return ResultUtil.successResult(response);
    }

    private void transactionSyncAndSendMsg(OrderInfo orderInfo, OrderMsgTypeEnum orderMsgTypeEnum) {
        // 注册事务同步回调，发送消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                // 在事务提交后异步调用消息发送
                orderMsgProducer.sendOrderMsg(orderInfo.getId(), orderMsgTypeEnum);
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, transactionManager = TransactionManagerName.TRADE)
    @EventLog(businessCode = BusinessCode.ORDER_SELF_SCHEDULED)
    public Result<Boolean> scheduledCar(SelfPickReturnConfirmVO param, LoginVo loginVo) {
        logger.startLog().businessCode(BusinessCode.ORDER_SELF_SCHEDULED).with("param", param).ext(param.getOrderId()).logAudit("自助取还商家验车");
        if (param == null || loginVo == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }

        OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(param.getOrderId());
        if (orderInfo == null) {
            return ResultUtil.failResult(ResultEnum.e004);
        }
        if (!OrderStatusEnum.isScheduled(orderInfo.getOrderStatus())) {
            return ResultUtil.failResult("当前订单状态无法验车");
        }
        if (!orderInfo.getSelfPrOrder().equals(SelfPrOrderEnum.SELF_PR.getValue()) || !SelfPrStatusEnum.beforePickingUp(orderInfo.getSelfPrStatus())) {
            return ResultUtil.failResult("当前订单不支持自助取还 或已经整备完成");
        }

        if (orderInfo.getMerchantId().longValue() != loginVo.getMerchantId()) {
            return ResultUtil.failResult("越权操作");
        }

        /*
         * 写入vehicle_self_pick_return_operation，附件vehicle_self_pick_return_operation_att
         * 调用携程接口，修改orderInfo的状态
         */
        OrderSelfPickReturnOperationExample operationExample = new OrderSelfPickReturnOperationExample();
        operationExample.createCriteria().andOrderIdEqualTo(orderInfo.getId())
            .andVehicleIdEqualTo(orderInfo.getVehicleId())
            .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
            .andOpTypeEqualTo(OpType.MERCHANT_CHECK.getType());
        List<OrderSelfPickReturnOperation> oldOperations =
            orderSelfPickReturnOperationMapper.selectByExample(operationExample);
        // 车机及子车系相关数据
        VehicleDeviceStateDTO vehicleDeviceStateDTO =
                vehicleDeviceSettingService.deviceState(orderInfo.getMerchantId(),  orderInfo.getVehicleId(), null);

        if (CollectionUtils.isNotEmpty(oldOperations)) {
            this.updateMerchantCheckOperation(param, oldOperations.get(0), orderInfo, vehicleDeviceStateDTO, loginVo);
        } else {
            this.addNewOperation(param, orderInfo, vehicleDeviceStateDTO, loginVo);
        }
        OrderInfo updOrder = new OrderInfo();
        updOrder.setId(orderInfo.getId());
        updOrder.setSelfPrStatus(SelfPrStatusEnum.PREPARED.getStatus());
        updOrder.setOrderStatus(OrderStatusEnum.SCHEDULED.getStatus());
        updOrder.setOpTime(System.currentTimeMillis());
        updOrder.setOpUserId(loginVo.getUserId());
        orderInfoMapper.updateByPrimaryKeySelective(updOrder);

        // 推携程
        PlatformBaseRequest<OrderInfoCallbackScheduledReq> request = new PlatformBaseRequest<>();
        request.setChannelId(orderInfo.getOrderSource().longValue());
        request.setMerchantId(orderInfo.getMerchantId());
        OrderInfoCallbackScheduledReq req = new OrderInfoCallbackScheduledReq();

        req.setIsSelfPickReturn(true);
        req.setCarLicense(orderInfo.getVehicleNo());
        req.setOperateSerialNumber(DateUtil.getFormatDateStr(new Date(), "yyyyMMddHHmmss") + "_" + orderInfo + orderInfo.getSelfPrStatus());
        req.setCtripOrderId(orderInfo.getSourceOrderId());
        req.setCarPicList(this.buildCarImg(param.getAtt()));
        req.setCarLocationRemark(StringUtils.defaultString(param.getPositionRemark()));
        // todo 和携程聊下vvc的事情
        req.setVendorVehicleCode(vehicleDeviceStateDTO.getVvc());
        request.setData(req);
        try {
            log.info("自助取还车排车 req: {}", JSON.toJSONString(request));
            OrderInfoCallbackV2Resp resp = apiClient.scheduled(request);
            log.info("自助取还车排车 req: {}, resp : {}", JSON.toJSONString(request), JSON.toJSONString(resp));
            if (resp == null || !resp.success()) {
                throw new BizException(resp != null ? resp.getMessage() : "三方整备失败");
            }
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("三方整备异常,merchantId={},orderId={}", loginVo.getMerchantId(), orderInfo.getId(), e);
            throw new BizException("三方整备异常");
        }

        this.transactionSyncAndSendMsg(orderInfo, OrderMsgTypeEnum.SELF_CHECK_CAR);
        return ResultUtil.successResult(true);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, transactionManager = TransactionManagerName.TRADE)
    @EventLog(businessCode = BusinessCode.ORDER_SELF_NOTIFY_PICK_RETURN)
    public Result<PushPickReturnToVendorResponse> notifyPickReturn(Long channelId, Long merchantId, PushPickReturnToVendorRequest request) {
        logger.startLog().businessCode(BusinessCode.ORDER_SELF_NOTIFY_PICK_RETURN).with("param", request)
                .ext(request.getOrderId()).logAudit("自助取还车通知");
        OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(request.getOrderId());
        if (orderInfo == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }
        if (!orderInfo.getSelfPrOrder().equals(SelfPrOrderEnum.SELF_PR.getValue()) || StringUtils.isBlank(orderInfo.getSourceOrderId())) {
            return ResultUtil.failResult("非自助取还订单");
        }

        // 车机及子车系相关数据
        VehicleDeviceStateDTO vehicleDeviceStateDTO =
                vehicleDeviceSettingService.deviceState(orderInfo.getMerchantId(),  orderInfo.getVehicleId(), null);
        /*
         * 写入vehicle_self_pick_return_operation，附件vehicle_self_pick_return_operation_att
         * 写vehicle_pick_return
         * 改订单状态
         */
        RemainingEnergyDTO remainingEnergyDTO = null;
        if (request.getOperateType() == 2) {
            // 已取车(对应携程 已取车)
            remainingEnergyDTO = pick(request, orderInfo, vehicleDeviceStateDTO);
            this.transactionSyncAndSendMsg(orderInfo, OrderMsgTypeEnum.SELF_PICK_UP);
        } else if (request.getOperateType() == 1) {
            // 取车验车(对应携程 取车验车)
            remainingEnergyDTO = pickCheck(request, orderInfo, vehicleDeviceStateDTO);
            this.transactionSyncAndSendMsg(orderInfo, OrderMsgTypeEnum.SELF_PICK_UP_CHECK);
        } else if (request.getOperateType() == 3) {
            // 还车验车(对应携程 还车验车)
            remainingEnergyDTO = returnCheck(request, orderInfo, vehicleDeviceStateDTO, channelId);
            this.transactionSyncAndSendMsg(orderInfo, OrderMsgTypeEnum.SELF_RETURN_CHECK);
        } else if (request.getOperateType() == 4) {
            // 已还车(对应携程 已还车)
            remainingEnergyDTO = finishReturn(request, orderInfo, vehicleDeviceStateDTO, channelId, merchantId);
            this.transactionSyncAndSendMsg(orderInfo, OrderMsgTypeEnum.SELF_RETURN);
        }
        PushPickReturnToVendorResponse pushPickReturnToVendorResponse = new PushPickReturnToVendorResponse();
        pushPickReturnToVendorResponse.setRemainingEnergy(remainingEnergyDTO);
        log.info("自助取还车 notifyPickReturn merchantId:{} request:{} response:{}", merchantId, JSON.toJSONString(request), JSON.toJSONString(pushPickReturnToVendorResponse));
        return ResultUtil.successResult(pushPickReturnToVendorResponse);
    }

    private RemainingEnergyDTO finishReturn(PushPickReturnToVendorRequest request, OrderInfo orderInfo, VehicleDeviceStateDTO vehicleDeviceStateDTO, Long channelId, Long merchantId) {
        log.info("自助取还,已还车 start orderId:{} ctripOrderId:{} request:{} vehicleDeviceStateDTO:{}",
                orderInfo.getId(), orderInfo.getSourceOrderId(), JSON.toJSONString(request), JSON.toJSONString(vehicleDeviceStateDTO));
        Long nowTime = System.currentTimeMillis();
        if (!SelfPrStatusEnum.RETURN_CHECKED.getStatus().equals(orderInfo.getSelfPrStatus())
                && !SelfPrStatusEnum.RETURNED.getStatus().equals(orderInfo.getSelfPrStatus())) {
            throw new BizException("当前订单无法完成还车");
        }

        // 二次check
        CheckCarPickReturnRequest checkPrRequest = new CheckCarPickReturnRequest();
        checkPrRequest.setOrderId(orderInfo.getId());
        checkPrRequest.setOperateType(2);
        checkPrRequest.setRecordList(request.getRecordList());
        checkPrRequest.setSourceOrderId(orderInfo.getSourceOrderId());
        Result<CtripCheckCarPickReturnResponse> checkCarPrResult =
                this.checkCarPickReturn(channelId, orderInfo.getMerchantId(), checkPrRequest);
        if (ResultUtil.isModelNotNull(checkCarPrResult)
                && CollectionUtils.isNotEmpty(checkCarPrResult.getModel().getReturnReason())) {
            CtripCheckCarPickReturnResponse.FailInfo failInfo = checkCarPrResult.getModel().getReturnReason().get(0);
            log.info("自助取还车,二次校验未通过 {} {}", failInfo.getCode(), failInfo.getMessage());
            ResultUtil.failResult(failInfo.getCode(), failInfo.getMessage());
        }

        // 查自助取还记录表
        OrderSelfPickReturnOperationExample operationExample = new OrderSelfPickReturnOperationExample();
        operationExample.createCriteria().andOrderIdEqualTo(orderInfo.getId())
                .andVehicleIdEqualTo(orderInfo.getVehicleId())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andOpTypeIn(Arrays.asList(OpType.CUSTOMER_RETURN.getType(), OpType.CUSTOMER_PICK.getType()));
        List<OrderSelfPickReturnOperation> oldOperations =
                orderSelfPickReturnOperationMapper.selectByExample(operationExample);
        OrderSelfPickReturnOperation pickOperation = null;
        OrderSelfPickReturnOperation returnOperation = null;
        for (OrderSelfPickReturnOperation operation : oldOperations) {
            if (operation.getOpType().equals(OpType.CUSTOMER_RETURN.getType())) {
                returnOperation = operation;
            } else if (operation.getOpType().equals(OpType.CUSTOMER_PICK.getType())) {
                pickOperation = operation;
            }
        }
        if (pickOperation == null) {
            throw new BizException("当前订单车辆无取车记录");
        }
        if (returnOperation == null) {
            throw new BizException("当前订单车辆无还车记录");
        }

        // 设置车机相关数据
        RemainingEnergyDTO remainingEnergyDTO = this.setCarEngine(orderInfo, returnOperation, vehicleDeviceStateDTO, false);

        returnOperation.setEndTime(nowTime);
        returnOperation.setOpTime(nowTime);
        orderSelfPickReturnOperationMapper.updateByPrimaryKeySelective(returnOperation);

        // 订单 还车中->已还车
        OrderInfo updOrder = new OrderInfo();
        updOrder.setId(orderInfo.getId());
        updOrder.setSelfPrStatus(SelfPrStatusEnum.RETURNED.getStatus());
        updOrder.setOpTime(System.currentTimeMillis());
        orderInfoMapper.updateByPrimaryKeySelective(updOrder);

        List<ExpenseRecordDTO> recordList = request.getRecordList();
        // 更新已支付流水
        Integer totalActualPayAmount = this.updSelfPrPayForPaid(orderInfo, recordList);

        // 拦截车辆  上汽不拦截
        if (!openMerchantConfig.isEvcard(merchantId)) {
            asyncPromiseExecutor.execute(() -> {
                LoginVo loginVo = new LoginVo();
                loginVo.setMerchantId(merchantId);
                loginVo.setUserId(-1L);
                try {
                    VehicleDeviceUpdateRequest updateRequest = new VehicleDeviceUpdateRequest();
                    updateRequest.setVehicleId(orderInfo.getVehicleId());
                    updateRequest.setInterceptStatus((int) YesOrNoEnum.YES.getValue());
                    vehicleDeviceSettingService.interceptDevice(loginVo, updateRequest);
                } catch (Exception e) {
                    log.error("拦截车辆失败, merchantId={}, updateRequest={}", merchantId, updOrder, e);
                }
            });
        }

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                // 违章转移 终止合同
                if (!OrderStatusEnum.RETURNED.getStatus().equals(orderInfo.getOrderStatus())) {
                    vehicleIllegalContractService.asyncAbortContractByOrderId(orderInfo.getId());
                }
            }
        });

        // 计算费用项
//        List<VehicleReturnExpenseItem> expenseItemList =
//                this.calculateCharge(orderInfo, returnOperation, pickOperation, vehicleDeviceStateDTO);

//        // todo 二次校验金额  误差值  update费用项
//        Integer totalCharge = 0;
//        for (VehicleReturnExpenseItem expenseItem : expenseItemList) {
//            if (expenseItem.getItemType().equals((byte) 1)) {
//                totalCharge += expenseItem.getExpenseAmount().intValue();
//            } else {
//                totalCharge -= expenseItem.getExpenseAmount().intValue();
//            }
//        }
//
//        if ((totalCharge - totalActualPayAmount) > 100) {
//            VehicleReturnExpenseItemExample expenseItemExample = new VehicleReturnExpenseItemExample();
//            expenseItemExample.createCriteria().andDeletedEqualTo(YesOrNoEnum.NO.getValue())
//                            .andOrderIdEqualTo(orderInfo.getId());
//            List<VehicleReturnExpenseItem> oldExpenseItemList = vehicleReturnExpenseItemMapper.selectByExample(expenseItemExample);
//            // 幂等 删除原有的油/电费 加油/电服务费
//            VehicleReturnExpenseItemExample vehicleReturnExpenseItemExample = new VehicleReturnExpenseItemExample();
//            vehicleReturnExpenseItemExample.createCriteria().andDeletedEqualTo(YesOrNoEnum.NO.getValue())
//                    .andOrderIdEqualTo(orderInfo.getId())
//                    .andExpenseItemPropIdIn(Arrays.asList(17L, 18L, 19L, 20L, 5L));
//            VehicleReturnExpenseItem deletedExpenseItem = new VehicleReturnExpenseItem();
//            deletedExpenseItem.setDeleted(YesOrNoEnum.YES.getValue());
//            deletedExpenseItem.setOpTime(nowTime);
//            deletedExpenseItem.setOpUserId(0L);
//            vehicleReturnExpenseItemMapper.updateByExampleSelective(deletedExpenseItem, vehicleReturnExpenseItemExample);
//            vehicleReturnExpenseItemMapper.batchInsert(expenseItemList);
//        }

        return remainingEnergyDTO;
    }

    private Integer updSelfPrPayForPaid(OrderInfo orderInfo,List<ExpenseRecordDTO> recordList) {
        Long nowTime = System.currentTimeMillis();
        // 实际支付总金额
        int totalActualPayAmount = 0;
        if (CollectionUtils.isNotEmpty(recordList)) {
            PayExample payExample = new PayExample();
            payExample.createCriteria().andOrderIdEqualTo(orderInfo.getId())
                    .andFeeTypeEqualTo(PayFeeTypeEnum.SELF_PICK_RETURN_CHARGE.getType())
                    .andPayStatusEqualTo(PayStatusEnum.UNPAID.getStatus())
                    .andPaySourceEqualTo(orderInfo.getOrderSource());
            List<Pay> pays = payMapper.selectByExample(payExample);
            if (CollectionUtils.isEmpty(pays)) {
                throw new BizException("支付流水异常");
            }
            for (ExpenseRecordDTO expenseRecordDTO : recordList) {
                int actualPayAmount = expenseRecordDTO.getAmount().multiply(BigDecimal.valueOf(100)).intValue();
                totalActualPayAmount += actualPayAmount;
                if (CollectionUtils.isNotEmpty(pays) && pays.get(0).getPaySourceId().equals(expenseRecordDTO.getSerialNumber())) {
                    Pay pay = pays.get(0);
                    pay.setActualPayAmount(actualPayAmount);
                    pay.setPayStatus(PayStatusEnum.ALL_PAID.getStatus());
                    pay.setPaySourceId(expenseRecordDTO.getSerialNumber());
                    pay.setLastVer(pay.getLastVer() + 1);
                    pay.setPayTime(nowTime);
                    pay.setPayOpTime(nowTime);
                    pay.setOpTime(nowTime);
                    payMapper.updateByPrimaryKeySelective(pay);
                }
            }
        }
        return totalActualPayAmount;
    }

    /**
     * 还车验车
     * @param request
     * @param orderInfo
     * @param vehicleDeviceStateDTO
     * @param channelId
     * @return
     */
    private RemainingEnergyDTO returnCheck(PushPickReturnToVendorRequest request, OrderInfo orderInfo, VehicleDeviceStateDTO vehicleDeviceStateDTO, Long channelId) {
        log.info("自助取还,还车验车 start orderId:{} ctripOrderId:{} request:{} vehicleDeviceStateDTO:{}",
                orderInfo.getId(), orderInfo.getSourceOrderId(), JSON.toJSONString(request), JSON.toJSONString(vehicleDeviceStateDTO));
        Long nowTime = System.currentTimeMillis();
        if (!SelfPrStatusEnum.PICKED_UP_CHECKED.getStatus().equals(orderInfo.getSelfPrStatus())
                && !SelfPrStatusEnum.RETURN_CHECKED.getStatus().equals(orderInfo.getSelfPrStatus())) {
            throw new BizException("当前订单无法还车验车");
        }
        // 查自助取还记录表
        OrderSelfPickReturnOperationExample operationExample = new OrderSelfPickReturnOperationExample();
        operationExample.createCriteria().andOrderIdEqualTo(orderInfo.getId())
                .andVehicleIdEqualTo(orderInfo.getVehicleId())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andOpTypeIn(Arrays.asList(OpType.CUSTOMER_RETURN.getType(), OpType.CUSTOMER_PICK.getType()));
        List<OrderSelfPickReturnOperation> oldOperations =
                orderSelfPickReturnOperationMapper.selectByExample(operationExample);
        OrderSelfPickReturnOperation pickOperation = null;
        OrderSelfPickReturnOperation oldReturnOperation = null;
        for (OrderSelfPickReturnOperation oldOperation : oldOperations) {
            if (oldOperation.getOpType().equals(OpType.CUSTOMER_RETURN.getType())) {
                oldReturnOperation = oldOperation;
            }
            if (oldOperation.getOpType().equals(OpType.CUSTOMER_PICK.getType())) {
                pickOperation = oldOperation;
            }
        }
        if (pickOperation == null) {
            throw new BizException("取车记录不存在");
        }

        OrderSelfPickReturnOperation operation = new OrderSelfPickReturnOperation();
        operation.setOpType(OpType.CUSTOMER_RETURN.getType());
        operation.setOrderId(orderInfo.getId());
        operation.setVehicleId(orderInfo.getVehicleId());
        operation.setVehicleModelId(orderInfo.getVehicleModelId());
        operation.setStartTime(nowTime);
        operation.setEndTime(0L);

        // 设置车机相关数据
        RemainingEnergyDTO remainingEnergyDTO = this.setCarEngine(orderInfo, operation, vehicleDeviceStateDTO, true);

        // 设置 车辆问题 车况备注 车辆位置指引备注
        OrderSelfOperationExtraVo orderSelfOperationExtraVo = new OrderSelfOperationExtraVo();
        orderSelfOperationExtraVo.setCarProblem(request.getCarProblem());
        orderSelfOperationExtraVo.setCarRemark(request.getCarRemark());
        orderSelfOperationExtraVo.setPositionRemark(request.getCarPlaceNotes());
        operation.setExtra(JSON.toJSONString(orderSelfOperationExtraVo));

        operation.setDeleted(YesOrNoEnum.NO.getValue());
        operation.setOpUserId(0L);
        operation.setOpTime(nowTime);
        // 多次调用，需要幂等
        if (oldReturnOperation == null) {
            operation.setCreateTime(nowTime);
            orderSelfPickReturnOperationMapper.insertSelective(operation);
        } else {
            operation.setId(oldReturnOperation.getId());
            orderSelfPickReturnOperationMapper.updateByPrimaryKeySelective(operation);
        }

        //  写入车辆和位置图片
        OrderSelfPickReturnOperationAttExample orderSelfPickReturnOperationAttExample = new OrderSelfPickReturnOperationAttExample();
        orderSelfPickReturnOperationAttExample.createCriteria().andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                        .andOrderSelfOperationIdEqualTo(operation.getId());
        OrderSelfPickReturnOperationAtt deletedAtt = new OrderSelfPickReturnOperationAtt();
        deletedAtt.setDeleted(YesOrNoEnum.YES.getValue());
        deletedAtt.setOpTime(nowTime);
        deletedAtt.setOpUserId(0L);
        orderSelfPickReturnOperationAttMapper.updateByExampleSelective(deletedAtt, orderSelfPickReturnOperationAttExample);
        if (CollectionUtils.isNotEmpty(request.getCarPicList())) {
            List<OrderSelfPickReturnOperationAtt> insertList = new ArrayList<>();
            request.getCarPicList().forEach(carPic ->
                    insertList.add(this.buildOpAttForCtrip(operation.getId(), (byte) carPic.getType(),
                            carPic.getUrl()))
            );
            orderSelfPickReturnOperationAttMapper.batchInsert(insertList);
        }

        // 订单 取车验车->还车验车
        OrderInfo updOrder = new OrderInfo();
        updOrder.setId(orderInfo.getId());
        updOrder.setSelfPrStatus(SelfPrStatusEnum.RETURN_CHECKED.getStatus());
        updOrder.setOpTime(System.currentTimeMillis());
        orderInfoMapper.updateByPrimaryKeySelective(updOrder);

//        // 计算费用项
//        List<VehicleReturnExpenseItem> expenseItemList =
//                this.calculateCharge(orderInfo, operation, pickOperation, vehicleDeviceStateDTO);
//        // 幂等 删除原有的油/电费 加油/电服务费
//        VehicleReturnExpenseItemExample vehicleReturnExpenseItemExample = new VehicleReturnExpenseItemExample();
//        vehicleReturnExpenseItemExample.createCriteria().andDeletedEqualTo(YesOrNoEnum.NO.getValue())
//                .andOrderIdEqualTo(orderInfo.getId())
//                .andExpenseItemPropIdIn(Arrays.asList(17L, 18L, 19L, 20L, 5L));
//        VehicleReturnExpenseItem deletedExpenseItem = new VehicleReturnExpenseItem();
//        deletedExpenseItem.setDeleted(YesOrNoEnum.YES.getValue());
//        deletedExpenseItem.setOpTime(nowTime);
//        deletedExpenseItem.setOpUserId(0L);
//        vehicleReturnExpenseItemMapper.updateByExampleSelective(deletedExpenseItem, vehicleReturnExpenseItemExample);
//        vehicleReturnExpenseItemMapper.batchInsert(expenseItemList);
//        saveSelfPrPay(orderInfo, channelId, nowTime, expenseItemList);
        log.info("自助取还,还车验车 end orderId:{} ctripOrderId:{} response:{}",
                orderInfo.getId(), orderInfo.getSourceOrderId(), JSON.toJSONString(remainingEnergyDTO));
        return remainingEnergyDTO;
    }

    private String saveSelfPrPay(OrderInfo orderInfo, Long channelId, Long nowTime, BigDecimal payAmount,
                                 List<ExpenseRecordDTO> recordDTOS) {

        String serialNumber = String.valueOf(IdUtil.getSnowflakeNextId());

        PayExample payExample = new PayExample();
        PayExample.Criteria criteria = payExample.createCriteria();
        criteria.andOrderIdEqualTo(orderInfo.getId())
                        .andFeeTypeEqualTo(PayFeeTypeEnum.SELF_PICK_RETURN_CHARGE.getType());
        List<Pay> payList = payMapper.selectByExample(payExample);
        Map<String, Pay> payMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(payList)) {
            payMap = payList.stream()
                    .collect(Collectors.toMap(Pay::getPaySourceId, Function.identity(),
                            (k1, k2) -> k1));
        }

        // 二次校验传入流水号list
        if (CollectionUtils.isNotEmpty(recordDTOS)) {
            for (ExpenseRecordDTO recordDTO : recordDTOS) {
                Pay pay = payMap.get(recordDTO.getSerialNumber());
                if (Objects.isNull(pay)) {
                    Pay newPay = new Pay();
                    newPay.setOrderId(orderInfo.getId());
                    newPay.setStoreId(orderInfo.getPickupStoreId());
                    newPay.setQuantity((short) 1);
                    newPay.setPayAmount(recordDTO.getAmount().multiply(new BigDecimal(100)).intValue());
                    newPay.setFeeType(PayFeeTypeEnum.SELF_PICK_RETURN_CHARGE.getType());
                    newPay.setPayStatus(PayStatusEnum.ALL_PAID.getStatus());
                    newPay.setPaySource(channelId.byteValue());
                    newPay.setOpUserId(0L);
                    newPay.setCreateTime(nowTime);
                    newPay.setPaySourceId(serialNumber);
                    newPay.setLastVer(1);
                    newPay.setOpTime(nowTime);
                    payMapper.insertSelective(newPay);
                }
            }
        } else {
            Pay oldPay = payList.stream().findFirst().orElse(null);
            if (payAmount.compareTo(BigDecimal.ZERO) > 0) {
                Pay pay = new Pay();
                pay.setOrderId(orderInfo.getId());
                pay.setStoreId(orderInfo.getPickupStoreId());
                pay.setQuantity((short) 1);
                pay.setPayAmount(payAmount.multiply(new BigDecimal(100)).intValue());
                pay.setFeeType(PayFeeTypeEnum.SELF_PICK_RETURN_CHARGE.getType());
                pay.setPayStatus(PayStatusEnum.UNPAID.getStatus());
                pay.setPaySource(channelId.byteValue());
                pay.setOpUserId(0L);
                pay.setCreateTime(nowTime);
                if (oldPay == null) {
                    pay.setPaySourceId(serialNumber);
                    pay.setLastVer(1);
                    pay.setOpTime(nowTime);
                    payMapper.insertSelective(pay);
                } else {
                    serialNumber = oldPay.getPaySourceId();
                    pay.setOpTime(new Date().getTime());
                    pay.setId(oldPay.getId());
                    pay.setLastVer(oldPay.getLastVer() + 1);
                    payMapper.updateByPrimaryKeySelective(pay);
                }
                return serialNumber;
            } else {
                PayExample example = new PayExample();
                example.createCriteria()
                        .andOrderIdEqualTo(orderInfo.getId())
                        .andFeeTypeEqualTo(PayFeeTypeEnum.SELF_PICK_RETURN_CHARGE.getType());
                payMapper.deleteByExample(example);
            }
        }

        return null;

    }


    /**
     * 计算费用项
     *
     * @param orderInfo
     * @param returnOperation
     * @param pickOperation
     * @param vehicleDeviceStateDTO
     */
    @Deprecated
    private List<VehicleReturnExpenseItem> calculateCharge(OrderInfo orderInfo, OrderSelfPickReturnOperation returnOperation,
                                                     OrderSelfPickReturnOperation pickOperation, VehicleDeviceStateDTO vehicleDeviceStateDTO) {
        Long orderId = orderInfo.getId();
        Map<Long, VehicleReturnExpenseItemPropVO> expenseItemPropMap =
                vehicleReturnExpenseItemPropService.listVehicleReturnExpenseItemPropByType(null, null).getModel()
                .stream().collect(Collectors.toMap(VehicleReturnExpenseItemPropVO::getId, e -> e));

        List<VehicleReturnExpenseItem> expenseItemList = new ArrayList<>();
        if (FuelFormEnum.GASOLINE.getCtripEnergyType().equals(vehicleDeviceStateDTO.getEnergyType())) {
            // 算油价
            BigDecimal oilPrice = getOilPrice(returnOperation, pickOperation);
            if (oilPrice.compareTo(BigDecimal.ZERO) != 0) {
                long itemPropId = oilPrice.compareTo(BigDecimal.ZERO) > 0 ? (long) 17 : (long) 19;
                this.addExpenseItem(orderId, expenseItemPropMap, expenseItemList, oilPrice, itemPropId);
                // 油费>0 需要计算服务费
                if (oilPrice.compareTo(BigDecimal.ZERO) > 0) {
                    this.addExpenseItem(orderId, expenseItemPropMap, expenseItemList,
                            BigDecimal.valueOf(pickOperation.getServiceprice()), 5);
                }
            }
        } else if (FuelFormEnum.ELECTRIC.getCtripEnergyType().equals(vehicleDeviceStateDTO.getEnergyType())) {
            // 算电价(里程)
            BigDecimal mileagePrice = getMileagePrice(returnOperation, pickOperation);
            if (mileagePrice.compareTo(BigDecimal.ZERO) != 0) {
                long itemPropId = mileagePrice.compareTo(BigDecimal.ZERO) > 0 ? (long) 18 : (long) 20;
                this.addExpenseItem(orderId, expenseItemPropMap, expenseItemList, mileagePrice, itemPropId);
            }
            // 电费>0 需要计算服务费
            if (mileagePrice.compareTo(BigDecimal.ZERO) > 0) {
                this.addExpenseItem(orderId, expenseItemPropMap, expenseItemList,
                        BigDecimal.valueOf(pickOperation.getServiceprice()), 5);
            }
        } else {
            // 算油电价
            BigDecimal oilPrice = getOilPrice(returnOperation, pickOperation);
            long oilItemPropId = oilPrice.compareTo(BigDecimal.ZERO) > 0 ? (long) 17 : (long) 19;
            this.addExpenseItem(orderId, expenseItemPropMap, expenseItemList, oilPrice, oilItemPropId);

            BigDecimal mileagePrice = getMileagePrice(returnOperation, pickOperation);
            long mileageItemPropId = mileagePrice.compareTo(BigDecimal.ZERO) > 0 ? (long) 18 : (long) 20;
            this.addExpenseItem(orderId, expenseItemPropMap, expenseItemList, mileagePrice, mileageItemPropId);

            // 油费>0 需要计算服务费
            if (oilPrice.compareTo(BigDecimal.ZERO) > 0) {
                this.addExpenseItem(orderId, expenseItemPropMap, expenseItemList,
                        BigDecimal.valueOf(pickOperation.getServiceprice()), 5);
            }
        }

        return expenseItemList;
    }

    private void addExpenseItem(Long orderId, Map<Long, VehicleReturnExpenseItemPropVO> expenseItemPropMap, List<VehicleReturnExpenseItem> expenseItemList, BigDecimal mileagePrice, long itemPropId) {
        long time = System.currentTimeMillis();
        VehicleReturnExpenseItem energyItem = new VehicleReturnExpenseItem();
        energyItem.setOrderId(orderId);
        energyItem.setVehicleReturnId(0L);
        energyItem.setExpenseItemPropId(itemPropId);
        energyItem.setExpenseItemName(expenseItemPropMap.get(itemPropId).getItemName());
        energyItem.setExpenseAmount(mileagePrice.abs().longValue());
        energyItem.setItemType(mileagePrice.compareTo(BigDecimal.ZERO) > 0 ? (byte) 1 : (byte) 2);
        energyItem.setLastVer(1);
        energyItem.setOpUserId(0L);
        energyItem.setDeleted(YesOrNoEnum.NO.getValue());
        energyItem.setCreateTime(time);
        energyItem.setOpTime(time);
        expenseItemList.add(energyItem);
    }

    // 算油价
    @NotNull
    private BigDecimal getOilPrice(OrderSelfPickReturnOperation returnOperation, OrderSelfPickReturnOperation pickOperation) {
        return returnOperation.getOilLiter().subtract(pickOperation.getOilLiter())
                .multiply(BigDecimal.valueOf(pickOperation.getOilUnitPrice()));
    }

    // 算电价(里程)
    @NotNull
    private BigDecimal getMileagePrice(OrderSelfPickReturnOperation returnOperation, OrderSelfPickReturnOperation pickOperation) {
        return BigDecimal.valueOf(returnOperation.getRemindMileage() - pickOperation.getRemindMileage())
                .multiply(BigDecimal.valueOf(pickOperation.getRemindMileage()));
    }

    /**
     * 取车验车
     * @param request
     * @param orderInfo
     * @param vehicleDeviceStateDTO
     */
    private RemainingEnergyDTO pickCheck(PushPickReturnToVendorRequest request, OrderInfo orderInfo, VehicleDeviceStateDTO vehicleDeviceStateDTO) {
        log.info("自助取还,取车验车 orderId:{} ctripOrderId:{} request:{} vehicleDeviceStateDTO:{}",
                orderInfo.getId(), orderInfo.getSourceOrderId(), JSON.toJSONString(request), JSON.toJSONString(vehicleDeviceStateDTO));
        Long nowTime = System.currentTimeMillis();
        if (!SelfPrStatusEnum.PICKED_UP.getStatus().equals(orderInfo.getSelfPrStatus())) {
            throw new BizException("当前订单无法取车验车");
        }

        // 查自助取还记录表
        OrderSelfPickReturnOperationExample operationExample = new OrderSelfPickReturnOperationExample();
        operationExample.createCriteria().andOrderIdEqualTo(orderInfo.getId())
                .andVehicleIdEqualTo(orderInfo.getVehicleId())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andOpTypeEqualTo(OpType.CUSTOMER_PICK.getType());
        List<OrderSelfPickReturnOperation> oldOperations =
                orderSelfPickReturnOperationMapper.selectByExample(operationExample);
        if (CollectionUtils.isEmpty(oldOperations)) {
            throw new BizException("当前订单车辆尚未开始取车");
        }
        OrderSelfPickReturnOperation operation = oldOperations.get(0);

        // 设置车机相关数据
        RemainingEnergyDTO remainingEnergyDTO = this.setCarEngine(orderInfo, operation, vehicleDeviceStateDTO, false);

        operation.setEndTime(nowTime);
        operation.setOpTime(nowTime);

        // 设置 车辆问题 车况备注 车辆位置指引备注
        OrderSelfOperationExtraVo orderSelfOperationExtraVo = new OrderSelfOperationExtraVo();
        orderSelfOperationExtraVo.setCarProblem(request.getCarProblem());
        orderSelfOperationExtraVo.setCarRemark(request.getCarRemark());
        orderSelfOperationExtraVo.setPositionRemark(request.getCarPlaceNotes());
        operation.setExtra(JSON.toJSONString(orderSelfOperationExtraVo));

        orderSelfPickReturnOperationMapper.updateByPrimaryKeySelective(operation);

        //  写入车辆和位置图片
        if (CollectionUtils.isNotEmpty(request.getCarPicList())) {
            List<OrderSelfPickReturnOperationAtt> insertList = new ArrayList<>();
            request.getCarPicList().forEach(carPic ->
                    insertList.add(this.buildOpAttForCtrip(operation.getId(), (byte) carPic.getType(),
                            carPic.getUrl()))
            );
            orderSelfPickReturnOperationAttMapper.batchInsert(insertList);
        }

        // 订单 已取车->取车已验
        OrderInfo updOrder = new OrderInfo();
        updOrder.setId(orderInfo.getId());
        updOrder.setSelfPrStatus(SelfPrStatusEnum.PICKED_UP_CHECKED.getStatus());
        updOrder.setOpTime(System.currentTimeMillis());
        orderInfoMapper.updateByPrimaryKeySelective(updOrder);

        return remainingEnergyDTO;
    }

    /**
     * 已取车
     * @param request
     * @param orderInfo
     * @param vehicleDeviceStateDTO
     */
    private RemainingEnergyDTO pick(PushPickReturnToVendorRequest request, OrderInfo orderInfo, VehicleDeviceStateDTO vehicleDeviceStateDTO) {
        log.info("自助取还,已取车 orderId:{} ctripOrderId:{} request:{} vehicleDeviceStateDTO:{}",
                orderInfo.getId(), orderInfo.getSourceOrderId(), JSON.toJSONString(request), JSON.toJSONString(vehicleDeviceStateDTO));
        long nowTime = System.currentTimeMillis();
        if (!SelfPrStatusEnum.PREPARED.getStatus().equals(orderInfo.getSelfPrStatus())) {
            throw new BizException("当前订单无法取车");
        }
        // 查自助取还记录表
        OrderSelfPickReturnOperationExample operationExample = new OrderSelfPickReturnOperationExample();
        operationExample.createCriteria().andOrderIdEqualTo(orderInfo.getId())
                .andVehicleIdEqualTo(orderInfo.getVehicleId())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andOpTypeEqualTo(OpType.CUSTOMER_PICK.getType());
        List<OrderSelfPickReturnOperation> oldOperations = orderSelfPickReturnOperationMapper.selectByExample(operationExample);
        if (CollectionUtils.isNotEmpty(oldOperations)) {
            throw new BizException("当前订单车辆已开始取车");
        }

        OrderSelfPickReturnOperation operation = new OrderSelfPickReturnOperation();
        operation.setOpType(OpType.CUSTOMER_PICK.getType());
        operation.setOrderId(orderInfo.getId());
        operation.setVehicleId(orderInfo.getVehicleId());
        operation.setVehicleModelId(orderInfo.getVehicleModelId());
        operation.setStartTime(nowTime);
        operation.setEndTime(0L);

        // 设置车机相关数据
        RemainingEnergyDTO remainingEnergyDTO = this.setCarEngine(orderInfo, operation, vehicleDeviceStateDTO, true);

        operation.setDeleted(YesOrNoEnum.NO.getValue());
        operation.setOpUserId(0L);
        operation.setCreateTime(nowTime);
        operation.setOpTime(nowTime);
        orderSelfPickReturnOperationMapper.insertSelective(operation);

        // 写入vehicle_pick_return
        VehiclePickReturn vehiclePickReturn = new VehiclePickReturn();
        vehiclePickReturn.setOrderId(orderInfo.getId());
        vehiclePickReturn.setVehicleId(orderInfo.getVehicleId());
        vehiclePickReturn.setPrTime(new Date(nowTime));
        vehiclePickReturn.setPrType(VehiclePickReturnEnum.PICK.getType());
        vehiclePickReturn.setLastVer(1);
        vehiclePickReturn.setOpUserId(0L);
        vehiclePickReturn.setDeductionPayType((byte)0);
        vehiclePickReturn.setRefundPayType((byte)0);
        vehiclePickReturn.setMerchantId(orderInfo.getMerchantId());
        vehiclePickReturn.setDeleted(YesOrNoEnum.NO.getValue());
        vehiclePickReturn.setCreateTime(System.currentTimeMillis());
        vehiclePickReturn.setOpTime(System.currentTimeMillis());
        vehiclePickReturnMapper.insertSelective(vehiclePickReturn);

        // 订单 已整备->已取车
        OrderInfo updOrder = new OrderInfo();
        updOrder.setId(orderInfo.getId());
        updOrder.setSelfPrStatus(SelfPrStatusEnum.PICKED_UP.getStatus());
        updOrder.setOrderStatus(OrderStatusEnum.PICKED_UP.getStatus());
        updOrder.setOpTime(System.currentTimeMillis());
        orderInfoMapper.updateByPrimaryKeySelective(updOrder);

        // 修改车辆状态为租赁中
        vehicleInfoService.pickUpVehicle(vehiclePickReturn.getVehicleId(), 0L);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                CompletableFuture.runAsync(() -> {
                    orderInfoCallbackPickUp(orderInfo, vehiclePickReturn.getPrTime());
                }, asyncPromiseExecutor).exceptionally(ex -> {
                    log.error("自助取还,订单回调pickedUp OrderInfoCallbackV2 异步回调失败", ex);
                    return null;
                });
                // 违章转移 录入合同
                vehicleIllegalContractService.asyncSubmitContract(orderInfo.getId(), vehiclePickReturn.getPrTime(), orderInfo.getLastReturnDate());
            }
        });
        return remainingEnergyDTO;
    }

    private void orderInfoCallbackPickUp(OrderInfo orderInfo, Date prTime) {
        try {
            Retryer<OrderInfoCallbackV2Resp> retryer = RetryerBuilder.<OrderInfoCallbackV2Resp>newBuilder()
                    .retryIfExceptionOfType(Exception.class)
                    .withWaitStrategy(WaitStrategies.incrementingWait(1, TimeUnit.SECONDS, 4, TimeUnit.SECONDS))
                    .retryIfResult(resp -> resp == null || !resp.success())
                    .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                    .build();
            retryer.call(() -> {
                OrderInfoStatusCallBackReq thirdRequest1 = new OrderInfoStatusCallBackReq();
                thirdRequest1.setActualPickupTime(
                        DateUtil.getFormatDateStr(prTime, DateUtil.yyyyMMddHHmmss));
                thirdRequest1.setOperateSerialNumber(
                        orderInfo.getId() + "-" +  DateUtil.getFormatDateStr(prTime, DateUtil.yyyyMMddHHmm));
                thirdRequest1.setCtripOrderId(orderInfo.getSourceOrderId());

                PlatformBaseRequest<OrderInfoStatusCallBackReq> platformBaseRequest =
                        new PlatformBaseRequest<>();
                platformBaseRequest.setData(thirdRequest1);
                Byte orderSource = OrderSourceEnum.transferCtripResale(orderInfo.getOrderSource());
                platformBaseRequest.setChannelId(orderSource.longValue());
                platformBaseRequest.setMerchantId(orderInfo.getMerchantId());
                // 在事务提交后异步调用消息发送
                log.info("自助取还,订单回调pickedUp OrderInfoCallbackV2 start orderId:{} ctripOrderId:{} platformBaseRequest:{} ",
                        orderInfo.getId(), orderInfo.getSourceOrderId(), JSON.toJSONString(platformBaseRequest));
                OrderInfoCallbackV2Resp resp = apiClient.pickedUp(platformBaseRequest);
                log.info("自助取还,订单回调pickedUp OrderInfoCallbackV2 end orderId:{} ctripOrderId:{} callbackResp:{} ",
                        orderInfo.getId(), orderInfo.getSourceOrderId(), JSON.toJSONString(resp));
                if (resp == null || !resp.success()) {
                    log.error("自助取还,订单回调pickedUp");
                }
                return resp;
            });
        } catch (Exception e) {
            log.error("自助取还,订单回调pickedUp OrderInfoCallbackV2 异步回调失败, orderInfo={}", JSON.toJSONString(orderInfo), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, transactionManager = TransactionManagerName.TRADE)
    @EventLog(businessCode = BusinessCode.ORDER_SELF_CONFIRM_RETURN)
    public Result<Boolean> confirmReturn(ConfirmReturnParam param, LoginVo loginVo) {
        logger.startLog().businessCode(BusinessCode.ORDER_SELF_CONFIRM_RETURN).with("param", param)
                .ext(param.getOrderId()).logAudit("自助取还确认还车");
        if (param == null || loginVo == null || param.getOrderId() == null
                || loginVo.getUserId() == null || loginVo.getMerchantId() == null) {
            return ResultUtil.failResult("参数错误");
        }
        Long orderId = param.getOrderId();
        Long merchantId = loginVo.getMerchantId();
        long nowTime = System.currentTimeMillis();
        Long opUserId = loginVo.getUserId();
        OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(orderId);
        if (orderInfo == null) {
            return ResultUtil.failResult("未查询到对应的订单信息，请刷新页面");
        }
        if (!merchantId.equals(orderInfo.getMerchantId())) {
            return ResultUtil.failResult(ResultEnum.e007);
        }
        if (OrderStatusEnum.isReturn(orderInfo.getOrderStatus())) {
            return ResultUtil.failResult("当前车辆已还车，无法还车");
        }
        if (!OrderStatusEnum.isPickedUp(orderInfo.getOrderStatus())) {
            return ResultUtil.failResult("当前订单状态状态无法还车");
        }
        if (orderInfo.getMerchantId().longValue() != loginVo.getMerchantId()) {
            return ResultUtil.failResult("越权操作");
        }
        VehiclePickReturnExample example = new VehiclePickReturnExample();
        example.createCriteria().andOrderIdEqualTo(orderId)
                .andPrTypeEqualTo(VehiclePickReturnEnum.PICK.getType());
        List<VehiclePickReturn> list = vehiclePickReturnMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return ResultUtil.failResult("当前车辆未取车，无法还车");
        }

        Byte orderSource = OrderSourceEnum.transferCtripResale(orderInfo.getOrderSource());
        // 是否要更改 车辆状态到未租赁
        boolean updateVehicleStatus = orderComponent.checkVehicleOrderLeaveUnused(
                orderInfo.getVehicleId(), orderInfo.getId(), orderInfo.getMerchantId());
        // 商家还车 写入vehicle_pick_return
        VehiclePickReturn vehiclePickReturn = new VehiclePickReturn();
        vehiclePickReturn.setPrType(VehiclePickReturnEnum.RETURN_BACK.getType());
        vehiclePickReturn.setRefundPayType(CollectionUtils.isEmpty(param.getReturnRefundItemList()) ?
                (byte)0 : (byte)1);
        List<VehicleReturnExpenseItemVO> itemVOList = vehicleReturnExpenseItemService.expenseItemByOrderId(orderId).getModel();
        List<Long> deletedItemIds = new ArrayList<>();
        boolean haveDeduction = false;
        if (CollectionUtils.isNotEmpty(param.getReturnDeductionItemList())) {
            haveDeduction = true;
        }
        Set<Long> paramRefundSet = param.getReturnRefundItemList().stream().map(VehicleReturnExpenseItemVO::getId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(itemVOList)) {
            for (VehicleReturnExpenseItemVO itemVO : itemVOList) {
                if (itemVO.getItemType() == 1) {
                    haveDeduction = true;
                    break;
                }
                if ((itemVO.getExpenseItemPropId().intValue() == 19 || itemVO.getExpenseItemPropId().intValue() == 20)
                        && !paramRefundSet.contains(itemVO.getId())) {
                    deletedItemIds.add(itemVO.getId());
                }
            }
        }

        vehiclePickReturn.setDeductionPayType((byte) (haveDeduction ? 1 : 0));
        vehiclePickReturn.setOrderId(orderInfo.getId());
        vehiclePickReturn.setVehicleId(orderInfo.getVehicleId());
        vehiclePickReturn.setPrTime(new Date(nowTime));
        vehiclePickReturn.setLastVer(1);
        vehiclePickReturn.setOpUserId(0L);
        vehiclePickReturn.setMerchantId(orderInfo.getMerchantId());
        vehiclePickReturn.setDeleted(YesOrNoEnum.NO.getValue());
        vehiclePickReturn.setCreateTime(nowTime);
        vehiclePickReturn.setOpTime(nowTime);
        if (vehiclePickReturnMapper.insertSelective(vehiclePickReturn) != 1) {
            throw new BizException("取还车失败");
        }

        VehiclePickReturnParam vehiclePickReturnParam = new VehiclePickReturnParam();
        BeanUtils.copyProperties(param, vehiclePickReturnParam);
        vehiclePickReturnParam.setId(vehiclePickReturn.getId());
        vehiclePickReturnParam.setPrTime(new Date(nowTime));
        vehiclePickReturnParam.setId(0L);
        // 订单已还车
        orderService.updateOrderStatus(orderInfo.getId(), OrderStatusEnum.RETURNED.getStatus(), opUserId);
        // 删除商家没选择的油电退费项
        if (CollectionUtils.isNotEmpty(deletedItemIds)) {
            VehicleReturnExpenseItem deleteItem = new VehicleReturnExpenseItem();
            deleteItem.setDeleted(YesOrNoEnum.YES.getValue());
            deleteItem.setOpTime(nowTime);
            deleteItem.setOpUserId(opUserId);
            VehicleReturnExpenseItemExample deletedItemExample = new VehicleReturnExpenseItemExample();
            deletedItemExample.createCriteria().andIdIn(deletedItemIds);
            vehicleReturnExpenseItemMapper.updateByExampleSelective(deleteItem, deletedItemExample);
        }
        // 生成退费记录
        vehicleReturnExpenseItemService.saveVehicleReturnExpenseItemWhenReturn(vehiclePickReturnParam, opUserId);
        orderBillService.createOrderBill(orderInfo.getId(), 0L);
        // 提前还车修改库存
        long returnTime = orderInfo.getLastReturnDate().getTime();
        boolean returnEarly = returnTime > nowTime;
        Result<Integer> vehicleInfoResult = vehicleInfoService
                .updateWhenReturnVehicle(vehiclePickReturn.getVehicleId(), updateVehicleStatus, null,
                        opUserId, returnEarly, orderInfo.getId(), new Date(nowTime));
        if (ResultUtil.isResultNotSuccess(vehicleInfoResult)) {
            throw new BizException(vehicleInfoResult.getMessage());
        }
        if (OrderSourceEnum.CTRIP.getSource().equals(orderSource)
                || OrderSourceEnum.CTRIP_RESALE.getSource().equals(orderSource)) {
            OrderInfoStatusCallBackReq finishedReq =
                    orderComponent.getOrderInfoCallbackStatusFinishedReq(vehiclePickReturnParam, orderInfo, list);
            PlatformBaseRequest<OrderInfoStatusCallBackReq> platformBaseRequest = new PlatformBaseRequest<>();
            platformBaseRequest.setData(finishedReq);
            platformBaseRequest.setChannelId(orderSource.longValue());
            platformBaseRequest.setMerchantId(orderInfo.getMerchantId());
            log.info("自助取还,商家确认还车 orderInfoCallbackV2 ctripFinish start finishedReq={}", JSON.toJSONString(platformBaseRequest));
            OrderInfoCallbackV2Resp resp = apiClient.finished(platformBaseRequest);
            log.info("自助取还,商家确认还车 orderInfoCallbackV2 ctripFinish end finishedReq={}, resp={}", JSON.toJSONString(platformBaseRequest),
                    JSON.toJSONString(resp));
            if (resp == null || !resp.success()) {
                throw new BizException("携程还车异常");
            }
        }

        // 注册事务同步回调，发送消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                // 违章转移 用户还车后 商家再确认还车不需要终止或补充合同
                if (!SelfPrStatusEnum.RETURNED.getStatus().equals(orderInfo.getSelfPrStatus())) {
                    vehicleIllegalContractService.asyncAbortContractByOrderId(orderInfo.getId());
                }
                // 模拟消息，异步创建对账明细和收支明细
                asyncPromiseExecutor.execute(() ->
                    orderReconciliationService.createReconciliation(param.getOrderId()));
            }
        });

        this.transactionSyncAndSendMsg(orderInfo, OrderMsgTypeEnum.SELF_CONFIRM_RETURN);
        return ResultUtil.successResult(true);
    }

    @Override
    public Result<SelfPickReturnConfirmVO> getSelfExpenseItem(Long orderId, LoginVo loginVo) {
        if (orderId == null) {
            return ResultUtil.failResult("参数错误");
        }
        OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(orderId);
        if (orderInfo == null) {
            return ResultUtil.failResult("订单不存在");
        }
        if (!orderInfo.getMerchantId().equals(loginVo.getMerchantId())) {
            return ResultUtil.failResult("越权查看");
        }
        SelfPickReturnConfirmVO selfPickReturnConfirmVO = new SelfPickReturnConfirmVO();
        List<VehicleReturnExpenseItemVO> retroactiveExpenseItemList = new ArrayList<>();
        List<VehicleReturnExpenseItemVO> refundExpenseItemList = new ArrayList<>();
        Long totalRetroactiveExpense = 0L;

        PayExample payExample = new PayExample();
        payExample.createCriteria().andOrderIdEqualTo(orderId).andFeeTypeEqualTo(PayFeeTypeEnum.SELF_PICK_RETURN_CHARGE.getType());
        List<Pay> pays = payMapper.selectByExample(payExample);
        if (CollectionUtils.isNotEmpty(pays)) {
            Pay pay = pays.stream().findFirst().orElse(null);
            if (Objects.nonNull(pay) && PayStatusEnum.ALL_PAID.getStatus().equals(pay.getPayStatus())) {
                Result<List<VehicleReturnExpenseItemVO>> expenseItemList = vehicleReturnExpenseItemService.expenseItemByOrderId(orderId);
                if (ResultUtil.isResultNotSuccess(expenseItemList)) {
                    return ResultUtil.failResult(ResultEnum.e004);
                }
                for (VehicleReturnExpenseItemVO expenseItemVO : expenseItemList.getModel()) {
                    if (expenseItemVO.getItemType().equals((byte) 1)) {
                        retroactiveExpenseItemList.add(expenseItemVO);
                        totalRetroactiveExpense += expenseItemVO.getExpenseAmount();
                    } else {
                        refundExpenseItemList.add(expenseItemVO);
                        totalRetroactiveExpense -= expenseItemVO.getExpenseAmount();
                    }
                }
                selfPickReturnConfirmVO.setRetroactiveExpenseItemList(retroactiveExpenseItemList);
                selfPickReturnConfirmVO.setRefundExpenseItemList(refundExpenseItemList);
                selfPickReturnConfirmVO.setTotalRetroactiveExpense(totalRetroactiveExpense);

                // 在换车校验和用户未支付之间,会出现用户没有未支付的中间态
                selfPickReturnConfirmVO.setRetroactivePayStatus(Byte.valueOf("1"));
            } else {
                selfPickReturnConfirmVO.setRetroactivePayStatus(Byte.valueOf("0"));
            }
        } else {
            Result<List<VehicleReturnExpenseItemVO>> expenseItemList = vehicleReturnExpenseItemService.expenseItemByOrderId(orderId);
            if (ResultUtil.isResultSuccess(expenseItemList)) {
                for (VehicleReturnExpenseItemVO expenseItemVO : expenseItemList.getModel()) {
                    if (expenseItemVO.getItemType().equals((byte) 1)) {
                        retroactiveExpenseItemList.add(expenseItemVO);
                        totalRetroactiveExpense += expenseItemVO.getExpenseAmount();
                    } else {
                        refundExpenseItemList.add(expenseItemVO);
                        totalRetroactiveExpense -= expenseItemVO.getExpenseAmount();
                    }
                }
            }
            selfPickReturnConfirmVO.setRetroactiveExpenseItemList(retroactiveExpenseItemList);
            selfPickReturnConfirmVO.setRefundExpenseItemList(refundExpenseItemList);
            selfPickReturnConfirmVO.setTotalRetroactiveExpense(totalRetroactiveExpense);
            selfPickReturnConfirmVO.setRetroactivePayStatus(Byte.valueOf("2"));
        }
        return ResultUtil.successResult(selfPickReturnConfirmVO);
    }


    private List<CarImageReq> buildCarImg(List<SelfPickReturnConfirmVO.SelfPickReturnAttVO> attList) {
        if (CollectionUtils.isEmpty(attList)) {
            return Collections.emptyList();
        }
        List<CarImageReq> carImageReqs = new ArrayList<>(attList.size());
        for (SelfPickReturnConfirmVO.SelfPickReturnAttVO att : attList) {
            // 如果没有图片，carPicList传个空list 也行
            if (StringUtils.isEmpty(att.getUrl())) {
                continue;
            }
            CarImageReq carImageReq = new CarImageReq();
            carImageReq.setType(att.getType());
            carImageReq.setUrl(ossComponent.buildStsSignUrl(FileUploader.removeFilePrefix(att.getUrl())));
            carImageReqs.add(carImageReq);
        }
        return carImageReqs;
    }


    private void addNewOperation(SelfPickReturnConfirmVO param, OrderInfo orderInfo, VehicleDeviceStateDTO vehicleDeviceStateDTO, LoginVo loginVo) {
        long nowTime = System.currentTimeMillis();
        OrderSelfPickReturnOperation operation = new OrderSelfPickReturnOperation();
        operation.setOrderId(orderInfo.getId());
        operation.setVehicleId(orderInfo.getVehicleId());
        operation.setVehicleModelId(orderInfo.getVehicleModelId());
        operation.setOpType(OpType.MERCHANT_CHECK.getType());
        operation.setStartTime(param.getStartTime());
        operation.setEndTime(nowTime);

        // 设置车机油电量等数据
        this.setCarEngine(orderInfo, operation, vehicleDeviceStateDTO, true);

        // 设置 车辆问题 车况备注 车辆位置指引备注
        OrderSelfOperationExtraVo orderSelfOperationExtraVo = new OrderSelfOperationExtraVo();
        orderSelfOperationExtraVo.setCarProblem(param.getCarProblem());
        orderSelfOperationExtraVo.setCarRemark(param.getCarRemark());
        orderSelfOperationExtraVo.setPositionRemark(param.getPositionRemark());
        operation.setExtra(JSON.toJSONString(orderSelfOperationExtraVo));

        operation.setDeleted(YesOrNoEnum.NO.getValue());
        operation.setOpUserId(loginVo.getUserId());
        operation.setCreateTime(nowTime);
        operation.setOpTime(nowTime);
        orderSelfPickReturnOperationMapper.insertSelective(operation);

        // 保存附件
        if (CollectionUtils.isNotEmpty(param.getAtt())) {
            List<OrderSelfPickReturnOperationAtt> attList = new ArrayList<>(param.getAtt().size());
            for (SelfPickReturnConfirmVO.SelfPickReturnAttVO attParam : param.getAtt()) {
                attList.add(this.buildOpAtt(operation.getId(), attParam.getType(),
                        FileUploader.removeFilePrefix(attParam.getUrl()), loginVo.getUserId()));
            }
            orderSelfPickReturnOperationAttMapper.batchInsert(attList);
        }

    }

    /**
     * 设置车机相关参数 油电量
     *
     * @param orderInfo
     * @param operation
     * @param vehicleDeviceStateDTO
     */
    private RemainingEnergyDTO setCarEngine(OrderInfo orderInfo, OrderSelfPickReturnOperation operation, VehicleDeviceStateDTO vehicleDeviceStateDTO, boolean isSet) {
        BigDecimal maxOilLiter = vehicleDeviceStateDTO.getTotalOilRemain();

        if (isSet) {
            operation.setDeviceNo(vehicleDeviceStateDTO.getDeviceNo());
            operation.setMaxOilLiter(maxOilLiter != null ? maxOilLiter.intValue() : null);
            operation.setOilLiter(vehicleDeviceStateDTO.getOilRemain());
            operation.setOilPercent(vehicleDeviceStateDTO.getPercentOilRemain());
            operation.setMaxMileage(vehicleDeviceStateDTO.getMilTotal());
            operation.setRemindMileage(vehicleDeviceStateDTO.getMilRange());
            operation.setEnergyType(vehicleDeviceStateDTO.getEnergyType().byteValue());
            operation.setBatteryPercent(vehicleDeviceStateDTO.getRemainBat() != null ? BigDecimal.valueOf(vehicleDeviceStateDTO.getRemainBat()) : null);

            // 油电单价 服务费
            List<VehicleFuelFormSettingVO> settingVOS =
                    vehicleFuelFormSettingService.fuelFormSetting(orderInfo.getMerchantId(), orderInfo.getReturnCityId());
            if (CollectionUtils.isNotEmpty(settingVOS)) {
                for (VehicleFuelFormSettingVO settingVO : settingVOS) {
                    if (vehicleDeviceStateDTO.getEnergyType() == 1) {
                        if (settingVO.getFuelFormType() == 1
                                && vehicleDeviceStateDTO.getFuelNum().startsWith(settingVO.getOilGrade())) {
                            operation.setServiceprice(Long.valueOf(settingVO.getServicePrice()));
                            operation.setOilUnitPrice(Long.valueOf(settingVO.getSettingPrice()));
                        }
                    } else if (vehicleDeviceStateDTO.getEnergyType() == 2) {
                        if (settingVO.getFuelFormType() == 2) {
                            operation.setServiceprice(Long.valueOf(settingVO.getServicePrice()));
                            operation.setMileageUnitPrice(Long.valueOf(settingVO.getSettingPrice()));
                        }
                    } else {
                        if (settingVO.getFuelFormType() == 1
                                && vehicleDeviceStateDTO.getFuelNum().startsWith(settingVO.getOilGrade())) {
                            operation.setServiceprice(Long.valueOf(settingVO.getServicePrice()));
                            operation.setOilUnitPrice(Long.valueOf(settingVO.getSettingPrice()));
                        }
                        if (settingVO.getFuelFormType() == 2) {
                            operation.setMileageUnitPrice(Long.valueOf(settingVO.getSettingPrice()));
                        }
                    }
                }
            }
        }

        // 返回携程参数
        RemainingEnergyDTO remainingEnergyDTO = new RemainingEnergyDTO();
        remainingEnergyDTO.setEnergyType(vehicleDeviceStateDTO.getEnergyType());
        remainingEnergyDTO.setElectricPercent(this.convert2PercentForDecimal(vehicleDeviceStateDTO.getRemainBat()));
        if (FuelFormEnum.ELECTRIC.getCtripEnergyType() != remainingEnergyDTO.getEnergyType()) {
            remainingEnergyDTO.setRemainOil(vehicleDeviceStateDTO.getOilRemain().doubleValue());
        }
        remainingEnergyDTO.setOilPercent(this.convert2PercentForDecimal(vehicleDeviceStateDTO.getPercentOilRemain()));
        remainingEnergyDTO.setRange(vehicleDeviceStateDTO.getMilRange() != null ? Double.valueOf(vehicleDeviceStateDTO.getMilRange()) : null);
        return remainingEnergyDTO;
    }

    /**
     * 百分数转换成小数
     * @param percentage
     * @return
     */
    private Double convert2PercentForDecimal(BigDecimal percentage) {
        if (percentage == null) {
            return null;
        }
        return percentage.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP).doubleValue();
    }
    private Double convert2PercentForDecimal(Integer percentage) {
        if (percentage == null) {
            return null;
        }
        return BigDecimal.valueOf(percentage).divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP).doubleValue();
    }


    /**
     * 商家验车整备数据
     *
     * @param param         页面表单
     * @param operation     上次验车数据
     * @param orderInfo     订单
     * @param vehicleDeviceStateDTO 车机
     * @param loginVo       操作人
     */
    private void updateMerchantCheckOperation(SelfPickReturnConfirmVO param, OrderSelfPickReturnOperation operation,
                                              OrderInfo orderInfo, VehicleDeviceStateDTO vehicleDeviceStateDTO, LoginVo loginVo) {

        long nowTime = System.currentTimeMillis();
        // 设置车机相关 油电量
        this.setCarEngine(orderInfo, operation, vehicleDeviceStateDTO, true);

        // 设置 车辆问题 车况备注 车辆位置指引备注
        OrderSelfOperationExtraVo orderSelfOperationExtraVo = new OrderSelfOperationExtraVo();
        orderSelfOperationExtraVo.setCarProblem(param.getCarProblem());
        orderSelfOperationExtraVo.setCarRemark(param.getCarRemark());
        orderSelfOperationExtraVo.setPositionRemark(param.getPositionRemark());
        operation.setExtra(JSON.toJSONString(orderSelfOperationExtraVo));
        operation.setEndTime(nowTime);

        operation.setOpTime(nowTime);
        operation.setOpUserId(loginVo.getUserId());
        orderSelfPickReturnOperationMapper.updateByPrimaryKeySelective(operation);

        // 查找修改附件
        OrderSelfPickReturnOperationAttExample attExample = new OrderSelfPickReturnOperationAttExample();
        attExample.createCriteria().andOrderSelfOperationIdEqualTo(operation.getId()).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<OrderSelfPickReturnOperationAtt> attList =
            orderSelfPickReturnOperationAttMapper.selectByExample(attExample);
        int minSize = Math.min(attList.size(), param.getAtt().size());
        for (int i = 0; i < minSize; i++) {
            OrderSelfPickReturnOperationAtt oldAtt = attList.get(i);
            SelfPickReturnConfirmVO.SelfPickReturnAttVO attParam = param.getAtt().get(i);
            String paramUrl = FileUploader.removeFilePrefix(attParam.getUrl());
            if (!oldAtt.getUrl().equals(paramUrl) || ObjectUtils.notEqual(attParam.getType(), oldAtt.getType())) {
                oldAtt.setType(attParam.getType());
                oldAtt.setUrl(paramUrl);
                oldAtt.setOpTime(nowTime);
                oldAtt.setOpUserId(loginVo.getUserId());
                orderSelfPickReturnOperationAttMapper.updateByPrimaryKeySelective(oldAtt);
            }
        }

        // 删除后面的即可
        if (attList.size() > param.getAtt().size()) {
            List<Long> deleteIds = attList.stream().skip(param.getAtt().size())
                .map(OrderSelfPickReturnOperationAtt::getId).collect(Collectors.toList());
            OrderSelfPickReturnOperationAttExample deleteExample = new OrderSelfPickReturnOperationAttExample();
            deleteExample.createCriteria().andIdIn(deleteIds).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
            OrderSelfPickReturnOperationAtt record = new OrderSelfPickReturnOperationAtt();
            record.setDeleted(YesOrNoEnum.YES.getValue());
            record.setOpUserId(loginVo.getUserId());
            record.setOpTime(nowTime);
            orderSelfPickReturnOperationAttMapper.updateByExampleSelective(record, deleteExample);
        } else if (attList.size() < param.getAtt().size()) {
            List<OrderSelfPickReturnOperationAtt> insertList = new ArrayList<>(param.getAtt().size() - attList.size());
            param.getAtt().stream().skip(attList.size()).forEach(attParam ->
                insertList.add(this.buildOpAtt(operation.getId(), attParam.getType(),
                        FileUploader.removeFilePrefix(attParam.getUrl()), loginVo.getUserId()))
            );
            orderSelfPickReturnOperationAttMapper.batchInsert(insertList);
        }

    }


    private OrderSelfPickReturnOperationAtt buildOpAtt(Long orderSelfOperationId, Byte type, String ossKey, Long userId) {
        OrderSelfPickReturnOperationAtt att = new OrderSelfPickReturnOperationAtt();
        att.setOrderSelfOperationId(orderSelfOperationId);
        att.setUrl(ossKey);
        att.setType(type);
        att.setDeleted(YesOrNoEnum.NO.getValue());
        att.setCreateTime(System.currentTimeMillis());
        att.setOpTime(System.currentTimeMillis());
        att.setOpUserId(userId);
        return att;
    }


    private OrderSelfPickReturnOperationAtt buildOpAttForCtrip(Long orderSelfOperationId, Byte type, String url) {
        OrderSelfPickReturnOperationAtt att = new OrderSelfPickReturnOperationAtt();
        att.setOrderSelfOperationId(orderSelfOperationId);
        att.setUrl(ossComponent.uploadByUrl(url));
        att.setType(type);
        att.setDeleted(YesOrNoEnum.NO.getValue());
        att.setCreateTime(System.currentTimeMillis());
        att.setOpTime(System.currentTimeMillis());
        att.setOpUserId(0L);
        return att;
    }


    /**
     * 构建费用项
     */
    private void buildVehicleItem(OrderInfo orderInfo, VehicleDeviceStateDTO deviceStateDTO,
                                  CtripCheckCarPickReturnResponse response, List<com.ql.dto.price.ExpenseRecordDTO> recordDTOS) {

        // 获取油/电价格规则数据，根据油号。处理费用单价/数量/单价单位
        OrderSelfPickReturnOperationExample operationExample = new OrderSelfPickReturnOperationExample();
        operationExample.createCriteria().andOrderIdEqualTo(orderInfo.getId())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<OrderSelfPickReturnOperation> selfOperations =
                orderSelfPickReturnOperationMapper.selectByExample(operationExample);
        if (CollectionUtils.isEmpty(selfOperations) || selfOperations.size() < 2) {
            log.info("自助取还还车校验,费用项基础数据不完整 orderId:{}", orderInfo.getId());
            return;
        }
        OrderSelfPickReturnOperation pickOperation = selfOperations.stream()
                .filter(op -> orderInfo.getVehicleId().equals(op.getVehicleId()) && OpType.CUSTOMER_PICK.getType().equals(op.getOpType()))
                .findFirst().orElse(null);

        OrderSelfPickReturnOperation returnOperation = selfOperations.stream()
                .filter(op -> orderInfo.getVehicleId().equals(op.getVehicleId()) && OpType.CUSTOMER_RETURN.getType().equals(op.getOpType()))
                .findFirst().orElse(null);
        if (Objects.isNull(pickOperation) || Objects.isNull(returnOperation)) {
            log.info("自助取还还车校验,费用项基础数据不完整 orderId:{} pickOperation {} or returnOperation {} is null",
                    orderInfo.getId(), pickOperation, returnOperation);
            return;
        }
        log.info("自助取还还车校验,费用项 取还车辆快照 orderId:{} pickOperation: {} returnOperation: {}",
                orderInfo.getId(), JSON.toJSONString(pickOperation), JSON.toJSONString(returnOperation));

        List<CtripCheckCarPickReturnResponse.FeeDTO> buildFeeList = Lists.newArrayList();
        List<CtripCheckCarPickReturnResponse.FeeDTO> buildBackFeeList = Lists.newArrayList();

        Date now = new Date();
        // 延迟还车计算续租费用
       /*  if (now.after(orderInfo.getReturnDate())) {
            orderInfo.getId(), JSON.toJSONString(abbrVoResult), rerentOrderResult.getModel());
            Result<VehicleModelPriceAbbrVo> abbrVoResult = orderService.rerentPrecalculateForThird(orderInfo.getId(), now.getTime(), 2L);

            Result<Long> rerentOrderResult = orderService.createRerentOrder(orderInfo.getId(), now.getTime(),
                    true, (byte) 0, true, 0, 0L);
            log.info("自助取还还车校验,强制续租： orderId:{} abbrVoResult:{} 续租单id:{} ",
            if (abbrVoResult.isSuccess() && Objects.nonNull(abbrVoResult.getModel())
                    && rerentOrderResult.isSuccess() && Objects.nonNull(rerentOrderResult.getModel())) {
                // 补款
                CtripCheckCarPickReturnResponse.FeeDTO topUpFeeDTO = new CtripCheckCarPickReturnResponse.FeeDTO();
                topUpFeeDTO.setCode(FeeEnum.SELF_DEPOSIT.getCode());
                topUpFeeDTO.setName(FeeEnum.SELF_DEPOSIT.getName());
                topUpFeeDTO.setAmount(new BigDecimal(abbrVoResult.getModel().getTotalAmount()));
                topUpFeeDTO.setUnit(topUpFeeDTO.getAmount() + "元/次");
                topUpFeeDTO.setUnitPrice(topUpFeeDTO.getAmount());
                topUpFeeDTO.setQuantity(new BigDecimal(1));
               // buildFeeList.add(topUpFeeDTO);
            } else {
                log.info("自助取还还车校验,强制续租： orderId:{} now:{} 异常", orderInfo.getId(), now);
            }
        }*/
        // 提前还车计算退款费用


        CtripCheckCarPickReturnResponse.FeeDTO energyServiceFeeDTO = new CtripCheckCarPickReturnResponse.FeeDTO();
        CtripCheckCarPickReturnResponse.FeeDTO energyFeeDTO = new CtripCheckCarPickReturnResponse.FeeDTO();
        // 电车 计算补/退款 加油/电服务费 FeeEnum.REFUEL_ELECTRICITY_FEE.getCode()
        // 无忧租订单，不收油电服务费
        boolean isPackage = false;
        if (StringUtils.isNotBlank(orderInfo.getExtra())) {
            OrderExtraDTO orderExtraDTO = JSON.parseObject(orderInfo.getExtra(), OrderExtraDTO.class);
            isPackage = StringUtils.isNotBlank(orderExtraDTO.getPackageId()) && "sec".equals(orderExtraDTO.getPackageId());
        }
        if (FuelFormEnum.ELECTRIC.getCtripEnergyType().equals(deviceStateDTO.getEnergyType())) {
            // 还车里程 < 取车里程。 收取加电费
            int mileageDifference = returnOperation.getRemindMileage() - pickOperation.getRemindMileage();
            if (mileageDifference < 0) {
                energyFeeDTO.setCode(FeeEnum.PAY_ONLINE_ELECTRICITY.getCode());
                energyFeeDTO.setName(FeeEnum.PAY_ONLINE_ELECTRICITY.getName());
                // 补款dto
                this.handelFeeDTOUnit(energyFeeDTO, returnOperation, pickOperation);
                // 服务费dto
                if (BooleanUtils.isFalse(isPackage)) {
                    energyServiceFeeDTO.setCode(FeeEnum.REFUEL_ELECTRICITY_FEE.getCode());
                    energyServiceFeeDTO.setName(FeeEnum.REFUEL_ELECTRICITY_FEE.getName());
                    this.handelFeeDTOUnit(energyServiceFeeDTO, returnOperation, pickOperation);
                    if (energyServiceFeeDTO.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                        buildFeeList.add(energyServiceFeeDTO);
                    }
                }
                if (energyFeeDTO.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                    buildFeeList.add(energyFeeDTO);
                }
            } else if (mileageDifference > 0) {
                // 退款dto
                energyFeeDTO.setCode(FeeEnum.PAY_ONLINE_ELECTRICITY_BACK.getCode());
                energyFeeDTO.setName(FeeEnum.PAY_ONLINE_ELECTRICITY_BACK.getName());
                this.handelFeeDTOUnit(energyFeeDTO, returnOperation, pickOperation);
                if (energyFeeDTO.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                    buildBackFeeList.add(energyFeeDTO);
                }

            }
        }
        // 混动 计算油量的补/退款 加油/电服务费
        // 油车 计算补/退款 加油/电服务费
        else {
            // oil_liter
            BigDecimal oilDifference = returnOperation.getOilLiter().subtract(pickOperation.getOilLiter());
            // 判断 i 是否大于0
            if (oilDifference.compareTo(BigDecimal.ZERO) < 0) {
                // 补款dto
                energyFeeDTO.setCode(FeeEnum.PAY_ONLINE_FUEL.getCode());
                energyFeeDTO.setName(FeeEnum.PAY_ONLINE_FUEL.getName());
                this.handelFeeDTOUnit(energyFeeDTO, returnOperation, pickOperation);
                // 服务费dto
                if (BooleanUtils.isFalse(isPackage)) {
                    energyServiceFeeDTO.setCode(FeeEnum.REFUEL_ELECTRICITY_FEE.getCode());
                    energyServiceFeeDTO.setName(FeeEnum.REFUEL_ELECTRICITY_FEE.getName());
                    this.handelFeeDTOUnit(energyServiceFeeDTO, returnOperation, pickOperation);
                    if (energyServiceFeeDTO.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                        buildFeeList.add(energyServiceFeeDTO);
                    }
                }
                if (energyFeeDTO.getAmount().compareTo(BigDecimal.ZERO) > 0){
                    buildFeeList.add(energyFeeDTO);
                }
            } else if (oilDifference.compareTo(BigDecimal.ZERO) > 0) {
                // 退款dto
                energyFeeDTO.setCode(FeeEnum.PAY_ONLINE_FUEL.getCode());
                energyFeeDTO.setName(FeeEnum.PAY_ONLINE_FUEL.getName());
                this.handelFeeDTOUnit(energyFeeDTO, returnOperation, pickOperation);
                if (energyFeeDTO.getAmount().compareTo(BigDecimal.ZERO) > 0){
                    buildBackFeeList.add(energyFeeDTO);
                }
            }
        }
        BigDecimal payAmount = new BigDecimal(0);
        if (CollectionUtils.isNotEmpty(buildBackFeeList)) {
            buildBackFeeList = buildBackFeeList.stream().filter(Objects::nonNull)
                    .filter(feeDTO -> Objects.nonNull(feeDTO.getAmount()))
                    .peek(feeDTO -> feeDTO.setAmount(feeDTO.getAmount()))
                    .collect(Collectors.toList());
            BigDecimal totalAmount = buildBackFeeList.stream()
                    .map(CtripCheckCarPickReturnResponse.FeeDTO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            response.setBackFeeList(buildBackFeeList);
            payAmount = totalAmount.negate();
            if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                response.setToRefundAmount(totalAmount);
            }
        } else {
            buildFeeList = buildFeeList.stream().filter(Objects::nonNull)
                    .filter(feeDTO -> Objects.nonNull(feeDTO.getAmount()))
                    .collect(Collectors.toList());
            BigDecimal totalAmount = buildFeeList.stream()
                    .map(CtripCheckCarPickReturnResponse.FeeDTO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            response.setFeeList(buildFeeList);
            payAmount = totalAmount;
            response.setFeeAmount(totalAmount);
        }
        log.info("自助取还还车校验,费用项 orderId:{} buildFeeList:{} buildBackFeeList:{}",
                orderInfo.getId(), JSON.toJSONString(buildFeeList), JSON.toJSONString(buildBackFeeList));
        // 落库
        List<VehicleReturnExpenseItem> itemResult = Lists.newArrayList();
        itemResult.addAll(this.converterExpenseItem(orderInfo, buildFeeList, false));
        itemResult.addAll(this.converterExpenseItem(orderInfo, buildBackFeeList, true));
        // 第二次校验起，删除再插入
        VehicleReturnExpenseItemExample vehicleReturnExpenseItemExample = new VehicleReturnExpenseItemExample();
        vehicleReturnExpenseItemExample.createCriteria().andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andOrderIdEqualTo(orderInfo.getId())
                .andExpenseItemPropIdIn(Arrays.asList(17L, 18L, 19L, 20L, 5L));
        Date date = new Date();
        vehicleReturnExpenseItemMapper.deleteByExample(vehicleReturnExpenseItemExample);
        if (CollectionUtils.isNotEmpty(itemResult)) {
            vehicleReturnExpenseItemMapper.batchInsert(itemResult);
        }
        String serialNumber = saveSelfPrPay(orderInfo, 2L, date.getTime(), payAmount, recordDTOS);
        if (StringUtils.isNotBlank(serialNumber)) {
            response.setToPaySerialNumber(serialNumber);
        }
    }


    /**
     * 处理明细项单位数据
     */
    private void handelFeeDTOUnit(CtripCheckCarPickReturnResponse.FeeDTO feeDTO, OrderSelfPickReturnOperation returnOperation,
                                  OrderSelfPickReturnOperation pickOperation) {

        if (feeDTO.getCode() == null) {
            log.info("feeDTO code is null");
            return;
        }
        String unit;
        BigDecimal quantity;
        BigDecimal unitPrice;
        if (Stream.of(FeeEnum.PAY_ONLINE_FUEL.getCode(), FeeEnum.PAY_ONLINE_FUEL_BACK.getCode())
                .anyMatch(code -> Objects.equals(code, feeDTO.getCode()))) {
            if (pickOperation.getOilUnitPrice() > 0) {
                unitPrice = new BigDecimal(pickOperation.getOilUnitPrice()).divide(new BigDecimal(100));
            } else {
                unitPrice = new BigDecimal(0);
            }
            unit = "L";
            quantity = returnOperation.getOilLiter().subtract(pickOperation.getOilLiter()).abs();
        } else if (Stream.of(FeeEnum.PAY_ONLINE_ELECTRICITY.getCode(), FeeEnum.PAY_ONLINE_ELECTRICITY_BACK.getCode())
                .anyMatch(code -> Objects.equals(code, feeDTO.getCode()))) {
            if (pickOperation.getMileageUnitPrice() > 0) {
                unitPrice = new BigDecimal(pickOperation.getMileageUnitPrice()).divide(new BigDecimal(100));
            } else {
                unitPrice = new BigDecimal(0);
            }
            unit = "公里";
            quantity = BigDecimal.valueOf(Math.abs(returnOperation.getRemindMileage() - pickOperation.getRemindMileage()));
        } else {
            if (pickOperation.getServiceprice() > 0) {
                unitPrice = new BigDecimal(pickOperation.getServiceprice()).divide(new BigDecimal(100));
            } else {
                unitPrice = new BigDecimal(0);
            }
            unit = "次";
            quantity = new BigDecimal(1);
        }
        if (unitPrice.compareTo(BigDecimal.ZERO) > 0) {
            feeDTO.setAmount(unitPrice.multiply(quantity));
        } else {
            feeDTO.setAmount(new BigDecimal(0));
        }
        feeDTO.setUnit(unit);
        feeDTO.setQuantity(quantity);
        feeDTO.setUnitPrice(unitPrice);

    }

    /**
     * 携程费用项转换saas费用项
     * @param orderInfo 订单详情
     * @param feeDTOS 携程费用项
     * @param isBack true 退款 false补款
     * @return
     */
    private List<VehicleReturnExpenseItem> converterExpenseItem(OrderInfo orderInfo, List<CtripCheckCarPickReturnResponse.FeeDTO> feeDTOS, Boolean isBack) {
        if (CollectionUtils.isEmpty(feeDTOS)) {
            return Lists.newArrayList();
        }
        List<VehicleReturnExpenseItem> result = Lists.newArrayList();
        long cur = System.currentTimeMillis();
        for (CtripCheckCarPickReturnResponse.FeeDTO feeDTO : feeDTOS) {
            VehicleReturnExpenseItem item = new VehicleReturnExpenseItem();
            FeeEnum feeEnum = FeeEnum.feeEnumByCodeAndBackTag(feeDTO.getCode(), isBack);
            item.setOrderId(orderInfo.getId());
            item.setVehicleReturnId(0L);
            item.setExpenseItemPropId(feeEnum.getSaasItemPropId());
            item.setExpenseItemName(feeEnum.getSaasItemName());
            item.setExpenseAmount(feeDTO.getAmount().multiply(new BigDecimal(100)).longValue());
            item.setItemType(isBack ? (byte)2 : (byte)1);
            item.setLastVer(0);
            item.setCreateTime(cur);
            item.setOpTime(cur);
            item.setOpUserId(0L);
            item.setDeleted(YesOrNoEnum.NO.getValue());
            item.setPayKind(PayKindEnum.OTHER.getKind());
            result.add(item);
        }
        return result;
    }

}
