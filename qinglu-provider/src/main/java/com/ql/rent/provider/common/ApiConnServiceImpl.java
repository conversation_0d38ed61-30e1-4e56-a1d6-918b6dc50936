package com.ql.rent.provider.common;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ql.Constant;
import com.ql.rent.api.aggregate.model.remote.ctrip.dto.CtripStandardFeeStatusDTO;
import com.ql.rent.constant.QyWechatConstant;
import com.ql.rent.common.IRedisService;
import com.ql.rent.constant.RedisConstant;
import com.ql.rent.dao.common.ApiConnApplyMapper;
import com.ql.rent.dao.common.ApiConnMapper;
import com.ql.rent.entity.common.ApiConn;
import com.ql.rent.entity.common.ApiConnApply;
import com.ql.rent.entity.common.ApiConnApplyExample;
import com.ql.rent.entity.common.ApiConnExample;
import com.ql.rent.enums.CommonEnums;
import com.ql.rent.enums.HelloChannelInitStatusEnum;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.common.ChannelStatusEnum;
import com.ql.rent.enums.common.ResultEnum;
import com.ql.rent.enums.trade.OrderSourceEnum;
import com.ql.rent.param.common.ApiConnBusiParam;
import com.ql.rent.param.common.ApiConnParam;
import com.ql.rent.param.merchant.MerchantInfoQuery;
import com.ql.rent.provider.common.mapper.ApiConnStructMapper;
import com.ql.rent.service.common.IApiConnService;
import com.ql.rent.service.common.IChannelService;
import com.ql.rent.service.merchant.MerchantInfoService;
import com.ql.rent.service.third.IEnterpriseWechatService;
import com.ql.rent.service.trade.IMerchantAccountService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.UuidUtil;
import com.ql.rent.util.CopyUtil;
import com.ql.rent.util.MailUtil;
import com.ql.rent.vo.common.*;
import com.ql.rent.vo.merchant.MerchantInfoVo;
import com.ql.rent.vo.trade.MerchantAccountVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ql.Constant.API_CONN_KEY;

/**
 * @desc: 商家&渠道绑定
 * @author: zhengminjie
 * @time: 2022-09-16 20:50
 * @Version: 1.0
 */
@Service
@Slf4j
public class ApiConnServiceImpl implements IApiConnService {

    @Resource
    private ApiConnMapper apiConnMapper;
    @Resource
    private IChannelService channelService;
    @Resource
    private MerchantInfoService merchantInfoService;
    @Resource
    private MailUtil mailUtil;
    @Resource
    private IMerchantAccountService merchantAccountService;
    @Resource
    private IEnterpriseWechatService enterpriseWechatService;
    @Value("${ctrip.standard.notify.customer.service.user:''}")
    private String ctripStandardNotifyCustomerServiceUser;
    @Resource
    private IRedisService redisService;
    @Resource
    private ApiConnApplyMapper apiConnApplyMapper;
    @Value("${mail.channel.apply}")
    private String apply;
    @Value("${profile.env}")
    private String profileEnv;
    @Value("${hello.sign.app-key}")
    private String helloAppKey;
    @Value("${hello.sign.app-secret}")
    private String helloAppSecret;

    /**
     * 商家&渠道绑定列表
     */
    @Override
    public Result<List<ApiConnVo>> listChannel(Long merchantId) {
        ApiConnExample example = new ApiConnExample();
        example.createCriteria().andMerchantIdEqualTo(merchantId);
        List<ApiConn> list = apiConnMapper.selectByExample(example);

        Result<List<ChannelVO>> channelListResult = channelService.listAllChannel(YesOrNoEnum.NO.getValue());
        Map<Long, String> channelNameMap =
                Optional.ofNullable(channelListResult).map(Result::getModel).map(List::stream).map(stream -> stream.collect(
                        Collectors.toMap(ChannelVO::getId, ChannelVO::getChannelName))).orElse(Collections.emptyMap());

        List<ApiConnVo> retList = new ArrayList<>(list.size());
        Result<MerchantAccountVO> accountVOResult = merchantAccountService.getByMerchantId(merchantId);
        MerchantAccountVO accountVO = accountVOResult.getModel();
        for (ApiConn bean : list) {
            ApiConnVo vo = ApiConnStructMapper.INSTANCE.toApiConnVo(bean);
            vo.setChannelName(channelNameMap.get(vo.getChannelId()));
            if (accountVO != null) {
                vo.setArreared(accountVO.getArreared());
                vo.setArrearsTime(accountVO.getArrearsTime());
            }
            retList.add(vo);
        }
        return ResultUtil.successResult(retList);
    }

    /**
     * 商家&渠道绑定列表（前端根据业务展示）
     */
    @Override
    public Result<List<ApiConnVo>> listChannelByBusi(ApiConnBusiParam param) {
        if (YesOrNoEnum.isYes(param.getWorryFreeRent())) {
            param.setIncludeApi(YesOrNoEnum.YES.getValue());
            param.setStatus(ChannelStatusEnum.OPEN.getType());
            param.setIncludeSync(YesOrNoEnum.NO.getValue());
            param.setIncludeOffLine(YesOrNoEnum.NO.getValue());
        }
        ApiConnExample example = new ApiConnExample();
        ApiConnExample.Criteria criteria = example.createCriteria().andMerchantIdEqualTo(param.getMerchantId())
            .andStatusGreaterThan(ChannelStatusEnum.FAIL.getType());
        if (param.getStatus() != null) {
            criteria.andStatusEqualTo(param.getStatus());
        }

        List<ApiConn> list = apiConnMapper.selectByExample(example);

        Result<List<ChannelVO>> channelListResult = channelService.listAllChannel(YesOrNoEnum.YES.getValue());
        Map<Long, ChannelVO> channelMap = channelListResult.getModel().stream().collect(Collectors.toMap(e -> e.getId(), e -> e));

        List<ApiConnVo> retList = new ArrayList<>(list.size());
        if (param.getIncludeOffLine() != null && YesOrNoEnum.isYes(param.getIncludeOffLine())) {
            ApiConnVo vo = setConnVo(1L, channelMap);
            vo.setConnType((byte) 1);
            retList.add(vo);
        }
        for (ApiConn bean : list) {
            if (param.getIncludeSync() == null || YesOrNoEnum.isNo(param.getIncludeSync())){
                if (bean.getConnType() == 2 || bean.getConnType() == 4) {
                    continue;
                }
            }
            if (param.getIncludeApi() != null && YesOrNoEnum.isNo(param.getIncludeApi())){
                if (bean.getConnType() == 1) {
                    continue;
                }
            }
            ApiConnVo vo = setConnVo(bean.getChannelId(), channelMap);
            vo.setConnType(bean.getConnType());
            if (param.getIncludeSync() != null
                    && YesOrNoEnum.isYes(param.getIncludeSync())
                    && bean.getConnType() == 2
                    && !OrderSourceEnum.MOJIE.longValue().equals(bean.getChannelId())) {
                vo.setChannelName(vo.getChannelName() + "同步");
            }
            retList.add(vo);
        }
        if (param.getIncludeSub() != null && YesOrNoEnum.isYes(param.getIncludeSub())) {
            List<ChannelVO> tempList = channelListResult.getModel().stream().
                    filter(e -> !e.getParentId().equals(0L)).collect(Collectors.toList());
            for (ChannelVO channelVO : tempList) {
                ApiConnVo parentVo = retList.stream()
                        .filter(e -> e.getChannelId().equals(channelVO.getParentId())).collect(Collectors.toList())
                        .stream().findFirst().orElse(null);
                if (parentVo == null) {
                    continue;
                }
                ApiConnVo vo = setConnVo(channelVO.getId(), channelMap);
                vo.setConnType(parentVo.getConnType());
                retList.add(vo);
            }
        }
        retList.sort(Comparator.comparing(ApiConnVo::getConnType).thenComparing(ApiConnVo::getSort));
        // 仅无忧租页面显示携程和飞猪
        if (YesOrNoEnum.isYes(param.getWorryFreeRent())) {
            retList.removeIf(e -> !Arrays.asList(OrderSourceEnum.CTRIP.longValue(), OrderSourceEnum.FEIZHU.longValue()).contains(e.getChannelId()));
            retList.forEach(e -> {
                if (OrderSourceEnum.CTRIP.longValue().equals(e.getChannelId())) {
                    e.setChannelName(e.getChannelName() + "/" + OrderSourceEnum.CTRIP_RESALE.getName());
                }
            });
        }
        return ResultUtil.successResult(retList);
    }

    private ApiConnVo setConnVo(Long channelId, Map<Long, ChannelVO> channelMap) {
        String channelName = "";
        int sort = 9999;
        ChannelVO channelVO = channelMap.get(channelId);
        if (channelVO != null) {
            channelName = channelVO.getChannelName();
            if (channelVO.getSort() != null) {
                sort = channelVO.getSort();
            }
        }
        ApiConnVo vo = new ApiConnVo();
        vo.setChannelId(channelId);
        vo.setChannelName(channelName);
        vo.setSort(sort);
        return vo;
    }

    /**
     * 查询数据是否存在
     *
     * @param id
     * @return
     */
    @Override
    public Result<ApiConnVo> find(Long id) {
        ApiConn apiConn = apiConnMapper.selectByPrimaryKey(id);
        if (apiConn == null || ChannelStatusEnum.STOP_APPLY.getType() .equals(apiConn.getStatus())) {
            return ResultUtil.failResult("数据不存在");
        }
        ApiConnVo vo = ApiConnStructMapper.INSTANCE.toApiConnVo(apiConn);
        return ResultUtil.successResult(vo);
    }

    /**
     * 数据保存
     *
     * @param vo
     * @return
     */
    @Override
    public Result<Boolean> save(ApiConnParam vo) {
        ApiConn apiConn = ApiConnStructMapper.INSTANCE.toApiConn(vo);
        // 查询数据是否存在
        ApiConnExample example = new ApiConnExample();
        example.createCriteria().andMerchantIdEqualTo(vo.getMerchantId()).andChannelIdEqualTo(vo.getChannelId());
        List<ApiConn> list = apiConnMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            ApiConn apiConnData = list.get(0);
            byte status = apiConnData.getStatus();;
            if (Byte.valueOf("-1").equals(apiConnData.getStatus())){
                status = ChannelStatusEnum.APPLY.getType();
            } else {
                if (apiConnData.getConnType() != 2) {
                    return ResultUtil.failResult("对接类型不支持修改密码");
                }
                if (status == ChannelStatusEnum.APPLY.getType()) {
                    return ResultUtil.failResult("渠道申请中");
                }
            }
            apiConn.setStatus(status);
            apiConn.setId(apiConnData.getId());
            apiConn.setChannelSharedKey(apiConnData.getChannelSharedKey());
        }

        apiConn.setOpTime(System.currentTimeMillis());
        if (StringUtils.isEmpty(apiConn.getChannelSharedKey()) &&
            vo.getChannelId().compareTo(OrderSourceEnum.WUKONG.getSource().longValue()) == 0 &&
            vo.getConnType() == 2) {
            apiConn.setChannelSharedKey(UUID.randomUUID().toString().replace("-",""));
        }
        if (apiConn.getId() == null) {
            apiConn.setStatus(ChannelStatusEnum.APPLY.getType());
            apiConnMapper.insertSelective(apiConn);
        } else {
            apiConnMapper.updateByPrimaryKeySelective(apiConn);
        }
        try {
            ExecutorService executorService = Executors.newCachedThreadPool();
            executorService.execute(() -> {
                Result<ChannelVO> channelResult = channelService.findChannel(vo.getChannelId());
                Result<MerchantInfoVo> merchantResult = merchantInfoService.findById(vo.getMerchantId());
                StringBuffer content = new StringBuffer();
                content.append("供应商信息：");
                content.append(vo.getMerchantId());
                content.append("-");
                content.append(merchantResult.getModel().getName());
                content.append("<BR>");
                content.append("申请开通渠道信息：");
                content.append(channelResult.getModel().getChannelName());
                content.append("<BR>");
                content.append("申请人手机号：");
                content.append(apiConn.getChannelRegPhone());
                List<String> mailAdds = Arrays.asList(apply.split(";"));
                mailUtil.sendMail(mailAdds, "供应商渠道申请", content.toString());
            });
            executorService.shutdown();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResultUtil.successResult(true);
    }

    @Override
    public Result<Boolean> gdsAccreditApplyFor(ThirdAccreditApplyVO req) {
        if (Stream.of(req.getMerchantId(), req.getThirdMerchantId(), req.getChannelRegPhone(),
            req.getVerificationCode(), req.getConnType()).anyMatch(Objects::isNull)) {
            return ResultUtil.failResult("参数缺失");
        }

        // 验证手机验证码
        String channelRegPhone = req.getChannelRegPhone();
        if ("prod".equals(profileEnv)) {
            Object codeObj = redisService.get(RedisConstant.CodeRedisKey.SEND_SMS_OPEN_API + channelRegPhone);
            if (Objects.isNull(codeObj)) {
                return ResultUtil.failResult("验证码已过期");
            }
            String code = String.valueOf(codeObj);
            if (!Objects.equals(req.getVerificationCode(), code)) {
                return ResultUtil.failResult("验证码错误");
            }
        }

        long nowTime = System.currentTimeMillis();
        ApiConn apiConn = new ApiConn();
        apiConn.setChannelId(req.getChannelId());
        apiConn.setConnType(req.getConnType());
        apiConn.setMerchantId(req.getMerchantId());
        apiConn.setChannelRegPhone(req.getChannelRegPhone());
        apiConn.setChannelVendorCode(req.getThirdMerchantId());
        apiConn.setAppkey(helloAppKey);
        apiConn.setAppsecret(helloAppSecret);
        apiConn.setOpTime(nowTime);

        // 查询数据是否存在
        ApiConnExample example = new ApiConnExample();
        example.createCriteria().andMerchantIdEqualTo(req.getMerchantId()).andChannelIdEqualTo(req.getChannelId());
        List<ApiConn> list = apiConnMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            ApiConn apiConnData = list.get(0);
            byte status = apiConnData.getStatus();;
            apiConn.setStatus(status);
            apiConn.setId(apiConnData.getId());
            apiConn.setChannelSharedKey(apiConnData.getChannelSharedKey());
        }

        if (StringUtils.isEmpty(apiConn.getChannelSharedKey())
            && Constant.ChannelId.WUKONG.equals(req.getChannelId())
            && req.getConnType() == 2) {
            apiConn.setChannelSharedKey(UuidUtil.getUUID());
        }
        if (apiConn.getId() == null) {
            apiConn.setStatus(ChannelStatusEnum.STOP_APPLY.getType());
            apiConn.setCreateTime(nowTime);
            apiConnMapper.insertSelective(apiConn);
        } else {
            apiConnMapper.updateByPrimaryKeySelective(apiConn);
            String key = String.format(API_CONN_KEY, apiConn.getMerchantId(), apiConn.getChannelId());
            redisService.delete(key);
        }

        return ResultUtil.successResult(Boolean.TRUE);
    }


    /**
     * 修改数据
     *
     * @return
     */
    @Override
    public Result<Boolean> updStatus(Long id, Byte status) {
        if (!ChannelStatusEnum.OPEN.getType().equals(status) && !ChannelStatusEnum.CLOSE.getType().equals(status)) {
            return ResultUtil.failResult("状态错误");
        }
        ApiConn old = apiConnMapper.selectByPrimaryKey(id);
        if (old == null) {
            return ResultUtil.failResult("数据不存在");
        }
        ApiConn apiConn = new ApiConn();
        apiConn.setId(id);
        apiConn.setLastVer(old.getLastVer() + 1);
        apiConn.setStatus(status);
        apiConn.setOpTime(System.currentTimeMillis());
        apiConnMapper.updateByPrimaryKeySelective(apiConn);

        String key = String.format(API_CONN_KEY, apiConn.getMerchantId(), apiConn.getChannelId());
        redisService.delete(key);
        return ResultUtil.successResult(true);
    }


    @Override
    public Result<String> unbind(Long id, Byte status, String channelRegPass) {
        if (StringUtils.isEmpty(channelRegPass)) {
            return ResultUtil.failResult("渠道密码不能为空");
        }
        if (!ChannelStatusEnum.CLOSE.getType().equals(status)) {
            return ResultUtil.failResult("状态错误");
        }
        ApiConn old = apiConnMapper.selectByPrimaryKey(id);
        if (old == null) {
            return ResultUtil.failResult("数据不存在");
        }
        if (!channelRegPass.equals(old.getChannelRegPass())) {
            return ResultUtil.failResult("密码错误,解绑失败");
        }
        ApiConn apiConn = new ApiConn();
        apiConn.setId(id);
        apiConn.setLastVer(old.getLastVer() + 1);
        apiConn.setStatus(ChannelStatusEnum.STOP_APPLY.getType());
        apiConn.setChannelRegPass("");
        apiConn.setChannelRegUser("");
        apiConn.setOpTime(System.currentTimeMillis());
        if (apiConnMapper.updateByPrimaryKeySelective(apiConn) > 0) {
            String key = String.format(API_CONN_KEY, old.getMerchantId(), old.getChannelId());
            redisService.delete(key);
            return ResultUtil.successResult("解绑成功");
        } else {
            return ResultUtil.successResult("解绑失败");
        }
    }

    @Override
    public Result<ApiConnMoreVo> getApiConnMoreByMerchantIdAndChannelId(Long merchantId, Long channelId) {
        if (merchantId == null || channelId == null) {
            return ResultUtil.failResult("参数错误");
        }
        ApiConnExample apiConnExample = new ApiConnExample();
        apiConnExample.createCriteria().andMerchantIdEqualTo(merchantId).andChannelIdEqualTo(channelId)
                .andStatusEqualTo(ChannelStatusEnum.OPEN.getType());
        List<ApiConn> apiConnList = apiConnMapper.selectByExample(apiConnExample);
        if (CollectionUtils.isEmpty(apiConnList)) {
            return ResultUtil.failResult("未查询到对应数据");
        }
        if (apiConnList.size() > 1) {
            return ResultUtil.failResult("商家和渠道绑定数据错误，请联系管理员修改");
        }
        ApiConnMoreVo vo = ApiConnStructMapper.INSTANCE.toApiConnMoreVo(apiConnList.get(0));
        return ResultUtil.successResult(vo);
    }

    @Override
    public Result<ApiConnMoreVo> getApiConnMoreByVendorIdAndChannelId(String vendorId, Long channelId) {
        if (vendorId == null || channelId == null) {
            return ResultUtil.failResult("参数错误");
        }
        ApiConnExample apiConnExample = new ApiConnExample();
        apiConnExample.createCriteria().andChannelVendorCodeEqualTo(vendorId).andChannelIdEqualTo(channelId)
            .andStatusEqualTo(ChannelStatusEnum.OPEN.getType());
        List<ApiConn> apiConnList = apiConnMapper.selectByExample(apiConnExample);
        if (CollectionUtils.isEmpty(apiConnList)) {
            return ResultUtil.failResult("未查询到对应数据");
        }
        if (apiConnList.size() > 1) {
            return ResultUtil.failResult("商家和渠道绑定数据错误，请联系管理员修改");
        }
        ApiConnMoreVo vo = ApiConnStructMapper.INSTANCE.toApiConnMoreVo(apiConnList.get(0));
        return ResultUtil.successResult(vo);
    }


    @Override
    public Result<ApiConnVo> getByMerchantIdAndChannelId(Long merchantId, Long channelId) {
        if (merchantId == null || channelId == null) {
            return ResultUtil.failResult("参数错误");
        }
        ApiConnExample apiConnExample = new ApiConnExample();
        apiConnExample.createCriteria().andMerchantIdEqualTo(merchantId).andChannelIdEqualTo(channelId)
                .andStatusNotEqualTo(ChannelStatusEnum.STOP_APPLY.getType());
        List<ApiConn> apiConnList = apiConnMapper.selectByExample(apiConnExample);
        if (CollectionUtils.isEmpty(apiConnList)) {
            return ResultUtil.failResult("未查询到对应数据");
        }
        if (apiConnList.size() > 1) {
            return ResultUtil.failResult("商家和渠道绑定数据错误，请联系管理员修改");
        }

        ApiConnVo vo = ApiConnStructMapper.INSTANCE.toApiConnVo(apiConnList.get(0));
        String extra = apiConnList.get(0).getExtra();
        if (StringUtils.isNotBlank(extra)) {
            ApiConnEXVo exVo = JSONUtil.toBean(extra, ApiConnEXVo.class);
            vo.setCheckPriceSwitch(exVo.getCheckPriceSwitch());
        }
        return ResultUtil.successResult(vo);
    }



   @Override
    public Result<List<ApiConnVo>> getByMerchantIdAndChannelId(Long merchantId,  List<Long> channelId) {
        if (merchantId == null || channelId == null) {
            return ResultUtil.failResult("参数错误");
        }
        ApiConnExample apiConnExample = new ApiConnExample();
        apiConnExample.createCriteria().andMerchantIdEqualTo(merchantId).andChannelIdIn(channelId)
                .andStatusNotEqualTo(ChannelStatusEnum.STOP_APPLY.getType());
        List<ApiConn> apiConnList = apiConnMapper.selectByExample(apiConnExample);
        if (CollectionUtils.isEmpty(apiConnList)) {
            return ResultUtil.failResult("未查询到对应数据");
        }

       List<ApiConnVo> voArrayList = Lists.newArrayList();
       for (ApiConn apiConn : apiConnList) {
           ApiConnVo vo = new ApiConnVo();
           vo.setChannelId(apiConn.getChannelId());
           vo.setMerchantId(apiConn.getMerchantId());
           vo.setCtripPushStatus(apiConn.getCtripPushStatus());
           // todo 临时代码处理。 悟空真实启用，根据是否有appsecret
           if (IChannelService.WUKONG_CHANNEL_ID.equals(apiConn.getChannelId())) {
              vo.setStatus(StringUtils.isNotBlank(apiConn.getAppsecret()) ? (byte)3 : (byte)2);
           } else {
               vo.setStatus(apiConn.getStatus());
           }
           voArrayList.add(vo);
       }
        return ResultUtil.successResult(voArrayList);
    }

    @Override
    public Result<List<ApiConnVo>> getAllMerchantInfoByStatus() {
        List<ApiConn> apiConnList = apiConnMapper.selectAllMerchantList();
        if (CollectionUtils.isEmpty(apiConnList)) {
            return ResultUtil.failResult("未查询到对应数据");
        }
        List<ApiConnVo> apiConnVos = CopyUtil.copyList(apiConnList, ApiConnVo::new);
        return ResultUtil.successResult(apiConnVos);
    }

    @Override
    public Result<Boolean> updCtripPushStatus(Long merchantId, Byte status) {
        if (null == merchantId || null == status) {
            return ResultUtil.failResult("参数错误");
        }
        ApiConnExample apiConnExample = new ApiConnExample();
        apiConnExample.createCriteria().andMerchantIdEqualTo(merchantId).andChannelIdEqualTo(OrderSourceEnum.CTRIP.getSource().longValue());
        List<ApiConn> apiConnList = apiConnMapper.selectByExample(apiConnExample);
        if (CollectionUtils.isEmpty(apiConnList)) {
            return ResultUtil.failResult("未查询到对应数据");
        }
        if (apiConnList.size() > 1) {
            return ResultUtil.failResult("商家和渠道绑定数据错误，请联系管理员修改");
        }
        ApiConn apiConn = apiConnList.get(0);
        ApiConn record = new ApiConn();
        ApiConnEXVo apiConnEXVo = new ApiConnEXVo();
        apiConnEXVo.setCheckPriceSwitch((byte)0);
        if(status==10){
            status=(byte)1;
            apiConnEXVo.setCheckPriceSwitch((byte)1);
        }
        record.setExtra(JSONUtil.toJsonStr(apiConnEXVo));
        record.setId(apiConn.getId());
        record.setLastVer(apiConn.getLastVer() + 1);
        record.setCtripPushStatus(status);
        record.setOpTime(System.currentTimeMillis());
        apiConnMapper.updateByPrimaryKeySelective(record);
        return ResultUtil.successResult(true);
    }

    @Override
    public Result<Boolean> stopApply(Long merchantId, Long channelId) {
        ApiConnExample apiConnExample = new ApiConnExample();
        apiConnExample.createCriteria().andMerchantIdEqualTo(merchantId).andChannelIdEqualTo(channelId);
        List<ApiConn> apiConns = apiConnMapper.selectByExample(apiConnExample);
        if (CollectionUtils.isEmpty(apiConns)) {
            return ResultUtil.successResult(false);
        }
        ApiConn apiConn = apiConns.get(0);
        apiConn.setStatus(Byte.valueOf("-1"));
        apiConn.setOpTime(System.currentTimeMillis());
        int ret = apiConnMapper.updateByPrimaryKey(apiConn);
        return ResultUtil.successResult(ret > 0);
    }

    @Override
    public Result<Boolean> updatePWD(Long merchantId, Long channelId, String regUser, String pwd) {
        ApiConnExample apiConnExample = new ApiConnExample();
        apiConnExample.createCriteria().andMerchantIdEqualTo(merchantId).andChannelIdEqualTo(channelId);
        List<ApiConn> apiConns = apiConnMapper.selectByExample(apiConnExample);
        if (CollectionUtils.isEmpty(apiConns)) {
            return ResultUtil.successResult(false);
        }
        ApiConn apiConn = apiConns.get(0);
        apiConn.setChannelRegUser(regUser);
        apiConn.setChannelRegPass(pwd);
        return ResultUtil.successResult(apiConnMapper.updateByPrimaryKey(apiConn) > 1);
    }

    @Override
    public Result<List<Long>> getPushChannelByMerchantId(Long merchantId) {
        if (merchantId == null) {
            return ResultUtil.failResult("参数错误");
        }

        ApiConnExample apiConnExample = new ApiConnExample();
        apiConnExample.createCriteria().andMerchantIdEqualTo(merchantId);
        List<ApiConn> apiConnList = apiConnMapper.selectByExample(apiConnExample);
        if (CollectionUtils.isEmpty(apiConnList)) {
            return ResultUtil.successResult(Collections.emptyList());
        }

        List<Long> openPushChannelIds = new ArrayList<>();
        for (ApiConn apiConn : apiConnList) {
            ApiConnEXVo exVo = getApiConnEXVo(apiConn);

            // 根据是否有appSecret判断是否悟空的推送能力是否打开
            if (Constant.ChannelId.WUKONG.equals(apiConn.getChannelId())) {
                if (StringUtils.isNotBlank(apiConn.getAppsecret())) {
                    openPushChannelIds.add(apiConn.getChannelId());
                }
                continue;
            }

            if (Constant.ChannelId.CTRIP.equals(apiConn.getChannelId())) {
                if (YesOrNoEnum.isYes(apiConn.getCtripPushStatus())) {
                    openPushChannelIds.add(apiConn.getChannelId());
                }
                continue;
            }

            // 飞猪推送能力
            if (Constant.ChannelId.FEIZHU.equals(apiConn.getChannelId())) {
                if (YesOrNoEnum.isYes(exVo.getFeizhuPushSwitch())) {
                    openPushChannelIds.add(apiConn.getChannelId());
                }
                continue;
            }
            if (Constant.ChannelId.TIE_XING.equals(apiConn.getChannelId())) {
                if (CommonEnums.ApiConnStatusEnum.SUCCESS.getStatus().equals(apiConn.getStatus())
                    && StringUtils.isNotBlank(apiConn.getChannelVendorCode())) {
                    openPushChannelIds.add(apiConn.getChannelId());
                }
                continue;
            }

            // 哈啰推送能力
            if (Constant.ChannelId.HELLO.equals(apiConn.getChannelId())) {
                if (YesOrNoEnum.isYes(exVo.getHelloPushSwitch())) {
                    openPushChannelIds.add(apiConn.getChannelId());
                }
                continue;
            }
             if (Constant.ChannelId.DIDI.equals(apiConn.getChannelId())) {
                if (CommonEnums.ApiConnStatusEnum.SUCCESS.getStatus().equals(apiConn.getStatus())
                        && StringUtils.isNotBlank(apiConn.getChannelVendorCode())) {
                    openPushChannelIds.add(apiConn.getChannelId());
                }
                continue;
            }
        }
        return ResultUtil.successResult(openPushChannelIds);
    }

    private ApiConnEXVo getApiConnEXVo(ApiConn apiConn) {
        String extra = apiConn.getExtra();
        ApiConnEXVo exVo;
        if (StringUtils.isNotBlank(extra)) {
            exVo = JSONUtil.toBean(extra, ApiConnEXVo.class);
        } else {
            exVo = new ApiConnEXVo();
        }
        return exVo;
    }

    @Override
    public Result<Boolean> getApiConnOpenRpaStatus(Long merchantId) {
        ApiConnExample apiConnExample = new ApiConnExample();
        apiConnExample.createCriteria().andMerchantIdEqualTo(merchantId)
                .andConnTypeEqualTo((byte) 2)
                .andStatusEqualTo(ChannelStatusEnum.OPEN.getType());
        return ResultUtil.successResult( apiConnMapper.countByExample(apiConnExample) > 0L);
    }

    @Override
    public Result<Boolean> getApiConnOpenStandardStatus(Long merchantId) {
        return ResultUtil.successResult(true);
//        boolean result = false;
//        ApiConnExample apiConnExample = new ApiConnExample();
//        apiConnExample.createCriteria().andMerchantIdEqualTo(merchantId)
//                .andConnTypeEqualTo((byte) 1)
//                .andChannelIdEqualTo(Constant.ChannelId.CTRIP)
//                .andStatusEqualTo(ChannelStatusEnum.OPEN.getType());
//        List<ApiConn> apiConnList = apiConnMapper.selectByExample(apiConnExample);
//        if (CollectionUtils.isNotEmpty(apiConnList)) {
//            String extra = apiConnList.get(0).getExtra();
//            if (StringUtils.isNotEmpty(extra)) {
//                ApiConnEXVo exVo = JSONUtil.toBean(extra, ApiConnEXVo.class);
//                if (exVo != null && YesOrNoEnum.isYes(exVo.getCtripStandardFee())) {
//                    result = true;
//                }
//            }
//        }
//        return ResultUtil.successResult(result);
    }

    @Override
    public Result<Boolean> updateCtripStandardStatus(String content){
        String infoTitle = "携程标准化开关通知,";
        log.info(infoTitle + "request={}", content);
        List<CtripStandardFeeStatusDTO> statusList = JSON.parseArray(content, CtripStandardFeeStatusDTO.class);
        if (CollectionUtils.isEmpty(statusList)) {
            return ResultUtil.successResult(true);
        }

        for (CtripStandardFeeStatusDTO dto : statusList) {
            String message = "携程标准化开关通知\n" +
                    "环境：" + profileEnv + "\n" +
                    "类型：apiType\n" +
                    "关闭：close\n" +
                    "开启：open\n" +
                    "参数：" + JSON.toJSONString(dto);
            // 处理黑白名单数据
            if (dto.getSwitchStatus()) {
                dto.setGrayVendor(StringUtils.EMPTY);
            } else {
                List<String> grayList = new ArrayList<>();
                if (StringUtils.isNotEmpty(dto.getGrayVendor())) {
                    grayList = Arrays.asList(dto.getGrayVendor().split(","));
                }
                List<String> blackList = new ArrayList<>();
                if (StringUtils.isNotEmpty(dto.getBlackVendor())) {
                    blackList = Arrays.asList(dto.getBlackVendor().split(","));
                    List<String> blackTmp = blackList.stream().collect(Collectors.toList());
                    grayList = grayList.stream().filter(e -> !blackTmp.contains(e)).collect(Collectors.toList());
                }
                dto.setGrayVendor(String.join(",", grayList));
            }
            // 查询apiConn需要使用的数据
            ApiConnExample apiConnExample = new ApiConnExample();
            ApiConnExample.Criteria criteria = apiConnExample.createCriteria();
            criteria.andChannelIdEqualTo(Constant.ChannelId.CTRIP);
            criteria.andStatusEqualTo((byte) 3);
            criteria.andConnTypeEqualTo((byte) 1);
            // 直连or上货
            if (dto.getSwitchType().equals("saasUpload")) {
                criteria.andApiTypeEqualTo((byte) 1);
                message = message.replace("apiType", "上货商家");
            } else if (dto.getSwitchType().equals("saasApi")) {
                message = message.replace("apiType", "直连商家");
                criteria.andApiTypeEqualTo((byte) 2);
            } else {
                // 数据异常，忽略
                enterpriseWechatService.sendGroupMsgV2(QyWechatConstant.COMPANY_ROBOT_KEY, "携程标准化开关通知,参数异常：req=" + JSON.toJSONString(dto), Collections.singletonList(ctripStandardNotifyCustomerServiceUser));
                continue;
            }
            List<String> blackVendors = new ArrayList<>();
            if (StringUtils.isNotEmpty(dto.getBlackVendor())) {
                blackVendors = Arrays.asList(dto.getBlackVendor().split(",")).stream().distinct().collect(Collectors.toList());
            }
            List<String> grayVendors = new ArrayList<>();
            if (StringUtils.isNotEmpty(dto.getGrayVendor())) {
                grayVendors = Arrays.asList(dto.getGrayVendor().split(",")).stream().distinct().collect(Collectors.toList());
            }
            List<ApiConn> apiConnList = apiConnMapper.selectByExample(apiConnExample);
            // 过滤未设置appKey的携程商家
            apiConnList = apiConnList.stream()
                    .filter(e -> StringUtils.isNotEmpty(e.getAppkey()))
                    .filter(e -> StringUtils.isNotEmpty(e.getAppsecret()))
                    .filter(e -> StringUtils.isNotEmpty(e.getChannelVendorCode()))
                    .collect(Collectors.toList());
            Map<String, ApiConn> apiConnMap = apiConnList.stream().collect(Collectors.toMap(ApiConn::getChannelVendorCode, e -> e));
            // 组装数据
            List<String> blacks;
            List<String> grays;
            List<Long> mIds = apiConnList.stream().map(e -> e.getMerchantId()).collect(Collectors.toList());
            Map<Long, String> mMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(mIds)) {
                MerchantInfoQuery query = new MerchantInfoQuery();
                query.setIdList(mIds);
                Result<List<MerchantInfoVo>> mResult = merchantInfoService.listMerchantInfoVo(query);
                if (mResult.isSuccess()) {
                    mMap = mResult.getModel().stream().collect(Collectors.toMap(MerchantInfoVo::getId, e -> e.getName()));
                }
            }
            if (dto.getSwitchStatus()) {
                blacks = blackVendors.stream().collect(Collectors.toList());
                grays = apiConnList.stream().filter(e -> !blacks.contains(e.getChannelVendorCode()))
                        .map(e -> e.getChannelVendorCode()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(blacks)) {
                    message = message.replace("open", "开启所有商家");
                    message = message.replace("close","无");
                } else {
                    message = message.replace("open", "开启所有商家（排除关闭）");
                    message = message.replace("close", getVendorName(blacks, apiConnMap, mMap));
                }
            } else {
                if (CollectionUtils.isEmpty(grayVendors)) {
                    blacks = apiConnList.stream()
                            .map(e -> e.getChannelVendorCode()).collect(Collectors.toList());
                    grays = new ArrayList<>();
                    message = message.replace("open", "无");
                    message = message.replace("close", "关闭所有商家");
                } else {
                    //blacks = blackVendors.stream().collect(Collectors.toList());
                    grays = grayVendors.stream().collect(Collectors.toList());
                    message = message.replace("open", getVendorName(grays, apiConnMap, mMap));
                    blacks = apiConnList.stream().filter(e -> !grays.contains(e.getChannelVendorCode()))
                            .map(e -> e.getChannelVendorCode()).collect(Collectors.toList());
                    message = message.replace("close", "关闭所有商家（排除开启）");
                }
            }
            // 数据处理
            log.info(infoTitle + "数据处理;messageDate={},打开={},关闭={}", JSON.toJSONString(dto), JSON.toJSONString(grays), JSON.toJSONString(blacks));
            updateCtripStandardFeeStatus(apiConnMap, blacks, grays);
            //enterpriseWechatService.sendGroupMsgV2(QyWechatConstant.COMPANY_ROBOT_KEY, message, Collections.singletonList(ctripStandardNotifyCustomerServiceUser));

        }
        return ResultUtil.successResult(true);
    }

    private String getVendorName(List<String> vendorCodes, Map<String, ApiConn> apiConnMap,  Map<Long, String> mMap) {
        if (CollectionUtils.isEmpty(vendorCodes)){
            return "";
        }
        String formatName = "%s:%s:%s";
        List<String> vendorNames = new ArrayList<>();
        int index = 0;
        for (String vendorCode : vendorCodes) {
            if (apiConnMap.containsKey(vendorCode)) {
                ApiConn apiconn = apiConnMap.get(vendorCode);
                if (apiconn == null) {
                    continue;
                }
                String name = mMap.get(apiconn.getMerchantId());
                if (name == null) {
                    name = "未设置名称";
                }
                name = String.format(formatName, vendorCode, apiconn.getMerchantId(), name);
                if (index != 0) {
                    name = "\n　　　" + name;
                }
                vendorNames.add(name);
                index++;
            }
        }
        if (CollectionUtils.isEmpty(vendorNames)) {
            return "";
        }
        String names[] = vendorNames.toArray(new String[vendorNames.size()]);
        return String.join("", names);
    }

    private void updateCtripStandardFeeStatus(Map<String, ApiConn> apiConnMap, List<String> blackVendors, List<String> grayVendors) {
        for (String vendorCode : blackVendors) {
            ApiConn apiConn = apiConnMap.get(vendorCode);
            updateApiConnStatus(apiConn, YesOrNoEnum.NO.getValue());
        }
        for (String vendorCode : grayVendors) {
            ApiConn apiConn = apiConnMap.get(vendorCode);
            updateApiConnStatus(apiConn, YesOrNoEnum.YES.getValue());
        }
    }

    private void updateApiConnStatus(ApiConn apiConn, Byte status) {
        if (apiConn == null) {
            return;
        }
        String extra = apiConn.getExtra();
        if (StringUtils.isBlank(extra)) {
            extra = "{}";
        }
        ApiConnEXVo exVo = JSONUtil.toBean(extra, ApiConnEXVo.class);
        Byte standardFee = exVo.getCtripStandardFee();
        if (YesOrNoEnum.isYes(status)) {
            if (standardFee == null || YesOrNoEnum.isNo(standardFee)) {
                exVo.setCtripStandardFee(YesOrNoEnum.YES.getValue());
            } else {
                return;
            }
        } else {
            if (standardFee != null && YesOrNoEnum.isYes(standardFee)) {
                exVo.setCtripStandardFee(YesOrNoEnum.NO.getValue());
            } else {
                return;
            }
        }
        ApiConn updApiConn = new ApiConn();
        updApiConn.setId(apiConn.getId());
        updApiConn.setExtra(JSONUtil.toJsonStr(exVo));
        updApiConn.setOpTime(System.currentTimeMillis());
        apiConnMapper.updateByPrimaryKeySelective(updApiConn);
    }

    @Override
    public Result<Boolean> updStatusWithNoCheck(Long merchantId, Long channelId, Byte status) {
        if (merchantId == null || channelId == null || status == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }

        ApiConnExample apiConnExample = new ApiConnExample();
        apiConnExample.createCriteria().andMerchantIdEqualTo(merchantId).andChannelIdEqualTo(channelId);
        List<ApiConn> apiConns = apiConnMapper.selectByExample(apiConnExample);
        if (CollectionUtils.isEmpty(apiConns)) {
            return ResultUtil.failResult(ResultEnum.e004, "渠道信息为空");
        }

        ApiConn old = apiConns.get(0);
        if (old == null) {
            return ResultUtil.failResult("数据不存在");
        }
        ApiConn apiConn = new ApiConn();
        apiConn.setId(old.getId());
        apiConn.setLastVer(old.getLastVer() + 1);
        apiConn.setStatus(status);
        apiConn.setOpTime(System.currentTimeMillis());
        apiConnMapper.updateByPrimaryKeySelective(apiConn);

        String key = String.format(API_CONN_KEY, apiConn.getMerchantId(), apiConn.getChannelId());
        redisService.delete(key);
        return ResultUtil.successResult(true);
    }

    @Override
    public Result<TaskStatusVO> getApplyResult(Long merchantId, Long channelId) {
        if (merchantId == null || channelId == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }

        ApiConnExample apiConnExample = new ApiConnExample();
        apiConnExample.createCriteria().andMerchantIdEqualTo(merchantId).andChannelIdEqualTo(channelId);
        List<ApiConn> apiConns = apiConnMapper.selectByExample(apiConnExample);
        if (CollectionUtils.isEmpty(apiConns)) {
            return ResultUtil.failResult(ResultEnum.e004);
        }

        ApiConnApplyExample apiConnApplyExample = new ApiConnApplyExample();
        apiConnApplyExample.createCriteria().andMerchantIdEqualTo(merchantId)
            .andChannelIdEqualTo(channelId).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<ApiConnApply> apiConnApplies = apiConnApplyMapper.selectByExample(apiConnApplyExample);
        if (CollectionUtils.isEmpty(apiConnApplies)) {
            return ResultUtil.successResult(new TaskStatusVO());
        }

        TaskStatusVO taskStatusVO = new TaskStatusVO();
        Optional<ApiConnApply> any =
            apiConnApplies.stream().max(Comparator.comparing(ApiConnApply::getId)).stream().findAny();
        ApiConnApply apiConnApply = any.get();

        //
        if (HelloChannelInitStatusEnum.MAX_STEP == apiConnApply.getStep() && apiConnApply.getStatus() == 1) {
            taskStatusVO.setStatus(3);
        } else {
            if (apiConnApply.getStatus() == 2) {
                taskStatusVO.setStatus(1);
                taskStatusVO.setMessage(apiConnApply.getDisplayMessage());
            } else {
                taskStatusVO.setStatus(0);
            }
        }
        return ResultUtil.successResult(taskStatusVO);
    }
}
