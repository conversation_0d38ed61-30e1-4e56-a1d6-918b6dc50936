package com.ql.rent.provider.pay;

import com.qinglusaas.logutil.QingluLogger;
import com.qinglusaas.logutil.QingluLoggerFactory;
import com.ql.dto.ApiResultResp;
import com.ql.rent.api.aggregate.remote.platform.api.PlatformApiClient;
import com.ql.rent.api.aggregate.remote.platform.vo.request.ThirdPayCreateOrderReq;
import com.ql.rent.api.aggregate.remote.platform.vo.request.ThirdPayRefundReq;
import com.ql.rent.api.aggregate.remote.platform.vo.response.ThirdPayCreateOrderResp;
import com.ql.rent.api.aggregate.remote.platform.vo.response.ThirdPayRefundResp;
import com.ql.rent.converter.pay.WxPayConverter;
import com.ql.rent.share.utils.UuidUtil;
import org.springframework.stereotype.Component;

import com.ql.dto.pay.RefundResult;
import com.ql.dto.pay.ThirdPayResult;
import com.ql.enums.ThirdPayEnum;
import com.ql.rent.param.pay.RefundParam;
import com.ql.rent.param.pay.ThirdPayCreateParam;

import javax.annotation.Resource;
import java.util.UUID;


@Component
public class WxMiniProgramPayHandler implements ThirdPayHandler {
    
    private final QingluLogger logger = QingluLoggerFactory.getLogger(WxMiniProgramPayHandler.class);
    
    @Resource
    private PlatformApiClient platformApiClient;

    @Override
    public ThirdPayResult createThirdPayOrder(ThirdPayCreateParam thirdPayCreateParam) {
        logger.startLog().with("微信小程序创建支付订单开始", thirdPayCreateParam).log("WX_PAY_CREATE_START");
        
        try {
            // 构建平台接口请求参数
            ThirdPayCreateOrderReq request = WxPayConverter.INSTANCE.toCreateOrderRequest(thirdPayCreateParam);
            
            // 调用平台接口
            ApiResultResp<ThirdPayCreateOrderResp> response = platformApiClient.createThirdPayOrder(request);
            
            logger.startLog().with("调用platform创建微信支付订单响应", response).logAudit("WX_PAY_CREATE_RESPONSE");
            
            // 转换响应结果
            return convertToThirdPayResult(response);
            
        } catch (Exception e) {
            logger.startLog().with("异常信息", e.getMessage()).logError("微信小程序创建支付订单异常");
            return ThirdPayResult.failedResult("创建微信支付订单失败：" + e.getMessage());
        }
    }

    @Override
    public RefundResult refund(RefundParam refundParam) {
        logger.startLog().with("微信小程序退款开始", refundParam).log("WX_PAY_REFUND_START");
        
        try {
            // 构建平台接口请求参数
            ThirdPayRefundReq request = WxPayConverter.INSTANCE.toRefundRequest(refundParam);
            
            // 调用平台接口
            ApiResultResp<ThirdPayRefundResp> response = platformApiClient.thirdPayRefund(request);
            
            logger.startLog().with("调用platform微信退款响应", response).logAudit("WX_PAY_REFUND_RESPONSE");
            
            // 转换响应结果
            return convertToRefundResult(response);
            
        } catch (Exception e) {
            logger.startLog().with("异常信息", e.getMessage()).logError("微信小程序退款异常");
            return RefundResult.failedResult("微信退款失败：" + e.getMessage());
        }
    }

    @Override
    public Integer thirdPaySource() {
        return ThirdPayEnum.ThirdPaySource.WX_MINI_PROGRAM.getSource();
    }
    
    /**
     * 转换创建订单响应结果
     */
    private ThirdPayResult convertToThirdPayResult(ApiResultResp<ThirdPayCreateOrderResp> response) {
        if (response == null || !ApiResultResp.isSuccess(response)) {
            String errorMsg = response != null ? response.getMessage() : "调用platform接口失败";
            return ThirdPayResult.failedResult(errorMsg);
        }
        
        ThirdPayCreateOrderResp data = response.getData();
        if (data == null) {
            return ThirdPayResult.failedResult("platform返回数据为空");
        }

        ThirdPayResult thirdPayResult = WxPayConverter.INSTANCE.toThirdPayResult(data);
        thirdPayResult.setNonceStr(UuidUtil.getUUID());
        thirdPayResult.setTimeStamp(String.valueOf(System.currentTimeMillis()));
        return thirdPayResult;
    }
    
    /**
     * 转换退款响应结果
     */
    private RefundResult convertToRefundResult(ApiResultResp<ThirdPayRefundResp> response) {
        if (response == null || !ApiResultResp.isSuccess(response)) {
            String errorMsg = response != null ? response.getMessage() : "调用platform接口失败";
            return RefundResult.failedResult(errorMsg);
        }
        
        ThirdPayRefundResp data = response.getData();
        if (data == null) {
            return RefundResult.failedResult("platform返回数据为空");
        }
        
        return WxPayConverter.INSTANCE.toRefundResult(data);
    }

}
