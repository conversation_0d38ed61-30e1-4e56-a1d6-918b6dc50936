package com.ql.rent.provider.common;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.ql.rent.dao.common.CalendarHolidayMapper;
import com.ql.rent.entity.common.CalendarHoliday;
import com.ql.rent.entity.common.CalendarHolidayExample;
import com.ql.rent.enums.price.CtripHolidayTypeEnum;
import com.ql.rent.service.common.ICalendarHolidayService;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.vo.common.CalendarHolidayVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * @desc: 携程节假日Service
 * @author: pijiu
 * @time: 2025-02-05 16:12
 * @Version: 1.0
 */
@Service
public class CalendarHolidayServiceImpl implements ICalendarHolidayService {

    @Resource
    private CalendarHolidayMapper calendarHolidayMapper;

    /**
     * 使用本地缓存
     */
    private final Cache<String, List<CalendarHolidayVO>> HOLIDAY_CACHE = CacheBuilder.newBuilder()
        .initialCapacity(1)
        .maximumSize(2)
        .expireAfterWrite(10, TimeUnit.MINUTES).build();

    @Override
    public Long getMidAndNational() {
        LocalDate now = LocalDate.now();

        //查询携程 中秋&国庆合并数据
        CalendarHolidayExample holidayExample = new CalendarHolidayExample();
        holidayExample.createCriteria()
                .andHolidayTypeEqualTo(String.valueOf(CtripHolidayTypeEnum.MID_AUTUMN_AND_NATIONAL_DAY.getId()))
                .andYearEqualTo(now.getYear());
        return calendarHolidayMapper.countByExample(holidayExample);
    }

    @Override
    public List<CalendarHolidayVO> listValidHoliday() {
        final String key = "listValidHoliday";
        try {
            return HOLIDAY_CACHE.get(key, () -> {
                CalendarHolidayExample holidayExample = new CalendarHolidayExample();
                holidayExample.createCriteria().andYearGreaterThanOrEqualTo(LocalDate.now().getYear() - 1);
                List<CalendarHoliday> calendarHolidays = calendarHolidayMapper.selectByExample(holidayExample);
                if (CollectionUtils.isEmpty(calendarHolidays)) {
                    return null;
                }
                List<CalendarHolidayVO> resultList = new ArrayList<>(calendarHolidays.size());
                for (CalendarHoliday entity : calendarHolidays) {
                    CalendarHolidayVO calendarHolidayVO = new CalendarHolidayVO();
                    calendarHolidayVO.setId(entity.getId());
                    calendarHolidayVO.setYear(entity.getYear());
                    calendarHolidayVO.setName(entity.getName());
                    calendarHolidayVO.setHolidayType(entity.getHolidayType());
                    calendarHolidayVO.setHolidayDateStart(DateUtil.dateToLocalDate(entity.getHolidayDateStart()));
                    calendarHolidayVO.setHolidayDateEnd(DateUtil.dateToLocalDate(entity.getHolidayDateEnd()));
                    resultList.add(calendarHolidayVO);
                }
                return resultList;
            });
        } catch (ExecutionException e) {
            return new ArrayList<>();
        }
    }

    @Override
    public Boolean includingHoliday(String startTime, String endTime) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return null;
        }
        LocalDate start = LocalDateTime.parse(startTime).toLocalDate();
        LocalDate end = LocalDateTime.parse(endTime).toLocalDate();
        return includingHoliday(start, end);
    }

    @Override
    public Boolean includingHoliday(LocalDate startTime, LocalDate endTime) {
        List<CalendarHolidayVO> validHolidays = listValidHoliday();
        if (CollectionUtils.isEmpty(validHolidays)) {
            return false;
        }
        for (CalendarHolidayVO validHoliday : validHolidays) {
            if (!endTime.isBefore(validHoliday.getHolidayDateStart())
                && !startTime.isAfter(validHoliday.getHolidayDateEnd())) {
                return true;
            }
        }
        return false;
    }
}
