package com.ql.rent.provider.common;

import com.alibaba.fastjson.JSONObject;
import com.ql.Constant;
import com.ql.dto.open.request.PlatformBaseRequest;
import com.ql.rent.api.aggregate.remote.api.APIClient;
import com.ql.rent.api.aggregate.remote.vo.request.ServiceCircleInitReq;
import com.ql.rent.api.aggregate.remote.vo.request.StoreInitReq;
import com.ql.rent.api.aggregate.remote.vo.request.VehicleInitReq;
import com.ql.rent.common.IRedisService;
import com.ql.rent.component.PlatformBiz;
import com.ql.rent.dao.common.ApiConnApplyMapper;
import com.ql.rent.entity.common.ApiConnApply;
import com.ql.rent.entity.common.ApiConnApplyExample;
import com.ql.rent.enums.CommonEnums;
import com.ql.rent.enums.HelloChannelInitStatusEnum;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.common.ResultEnum;
import com.ql.rent.enums.store.IdRelationEnum;
import com.ql.rent.service.common.IApiConnService;
import com.ql.rent.service.common.IHelloChannelInitService;
import com.ql.rent.service.store.IThirdIdRelationService;
import com.ql.rent.service.trade.IOrderSyncService;
import com.ql.rent.service.vehicle.IVehicleBindService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.common.TaskRecordVo;
import com.ql.rent.vo.common.ThirdBatchAddVo;
import com.ql.rent.vo.store.IdRelationVo;
import com.ql.rent.vo.store.ThirdStoreAddVo;
import com.ql.rent.vo.store.ThirdStoreBatchAddVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ql.rent.service.common.IChannelService.HELLO_CHANNEL_ID;

@Slf4j
@Service
@RequiredArgsConstructor
public class HelloChannelInitServiceImpl implements IHelloChannelInitService {

    private final static String HELLO_INIT_LOCK = "HELLO_INIT_LOCK";

    @Resource
    private PlatformBiz platformBiz;
    @Resource
    private IApiConnService apiConnService;
    @Resource
    private IThirdIdRelationService thirdIdRelationService;
    @Resource
    private APIClient apiClient;
    @Resource
    private IVehicleBindService vehicleBindService;
    @Resource
    private ApiConnApplyMapper apiConnApplyMapper;
    @Resource
    private Executor asyncPromiseExecutor;
    @Resource
    private IRedisService redisService;
    @Resource
    private IOrderSyncService orderSyncService;

    private final Map<Integer, Function<ThirdBatchAddVo, Result>> taskMap = new HashMap<>();

    private final Long channelId = Constant.ChannelId.HELLO;

    @PostConstruct
    public void init() {
        // raptor回调通知
        taskMap.put(HelloChannelInitStatusEnum.STORE_MAPPING.getIndex(), this::handleStoreMatchData);
        // raptor回调通知
        taskMap.put(HelloChannelInitStatusEnum.INIT_CIRCLE.getIndex(), this::initStoreCircleData);
        taskMap.put(HelloChannelInitStatusEnum.VEHICLE_BIND.getIndex(), this::handleVehicleMatchData);
        taskMap.put(HelloChannelInitStatusEnum.SAVE_SHOP.getIndex(), this::saveHelloShop);
        taskMap.put(HelloChannelInitStatusEnum.SAVE_VEHICLE.getIndex(), this::initVehicleData);
        taskMap.put(HelloChannelInitStatusEnum.INIT_PRICE.getIndex(), this::initPriceData);
        taskMap.put(HelloChannelInitStatusEnum.INIT_STOCK.getIndex(), this::initStockData);
        taskMap.put(HelloChannelInitStatusEnum.INIT_ORDER.getIndex(), this::initOrderData);
    }

    @Override
    public Result<Boolean> addGdsThirdMatchInfo(ThirdBatchAddVo req) {
        if (req == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }

        // 修改状态
        Result<Boolean> startResult = apiConnService.updStatusWithNoCheck(req.getMerchantId(), req.getChannelId(),
            CommonEnums.ApiConnStatusEnum.APPLY.getStatus());
        if (ResultUtil.isResultNotSuccess(startResult)) {
            return startResult;
        }
        // 先把参数 粗暴丢到redis里,以便后续任务取出
        redisService.set(this.getRedisParamKey(req.getMerchantId()), req, -1);

        // 启动第一个任务
        asyncPromiseExecutor.execute(() -> {
            // 开始各部分的拉取动作
            ApiConnApply apiConnApply = this.saveApiConnApply(req.getMerchantId(), req.getChannelId(),
                HelloChannelInitStatusEnum.STORE_MAPPING);
            try {
                taskMap.get(HelloChannelInitStatusEnum.STORE_MAPPING.getIndex()).apply(req);
            } catch (Exception e) {
                this.setFailed(apiConnApply.getId(), "初始化门店失败", e.getMessage());
                log.error("[HELLO-INIT]任务失败, merchantId:{}, channelId:{}, {} - {}",
                    req.getMerchantId(), req.getChannelId(),
                    HelloChannelInitStatusEnum.STORE_MAPPING.getIndex(),
                    HelloChannelInitStatusEnum.STORE_MAPPING.getSceneName(),
                    e);
            }
        });
        return ResultUtil.successResult(true);
    }

    @Override
    public Result<Integer> statusCallBack(TaskRecordVo callback) {
        ApiConnApplyExample apiConnApplyExample = new ApiConnApplyExample();
        apiConnApplyExample.createCriteria().andChannelIdEqualTo(callback.getChannelId()).andDeletedEqualTo(YesOrNoEnum.NO.getValue())
            .andMerchantIdEqualTo(callback.getMerchantId()).andStepEqualTo(callback.getIndex());
        apiConnApplyExample.setOrderByClause("id desc limit 1");

        List<ApiConnApply> apiConnApplies = apiConnApplyMapper.selectByExample(apiConnApplyExample);
        if (CollectionUtils.isEmpty(apiConnApplies)) {
            // todo
        }
        ApiConnApply apiConnApply = new ApiConnApply();
        apiConnApply.setId(apiConnApplies.get(0).getId());
        apiConnApply.setDisplayMessage(StringUtils.truncate(StringUtils.defaultString(callback.getDisplayMessage()), 500));
        apiConnApply.setDetailMessage(StringUtils.truncate(StringUtils.defaultString(callback.getDetailMessage()), 500));
        apiConnApply.setStatus(callback.getStatus());
        apiConnApply.setOpTime(System.currentTimeMillis());
        apiConnApply.setLastVer(apiConnApplies.get(0).getLastVer() + 1);
        apiConnApplyMapper.updateByPrimaryKeySelective(apiConnApply);

        if (apiConnApply.getStatus() == 1) {
            Optional<HelloChannelInitStatusEnum> nextTaskOpt =
                Arrays.stream(HelloChannelInitStatusEnum.values()).filter(e -> e.getIndex() > callback.getIndex())
                    .min(Comparator.comparing(HelloChannelInitStatusEnum::getIndex));
            String redisParamKey = this.getRedisParamKey(callback.getMerchantId());
            if (nextTaskOpt.isPresent()) {
                // 后续还有任务接着触发
                ThirdBatchAddVo param = (ThirdBatchAddVo)redisService.get(redisParamKey);
                this.saveApiConnApply(callback.getMerchantId(), callback.getChannelId(), nextTaskOpt.get());
                log.info("[HELLO-INIT]任务触发 {}, {}", nextTaskOpt.get(), param);
                asyncPromiseExecutor.execute(() -> {
                    Function<ThirdBatchAddVo, Result> function = taskMap.get(nextTaskOpt.get().getIndex());
                    try {
                        if (function != null) {
                            log.info("[HELLO-INIT]任务触发 {}, {}", nextTaskOpt.get(), param);
                            function.apply(param);
                        }
                    } catch (Exception e) {
                        this.setFailed(apiConnApply.getId(), nextTaskOpt.get().getSceneName() + "发生错误", e.getMessage());
                        log.error("[HELLO-INIT] error {}", nextTaskOpt.get(), e);
                    }
                });
            } else {
                log.info("[HELLO-INIT]业务初始化完成,{}", callback.getMerchantId());
                // 更新
                apiConnService.updStatusWithNoCheck(callback.getMerchantId(), Constant.ChannelId.HELLO,
                    CommonEnums.ApiConnStatusEnum.SUCCESS.getStatus());
                // 任务结束，清除标记
                redisService.delete(redisParamKey);
            }
        }
        return ResultUtil.successResult(1);
    }

    @Override
    public Result<Boolean> continueGdsInit(Long merchantId) {
        if (merchantId == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }
        if (redisService.setnx(HELLO_INIT_LOCK + merchantId, 2L) > 1) {
            return ResultUtil.failResult(ResultEnum.e008);
        }

        Object commitParam = redisService.get(this.getRedisParamKey(merchantId));
        if (commitParam == null) {
            return ResultUtil.failResult("参数已过期,需要重新匹配");
        }

        ApiConnApplyExample apiConnApplyExample = new ApiConnApplyExample();
        apiConnApplyExample.createCriteria().andChannelIdEqualTo(channelId)
            .andMerchantIdEqualTo(merchantId).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        apiConnApplyExample.setOrderByClause("id desc limit 1");
        List<ApiConnApply> apiConnApplies = apiConnApplyMapper.selectByExample(apiConnApplyExample);
        if (CollectionUtils.isEmpty(apiConnApplies)) {
            return ResultUtil.failResult("未启动过初始化，无法继续");
        }

        ApiConnApply lastTask = apiConnApplies.get(0);
        if (lastTask.getStatus() == TaskStatusEnum.PROCESSING.getStatus()) {
            return ResultUtil.failResult("子任务进行中，请等待结果");
        }

        if (lastTask.getStep() == HelloChannelInitStatusEnum.MAX_STEP
            && TaskStatusEnum.SUCCESS.getStatus() == lastTask.getStatus()) {
            return ResultUtil.failResult("初始化已完成，无需重试");
        }

        if (lastTask.getStatus() == TaskStatusEnum.FAILED.getStatus()) {
            ApiConnApply updRecord = new ApiConnApply();
            updRecord.setId(lastTask.getId());
            updRecord.setStatus(TaskStatusEnum.PROCESSING.getStatus());
            apiConnApplyMapper.updateByPrimaryKeySelective(updRecord);
            asyncPromiseExecutor.execute(() -> taskMap.get(lastTask.getStep()).apply((ThirdBatchAddVo)commitParam));
            log.info("[HELLO-INIT]重试开始, step:{}", HelloChannelInitStatusEnum.getDesc(lastTask.getStep()));
        } else if (lastTask.getStatus() == TaskStatusEnum.SUCCESS.getStatus()) {
            Optional<HelloChannelInitStatusEnum> nextTaskOpt =
                Arrays.stream(HelloChannelInitStatusEnum.values()).filter(e -> e.getIndex() > lastTask.getStep())
                    .min(Comparator.comparing(HelloChannelInitStatusEnum::getIndex));
            if (nextTaskOpt.isPresent()) {
                HelloChannelInitStatusEnum nextTask = nextTaskOpt.get();
                this.saveApiConnApply(merchantId, Constant.ChannelId.HELLO, nextTask);
                taskMap.get(nextTask.getIndex()).apply((ThirdBatchAddVo)commitParam);
                log.info("[HELLO-INIT]重试开始, step:{}", nextTask.getSceneName());
            }
        }
        return ResultUtil.successResult(1);
    }

    private ApiConnApply saveApiConnApply(Long merchantId, Long channelId, HelloChannelInitStatusEnum statusEnum) {
        ApiConnApply apiConnApply = new ApiConnApply();
        apiConnApply.setChannelId(channelId);
        apiConnApply.setMerchantId(merchantId);
        apiConnApply.setDetailMessage("");
        apiConnApply.setStep(statusEnum.getIndex());
        apiConnApply.setStatus(TaskStatusEnum.PROCESSING.getStatus());
        apiConnApply.setLastVer(1);
        apiConnApply.setCreateTime(System.currentTimeMillis());
        apiConnApply.setOpTime(System.currentTimeMillis());
        apiConnApplyMapper.insertSelective(apiConnApply);
        log.info("[HELLO-INIT]进度保存, merchantId:{}, channelId:{}, {} - {}", merchantId, channelId,
            statusEnum.getIndex(), statusEnum.getSceneName());
        return apiConnApply;
    }

    private void setFailed(Long id, String disPlayMsg, String detailMsg) {
        ApiConnApply apiConnApply = new ApiConnApply();
        apiConnApply.setId(id);
        apiConnApply.setStatus(TaskStatusEnum.FAILED.getStatus());
        apiConnApply.setDetailMessage(StringUtils.truncate(StringUtils.defaultString(detailMsg), 500));
        apiConnApply.setDisplayMessage(StringUtils.truncate(StringUtils.defaultString(disPlayMsg), 500));
        apiConnApply.setOpTime(System.currentTimeMillis());
        apiConnApplyMapper.updateByPrimaryKeySelective(apiConnApply);
    }


    public Result handleVehicleMatchData(ThirdBatchAddVo param) {
        // 车型匹配数据
        List<ThirdBatchAddVo.ThirdVehicleAddVo> vehicleModelAddVos = param.getVehicleModelAddVos();
        if (CollectionUtils.isEmpty(vehicleModelAddVos)) {
            this.setFailed(param.getMerchantId(), HelloChannelInitStatusEnum.VEHICLE_BIND.getIndex(), "无车型绑定", "未绑定车型");
            return ResultUtil.failResult("匹配车型为空");
        }
        Long merchantId = param.getMerchantId();
        Map<Boolean, List<ThirdBatchAddVo.ThirdVehicleAddVo>> vehicleMap = vehicleModelAddVos
            .stream().collect(Collectors.groupingBy(e -> StringUtils.isNotBlank(e.getThirdVehicleModelId())));
        List<ThirdBatchAddVo.ThirdVehicleAddVo> matchedData = vehicleMap.get(true);
        if (CollectionUtils.isNotEmpty(matchedData)) {
            log.info("匹配成功的车型{}", JSONObject.toJSONString(matchedData));
            // 匹配成功的车型处理
            vehicleBindService.saveForHelloInit(matchedData, merchantId);
        }
        // 结束之后回调自己，改状态
        this.setSuccess(merchantId, HelloChannelInitStatusEnum.VEHICLE_BIND.getIndex());
        return ResultUtil.successResult(1);
    }

    private void setSuccess(Long merchantId, int index) {
        TaskRecordVo taskRecordVo = new TaskRecordVo();
        taskRecordVo.setStatus(TaskStatusEnum.SUCCESS.getStatus());
        taskRecordVo.setMerchantId(merchantId);
        taskRecordVo.setIndex(index);
        taskRecordVo.setChannelId(Constant.ChannelId.HELLO);
        this.statusCallBack(taskRecordVo);
    }

    private void setFailed(Long merchantId, int index, String detailMsg, String displayMsg) {
        TaskRecordVo taskRecordVo = new TaskRecordVo();
        taskRecordVo.setStatus(TaskStatusEnum.FAILED.getStatus());
        taskRecordVo.setMerchantId(merchantId);
        taskRecordVo.setIndex(index);
        taskRecordVo.setDetailMessage(detailMsg);
        taskRecordVo.setDisplayMessage(displayMsg);
        taskRecordVo.setChannelId(Constant.ChannelId.HELLO);
        this.statusCallBack(taskRecordVo);
    }

    private Result saveHelloShop(ThirdBatchAddVo param) {
        // 保存哈啰商品
        PlatformBaseRequest<?> request = new PlatformBaseRequest<>();
        request.setMerchantId(param.getMerchantId());
        request.setChannelId(Constant.ChannelId.HELLO);
        apiClient.helloInitVehicleModel(request);
        return ResultUtil.successResult(1);
    }

    private Result handleStoreMatchData(ThirdBatchAddVo param) {
        // 门店匹配数据
        List<ThirdBatchAddVo.ThirdStoreAddVo> storeAddVos = param.getStoreAddVos();
        if (CollectionUtils.isEmpty(storeAddVos)) {
            this.setSuccess(param.getMerchantId(), HelloChannelInitStatusEnum.STORE_MAPPING.getIndex());
            return ResultUtil.failResult("门店匹配数据为空");
        }
        Long merchantId = param.getMerchantId();
        Long channelId = param.getChannelId();

        Map<Byte, List<ThirdBatchAddVo.ThirdStoreAddVo>> matchMap =
            storeAddVos.stream().collect(Collectors.groupingBy(ThirdBatchAddVo.ThirdStoreAddVo::getMatch));
        List<ThirdBatchAddVo.ThirdStoreAddVo> matchedData = matchMap.get((byte)1);
        if (CollectionUtils.isNotEmpty(matchedData)) {
            // 匹配成功的门店处理
            ThirdStoreBatchAddVo batchAddVo = new ThirdStoreBatchAddVo();
            batchAddVo.setMerchantId(merchantId);
            batchAddVo.setChannelId(channelId);
            batchAddVo.setList(matchedData.stream().map(x -> {
                ThirdStoreAddVo vo = new ThirdStoreAddVo();
                vo.setStoreId(x.getStoreId());
                vo.setThirdStoreId(x.getThirdStoreId());
                vo.setThirdStoreName(x.getThirdStoreName());
                vo.setMatch(x.getMatch());
                return vo;
            }).collect(Collectors.toList()));

            // (幂等)如果映射关系已存在，则过滤
            List<String> thirdStoreIds = matchedData.stream()
                .map(ThirdBatchAddVo.ThirdStoreAddVo::getThirdStoreId)
                .collect(Collectors.toList());
            
            Result<List<IdRelationVo>> listResult = thirdIdRelationService.listByThirdIds(
                thirdStoreIds, HELLO_CHANNEL_ID, IdRelationEnum.STORE.getType(), merchantId);
            log.info("查询已有门店映射关系:{}， thirdStoreIds:{}", JSONObject.toJSONString(listResult), JSONObject.toJSONString(thirdStoreIds));
            
            if (!listResult.isSuccess()) {
                log.error("查询门店映射关系失败：{}", listResult.getMessage());
                this.setFailed(param.getMerchantId(), HelloChannelInitStatusEnum.STORE_MAPPING.getIndex(),
                    "查询门店映射关系失败", "");
                return ResultUtil.failResult("查询门店映射关系失败");
            }
            
            List<IdRelationVo> existRelations = listResult.getModel();
            Map<String, IdRelationVo> existRelationMap = CollectionUtils.isEmpty(existRelations) ? 
                new HashMap<>() : 
                existRelations.stream()
                    .collect(Collectors.toMap(IdRelationVo::getThirdId, Function.identity(), (v1, v2) -> v1));
            
            List<ThirdBatchAddVo.ThirdStoreAddVo> filteredData = matchedData.stream()
                .filter(x -> !existRelationMap.containsKey(x.getThirdStoreId()))
                .collect(Collectors.toList());
            
            if (CollectionUtils.isNotEmpty(filteredData)) {
                //插入映射关系
                thirdIdRelationService.batchSave(filteredData.stream().map(x -> {
                    IdRelationVo idRelationVo = new IdRelationVo();
                    idRelationVo.setMerchantId(merchantId);
                    idRelationVo.setSaasId(x.getStoreId());
                    idRelationVo.setStoreId(x.getStoreId());
                    idRelationVo.setType((IdRelationEnum.STORE.getType()));
                    idRelationVo.setChannelId(HELLO_CHANNEL_ID);
                    idRelationVo.setThirdId(x.getThirdStoreId());
                    return idRelationVo;
                }).collect(Collectors.toList()));
            } else {
                log.info("所有匹配的门店映射关系已存在");
            }
        }
        // 匹配未成功的门店处理
        List<ThirdBatchAddVo.ThirdStoreAddVo> unMatchedData = matchMap.get((byte)0);
        if (unMatchedData == null) {
            unMatchedData = new ArrayList<>();
        }
        log.info("匹配未成功的门店:{}", JSONObject.toJSONString(unMatchedData));

        Map<Byte, List<ThirdBatchAddVo.ThirdStoreAddVo>> storeMap =
            unMatchedData.stream().filter(x -> Objects.isNull(x.getStoreId())
                    && Objects.nonNull(x.getThirdStoreId()))
                .collect(Collectors.groupingBy(ThirdBatchAddVo.ThirdStoreAddVo::getCreated));

        StoreInitReq storeInitReq = new StoreInitReq();
        storeInitReq.setMerchantId(merchantId);
        storeInitReq.setPullStoreIds(Optional.ofNullable(storeMap.get((byte)1))
            .map(x -> x.stream().map(ThirdBatchAddVo.ThirdStoreAddVo::getThirdStoreId).collect(
                Collectors.toList())).orElse(null));
        storeInitReq.setOfflineStoreIds(Optional.ofNullable(storeMap.get((byte)0))
            .map(x -> x.stream().map(ThirdBatchAddVo.ThirdStoreAddVo::getThirdStoreId).collect(
                Collectors.toList())).orElse(null));
        log.info("哈啰门店处理开始,merchantId:{},req:{}", merchantId, storeInitReq);
        // 调用外部
        asyncPromiseExecutor.execute(() -> platformBiz.initStoreData(merchantId, channelId, storeInitReq));
        return ResultUtil.successResult(1);
    }

    private Result initStoreCircleData(ThirdBatchAddVo param) {
        log.info("[HELLO-INIT]服务圈初始化 {}", param);
        Map<Byte, List<ThirdBatchAddVo.ThirdStoreAddVo>> matchMap =
            param.getStoreAddVos().stream().collect(Collectors.groupingBy(ThirdBatchAddVo.ThirdStoreAddVo::getMatch));
        List<ThirdBatchAddVo.ThirdStoreAddVo> unMatchedData = matchMap.get((byte)0);
        ServiceCircleInitReq req = new ServiceCircleInitReq();
        if (CollectionUtils.isNotEmpty(unMatchedData)) {
            Map<Byte, List<ThirdBatchAddVo.ThirdStoreAddVo>> storeMap =
                    unMatchedData.stream().filter(x -> Objects.isNull(x.getStoreId()) && Objects.nonNull(x.getThirdStoreId()))
                            .collect(Collectors.groupingBy(ThirdBatchAddVo.ThirdStoreAddVo::getCreated));
            List<String> thirdStoreId = Optional.ofNullable(storeMap.get((byte) 1))
                    .map(x -> x.stream().map(ThirdBatchAddVo.ThirdStoreAddVo::getThirdStoreId).collect(
                            Collectors.toList())).orElse(null);
            req.setStoreIds(thirdStoreId);
        }

        asyncPromiseExecutor.execute(() -> platformBiz.initServiceCircle(param.getMerchantId(), param.getChannelId(), req));
        return ResultUtil.successResult(1);
    }

    private Result initVehicleData(ThirdBatchAddVo param) {
        VehicleInitReq req = new VehicleInitReq();
        req.setChannelId(param.getChannelId());
        req.setMerchantId(param.getMerchantId());
        asyncPromiseExecutor.execute(() -> platformBiz.initVehicleData(param.getMerchantId(), param.getChannelId(), req));
        return ResultUtil.successResult(1);
    }

    private Result initPriceData(ThirdBatchAddVo param) {
        VehicleInitReq req = new VehicleInitReq();
        req.setChannelId(param.getChannelId());
        req.setMerchantId(param.getMerchantId());
        asyncPromiseExecutor.execute(() -> platformBiz.initHelloPrice(param.getMerchantId(), param.getChannelId(), req));
        return ResultUtil.successResult(1);
    }

    private Result initStockData(ThirdBatchAddVo param) {
        VehicleInitReq req = new VehicleInitReq();
        req.setChannelId(param.getChannelId());
        req.setMerchantId(param.getMerchantId());
        asyncPromiseExecutor.execute(() -> platformBiz.initStock(param.getMerchantId(), param.getChannelId(), req));
        return ResultUtil.successResult(1);
    }

    private Result initOrderData(ThirdBatchAddVo param) {VehicleInitReq req = new VehicleInitReq();
        req.setChannelId(param.getChannelId());
        req.setMerchantId(param.getMerchantId());
        asyncPromiseExecutor.execute(() -> orderSyncService.initOrder(param.getMerchantId(), param.getChannelId()));
        return ResultUtil.successResult(1);
    }


    private String getRedisParamKey(Long merchantId) {
        return String.format("gds_init_param_%s_%s", merchantId, Constant.ChannelId.HELLO);
    }

}
