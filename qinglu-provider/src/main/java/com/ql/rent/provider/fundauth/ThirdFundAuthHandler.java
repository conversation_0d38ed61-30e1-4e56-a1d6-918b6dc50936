package com.ql.rent.provider.fundauth;

import com.ql.dto.fundauth.*;
import com.ql.rent.param.fundauth.*;
import org.jetbrains.annotations.NotNull;

/**
 * 三方预授权支付处理类
 */
public interface ThirdFundAuthHandler {
    /**
     * 线上资金授权冻结
     *
     * @param param
     * @return
     */
    @NotNull
    FundAuthFreezeResult fundAuthFreeze(FundAuthFreezeParam param);

    /**
     * 资金授权解冻
     * @param param
     * @return
     */
    @NotNull
    FundAuthUnfreezeResult fundAuthUnfreeze(FundAuthUnfreezeParam param);

    /**
     * 押金扣款
     * @param param
     * @return
     */
    @NotNull
    TradePayForFreezeResult tradePayForFreeze(TradePayForFreezeParam param);

    /**
     * 押金退款
     * @param param
     * @return
     */
    @NotNull
    RefundForFreezeResult refundForFreeze(RefundForFreezeParam param);

    /**
     * 关闭押金交易订单
     * @param param
     * @return
     */
    @NotNull
    TradeCloseForFreezeResult tradeCloseForFreeze(TradeCloseForFreezeParam param);


    /**
     * 返回对应的三方支付平台类型
     */
    @NotNull
    Byte thirdFundAuthSource();
}
