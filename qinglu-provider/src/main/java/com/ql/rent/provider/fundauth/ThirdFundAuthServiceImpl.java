package com.ql.rent.provider.fundauth;

import com.alibaba.fastjson.JSON;
import com.ql.dto.fundauth.*;
import com.ql.enums.ThirdFundAuthEnum;
import com.ql.enums.ThirdPayEnum;
import com.ql.rent.common.IRedisService;
import com.ql.rent.dao.bill.AuthPayMapper;
import com.ql.rent.dao.bill.AuthRefundPayMapper;
import com.ql.rent.dao.bill.FundAuthMapper;
import com.ql.rent.dao.bill.FundAuthOperationMapper;
import com.ql.rent.dao.trade.*;
import com.ql.rent.entity.trade.*;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.common.ResultEnum;
import com.ql.rent.multipledatasource.TransactionManagerName;
import com.ql.rent.param.fundauth.*;
import com.ql.rent.service.fundauth.IThirdFundAuthService;
import com.ql.rent.service.trade.IOrderService;
import com.ql.rent.service.trade.IThirdOrderService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.util.AmountUtil;
import com.ql.rent.vo.trade.OrderInfoVo;
import com.ql.rent.vo.trade.third.ThirdFreeDepositTradeNotifyDTO;
import com.ql.rent.vo.trade.third.ThirdFreezeNotifyDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class ThirdFundAuthServiceImpl implements IThirdFundAuthService, ApplicationContextAware {
    @Resource
    private FundAuthMapper fundAuthMapper;
    @Resource
    private FundAuthOperationMapper fundAuthOperationMapper;
    @Resource
    private IRedisService redisService;
    @Resource
    private AuthPayMapper authPayMapper;
    @Resource
    private AuthRefundPayMapper authRefundPayMapper;
    @Resource
    private IThirdOrderService iThirdOrderService;
    @Resource
    private IOrderService orderService;
    @Resource
    private PayMapper payMapper;

    private static Map<Byte, ThirdFundAuthHandler> thirdMap = Collections.emptyMap();

    @Override
    public Result<FundAuthFreezeResult> fundAuthFreeze(FundAuthFreezeParam param) {
        if (ObjectUtils.anyNull(param, param.getOrderNo(), param.getRequestNo(), param.getOrderTitle(), param.getAmount())) {
            return ResultUtil.failResult(ResultEnum.e001);
        }
        log.info("线上资金授权冻结 param={}", JSON.toJSONString(param));

        // 防重
        String redisLockKey = String.format("fund_auth_freeze_lock:%s",
                param.getOrderId());
        if (redisService.setnx(redisLockKey, 2L) > 1) {
            return ResultUtil.failResult(ResultEnum.e008);
        }

        Result<OrderInfoVo> orderInfoVoResult = orderService.getOrderInfo(param.getOrderId());
        if (ResultUtil.isModelNull(orderInfoVoResult)) {
            return ResultUtil.failResult("订单不存在");
        }
        OrderInfoVo orderInfoVo = orderInfoVoResult.getModel();
        param.setMerchantId(orderInfoVo.getMerchantId());

        FundAuth fundAuthed = this.getFundAuthed(param.getOrderId());
        if (fundAuthed != null) {
            return ResultUtil.failResult("订单已冻结押金");
        }

        // 冻结金额
        Long amount = param.getAmount();
        Long timeStamp = System.currentTimeMillis();
        if (amount <= 0) {
            return ResultUtil.failResult(ResultEnum.e002, "冻结金额需要大于0");
        }
        // 数据维护
        FundAuth fundAuth = new FundAuth();
        fundAuth.setThirdSource(param.getThirdFundAuthSource());
        fundAuth.setTotalFreezeAmount(amount);
        fundAuth.setDeleted(YesOrNoEnum.NO.getValue());
        fundAuth.setCreateTime(timeStamp);
        fundAuth.setOpTime(timeStamp);
        fundAuth.setOpUserId(param.getOpUserId());
        fundAuth.setOrderId(param.getOrderId());
        fundAuth.setTimeoutExpress(param.getTimeoutExpress());
        fundAuth.setStatus(ThirdFundAuthEnum.FundAuthStatus.AUTHORIZING.getStatus());
        fundAuth.setMerchantId(orderInfoVo.getMerchantId());
        fundAuth.setMerchantPay(param.getMerchantPay());
        fundAuth.setOrderNo(param.getOrderNo());
        fundAuthMapper.insertSelective(fundAuth);

        FundAuthOperation fundAuthOperation = new FundAuthOperation();
        fundAuthOperation.setFundAuthId(fundAuth.getId());
        fundAuthOperation.setRequestNo(param.getRequestNo());
        fundAuthOperation.setAmount(amount);
        fundAuthOperation.setOperationType(ThirdFundAuthEnum.OperationType.FREEZE.getType());
        fundAuthOperation.setStatus(ThirdFundAuthEnum.OperationStatus.INIT.getStatus());
        fundAuthOperation.setCreateTime(timeStamp);
        fundAuthOperation.setOpTime(timeStamp);
        fundAuthOperation.setOpUserId(param.getOpUserId());

        fundAuthOperationMapper.insertSelective(fundAuthOperation);

        // 获取对应的资金授权处理对象
        ThirdFundAuthHandler thirdFundAuthHandler = this.getThirdFundAuthHandler(param.getThirdFundAuthSource());
        FundAuthFreezeResult result = thirdFundAuthHandler.fundAuthFreeze(param);
        if (!result.isSuccess()) {
            fundAuthOperation.setStatus(ThirdFundAuthEnum.OperationStatus.FAILED.getStatus());
            fundAuthOperation.setSourceFailedReason(result.getSourceFailedReason());
            fundAuthOperationMapper.updateByPrimaryKeySelective(fundAuthOperation);

            fundAuth.setStatus(ThirdFundAuthEnum.FundAuthStatus.FAILED.getStatus());
            fundAuth.setSourceFailedReason(result.getSourceFailedReason());
            fundAuthMapper.updateByPrimaryKeySelective(fundAuth);
        }

        return ResultUtil.successResult(result);
    }

    @Override
    public Result<FundAuthUnfreezeResult> fundAuthUnfreeze(FundAuthUnfreezeParam param) {
        if (ObjectUtils.anyNull(param, param.getThirdFundAuthSource(), param.getRequestNo(), param.getOrderId(),
                param.getAmount(), param.getRemark())) {
            return ResultUtil.failResult(ResultEnum.e001);
        }
        log.info("资金授权解冻 {}", JSON.toJSONString(param));

        // 防重
        String redisLockKey = String.format("fund_auth_unfreeze_lock:%s_%s",
                param.getOrderId(), param.getRequestNo());
        if (redisService.setnx(redisLockKey, 2L) > 1) {
            return ResultUtil.failResult(ResultEnum.e008);
        }

        Result<OrderInfoVo> orderInfoVoResult = orderService.getOrderInfo(param.getOrderId());
        if (ResultUtil.isModelNull(orderInfoVoResult)) {
            return ResultUtil.failResult("订单不存在");
        }
        OrderInfoVo orderInfoVo = orderInfoVoResult.getModel();
        param.setMerchantId(orderInfoVo.getMerchantId());

        Long amount = param.getAmount();
        if (amount <= 0) {
            return ResultUtil.failResult(ResultEnum.e002, "解冻金额需要大于0");
        }

        FundAuth fundAuth = this.getFundAuthed(param.getOrderId());
        if (fundAuth == null) {
            throw new BizException("订单未冻结预授权支付单");
        }
        param.setThirdAuthNo(fundAuth.getThirdAuthNo());
        if (fundAuth.getRestAmount() < amount) {
            return ResultUtil.failResult("解冻金额不能大于剩余冻结金额");
        }
        param.setMerchantPay(fundAuth.getMerchantPay());

        Long timeStamp = System.currentTimeMillis();
        FundAuthOperation fundAuthOperation = new FundAuthOperation();
        fundAuthOperation.setFundAuthId(fundAuth.getId());
        fundAuthOperation.setRemark(param.getRemark());
        fundAuthOperation.setRequestNo(param.getRequestNo());
        fundAuthOperation.setAmount(amount);
        fundAuthOperation.setOperationType(ThirdFundAuthEnum.OperationType.UNFREEZE.getType());
        fundAuthOperation.setStatus(ThirdFundAuthEnum.OperationStatus.INIT.getStatus());
        fundAuthOperation.setCreateTime(timeStamp);
        fundAuthOperation.setOpTime(timeStamp);
        fundAuthOperation.setOpUserId(param.getOpUserId());
        fundAuthOperationMapper.insertSelective(fundAuthOperation);

        // 获取对应的资金授权处理对象
        ThirdFundAuthHandler thirdFundAuthHandler = this.getThirdFundAuthHandler(param.getThirdFundAuthSource());
        FundAuthUnfreezeResult result = thirdFundAuthHandler.fundAuthUnfreeze(param);

        Long actualAmount = result.getAmount();
        timeStamp = System.currentTimeMillis();
        if (!result.isSuccess()) {
            fundAuthOperation.setStatus(ThirdFundAuthEnum.OperationStatus.FAILED.getStatus());
            fundAuthOperation.setSourceFailedReason(result.getSourceFailedReason());
        } else {
            fundAuthOperation.setStatus(ThirdFundAuthEnum.OperationStatus.SUCCESS.getStatus());

            fundAuth.setRestAmount(fundAuth.getRestAmount() - actualAmount);
            fundAuth.setTotalUnfreezeAmount(fundAuth.getTotalUnfreezeAmount() + actualAmount);
            fundAuth.setOpTime(timeStamp);
            fundAuthMapper.updateByPrimaryKeySelective(fundAuth);
        }

        fundAuthOperation.setThirdOperationId(result.getOperationId());
        fundAuthOperation.setOpTime(timeStamp);
        fundAuthOperation.setActualAmount(actualAmount);
        fundAuthOperationMapper.updateByPrimaryKeySelective(fundAuthOperation);

        return ResultUtil.successResult(result);
    }

    private FundAuth getFundAuthed(Long orderId) {
        FundAuthExample example = new FundAuthExample();
        example.createCriteria().andOrderIdEqualTo(orderId)
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andStatusEqualTo(ThirdFundAuthEnum.FundAuthStatus.AUTHORIZED.getStatus());
        List<FundAuth> fundAuthList = fundAuthMapper.selectByExample(example);
        if (ObjectUtils.isEmpty(fundAuthList)) {
            return null;
        }
        FundAuth fundAuth = fundAuthList.get(0);
        return fundAuth;
    }

    @Override
    public Result<TradePayForFreezeResult> tradePayForFreeze(TradePayForFreezeParam param) {
        if (ObjectUtils.anyNull(param, param.getTotalAmount(),
                param.getSubject())) {
            return ResultUtil.failResult(ResultEnum.e001);
        }

        log.info("冻结资金扣款 {}", JSON.toJSONString(param));

        // 防重
        String redisLockKey = String.format("trade_pay_freeze:%s",
                param.getPayNo());
        if (redisService.setnx(redisLockKey, 2L) > 1) {
            return ResultUtil.failResult(ResultEnum.e008);
        }

        FundAuth fundAuth = this.getFundAuthed(param.getOrderId());
        if (fundAuth == null) {
            throw new BizException("订单未冻结预授权支付单");
        }
        param.setAuthNo(fundAuth.getThirdAuthNo());
        param.setMerchantPay(fundAuth.getMerchantPay());
        param.setMerchantId(fundAuth.getMerchantId());

        Long timeStamp = System.currentTimeMillis();
        AuthPay authPay = new AuthPay();
        authPay.setOrderId(param.getOrderId());
        authPay.setFundAuthId(fundAuth.getId());
        authPay.setAmount(param.getTotalAmount());
        authPay.setPayNo(param.getPayNo());
        authPay.setPayStatus(ThirdFundAuthEnum.ThirdPayStatus.UNPAID.getStatus());
        authPay.setThirdSource(param.getThirdFundAuthSource());
        authPay.setCreateTime(timeStamp);
        authPay.setOpTime(timeStamp);
        authPay.setOpUserId(param.getOpUserId());
        authPay.setMerchantPay(fundAuth.getMerchantPay());
        authPay.setMerchantId(fundAuth.getMerchantId());

        Result<Integer> checkResult = this.checkTradePayParam(param, fundAuth);
        if (!checkResult.isSuccess()) {
            authPay.setSourceFailedReason(checkResult.getMessage());
            authPay.setPayStatus(ThirdFundAuthEnum.ThirdPayStatus.FAIL_PAID.getStatus());
            authPayMapper.insertSelective(authPay);
            return ResultUtil.failResult(checkResult.getMessage());
        } else {
            authPayMapper.insertSelective(authPay);
        }

        // 获取对应的资金授权处理对象
        ThirdFundAuthHandler thirdFundAuthHandler = this.getThirdFundAuthHandler(param.getThirdFundAuthSource());
        TradePayForFreezeResult result = thirdFundAuthHandler.tradePayForFreeze(param);
        AuthPay updData = new AuthPay();
        updData.setId(authPay.getId());
        if (!result.isSuccess()) {
            updData.setPayStatus(ThirdFundAuthEnum.ThirdPayStatus.FAIL_PAID.getStatus());
            updData.setSourceFailedReason(result.getSourceFailedReason());
        }

        updData.setThirdSouceNo(result.getThirdTradeNO());
        authPayMapper.updateByPrimaryKeySelective(updData);

        return ResultUtil.successResult(result);
    }

    private Result<Integer> checkTradePayParam(TradePayForFreezeParam param, FundAuth fundAuth) {
        Long amount = param.getTotalAmount();
        if (amount <= 0) {
            return ResultUtil.failResult(ResultEnum.e002, "扣款金额需要大于0");
        }
        if (fundAuth.getRestAmount() < amount) {
            return ResultUtil.failResult("支付金额不能大于剩余冻结金额");
        }

        return ResultUtil.successResult(1);
    }

    @Override
    public Result<RefundForFreezeResult> refundForFreeze(RefundForFreezeParam param) {
        if (param == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }
        AuthPay authPay = authPayMapper.selectByPrimaryKey(param.getAuthPayId());
        if (authPay == null) {
            return ResultUtil.failResult(ResultEnum.e003);
        }
        log.info("预授权支付转退款 param={}, authPay={}", JSON.toJSONString(param), JSON.toJSONString(authPay));

        param.setMerchantPay(authPay.getMerchantPay());
        param.setMerchantId(authPay.getMerchantId());

        // 获取对应的支付处理对象
        ThirdFundAuthHandler thirdPayHandler = this.getThirdFundAuthHandler(param.getThirdFundAuthSource());

        AuthRefundPay authRefundPay = new AuthRefundPay();
        authRefundPay.setAuthPayId(authPay.getId());
        authRefundPay.setRefundNo(param.getRefundNo());
        authRefundPay.setAmount(param.getAmount());
        authRefundPay.setThirdSource(param.getThirdFundAuthSource());
        authRefundPay.setOpUserId(ObjectUtils.defaultIfNull(param.getOpUserId(), 0L));
        long timeMillis = System.currentTimeMillis();
        authRefundPay.setOpTime(timeMillis);
        authRefundPay.setCreateTime(timeMillis);
        authRefundPay.setPayOpTime(0L);
        authRefundPay.setLastVer(1);
        authRefundPay.setStatus(ThirdPayEnum.ThirdRefundPayStatus.CANCEL.getStatus());
        authRefundPayMapper.insertSelective(authRefundPay);

        param.setThirdSouceNo(authPay.getThirdSouceNo());
        RefundForFreezeResult refundResult = thirdPayHandler.refundForFreeze(param);

        if (!refundResult.isSuccess()) {
            authRefundPay.setSourceFailedReason(StringUtils.defaultString(refundResult.getSourceFailedReason()));
        } else {
            timeMillis = System.currentTimeMillis();
            authRefundPay.setPayTime(timeMillis);
        }

        authRefundPay.setActualAmount(refundResult.getActualAmount());
        authRefundPay.setStatus(refundResult.isSuccess() ?
                ThirdPayEnum.ThirdRefundPayStatus.SUCCESS.getStatus() : ThirdPayEnum.ThirdRefundPayStatus.FAIL.getStatus());
        authRefundPayMapper.updateByPrimaryKeySelective(authRefundPay);
        if (!refundResult.isSuccess()) {
            return ResultUtil.failResult(refundResult.getSourceFailedReason());
        } else {
            return ResultUtil.successResult(refundResult);
        }
    }

    @Override
    public Result<TradeCloseForFreezeResult> tradeCloseForFreeze(TradeCloseForFreezeParam param) {
        if (param == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }
        // 防重
        String redisLockKey = String.format("trade_close_for_freeze:%s",
                param.getPayNo());
        if (redisService.setnx(redisLockKey, 2L) > 1) {
            return ResultUtil.failResult(ResultEnum.e008);
        }
        AuthPayExample authPayExample = new AuthPayExample();
        authPayExample.createCriteria().andPayNoEqualTo(param.getPayNo()).andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                        .andMerchantIdEqualTo(param.getMerchantId());
        List<AuthPay> authPayList = authPayMapper.selectByExample(authPayExample);
        if (CollectionUtils.isEmpty(authPayList)) {
            return ResultUtil.failResult(ResultEnum.e004);
        }
        AuthPay authPay = authPayList.get(0);
        if (authPay.getPayStatus().equals(ThirdFundAuthEnum.ThirdPayStatus.CLOSED.getStatus())) {
            return ResultUtil.successResult(TradeCloseForFreezeResult.builder().success(true).build());
        } else if (authPay.getPayStatus().equals(ThirdFundAuthEnum.ThirdPayStatus.ALL_PAID.getStatus())) {
            return ResultUtil.failResult("已成功扣款,请走退款流程");
        }
        param.setMerchantPay(authPay.getMerchantPay());
        // 获取对应的支付处理对象
        ThirdFundAuthHandler thirdPayHandler = this.getThirdFundAuthHandler(param.getThirdFundAuthSource());
        TradeCloseForFreezeResult tradeCloseForFreezeResult = thirdPayHandler.tradeCloseForFreeze(param);
        if (!tradeCloseForFreezeResult.isSuccess()) {
            return ResultUtil.successResult(TradeCloseForFreezeResult.builder().success(false).sourceFailedReason(tradeCloseForFreezeResult.getSourceFailedReason()).build());
        }
        AuthPay updAuthPay = new AuthPay();
        updAuthPay.setId(authPay.getId());
        updAuthPay.setLastVer(authPay.getLastVer() + 1);
        updAuthPay.setOpTime(System.currentTimeMillis());
        updAuthPay.setOpUserId(param.getOpUserId());
        updAuthPay.setPayStatus(ThirdFundAuthEnum.ThirdPayStatus.CLOSED.getStatus());
        authPayMapper.updateByPrimaryKeySelective(updAuthPay);
        return ResultUtil.successResult(TradeCloseForFreezeResult.builder().success(true).build());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, transactionManager = TransactionManagerName.TRADE)
    public Result<Boolean> fundAuthNotify(FundAuthNotifyParam param) {
        log.info("收到第三方资金授权通知, param:{}", JSON.toJSONString(param));
        // 查资金授权信息
        FundAuthExample fundAuthExample = new FundAuthExample();
        fundAuthExample.createCriteria().andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andThirdSourceEqualTo(param.getThirdFundAuthSource()).andOrderNoEqualTo(param.getOrderNo());
        List<FundAuth> fundAuthList = fundAuthMapper.selectByExample(fundAuthExample);
        if (CollectionUtils.isEmpty(fundAuthList)) {
            throw new BizException(ResultEnum.e004);
        }
        FundAuth fundAuth = fundAuthList.get(0);
        fundAuth.setTotalFreezeAmount(param.getTotalFreezeAmount());
        fundAuth.setTotalUnfreezeAmount(param.getTotalUnfreezeAmount());
        fundAuth.setRestAmount(param.getRestAmount());
        fundAuth.setThirdAuthNo(param.getThirdAuthNo());

        // 查资金授权操作信息
        FundAuthOperationExample fundAuthOperationExample = new FundAuthOperationExample();
        FundAuthOperationExample.Criteria criteria = fundAuthOperationExample.createCriteria();
        criteria.andRequestNoEqualTo(param.getRequestNo())
                .andStatusEqualTo(ThirdFundAuthEnum.OperationStatus.INIT.getStatus());

        if (param.getOperationType().equals(ThirdFundAuthEnum.OperationType.FREEZE.getType())) {
            // 冻结
            fundAuth.setStatus(ThirdFundAuthEnum.FundAuthStatus.AUTHORIZED.getStatus());
            fundAuth.setCreditStatus(param.getCreditStatus());
            criteria.andOperationTypeEqualTo(ThirdFundAuthEnum.OperationType.FREEZE.getType());
        } else if (param.getOperationType().equals(ThirdFundAuthEnum.OperationType.UNFREEZE.getType())) {
            // 解冻
            criteria.andOperationTypeEqualTo(ThirdFundAuthEnum.OperationType.UNFREEZE.getType());
        }

        List<FundAuthOperation> operationList = fundAuthOperationMapper.selectByExample(fundAuthOperationExample);
        if (CollectionUtils.isEmpty(operationList)) {
            throw new BizException(ResultEnum.e004);
        }
        FundAuthOperation fundAuthOperation = operationList.get(0);
        // 幂等
        if (!fundAuthOperation.getStatus().equals(ThirdFundAuthEnum.OperationStatus.INIT.getStatus())) {
            return ResultUtil.successResult(true);
        }
        // 实际操作金额
        fundAuthOperation.setActualAmount(param.getAmount());
        fundAuthOperation.setStatus(ThirdFundAuthEnum.OperationStatus.SUCCESS.getStatus());
        fundAuthOperation.setThirdOperationId(param.getThirdOperationId());
        // 更新资金授权信息
        fundAuthMapper.updateByPrimaryKeySelective(fundAuth);
        // 更新操作信息
        fundAuthOperationMapper.updateByPrimaryKeySelective(fundAuthOperation);

        // 原逻辑后补免押通知
        Result<OrderInfoVo> orderResult = orderService.getOrderInfo(fundAuth.getOrderId());
        if (ResultUtil.isModelNull(orderResult)) {
            throw new BizException("订单不存在");
        }
        ThirdFreezeNotifyDTO thirdFreezeNotifyDTO = new ThirdFreezeNotifyDTO();
        thirdFreezeNotifyDTO.setOrderId(fundAuth.getOrderId());
        thirdFreezeNotifyDTO.setAmount(AmountUtil.amount2BigDecimal(fundAuth.getTotalFreezeAmount()).doubleValue());
        thirdFreezeNotifyDTO.setMerchantId(orderResult.getModel().getMerchantId());
        thirdFreezeNotifyDTO.setAuthNo(fundAuth.getThirdAuthNo());
        thirdFreezeNotifyDTO.setDepositAmount(fundAuth.getTotalFreezeAmount().intValue());
        thirdFreezeNotifyDTO.setFreeDepositWay(3);
        Result<Boolean> result = iThirdOrderService.freezeNotify(thirdFreezeNotifyDTO);
        if (!result.isSuccess()) {
            throw new BizException(result.getMessage());
        }
        return ResultUtil.successResult(true);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, transactionManager = TransactionManagerName.TRADE)
    public Result<Boolean> payNotifyForFreeze(PayNotifyForFreezeParam param) {
        if (param == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }
        log.info("收到第三方冻结资金扣款回调, param:{}", JSON.toJSONString(param));
        AuthPayExample authPayExample = new AuthPayExample();
        authPayExample.createCriteria().andPayNoEqualTo(param.getPayNo()).andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                        .andThirdSourceEqualTo(param.getThirdPaySource());
        List<AuthPay> authPayList = authPayMapper.selectByExample(authPayExample);
        if (CollectionUtils.isEmpty(authPayList)) {
            throw new BizException(ResultEnum.e004);
        }
        AuthPay authPay = authPayList.get(0);

        // 更新剩余冻结金额
        FundAuth fundAuth = fundAuthMapper.selectByPrimaryKey(authPay.getFundAuthId());
        if (fundAuth == null) {
            throw new BizException(ResultEnum.e004);
        }
        fundAuth.setRestAmount(fundAuth.getRestAmount() - param.getTotalAmount());
        fundAuth.setTotalPayAmount(fundAuth.getTotalPayAmount() + param.getTotalAmount());

        fundAuth.setOpTime(System.currentTimeMillis());
        fundAuthMapper.updateByPrimaryKeySelective(fundAuth);

        // 更新支付流水
        authPay.setPayStatus(param.getPayStatus());
        authPay.setThirdSouceNo(param.getThirdSouceNo());
        authPay.setActualAmount(param.getTotalAmount());
        authPay.setPayTime(param.getPayTime());
        authPay.setPayOpTime(param.getPayOpTime());
        authPay.setOpTime(System.currentTimeMillis());
        authPayMapper.updateByPrimaryKeySelective(authPay);

        // 调用原业务通知接口
        Result<OrderInfoVo> orderResult = orderService.getOrderInfo(authPay.getOrderId());
        if (ResultUtil.isModelNull(orderResult)) {
            throw new BizException("订单不存在");
        }
        PayExample payExample = new PayExample();
        payExample.createCriteria().andPaySourceIdEqualTo(authPay.getPayNo());
        List<Pay> pays = payMapper.selectByExample(payExample);
        if (CollectionUtils.isEmpty(pays)) {
            throw new BizException("pay不存在");
        }
        ThirdFreeDepositTradeNotifyDTO thirdFreeDepositTradeNotifyDTO = new ThirdFreeDepositTradeNotifyDTO();
        thirdFreeDepositTradeNotifyDTO.setAmount(BigDecimal.valueOf(authPay.getActualAmount()));
        thirdFreeDepositTradeNotifyDTO.setOrderId(authPay.getOrderId());
        thirdFreeDepositTradeNotifyDTO.setMerchantId(orderResult.getModel().getMerchantId());
        thirdFreeDepositTradeNotifyDTO.setTradeNo(String.valueOf(pays.get(0).getId()));
        thirdFreeDepositTradeNotifyDTO.setThirdTradeNo(authPay.getThirdSouceNo());
        thirdFreeDepositTradeNotifyDTO.setTradeType(0);
        thirdFreeDepositTradeNotifyDTO.setResult(true);
        Result<Boolean> result = iThirdOrderService.freeDepositTradeNotify(thirdFreeDepositTradeNotifyDTO);
        if (!result.isSuccess()) {
            throw new BizException(result.getMessage());
        }
        return ResultUtil.successResult(true);
    }

    private ThirdFundAuthHandler getThirdFundAuthHandler(Byte thirdPaySource) {
        ThirdFundAuthHandler thirdFundAuthHandler = thirdMap.get(thirdPaySource);
        if (thirdFundAuthHandler == null) {
            throw new BizException("暂不支持的支持方式");
        }
        return thirdFundAuthHandler;
    }

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        Map<Byte, ThirdFundAuthHandler> tempMap = new HashMap<>();
        ObjectProvider<ThirdFundAuthHandler> provider = applicationContext.getBeanProvider(ThirdFundAuthHandler.class);
        provider.forEach(e -> tempMap.put(e.thirdFundAuthSource(), e));
        thirdMap = Collections.unmodifiableMap(tempMap);
    }
}
