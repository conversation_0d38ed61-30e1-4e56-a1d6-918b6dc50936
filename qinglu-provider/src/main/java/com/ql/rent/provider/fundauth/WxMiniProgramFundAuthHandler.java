package com.ql.rent.provider.fundauth;

import com.qinglusaas.logutil.QingluLogger;
import com.qinglusaas.logutil.QingluLoggerFactory;
import com.ql.dto.ApiResultResp;
import com.ql.rent.api.aggregate.remote.platform.api.PlatformApiClient;
import com.ql.rent.api.aggregate.remote.platform.vo.request.*;
import com.ql.rent.api.aggregate.remote.platform.vo.response.*;
import com.ql.rent.converter.fundauth.WxFundAuthConverter;
import org.springframework.stereotype.Component;

import com.ql.dto.fundauth.FundAuthFreezeResult;
import com.ql.dto.fundauth.FundAuthUnfreezeResult;
import com.ql.dto.fundauth.RefundForFreezeResult;
import com.ql.dto.fundauth.TradeCloseForFreezeResult;
import com.ql.dto.fundauth.TradePayForFreezeResult;
import com.ql.enums.ThirdFundAuthEnum;
import com.ql.rent.param.fundauth.FundAuthFreezeParam;
import com.ql.rent.param.fundauth.FundAuthUnfreezeParam;
import com.ql.rent.param.fundauth.RefundForFreezeParam;
import com.ql.rent.param.fundauth.TradeCloseForFreezeParam;
import com.ql.rent.param.fundauth.TradePayForFreezeParam;

import javax.annotation.Resource;

@Component
public class WxMiniProgramFundAuthHandler implements ThirdFundAuthHandler {

    private final QingluLogger logger = QingluLoggerFactory.getLogger(WxMiniProgramFundAuthHandler.class);
    
    @Resource
    private PlatformApiClient platformApiClient;

    @Override
    public FundAuthFreezeResult fundAuthFreeze(FundAuthFreezeParam param) {
        logger.startLog().with("微信小程序资金授权冻结开始", param).log("WX_FUNDAUTH_FREEZE_START");
        
        try {
            // 构建平台接口请求参数
            ThirdFundAuthFreezeReq request = WxFundAuthConverter.INSTANCE.toFreezeRequest(param);
            
            // 调用平台接口
            ApiResultResp<ThirdFundAuthFreezeResp> response = platformApiClient.thirdFundAuthFreeze(request);
            
            logger.startLog().with("调用platform微信资金授权冻结响应", response).logAudit("WX_FUNDAUTH_FREEZE_RESPONSE");
            
            // 转换响应结果
            return convertToFreezeResult(response);
            
        } catch (Exception e) {
            logger.startLog().with("异常信息", e.getMessage()).logError("微信小程序资金授权冻结异常");
            return FundAuthFreezeResult.builder().success(false).sourceFailedReason("微信资金授权冻结失败：" + e.getMessage()).build();
        }
    }

    @Override
    public FundAuthUnfreezeResult fundAuthUnfreeze(FundAuthUnfreezeParam param) {
        logger.startLog().with("微信小程序资金授权解冻开始", param).log("WX_FUNDAUTH_UNFREEZE_START");
        
        try {
            // 构建平台接口请求参数
            ThirdFundAuthUnfreezeReq request = WxFundAuthConverter.INSTANCE.toUnfreezeRequest(param);
            
            // 调用平台接口
            ApiResultResp<ThirdFundAuthUnfreezeResp> response = platformApiClient.thirdFundAuthUnfreeze(request);
            
            logger.startLog().with("调用platform微信资金授权解冻响应", response).logAudit("WX_FUNDAUTH_UNFREEZE_RESPONSE");
            
            // 转换响应结果
            return convertToUnfreezeResult(response);
            
        } catch (Exception e) {
            logger.startLog().with("异常信息", e.getMessage()).logError("微信小程序资金授权解冻异常");
            return FundAuthUnfreezeResult.builder().success(false).sourceFailedReason("微信资金授权解冻失败：" + e.getMessage()).build();
        }
    }

    @Override
    public TradePayForFreezeResult tradePayForFreeze(TradePayForFreezeParam param) {
        logger.startLog().with("微信小程序预授权支付开始", param).log("WX_FUNDAUTH_PAY_START");
        
        try {
            // 构建平台接口请求参数
            ThirdFundAuthPayReq request = WxFundAuthConverter.INSTANCE.toPayRequest(param);
            
            // 调用平台接口
            ApiResultResp<ThirdFundAuthPayResp> response = platformApiClient.thirdFundAuthPay(request);
            
            logger.startLog().with("调用platform微信预授权支付响应", response).logAudit("WX_FUNDAUTH_PAY_RESPONSE");
            
            // 转换响应结果
            return convertToPayResult(response);
            
        } catch (Exception e) {
            logger.startLog().with("异常信息", e.getMessage()).logError("微信小程序预授权支付异常");
            return TradePayForFreezeResult.builder().success(false).sourceFailedReason("微信预授权支付失败：" + e.getMessage()).build();
        }
    }

    @Override
    public RefundForFreezeResult refundForFreeze(RefundForFreezeParam param) {
        logger.startLog().with("微信小程序预授权退款开始", param).log("WX_FUNDAUTH_REFUND_START");
        
        try {
            // 构建平台接口请求参数
            ThirdFundAuthRefundReq request = WxFundAuthConverter.INSTANCE.toRefundRequest(param);
            
            // 调用平台接口
            ApiResultResp<ThirdFundAuthRefundResp> response = platformApiClient.thirdFundAuthRefund(request);
            
            logger.startLog().with("调用platform微信预授权退款响应", response).logAudit("WX_FUNDAUTH_REFUND_RESPONSE");
            
            // 转换响应结果
            return convertToRefundResult(response);
            
        } catch (Exception e) {
            logger.startLog().with("异常信息", e.getMessage()).logError("微信小程序预授权退款异常");
            return RefundForFreezeResult.builder().success(false).sourceFailedReason("微信预授权退款失败：" + e.getMessage()).build();
        }
    }

    @Override
    public TradeCloseForFreezeResult tradeCloseForFreeze(TradeCloseForFreezeParam param) {
        logger.startLog().with("微信小程序预授权交易关闭开始", param).log("WX_FUNDAUTH_CLOSE_START");
        
        try {
            // 构建平台接口请求参数
            ThirdFundAuthCloseReq request = WxFundAuthConverter.INSTANCE.toCloseRequest(param);
            
            // 调用平台接口
            ApiResultResp<ThirdFundAuthCloseResp> response = platformApiClient.thirdFundAuthClose(request);
            
            logger.startLog().with("调用platform微信预授权交易关闭响应", response).logAudit("WX_FUNDAUTH_CLOSE_RESPONSE");
            
            // 转换响应结果
            return convertToCloseResult(response);
            
        } catch (Exception e) {
            logger.startLog().with("异常信息", e.getMessage()).logError("微信小程序预授权交易关闭异常");
            return TradeCloseForFreezeResult.builder().success(false).sourceFailedReason("微信预授权交易关闭失败：" + e.getMessage()).build();
        }
    }

    @Override
    public Byte thirdFundAuthSource() {
        return ThirdFundAuthEnum.ThirdFundAuthSource.WX_MINI_PROGRAM.getSource();
    }
    
    /**
     * 转换冻结响应结果
     */
    private FundAuthFreezeResult convertToFreezeResult(ApiResultResp<ThirdFundAuthFreezeResp> response) {
        if (response == null || !ApiResultResp.isSuccess(response)) {
            String errorMsg = response != null ? response.getMessage() : "调用platform接口失败";
            return FundAuthFreezeResult.builder().success(false).sourceFailedReason(errorMsg).build();
        }
        
        ThirdFundAuthFreezeResp data = response.getData();
        if (data == null) {
            return FundAuthFreezeResult.builder().success(false).sourceFailedReason("platform返回数据为空").build();
        }
        
        return WxFundAuthConverter.INSTANCE.toFreezeResult(data);
    }
    
    /**
     * 转换解冻响应结果
     */
    private FundAuthUnfreezeResult convertToUnfreezeResult(ApiResultResp<ThirdFundAuthUnfreezeResp> response) {
        if (response == null || !ApiResultResp.isSuccess(response)) {
            String errorMsg = response != null ? response.getMessage() : "调用platform接口失败";
            return FundAuthUnfreezeResult.builder().success(false).sourceFailedReason(errorMsg).build();
        }
        
        ThirdFundAuthUnfreezeResp data = response.getData();
        if (data == null) {
            return FundAuthUnfreezeResult.builder().success(false).sourceFailedReason("platform返回数据为空").build();
        }
        
        return WxFundAuthConverter.INSTANCE.toUnfreezeResult(data);
    }
    
    /**
     * 转换预授权支付响应结果
     */
    private TradePayForFreezeResult convertToPayResult(ApiResultResp<ThirdFundAuthPayResp> response) {
        if (response == null || !ApiResultResp.isSuccess(response)) {
            String errorMsg = response != null ? response.getMessage() : "调用platform接口失败";
            return TradePayForFreezeResult.builder().success(false).sourceFailedReason(errorMsg).build();
        }
        
        ThirdFundAuthPayResp data = response.getData();
        if (data == null) {
            return TradePayForFreezeResult.builder().success(false).sourceFailedReason("platform返回数据为空").build();
        }
        
        return WxFundAuthConverter.INSTANCE.toPayResult(data);
    }
    
    /**
     * 转换预授权退款响应结果
     */
    private RefundForFreezeResult convertToRefundResult(ApiResultResp<ThirdFundAuthRefundResp> response) {
        if (response == null || !ApiResultResp.isSuccess(response)) {
            String errorMsg = response != null ? response.getMessage() : "调用platform接口失败";
            return RefundForFreezeResult.builder().success(false).sourceFailedReason(errorMsg).build();
        }
        
        ThirdFundAuthRefundResp data = response.getData();
        if (data == null) {
            return RefundForFreezeResult.builder().success(false).sourceFailedReason("platform返回数据为空").build();
        }
        
        return WxFundAuthConverter.INSTANCE.toRefundResult(data);
    }
    
    /**
     * 转换交易关闭响应结果
     */
    private TradeCloseForFreezeResult convertToCloseResult(ApiResultResp<ThirdFundAuthCloseResp> response) {
        if (response == null || !ApiResultResp.isSuccess(response)) {
            String errorMsg = response != null ? response.getMessage() : "调用platform接口失败";
            return TradeCloseForFreezeResult.builder().success(false).sourceFailedReason(errorMsg).build();
        }
        
        ThirdFundAuthCloseResp data = response.getData();
        if (data == null) {
            return TradeCloseForFreezeResult.builder().success(false).sourceFailedReason("platform返回数据为空").build();
        }
        
        return WxFundAuthConverter.INSTANCE.toCloseResult(data);
    }

}
