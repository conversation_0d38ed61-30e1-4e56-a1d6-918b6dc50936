package com.ql.rent.provider.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ql.rent.dao.common.BizPushRecordMapper;
import com.ql.rent.entity.common.BizPushRecord;
import com.ql.rent.entity.common.BizPushRecordExample;
import com.ql.rent.service.common.IPushBizRecordService;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.common.BizMethodQuery;
import com.ql.rent.vo.common.BizPushRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class PushBizRecordServiceImpl implements IPushBizRecordService {

    @Resource
    private BizPushRecordMapper bizPushRecordMapper;

    @Override
    public void save(BizPushRecordDTO bizPushRecordDTO) {
        BizPushRecord bizPushRecord = new BizPushRecord();

        String traceId = StringUtils.isBlank(bizPushRecordDTO.getSaasTraceId()) ? "" : bizPushRecordDTO.getSaasTraceId();
        Long userId = Objects.nonNull(bizPushRecordDTO.getUserId()) ? bizPushRecordDTO.getUserId() : 0L;
        bizPushRecord.setMerchantId(bizPushRecordDTO.getMerchantId());
        bizPushRecord.setChannelId(bizPushRecordDTO.getChannelId());
        bizPushRecord.setSaasTraceId(traceId);
        bizPushRecord.setCtripReqId(bizPushRecordDTO.getCtripReqId());
        bizPushRecord.setStoreId(bizPushRecordDTO.getStoreId());
        bizPushRecord.setBizId(bizPushRecordDTO.getBizId());
        bizPushRecord.setBizMethod(bizPushRecordDTO.getBizMethod());
        bizPushRecord.setRepResult(bizPushRecordDTO.getRepResult());
        bizPushRecord.setResponseTime(bizPushRecordDTO.getResponseTime().getTime());
        bizPushRecord.setUserId(userId);
        if (StringUtils.isNotEmpty(bizPushRecordDTO.getRepResult())) {
            String code = "";
            String message = "";
            String result = "";
            JSONObject obj = JSONObject.parseObject(bizPushRecordDTO.getRepResult());
            code = obj.getString("code");
            code = code == null ? "" : code;
            if (BooleanUtils.toBoolean("000000".equals(code))) {
                result = String.valueOf(true);
            } else if (BooleanUtils.toBoolean("warn".equals(code))) {
                result = String.valueOf(code);;
            } else {
                result = String.valueOf(false);;
            }
            String errorResultList = obj.getString("errorResultList") != null ? obj.getString("errorResultList").replace("[]","") : "";
            String messageObj = obj.getString("message") != null ? obj.getString("message").replace("\"","") : "";
            message = errorResultList + messageObj;
            bizPushRecord.setCode(code);
            bizPushRecord.setMessage(message);
            bizPushRecord.setResult(result);
        }

        BizPushRecordExample example = new BizPushRecordExample();
        BizPushRecordExample.Criteria criteria = example.createCriteria();
        criteria.andMerchantIdEqualTo(bizPushRecordDTO.getMerchantId());
        criteria.andChannelIdEqualTo(bizPushRecordDTO.getChannelId());
        criteria.andStoreIdEqualTo(bizPushRecordDTO.getStoreId());
        criteria.andBizMethodEqualTo(bizPushRecordDTO.getBizMethod());
        criteria.andBizIdEqualTo(bizPushRecordDTO.getBizId());
        List<BizPushRecord> pushRecords = bizPushRecordMapper.selectByExample(example);
        String ctripReqId = StringUtils.isBlank(bizPushRecord.getCtripReqId()) ? "" : bizPushRecord.getCtripReqId();
        bizPushRecord.setCtripReqId(ctripReqId);

        String saasParam = StringUtils.isBlank(bizPushRecord.getSaasParam()) ? "" : bizPushRecord.getSaasParam();
        bizPushRecord.setCtripReqId(ctripReqId);
        bizPushRecord.setSaasParam(saasParam);
        log.info("pushRecords:{}", JSON.toJSONString(bizPushRecord));
        if (CollectionUtils.isEmpty(pushRecords)) {
            bizPushRecordMapper.insertSelective(bizPushRecord);
        } else {
            bizPushRecord.setId(pushRecords.get(0).getId());
            bizPushRecordMapper.updateByPrimaryKeySelective(bizPushRecord);
            int cnt = pushRecords.size();
            if (pushRecords.size() > 1) {
                for (int i = 1; i < cnt; i++) {
                    bizPushRecordMapper.deleteByPrimaryKey(pushRecords.get(i).getId());
                }
            }
        }
    }

    @Override
    public PageListVo<BizPushRecordDTO> getPushBizRecord(LoginVo loginVo, BizMethodQuery query) {
        BizPushRecordExample example = new BizPushRecordExample();
        BizPushRecordExample.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(query.getMerchantId())) {
            criteria.andMerchantIdEqualTo(query.getMerchantId());
        }
        if (Objects.nonNull(query.getChannelId())) {
            criteria.andChannelIdEqualTo(query.getChannelId());
        }
        if (Objects.nonNull(query.getStoreId())) {
            criteria.andStoreIdEqualTo(query.getStoreId());
        }
        if (Objects.nonNull(query.getBizMethod())) {
            criteria.andBizMethodEqualTo(query.getBizMethod());
        }
        if (Objects.nonNull(query.getBizId())) {
            criteria.andBizIdEqualTo(query.getBizId());
        }
        if (Objects.nonNull(query.getCode())) {
            criteria.andCodeEqualTo(query.getCode());
        }
        if (Objects.nonNull(query.getResult())) {
            criteria.andResultEqualTo(query.getResult());
        }

        long count = bizPushRecordMapper.countByExample(example);

        if (count <= 0) {
            return PageListVo.buildEmptyPage();
        }

        example.setOrderByClause(String.format("response_time desc limit %s,%s", query.getStartPos(), query.getPageSize()));
        List<BizPushRecord> pushRecords = bizPushRecordMapper.selectByExampleWithBLOBs(example);
        List<BizPushRecordDTO> pushRecordDTOList = Lists.newArrayList();

        for (BizPushRecord pushRecord : pushRecords) {
            BizPushRecordDTO recordDTO = new BizPushRecordDTO();
            recordDTO.setMerchantId(pushRecord.getMerchantId());
            recordDTO.setChannelId(pushRecord.getChannelId());
            recordDTO.setStoreId(pushRecord.getStoreId());
            recordDTO.setBizId(pushRecord.getBizId());
            recordDTO.setBizMethod(pushRecord.getBizMethod());
            recordDTO.setSaasTraceId(pushRecord.getSaasTraceId());
            recordDTO.setCtripReqId(pushRecord.getCtripReqId());
            recordDTO.setUserId(pushRecord.getUserId());
            recordDTO.setSaasParam(pushRecord.getSaasParam());
            recordDTO.setRepResult(pushRecord.getRepResult());
            recordDTO.setResponseTime(new Date(pushRecord.getResponseTime()));
            recordDTO.setCode(pushRecord.getCode());
            recordDTO.setMessage(pushRecord.getMessage());
            recordDTO.setResult(pushRecord.getResult());
            pushRecordDTOList.add(recordDTO);
        }
        return PageListVo.buildPageList(count, pushRecordDTOList);

    }

    public List<BizPushRecordDTO> getPushErrorRecords(List<BizMethodQuery> querys) {
        List<BizPushRecordDTO> ret = new ArrayList<>();
        for (BizMethodQuery query : querys) {
            ret.addAll(getPushErrorRecord(query));
        }
        return ret;
    }

    private List<BizPushRecordDTO> getPushErrorRecord(BizMethodQuery query) {
        BizPushRecordExample example = new BizPushRecordExample();
        BizPushRecordExample.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(query.getMerchantId())) {
            criteria.andMerchantIdEqualTo(query.getMerchantId());
        }
        if (Objects.nonNull(query.getChannelId())) {
            criteria.andChannelIdEqualTo(query.getChannelId());
        }
        if (Objects.nonNull(query.getStoreId())) {
            criteria.andStoreIdEqualTo(query.getStoreId());
        }
        if (Objects.nonNull(query.getBizMethod())) {
            criteria.andBizMethodEqualTo(query.getBizMethod());
        }
        if (Objects.nonNull(query.getBizId())) {
            criteria.andBizIdEqualTo(query.getBizId());
        }
        if (CollectionUtils.isNotEmpty(query.getBizIds())) {
            criteria.andBizIdIn(query.getBizIds());
        }
        if (Objects.nonNull(query.getCode())) {
            criteria.andCodeEqualTo(query.getCode());
        }
        if (Objects.nonNull(query.getResult())) {
            criteria.andResultEqualTo(query.getResult());
        }

        List<BizPushRecord> pushRecords = bizPushRecordMapper.selectByExampleWithBLOBs(example);
        List<BizPushRecordDTO> pushRecordDTOList = Lists.newArrayList();

        for (BizPushRecord pushRecord : pushRecords) {
            BizPushRecordDTO recordDTO = new BizPushRecordDTO();
            recordDTO.setMerchantId(pushRecord.getMerchantId());
            recordDTO.setChannelId(pushRecord.getChannelId());
            recordDTO.setStoreId(pushRecord.getStoreId());
            recordDTO.setBizId(pushRecord.getBizId());
            recordDTO.setBizMethod(pushRecord.getBizMethod());
            recordDTO.setSaasTraceId(pushRecord.getSaasTraceId());
            recordDTO.setCtripReqId(pushRecord.getCtripReqId());
            recordDTO.setUserId(pushRecord.getUserId());
            recordDTO.setSaasParam(pushRecord.getSaasParam());
            recordDTO.setRepResult(pushRecord.getRepResult());
            recordDTO.setResponseTime(new Date(pushRecord.getResponseTime()));
            recordDTO.setCode(pushRecord.getCode());
            recordDTO.setMessage(pushRecord.getMessage());
            recordDTO.setResult(pushRecord.getResult());
            pushRecordDTOList.add(recordDTO);
        }
        return pushRecordDTOList;
    }


}
