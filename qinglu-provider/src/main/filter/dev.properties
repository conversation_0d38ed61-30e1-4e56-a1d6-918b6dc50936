spring.datasource.common.url=*********************************************************************************************************************************************************************************************
spring.datasource.common.username=qinglu
spring.datasource.common.password=m3rTQ6XNA043uFSf


spring.datasource.slave.common.url=**********************************************************************************************************************************************************************************************
spring.datasource.slave.common.username=qinglu
spring.datasource.slave.common.password=m3rTQ6XNA043uFSf

spring.datasource.store.url=*********************************************************************************************************************************************************************************************
spring.datasource.store.username=qinglu
spring.datasource.store.password=m3rTQ6XNA043uFSf

spring.datasource.trade.url=*********************************************************************************************************************************************************************************************
spring.datasource.trade.username=qinglu
spring.datasource.trade.password=m3rTQ6XNA043uFSf

spring.datasource.slave.trade.url=*********************************************************************************************************************************************************************************************
spring.datasource.slave.trade.username=qinglu
spring.datasource.slave.trade.password=m3rTQ6XNA043uFSf

spring.datasource.vehicle.url=***********************************************************************************************************************************************************************************************
spring.datasource.vehicle.username=qinglu
spring.datasource.vehicle.password=m3rTQ6XNA043uFSf

spring.datasource.slave.vehicle.url=***********************************************************************************************************************************************************************************************
spring.datasource.slave.vehicle.username=qinglu
spring.datasource.slave.vehicle.password=m3rTQ6XNA043uFSf

spring.datasource.merchant.url=************************************************************************************************************************************************************************************************
spring.datasource.merchant.username=qinglu
spring.datasource.merchant.password=m3rTQ6XNA043uFSf

spring.datasource.bill.url=********************************************************************************************************************************************************************************************
spring.datasource.bill.username=qinglu
spring.datasource.bill.password=m3rTQ6XNA043uFSf

profile.env=dev
otel.instrumentation.jdbc.enabled=true


spring.mvc.pathmatch.matching-strategy=ant_path_matcher
service.port=8087
redis.address=r-uf6kyhn0dm2j5au3vp.redis.rds.aliyuncs.com
redis.password=hello@111
redis.port=6379
redis.database=0

# aliyun Oss model
aliyun.oss.endpoint=https://qinglu-file-dev.oss-cn-shanghai.aliyuncs.com
aliyun.oss.domain=https://qinglu-file-dev.oss-cn-shanghai.aliyuncs.com
aliyun.oss.accessKeyId=LTAI5tFjRww9p3FMWSdidZsG
aliyun.oss.accessKeySecret=******************************
aliyun.oss.bucketName=qinglu-file-dev
aliyun.oss.fileDir=uimg

# aliyun Oss private
aliyun.oss-private.endpoint=https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com
aliyun.oss-private.domain=https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com
aliyun.oss-private.accessKeyId=LTAI5tFjRww9p3FMWSdidZsG
aliyun.oss-private.accessKeySecret=******************************
aliyun.oss-private.bucketName=qinglu-file-private-dev
aliyun.oss-private.fileDir=uimg
aliyun.oss-private.real.endpoint=oss-cn-shanghai.aliyuncs.com

#aliyun ocr
aliyun.ocr.accessKey.id=LTAI5t6oCZLg3tpxuy22B4fo
aliyun.ocr.accessKey.secret=******************************
aliyun.ocr.endpoint=ocr-api.cn-hangzhou.aliyuncs.com
#gaode
gaode.lbs.web.key=96d6ccc9c59d6fe879790e2719eedf61
gaode.lbs.web.url=https://restapi.amap.com/v5/direction/driving
gaode.openapi.url=https://restapi.amap.com

## ----------------内部应用调用地址开始----------------------------------
api.ribbon.listOfServers=http://raptor-dev.dev.svc.cluster.local:8080
collector-api.ribbon.listOfServers=http://collector-dev:8080
#open-api.ribbon.listOfServers=http://qinglu-open-dev.dev.svc.cluster.local:8080
#hello环境
open-api.ribbon.listOfServers=http://qinglu-open-dev.dev.svc.cluster.local:8080
didi.ribbon.listOfServers=http://didi-api.dev.svc.cluster.local:8080
zzc.ribbon.listOfServers=http://zzc-api.dev.svc.cluster.local:8080
tc-api.ribbon.listOfServers=http://tc-api.dev.svc.cluster.local:8080
car.ribbon.listOfServers=http://car-engine.dev.svc.cluster.local:8080
wuKong-api.ribbon.listOfServers=http://kate.dev.svc.cluster.local:8080/api
tx-api.ribbon.listOfServers=http://tiexing-api.dev.svc.cluster.local:8080
saas-self-etc-api.ribbon.listOfServers=http://etc-api.dev.svc.cluster.local:8080
platform-api.ribbon.listOfServers=http://platform-api.dev.svc.cluster.local:8080

#collector-api.ribbon.listOfServers部分接口需要BasicAuth认证
collector-api.basic.auth.username=admin
collector-api.basic.auth.password=sdfsdfsdf

## ----------------内部应用调用地址结束----------------------------------

## 直接调用携程地址 (测试环境)
ctrip-api.ribbon.listOfServers=http://ctrip-proxy-21444.qinglusaas-dev.com/dev
ctrip-api-test.ribbon.listOfServers=http://ctrip-proxy-21444.qinglusaas-dev.com/dev
ctrip-api-sp.ribbon.listOfServers=http://ctrip-proxy-13077.qinglusaas-dev.com/dev
ctrip-contract-api.ribbon.listOfServers=http://ctrip-proxy-21444.qinglusaas-dev.com/dev
ctrip-contractV2-api.ribbon.listOfServers=http://ctrip-proxy-13077.qinglusaas-dev.com/dev

vehicle.sery.file.url=https://img.qinglusaas.com
#sign
qinglu.sign.app-key=qinglu
qinglu.sign.app-secret=lrtQyRKLmrdlfFAQ
raptor.sign.app-key=raptor
raptor.sign.app-secret=afD7sQBzrIadLAAx
hello.sign.app-secret=fa82fab7ff8c4ad2aa99d5e0ea83a73c
hello.sign.app-key=caoweicode-wnkWzgt6
#trace
otlp.enabled=true
#otlp.endpoint=http://otel-collector:4317
otlp.endpoint=http://otel-collector.dev.svc.cluster.local:4317

#发票接口
invoice.domain=https://huisuiyun.com
invoice.getToken=%s/api/v2/agent/common/cdk/getToken
invoice.createInvoiceSync=%s/api/v2/agent/cdk/invoice/createInvoiceSync
invoice.redInvoiceSync=%s/api/v2/agent/cdk/invoice/create/redInvoiceSync
invoice.saveRedInvoiceInfo=%s/api/v2/agent/cdk/invoice/red/saveRedInvoiceInfo
invoice.getRedInvoiceInfo=%s/api/v2/agent/cdk/invoice/red/getRedInvoiceInfo
invoice.deleteRedInvoiceInfo=%s/api/v2/agent/cdk/invoice/red/deleteRedInvoiceInfo
invoice.blankInvoice=%s/api/v2/agent/cdk/invoice/blankInvoice
invoice.queryBill=%s/api/v2/agent/cdk/agent-settlement/queryBill
#全电
invoice.allElectric.getQrCode=%s/api/v2/agent/cdk/allElectric/authentication/getQrCode?refreshType=%s&faceQrCodeType=%s
invoice.allElectric.creditLineQuery=%s/api/v2/agent/cdk/allElectric/creditLine/query
invoice.allElectric.red.apply=%s/api/v2/agent/cdk/allElectric/red/apply
invoice.allElectric.red.info=%s/api/v2/agent/cdk/allElectric/red/info
invoice.allElectric.create.redInvoice=%s/api/v2/agent/cdk/allElectric/create/redInvoice
invoice.allElectric.invoiceRepair=%s/api/v2/agent/cdk/allElectric/invoice/invoiceRepair
#发票通 ak sk(虚拟通道)
invoice.deviceNo=********
invoice.accessKey=11c9b47daf80f3de
invoice.secretKey=24f03f2ee061d74d
invoice.revenueCode=3040203000000000000
#卖家发票信息(虚拟通道023)
invoice.seller.sellerName=虚拟通道023
invoice.seller.sellerAddress=66+
invoice.seller.sellerBankName=56563
invoice.seller.sellerTaxNo=*********


## ----------------微信应用调用地址开始----------------------------------
#微信开放平台
wechat.openAppId=wx138435b5d16510c2
wechat.openAppSecret=5a228106a256358e2e12c3a8bf1aea5e
wechat.mobileAppId=wxeb73f8443a3c954a
wechat.mobileAppSecret=315d2b5a85ca510c1949b6bfef36ee2a
wechat.openQrcodeUrl=https://open.weixin.qq.com/connect/qrconnect?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_login&state=STATE#wechat_redirect
wechat.openAuthUrl=https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code
#wechat.openAuthBackUrl=http://cy501.jianxiao.ltd/api/login/v1/openCodeAuth
wechat.openAuthBackUrl=http://sa.qinglusaas-dev.com/${profile.env}/api/login/v1/openCodeAuth
wechat.openUserInfoUrl=https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s

#微信公众号
wechat.mpAppId=wx1517ed8050aa77e6
wechat.mpAppSecret=af6be356f4fa435e0c8be5642c0fc7b1
wechat.mpAccessUrl=https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s
wechat.mpUserInfoUrl=https://api.weixin.qq.com/cgi-bin/user/info?access_token=%s&openid=%s&lang=zh_CN
wechat.mpSendMsgUrl=https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=%s
wechat.mpSendSubscribeMsgUrl=https://api.weixin.qq.com/cgi-bin/message/subscribe/bizsend?access_token=%s
wechat.mpToken=0b283ba662eb4f5f83b86db9bff9a921

#微信小程序
wechat.miniAppId=wx6b13c23af5eca9ac
wechat.miniAppSecret=4119941cf2984f2d001f3a881da32993
wechat.miniAuthUrl=https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code
wechat.miniAccessUrl=https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s
wechat.miniQrcodeUrl=https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=%s

## 微信机器人发送消息地址
wx-robot-api.ribbon.listOfServers=https://qyapi.weixin.qq.com/cgi-bin/webhook
## ----------------微信应用调用地址结束----------------------------------

#第三方支付渠道地址
fin-link.ribbon.listOfServers=https://fin-link.qinglusaas-dev.com


#微信支付回调
wechatPay.notifyUrl=http://s.qinglusaas-dev.com/api/wechatPay/v1/callback
#支付宝支付回调
aliPay.notifyUrl=http://s.qinglusaas-dev.com/api/aliPay/v1/callback

# 支付密钥
payment.aesKey=Kj7mP9nX2vB4yQ8s

#车辆工单执行时间
quartz.vehicle.work.order.corn=0 0,5,10,15,20,25,30,35,40,45,50,55 * * * ? *

# 每日拉取携程子车系时，使用的商家id
ctrip.vehicle.pull-sub-sery.merchant-id=3

shangqi.api.url=https://md-st.evcard.vip/mdpartner/
shangqi.api.clientId=ba4370d8937700a0f08ee6b3714250d9
shangqi.api.clientSecret=041cf9087865423853e6bf6afba67e9f

# 上智广链企业 查违章等服务
vehicle.appId=2023QLKJ
vehicle.secretKey=378CFC76883DAEE6B908D6571E216C65
vehicle.url=https://sogrand.cn/
#vehicle.url=https://sogrand.cn/test/

# h5页面前缀
h5.page.prefix=http://dolphin.qinglusaas-dev.com/

ots.endPoint=https://qinglu-dev.cn-shanghai.ots.aliyuncs.com
ots.accessKeyId=LTAI5tKGLoLTPck1C5Uto2aB
ots.accessKeySecret=******************************
ots.instanceName=qinglu-dev

ots.timeSeries.tableName=device_timeseries_table
## gps 度量名称/数据源名称
ots.timeSeries.measure.gps=measure_gps
ots.timeSeries.ds.gps=ds_gps
## 车身状态 数据度量名称/数据源名称
ots.timeSeries.measure.deviceBody=measure_device_body
ots.timeSeries.ds.deviceBody=ds_device_body
## 仪表里程 数据度量名称/数据源名称
ots.timeSeries.measure.dashboard=measure_dashboard
ots.timeSeries.ds.dashboard=ds_dashboard
## 油量 数据度量名称/数据源名称
ots.timeSeries.measure.gasVolume=measure_gas_volume
ots.timeSeries.ds.gasVolume=ds_gas_volume
## 电压 数据度量名称/数据源名称
ots.timeSeries.measure.voltage=measure_voltage
ots.timeSeries.ds.voltage=ds_measure_voltage
## VIN+GPS+电压 数据度量名称/数据源名称
ots.timeSeries.measure.vinGpsVoltage=measure_vin_gps_voltage
ots.timeSeries.ds.vinGpsVoltage=ds_vin_gps_voltage
## 单项数据
ots.timeSeries.measure.singleData=measure_single_data
ots.timeSeries.ds.singleData=db_single_data
## 车机状态数据整合 宽表模型
ots.syncTable.deviceStateData=device_state_data

##  车机方url
remoteDevice.secretKey=QINGLUGDL0
remoteDevice.idsId=b09e0077-e4f3-4daf-9af7-f8c20334b04b
remoteDevice.controlCarUrl=http://zxtapi.wiselink.net.cn/RemoteControlCarsNew.ashx
remoteDevice.deviceRegUrl=https://fin3.wiselink.net.cn/fin/api/deviceReg
remoteDevice.deviceUpdateUrl=https://fin3.wiselink.net.cn/fin/api/deviceUpdate
remoteDevice.deviceDelUrl=https://fin3.wiselink.net.cn/fin/api/deviceDel

mall.service.finance=<EMAIL>

# 直连商家相关配置
# evcard商家idid
open.merchant.evcard.id=58
## 凹凸商家id 和 创建sku的门店id
open.merchant.aotu.id=
open.merchant.aotu.sku.store.id=
open.merchant.ids=

# vbk切SaaS提醒客服
ctrip.vbk.notify.customer.service.user=
# vbk切SaaS，异常提醒开发
ctrip.vbk.notify.developer=

#支付宝小程序
alipay.mini.program.pay.serverUrl=https://openapi.alipay.com/gateway.do
alipay.mini.program.pay.appId=2021004164619468
alipay.mini.program.pay.privateKey= MIIEuwIBADANBgkqhkiG9w0BAQEFAASCBKUwggShAgEAAoIBAQCVr7bNY6WvAJWExvBD/HfjWM4ON2l1hvlTcXJUzOqoPjHnBZ6T847XmQK10m7XDUua+F2XZMqsMhT7WJ4DVHqJ70V8fmQ0vDK8nu/sQ54r7tLsbTCpIlOD/Xkty2IIYJ2J2r7EDaX7ztTqJaUTU6EfKGHtSBGD27RCMADhLTw+M4e0dm07nGZGT+RvUBwh+2OT1+5m9onNS6Z+xPj2hDreAir7lzF4PeGuxnB1v+m2/aecz6AFeqLrb+akForZeCK+Jp5KoHnwaOil7zOE5tBu3Zv1kHn7hG654XkPQzNnopuphEBF3OA55LkaqepyJeNjAmjfLtXD0Cvs2QHz8PqrAgMBAAECggEAaHQ+52LDCrUooYhFv9udObwNxqPqZsYylIUVGJIKqi8Emcc1Gt+abV7sIj9fc5hmixUIEo6WU8V7GM6FLaO1Ay5CWhbFDHvMGA39NO0Wome9LpkfnNdEWlX2SvujRF9PcBSCjMNNccOqll6ArFTumZSPiYqCFnapeoqPApOTd39euuYYhgYLTt2+qdgCUdUruXeV8OLEOKTWEUMVvlAvcW2+5CYupHE0MjrF27vHm0DIGpu1BNJ7VorxsaJ0fPS6Sw8BBzkKYAGZyNpgaZ9pMxFDPSYJVe8LL0HcmQxzOxsZn6TxBGCnUlW8CnnHYGKJsqwYSxsm10nJOnlrPdkAQQKBgQDo6KTf+0d2Jmyt2Jqfs+2lSEf/6wsKqVNhJdqEh2BAw91O6hbDGCTBgeW8s1BktS9aqAF25IqiewKk4he0ut6P4O+SkvOEOICnJ3O3gZAPtZAPSYfW+f+6llZuLUT/GUQ+auOYJ+HCR4LfpUHYWP5JnTajWXtiVX7WeHtxvpItIQKBgQCkhtbHFJ8n+TC3VSw8JB87qdkqA1dgBUKtX+U0QoE7BVpgppLnnkXTTh361K46kYwYMhh+pcHTSEXZAS/pK/0ISdetWC2nRZdKsEXoQvPFAN9lZWw2L8gD9HMI5WbXOWz8qcgFjWH83UostOFEe3krhkv3Aq8ip03xvbm1ctOCSwKBgCQf+Xq4KdRcj1zDjPAaY1DQjkJWfYbwznrx+Ru3N6mz936G9nLaKtFMyYpkMaL9K1QfXOLI/evHsphKbSzeEykz1VThsB2g+etzRhJ/aQ3Wtnp4iDZC3nRdXxiFsv7yN1CLGbP+85hDqTvxEO5ylzZpyRC7D1UoN50iYNs6mMehAn9L1dsp6Fd0FWhfGONK1VuQJLn0o5WirMoPICSF8iGfEtiVtDICkPGedGNdOqsUVAI/JNgliEHPIYEQhXuP/rajG2ZOgFaKw4j45EdUx4eVRtGx6w317BBFwmdDsnm0usOBssB/zqtb4qRN9xHefwtbBIv/Tz1qu7MxukHfYQ4dAoGBANEohx01D46w0Z3BXmbYlQJZkHSLLFGdAqn3bAHFAdhNXjlDbRHHi2pW4LNcTIaKjwZaTDroHI+KWPUO2FJuZA3Pkgyayw+Ute5vouTol9O9fRCokyCfbv/uEfq29JNJDpOq1ihccRKc52PKYDWyZU9Qpy7J3OkBs9yN+zGfKTfD
alipay.mini.program.pay.alipayPublicKey=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgETg6IWey52a1dCP90Qm7Yx/0T1oJHbT1FuPjLkUTXBPPNnvknDGJKMni0/kTNj8JtjZR8IoYHtvQtSGu36Ckv2UWukjzKlWybA7x6G1y9VnGLYZi/Xg/pI/MUZA1rxo8O/lYKGdnrPbVCLS9ACGMDmSvcQfs7W6SrPcwZz/gf7lTNUKFs+bqPhaAD2pWonJyACkAVCUk6rcGNHBXsKYzrlmRhytHpYmuURMjfqef+bZQbmSGm7DMU7fJMxuXyM9tS8gxcj9MYFLqSGwE5CbxjGtsQyntOd10rUrHI1Lm3ArTyis+qc9t5r9jJjk3LB9tuWs4iFhD0nd57v5BkN8OwIDAQAB
alipay.mini.program.pay.notifyUrl=https://p.qinglusaas-dev.com/alipay/callback
alipay.mini.program.pay.encryptKey=hSeQzzZ+Npnpjsz3ySzrZQ==

# 是否开启swagger
swagger.enable=true

#支付宝
alipay.pay.serverUrl=https://openapi.alipay.com/gateway.do
alipay.pay.appId=2021005111615343
alipay.pay.privateKey=MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCuYkcgdIDEfDtX7WiuZd7M9PS3Xq3f0+v07bkiiXMorCO/BH3MrctWs2wifIQuHiPGDAp5fuz+1gJDwlVjm6BZoBOqXhZpJuJBUPH8md9o1Eh+YUmt5EFpOsOo5UlABs+c7bc2fC6eYcVC/llNUpTXI7GQ2xNCEJ8yS2/EIrJq5uM243rvA6tzuLMiJIHzUonjV2sprCPK3M6dkNvzz4CHUxqJ3qLFP9/yeld/N9LU/Tmyl2n/PDpzLjHGWWaoYfFZfSPRdCcLABQVfss/Hlj2mkjtRVA+HQ5gOVwsij0TBd9qXYbE1PYLi0EG5cwusGjtcCfqAqZJY9nWg62sVDonAgMBAAECggEAXVDd+vIdDgfkwbKbd+eviEH0ykNyC/qdo52qml6vLy6emlNO9j5yN10gGnxDR2J9hjdfS5CuRdhDsfrGMt1Ou2c73skmePKD1Kv6MlAtCy299VMeWF1y21rE5RZ9UjPS2Ykjp5ZgZASxyJ3PL99nIpFJLdjtv0ahygowLubaTvWEqixVQSrSH6a/Clc40pQJ5B3NgzNcKIgCj9BKXu9qiu1cTPc5NXwFfEYu3vpfvqpJWFESS0OwRj4WT+kEBZ6uvNE33WZtwCm6IHb5OAygl7ZORi4gryjKKG5ybkA4i1nOtk2R2O5fNCBgXHvefMKwHXTRlqNaO5obUcH7K/2R0QKBgQD/KZUVCb3WLfBaM4Y/D6L/oHtxDp7lbDd/L4WNt2xJDb/aNYMVEnZfnLS/WSXOkVZWEVIaK7n4jOCQe3YKCSAKf2qmexwy7WCnvJBTGk32V6iTq1K4r8+254GJdaYRLxK2OYL3Orkqrf+bW/nx0v5CskH18oMU6P0t9OOM5N9ejQKBgQCu9NDY/HECNL0hSTSVIQ4gQQWhtrft+MUkvhnUpLOeh4SvztXSbVQz7zL2556pZ5/ho9oPdILFkmXj4VqrGotC0SNCYWhiUnJ0fhB1fm0TW8BVQ2AcNgJKkkby9ZdBktkHDoOTmC5Gtj1gV/ON6UlgutSe0tyS+bxc7c51rLg4gwKBgBgAvGJ3QSFlvudtU2KZQPcVDKes/WuNi4buXHzhVVFl9rc7M3KHAxWpcyNQ6oqNB3cfN/74QA1Oi01sH8V/6EC0jb7Nc/CifeYpidYXcmALDPOfOS/gcgS9sGTS6WVSbUsxC83f+001gwcFVvTVqU9y31uYjZYo+LAbClDVsuF9AoGAMBZP9OPlt8KBHU3+quKL/GeCwcDz6u+OWBv8A4tFZ1Q+Lwg9kvnDxAaBzoU5Abzen+kmPUs7ykwDv0+oWRERp8rN0yfsxeF9XfFsWq7mEgUM5yJ+nyJlHNP5tju0WCyhhCQEHCQezAa49esWoTG2nmSbng+82pTTpgMi1QslEzsCgYAI7NQeB39W7DNWqhYY2/5UExPuOyT1tZ4XFwspC5iOQIwyuwGWE+xdALVXL0P0sbHLbYSRzC9jOwX1ZXKAjb3CvI3hnaeVFTAokC2ZgWUw4kLcuPeTJjLq83hBTewtyVis6PCV7scosvBkrM8qRNh5x08aVAD0pdTHkf13KsQOsg==
alipay.pay.notifyUrl=http://s.qinglusaas-dev.com/api/alipay/v2/callback
alipay.pay.alipayPublicKey=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgETg6IWey52a1dCP90Qm7Yx/0T1oJHbT1FuPjLkUTXBPPNnvknDGJKMni0/kTNj8JtjZR8IoYHtvQtSGu36Ckv2UWukjzKlWybA7x6G1y9VnGLYZi/Xg/pI/MUZA1rxo8O/lYKGdnrPbVCLS9ACGMDmSvcQfs7W6SrPcwZz/gf7lTNUKFs+bqPhaAD2pWonJyACkAVCUk6rcGNHBXsKYzrlmRhytHpYmuURMjfqef+bZQbmSGm7DMU7fJMxuXyM9tS8gxcj9MYFLqSGwE5CbxjGtsQyntOd10rUrHI1Lm3ArTyis+qc9t5r9jJjk3LB9tuWs4iFhD0nd57v5BkN8OwIDAQAB

# 短信服务配置
#sms.provider=yunpian
yunpian.sms.apiKey=7fe92004d4f5331f38ff855b3eef89a1


rocketmq.nameServer=rmq-cn-m414aypa501.cn-shanghai.rmq.aliyuncs.com:8080
rocketmq.accessKey=3fM22I4X9s4le8yG
rocketmq.secretKey=mBMYC2Qah3Cy6u51
rocketmq.namespace=rmq-cn-m414aypa501

#宝付生产环境主账号
allocation.master.contract.no=CM610000000000282658