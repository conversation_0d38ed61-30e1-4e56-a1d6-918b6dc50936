<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="warn" name="MyApp" packages="io.opentelemetry.instrumentation.log4j.appender.v2_17">

    <Properties>
        <Property name="LOG_PATTERN">%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} [%15.15t] %c{10}:%L
            trace_id=%X{trace_id} span_id=%X{span_id} trace_flags=%X{trace_flags} domain=%X{domain} - %msg%n
        </Property>
        <Property name="APP_LOG_ROOT" value="logs"></Property>
    </Properties>

    <Appenders>
        <OpenTelemetry name="OpenTelemetryAppender" captureContextDataAttributes="*" captureMapMessageAttributes="true"/>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} %5p --- [%15.15t] %c{10}:%L domain=%X{domain} : %m%n"/>
        </Console>
        <RollingFile name="errorLog"
                     fileName="${APP_LOG_ROOT}/error.log"
                     filePattern="${APP_LOG_ROOT}/error-%d{yyyy-MM-dd}-%i.log">
            <LevelRangeFilter minLevel="ERROR" maxLevel="ERROR"
                              onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
        </RollingFile>
        <RollingFile name="debugLog"
                     fileName="${APP_LOG_ROOT}/debug.log"
                     filePattern="${APP_LOG_ROOT}/debug-%d{yyyy-MM-dd}-%i.log">
            <LevelRangeFilter minLevel="DEBUG" maxLevel="DEBUG"
                              onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
        </RollingFile>

        <RollingFile name="infoLog"
                     fileName="${APP_LOG_ROOT}/info.log"
                     filePattern="${APP_LOG_ROOT}/info-%d{yyyy-MM-dd}-%i.log">
            <LevelRangeFilter minLevel="INFO" maxLevel="INFO"
                              onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
        </RollingFile>

        <RollingFile name="warnLog"
                     fileName="${APP_LOG_ROOT}/warn.log"
                     filePattern="${APP_LOG_ROOT}/warn-%d{yyyy-MM-dd}-%i.log">
            <LevelRangeFilter minLevel="WARN" maxLevel="WARN"
                              onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
        <RollingFile
                name="appLog"
                fileName="${APP_LOG_ROOT}/app.log"
                filePattern="${APP_LOG_ROOT}/app.%i.log.gz"
                ignoreExceptions="false">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="5">
                <Delete basePath="${APP_LOG_ROOT}" maxDepth="2">
                    <IfFileName glob="*/app-*.log.gz"/>
                    <IfLastModified age="P2D"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
    </Appenders>

    <Loggers>
        <Logger name="org.springframework" level="ERROR" additivity="false">
             <AppenderRef ref="OpenTelemetryAppender" level="ERROR"/>
             <AppenderRef ref="Console" level="ERROR"/>
        </Logger>
<!--        <Logger name="org.apache" level="INFO"-->
<!--                additivity="false">-->
<!--            <AppenderRef ref="appLog"/>-->
<!--        </Logger>-->
<!--        <Logger name="org.mybatis" level="DEBUG"-->
<!--                additivity="false">-->
<!--            <AppenderRef ref="appLog"/>-->
<!--        </Logger>-->
        <Logger name="com.ql" additivity="false">
<!--            <AppenderRef ref="errorLog"/>-->
<!--            <AppenderRef ref="debugLog"/>-->
<!--            <AppenderRef ref="infoLog"/>-->
<!--            <AppenderRef ref="warnLog"/>-->
<!--            <AppenderRef ref="appLog"/>-->
            <AppenderRef ref="OpenTelemetryAppender" level="INFO"/>
<!--            <AppenderRef ref="Console" level="INFO"/>-->
        </Logger>

        <Root level="INFO">
<!--            <AppenderRef ref="Console"/>-->
            <AppenderRef ref="OpenTelemetryAppender" level="ERROR"/>
        </Root>
    </Loggers>

</Configuration>