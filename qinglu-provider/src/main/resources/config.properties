profile.env=${profile.env}
#OSS
aliyun.oss.domain=${aliyun.oss.domain}
aliyun.oss.endpoint=${aliyun.oss.endpoint}
aliyun.oss.accessKeyId=${aliyun.oss.accessKeyId}
aliyun.oss.accessKeySecret=${aliyun.oss.accessKeySecret}
aliyun.oss.bucketName=${aliyun.oss.bucketName}
aliyun.oss.fileDir=${aliyun.oss.fileDir}

aliyun.oss-private.domain=${aliyun.oss-private.domain}
aliyun.oss-private.endpoint=${aliyun.oss-private.endpoint}
aliyun.oss-private.accessKeyId=${aliyun.oss-private.accessKeyId}
aliyun.oss-private.accessKeySecret=${aliyun.oss-private.accessKeySecret}
aliyun.oss-private.bucketName=${aliyun.oss-private.bucketName}
aliyun.oss-private.fileDir=${aliyun.oss-private.fileDir}
aliyun.oss-private.real.endpoint=${aliyun.oss-private.real.endpoint}
aliyun.sts.endpoint=sts.cn-shanghai.aliyuncs.com
aliyun.sts.accessKeyId=LTAI5tG5h1rYLia6zbjMRZfh
aliyun.sts.accessKeySecret=******************************
aliyun.sts.roleArn=acs:ram::1905963142961246:role/ramossstsrole

# aliyun ocr
aliyun.ocr.accessKey.id=${aliyun.ocr.accessKey.id}
aliyun.ocr.accessKey.secret=${aliyun.ocr.accessKey.secret}
aliyun.ocr.endpoint=${aliyun.ocr.endpoint}

wechat.appid=${wechat.appid}
wechat.appSecret=${wechat.appSecret}
wechat.accessToken.url=${wechat.accessToken.url}

# aliyun sms
aliyun.sms.accessKeyId=LTAI5tBhGF1Y9UPj5ZWiynjP
aliyun.sms.accessKeySecret=******************************
aliyun.sms.signName=\u64CE\u8DEF\u79D1\u6280

# aliyun afs
aliyun.acs.accessKeyId=LTAI5tBa7uWjZQF6xqMyHXtp
aliyun.acs.accessKeySecret=******************************
aliyun.acs.endpoint=cn-shanghai
aliyun.acs.appkey=FFFF0N0000000000B2D9

#alipay
alipay.serverUrl=https://openapi.alipay.com/gateway.do
alipay.appId=2021003173687538
alipay.privateKey=MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCAcs+5KPl2jCU/7LBslmIS/KpPEg01u55ombv8PERZtEePB8aZ/kRTaVnh5KzU4S53ooiQJ4RwWGuDBVLoFpQLfWSZsIdf5JPfKj3HQGra/izHs7gd9pKTRrrYNJBHq/AWnxgjWer6ROlOqEsWtCeNNJxM6lPCm9iblTavkrkPBI164krhL8HlZ74pYJVUvchck6HQiWAAh+GJ3SwsrWpb47rv5GPYSaEt6n61OlDDu5tOpVLpPtH+os2uv2R30hPLcrUS72x86Ow15gvpe4B7Nx8QXOFu3BE2LdEG/sdl+rbn71NbexikZiabwBqhR+frpZmWgpdofeno6piQIAJJAgMBAAECggEAS2AUWXmb+l7/NOX2GaKd5rpyTngvXEq/CG6+i6PqECHkpCBSAOfevHvkDNUePsXteLLeJImsBCk+3eHv5tXpBl0M/ctDhZgi1iB5pEU+70eci4wBDUQ2e0pUNq+Rwx04hG3TwlrvsvFhX5EOj0Iku44V8mTHxjmiAJqtPpaBFjgmKUiiSByhqHGVRhx+QoLK9smYRjw8ug0pFBBz/CwHNGIdpkjH0jLlt5j88dpvLfraLEborSgn6ErxJEwJLluSuRXLL2/d4ASL00UJiYpV+3TS1o2+pmx2ZMKPfqRwyQ+36vdNoElVfKtYIsjHiQYB2krDrQv1kIqQCYKzDrkv6QKBgQDE7zoqqBxc3X093nJXc9UciqVti+7EU940Bp9Of0aT3vEU6SbrnU93XoVxvB4mX6tsuZMoMp4+JmZZjWAhyOZBm8htVJNmf442rRKekvozgvjqf4wV99oCalEAYBxkac2jevTCezchO2aKJ2cp0pZkFs/fquJkI8Tc1bB8UVBtAwKBgQCm+S3p0fVxAXNqjeJnXoMbqtAAsZUkrc75UsV19pI0AsASRgPXuDkmFEJkiriMliJQRUOouP3Q9nOWY+4ucc72znY8r3fhFg+VaTAbwCjgnV/3OPUvZI/TK+yn9WtiMIiccNus4GEBDITwD4iZkTB6NDTqgGq0Ixsqar5EVddTwwKBgCVMGXwSsqxz1lBtXMJrUaVCLOp+Yg4UxDSePHWlbTx8MLSBI7nj5TT6VdOgiSVDYTgKPXCThZzHsQKs39QGNHWgh2kNZ402Az0fsjMOKwyudW4UEffnJQboE8c6bKYn06ZulwRuSZalpVns9IDbPVhfaac2ugG1NIeOgQpKbCz5AoGBAJR7S8f87J1kY+VibO3tTvvAhx3GzVEDuuVeJLhE3zifJXxahLpFWZSyOWQ9M+XY8ISfQ457i0JSlvEqxjjI/fneGajXhOxFePAtzRkwZWlwqVEodmiBraBlFBs8FBUKAcHUXpU6JykOn/z2Y0vkn85cNJ5AOiMcgODUcYRFLamPAoGBAJRdudaQ68072mb6xOogxWc0WJsuVPmZHw6H1LYvzi68DSpYmOTAonjithh38KpidFMdlhmeQCIw/WGE28NHpPTGIf8j5RTHTVfp/CMbp2uYCEfge/qCEyF5vlepl7ACoSIA4maeLWmGj12ikaNwH3PS0d8e87MYwLgplmJalYha
alipay.alipayPublicKey=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAiTpBhJka71n6ce33oFn6bDHU97hEK72mUT7ZjmnPVj/V3dh2FAkvNmqOnKNCOJ2jcEGFD3RkSKHKb9OqH5L5Ju2nVTaYPQHge6lMgLtPCqNxw71wJQRm6dTcY4Xjo1BCjh4EY3xvuV9UVX28CyyQKRdrE+nfT5mv/YXKoQoin6Sed1NX7OJfllVUJJPLh8Qr047avOt+301tZwbD026YRmAzlcPdzs4nZhG6tTNxtqMjCcvHxd5PD9jftda585xcm8yZVBMD5dYjEcSa0EJtz/oXY5/lLCaT5YA2+jHXONcWmlili18DM0ac6DmOEzMV5uzRvYFheeWZyQlgAOcjtwIDAQAB
alipay.notifyUrl=${aliPay.notifyUrl}

#\u652F\u4ED8\u5BC6\u94A5
payment.aesKey=${payment.aesKey}

# --------quartz corn\u8868\u8FBE\u5F0F---
# \u4FEE\u6539\u8C03\u8F66\u5355\u7684\u8F66\u8F86\u72B6\u6001 \u6BCF\u5C0F\u65F6\u6267\u884C
quartz.shuntinglist.clean.corn=0 0 * * * ?
# \u8F66\u8F86\u5DE5\u5355\u6267\u884C\u65F6\u95F4
quartz.vehicle.work.order.corn=0 0/5 * * * ? *
# \u5546\u5BB6\u6B20\u8D39\u63D0\u9192
quartz.merchant.insufficient.corn=0 0 8,12,16,20 * * ?
## \u5173\u95ED\u6B20\u8D39\u5546\u5BB6\u670D\u52A1
quartz.merchant.close.sever.corn=0 0 0 * * ?
## \u5E74\u68C0\u4E34\u8FD1\u63D0\u9192
quartz.yearly.inspection.notice.corn=0 0 9 * * ?
#
quartz.update.invoice.red.status.cron=0 0/5 * * * ?
#\u8F66\u8F86\u8D26\u5355\u5B9A\u65F6\u7EDF\u8BA1\u6BCF\u5929\u51CC\u6668 01:00 \u7EDF\u8BA1
quartz.vehicle.order.static.cron=0 0 1 * * ?

#\u643A\u7A0B\u9650\u4EF7\u4EFB\u52A1\u6570\u636E\u62C9\u53D6
quartz.ctrip.limit.price.cron=0 20 1,20 * * ?
# \u6570\u636E\u53C2\u8C0B
quartz.report.corn=0 0 * * * ?
quartz.report.recount.corn=30 * * * * ?
# \u81EA\u52A8\u67E5\u8BE2\u8FDD\u7AE0 \u6BCF\u59295\u70B9\u6267\u884C
quartz.vehicle.illegal.search.corn=0 0 5 * * ?
# \u8FDD\u7AE0\u8F6C\u79FB \u5408\u540C\u540C\u6B65
quartz.transfer.contract.sync.corn=0 0/2 * * * ?
# \u8FDD\u7AE0\u8F6C\u79FB \u5408\u540C\u7EED\u671F
quartz.vehicle.illegal.submit.delay.contract=0 0 3,6 * * ?
# è½¬ç§»ä¸æ¥ç»è®¡ä»»å¡ æ¯å¤©æ©ä¸6ç¹æ§è¡
quartz.transfer.report.corn=0 0 6 * * ?
# è¿ç« æåº¦è´¦åæ¨éä»»å¡ æ¯å¤©æ©ä¸5ç¹æ§è¡
quartz.violation.monthly.bill.corn=0 0 5 * * ?

# \u8BA2\u5355\u62BC\u91D1\u89E3\u51BB
order.deposit.thaw.corn=0 0/5 * * * ?
# \u6570\u636E\u603B\u89C8\u4EFB\u52A1
quartz.report.v2.corn=0 0 1 * * ?
quartz.report.stock.time.v2.corn=0 0 23 * * ?
#\u62C9\u53D6\u643A\u7A0B\u4EF7\u683C\u4FE1\u606F
ctrip.festival.holiday.schedule.corn=0 0 0/2 * * ?
ctrip.festival.holiday.pull.corn=0 0/5 * * * ?

#åè´¦å å¼
quartz.third.add.allocation.static.cron= 0 0 1 * * ?
#åè´¦åå¼
quartz.third.sub.allocation.static.cron= 0 0 1 * * ?

# åè´¦é»è®¤ä½£éçéç½®ï¼åä½ï¼%ï¼å¦1.0è¡¨ç¤º1%ï¼
allocation.default.commission.rate=1.0
# å¹³å°ä¸»è´¦æ·ååå·ï¼ä½£éæ±æ»å¥è´¦è´¦æ·ï¼
allocation.master.contract.no=${allocation.master.contract.no}

# jpush
jPush.app-key=8544852458607b1216495f07
jPush.master-secret=6ca312e12d2d79e5fcd057f7

# mail
mail.smtp.from=<EMAIL>
mail.smtp.host=smtp.mxhichina.com
mail.smtp.port=465
mail.personal=\u64CE\u8DEF\u79D1\u6280
mail.user=<EMAIL>
mail.password=4Gf6bQGpWJ!F2SA
mail.channel.apply=<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>
#gaode
gaode.lbs.web.key=${gaode.lbs.web.key}
gaode.lbs.web.url=${gaode.lbs.web.url}
gaode.openapi.url=${gaode.openapi.url}
vehicle.sery.file.url=${vehicle.sery.file.url}
#sign
qinglu.sign.app-key=${qinglu.sign.app-key}
qinglu.sign.app-secret=${qinglu.sign.app-secret}
raptor.sign.app-key=${raptor.sign.app-key}
raptor.sign.app-secret=${raptor.sign.app-secret}

collector-api.basic.auth.username=${collector-api.basic.auth.username}
collector-api.basic.auth.password=${collector-api.basic.auth.password}

invoice.accessKey=${invoice.accessKey}
invoice.secretKey=${invoice.secretKey}
invoice.domain=${invoice.domain}
invoice.getToken=${invoice.getToken}
invoice.createInvoiceSync=${invoice.createInvoiceSync}
invoice.redInvoiceSync=${invoice.redInvoiceSync}
invoice.saveRedInvoiceInfo=${invoice.saveRedInvoiceInfo}
invoice.getRedInvoiceInfo=${invoice.getRedInvoiceInfo}
invoice.deleteRedInvoiceInfo=${invoice.deleteRedInvoiceInfo}
invoice.deviceNo=${invoice.deviceNo}
invoice.revenueCode=${invoice.revenueCode}
invoice.blankInvoice=${invoice.blankInvoice}
invoice.queryBill=${invoice.queryBill}
invoice.allElectric.creditLineQuery=${invoice.allElectric.creditLineQuery}
invoice.allElectric.getQrCode=${invoice.allElectric.getQrCode}
invoice.allElectric.invoiceRepair=${invoice.allElectric.invoiceRepair}
invoice.allElectric.red.apply=${invoice.allElectric.red.apply}
invoice.allElectric.red.info=${invoice.allElectric.red.info}
invoice.allElectric.create.redInvoice=${invoice.allElectric.create.redInvoice}

invoice.seller.sellerName=${invoice.seller.sellerName}
invoice.seller.sellerAddress=${invoice.seller.sellerAddress}
invoice.seller.sellerBankName=${invoice.seller.sellerBankName}
invoice.seller.sellerTaxNo=${invoice.seller.sellerTaxNo}

#\u5FAE\u4FE1\u5F00\u653E\u5E73\u53F0
wechat.openAppId=${wechat.openAppId}
wechat.openAppSecret=${wechat.openAppSecret}
wechat.mobileAppId=${wechat.mobileAppId}
wechat.mobileAppSecret=${wechat.mobileAppSecret}
wechat.openQrcodeUrl=${wechat.openQrcodeUrl}
wechat.openAuthUrl=${wechat.openAuthUrl}
wechat.openAuthBackUrl=${wechat.openAuthBackUrl}
wechat.openUserInfoUrl=${wechat.openUserInfoUrl}
#\u5FAE\u4FE1\u516C\u4F17\u53F7
wechat.mpAppId=${wechat.mpAppId}
wechat.mpAppSecret=${wechat.mpAppSecret}
wechat.mpAccessUrl=${wechat.mpAccessUrl}
wechat.mpUserInfoUrl=${wechat.mpUserInfoUrl}
wechat.mpSendMsgUrl=${wechat.mpSendMsgUrl}
wechat.mpSendSubscribeMsgUrl=${wechat.mpSendSubscribeMsgUrl}
wechat.mpToken=${wechat.mpToken}
#\u5FAE\u4FE1\u5C0F\u7A0B\u5E8F
wechat.miniAppId=${wechat.miniAppId}
wechat.miniAppSecret=${wechat.miniAppSecret}
wechat.miniAuthUrl=${wechat.miniAuthUrl}
wechat.miniAccessUrl=${wechat.miniAccessUrl}
wechat.miniQrcodeUrl=${wechat.miniQrcodeUrl}

#\u5FAE\u4FE1\u652F\u4ED8
wechatPay.merchantId=1637115162
#wechatPay.privateKeyPath=D:\work\WXCertUtil\cert\apiclient_key.pem
wechatPay.privateKeyPath=/apiclient_key.pem
wechatPay.merchantSerialNumber=56365CB93A2286319A00C8815CA775BFE3CA6A94
wechatPay.apiV3key=ff992322f2f3ca05ee3a6bedfe6833b6
wechatPay.notifyUrl=${wechatPay.notifyUrl}
#trace
otlp.enabled=${otlp.enabled}
otlp.endpoint=${otlp.endpoint}

# \u643A\u7A0B\u4E0A\u8D27
# \u62C9\u53D6\u5B50\u8F66\u7CFB\u6570\u636E\u63A5\u53E3\u4F7F\u7528\u7684\u5546\u5BB6id
ctrip.vehicle.pull-sub-sery.merchant-id=${ctrip.vehicle.pull-sub-sery.merchant-id}

# \u62C9\u53D6\u5B50\u8F66\u7CFB\u6570\u636E \u6BCF\u5929\u6267\u884C\u4E00\u6B21
quartz.ctrip.vehicle.pull-sub-sery.corn=0 7 4 * * ?


shangqi.api.url=${shangqi.api.url}
shangqi.api.clientId=${shangqi.api.clientId}
shangqi.api.clientSecret=${shangqi.api.clientSecret}

# \u4E0A\u667A\u5E7F\u94FE\u4F01\u4E1A \u67E5\u8FDD\u7AE0\u7B49\u670D\u52A1
vehicle.appId=${vehicle.appId}
vehicle.secretKey=${vehicle.secretKey}
vehicle.url=${vehicle.url}
ots.endPoint=${ots.endPoint}
ots.accessKeyId=${ots.accessKeyId}
ots.accessKeySecret=${ots.accessKeySecret}
ots.instanceName=${ots.instanceName}
ots.timeSeries.tableName=${ots.timeSeries.tableName}
## gps \u5EA6\u91CF\u540D\u79F0/\u6570\u636E\u6E90\u540D\u79F0
ots.timeSeries.measure.gps=${ots.timeSeries.measure.gps}
ots.timeSeries.ds.gps=${ots.timeSeries.ds.gps}
## \u8F66\u8EAB\u72B6\u6001 \u6570\u636E\u5EA6\u91CF\u540D\u79F0/\u6570\u636E\u6E90\u540D\u79F0
ots.timeSeries.measure.deviceBody=${ots.timeSeries.measure.deviceBody}
ots.timeSeries.ds.deviceBody=${ots.timeSeries.ds.deviceBody}
## \u4EEA\u8868\u91CC\u7A0B \u6570\u636E\u5EA6\u91CF\u540D\u79F0/\u6570\u636E\u6E90\u540D\u79F0
ots.timeSeries.measure.dashboard=${ots.timeSeries.measure.dashboard}
ots.timeSeries.ds.dashboard=${ots.timeSeries.ds.dashboard}
## \u6CB9\u91CF \u6570\u636E\u5EA6\u91CF\u540D\u79F0/\u6570\u636E\u6E90\u540D\u79F0
ots.timeSeries.measure.gasVolume=${ots.timeSeries.measure.gasVolume}
ots.timeSeries.ds.gasVolume=${ots.timeSeries.ds.gasVolume}
## \u7535\u538B \u6570\u636E\u5EA6\u91CF\u540D\u79F0/\u6570\u636E\u6E90\u540D\u79F0
ots.timeSeries.measure.voltage=${ots.timeSeries.measure.voltage}
ots.timeSeries.ds.voltage=${ots.timeSeries.ds.voltage}
## VIN+GPS+\u7535\u538B \u6570\u636E\u5EA6\u91CF\u540D\u79F0/\u6570\u636E\u6E90\u540D\u79F0
ots.timeSeries.measure.vinGpsVoltage=${ots.timeSeries.measure.vinGpsVoltage}
ots.timeSeries.ds.vinGpsVoltage=${ots.timeSeries.ds.vinGpsVoltage}
## \u5355\u9879\u6570\u636E
ots.timeSeries.measure.singleData=${ots.timeSeries.measure.singleData}
ots.timeSeries.ds.singleData=${ots.timeSeries.ds.singleData}
## \u8F66\u673A\u72B6\u6001\u6570\u636E\u6574\u5408
ots.syncTable.deviceStateData=${ots.syncTable.deviceStateData}

remoteDevice.secretKey=${remoteDevice.secretKey}
remoteDevice.idsId=${remoteDevice.idsId}
remoteDevice.controlCarUrl=${remoteDevice.controlCarUrl}
remoteDevice.deviceRegUrl=${remoteDevice.deviceRegUrl}
remoteDevice.deviceUpdateUrl=${remoteDevice.deviceUpdateUrl}
remoteDevice.deviceDelUrl=${remoteDevice.deviceDelUrl}

# \u662F\u5426\u5F00\u542Fswagger
swagger.enable=${swagger.enable}

# h5\u9875\u9762\u524D\u7F00
h5.page.prefix=${h5.page.prefix}
# etc \u63D0\u73B0\u548C\u5BF9\u516C\u6C47\u6B3E\u90AE\u4EF6\u63D0\u9192
mall.service.finance=${mall.service.finance}

# \u8FDD\u7AE0\u8F6C\u79FB\u5408\u540C \u7EED\u671F\u5929\u6570 \u9700\u5927\u4E8E1 \u5C0F\u4E8E30
transfer.contract.renewal.days=25

# \u76F4\u8FDE\u5546\u5BB6\u7279\u6B8A\u914D\u7F6E\u4E34\u65F6\u4F7F\u7528
open.merchant.evcard.id=${open.merchant.evcard.id}
open.merchant.ids=${open.merchant.ids}

# vbk\u5207SaaS\u63D0\u9192\u5BA2\u670D
ctrip.vbk.notify.customer.service.user=${ctrip.vbk.notify.customer.service.user}
# vbk\u5207SaaS\uFF0C\u5F02\u5E38\u63D0\u9192\u5F00\u53D1
ctrip.vbk.notify.developer=${ctrip.vbk.notify.developer}
# \u6807\u51C6\u5316\u5F00\u5173\u901A\u77E5
ctrip.standard.notify.customer.service.user=18608083784

# \u81EA\u52A8\u5173\u95ED\u652F\u4ED8\u5355 \u6BCF\u5206\u949F\u68C0\u6D4B\u4E00\u6B21
quartz.bill.close.pay.corn=0 */1 * * * ?
# \u652F\u4ED8\u5355\u8D85\u65F6\u65F6\u95F4\u914D\u7F6E
bill.pay.timeout.minutes=15

# T+1\u5206\u8D26\u7ED3\u7B97\u4EFB\u52A1
quartz.bill.royalty.settle.corn=0 0 0 * * ?


hello.sign.app-secret=${hello.sign.app-secret}
hello.sign.app-key=${hello.sign.app-key}

## \u77ED\u4FE1\u670D\u52A1\u63D0\u4F9B\u5546\u914D\u7F6E \u53EF\u9009\u503C\uFF1Aali\u3001yunpian
#sms.provider=${sms.provider}

# \u4E91\u7247\u7F51SMS\u914D\u7F6E
yunpian.sms.apiKey=${yunpian.sms.apiKey}

# \u4E91\u7247\u7F51API\u670D\u52A1\u5730\u5740
yunpian-api.ribbon.listOfServers=https://sms.yunpian.com



# RocketMQ\u914D\u7F6E - \u6D4B\u8BD5\u73AF\u5883
rocketmq.nameServer=${rocketmq.nameServer}
#   \u963F\u91CC\u4E91 RocketMQ \u8D26\u53F7\u5BC6\u7801\u914D\u7F6E
rocketmq.accessKey= ${rocketmq.accessKey}
rocketmq.secretKey= ${rocketmq.secretKey}
rocketmq.namespace= ${rocketmq.namespace}