<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       	http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
    	http://www.springframework.org/schema/context
    	http://www.springframework.org/schema/context/spring-context-4.0.xsd">

    <!-- 自动扫描(自动注入)  -->
    <!--由于将数据源配置单独抽离成了一个properties文件，而context:property-placeholder的做法只能加载一个文件，所以在启动类上用PropertySource的方式注入 如果觉得没必要可以两个文件合并-->
<!--    <context:property-placeholder location="classpath:config.properties"/>-->
<!--    <context:property-placeholder location="classpath:multipleDataSourceConfig.properties"/>-->

    <!-- 添加对二方包中切面类的扫描 -->
    <context:component-scan base-package="com.qinglusaas.logutil" />
</beans>