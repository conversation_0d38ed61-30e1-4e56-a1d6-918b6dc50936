## é¨åºç³»ç»æ°æ®åºéç½®
spring.datasource.store.url=${spring.datasource.store.url}
spring.datasource.store.username=${spring.datasource.store.username}
spring.datasource.store.password=${spring.datasource.store.password}
store.mybatis.mapper-locations=classpath:/mapper/store/**/*.xml


## éç¨ç³»ç»æ°æ®åºéç½®
spring.datasource.common.url=${spring.datasource.common.url}
spring.datasource.common.username=${spring.datasource.common.username}
spring.datasource.common.password=${spring.datasource.common.password}
common.mybatis.mapper-locations=classpath:/mapper/common/**/*.xml

## ???????????
## éç¨ç³»ç»æ°æ®åºéç½® ä»åº
spring.datasource.slave.common.url=${spring.datasource.slave.common.url}
spring.datasource.slave.common.username=${spring.datasource.slave.common.username}
spring.datasource.slave.common.password=${spring.datasource.slave.common.password}
slave.common.mybatis.mapper-locations=classpath:/mapper/slave/common/**/*.xml


## è®¢åç³»ç»æ°æ®åºéç½®
spring.datasource.trade.url=${spring.datasource.trade.url}
spring.datasource.trade.username=${spring.datasource.trade.username}
spring.datasource.trade.password=${spring.datasource.trade.password}
trade.mybatis.mapper-locations=classpath:/mapper/trade/**/*.xml


## ä»åºè®¢åç³»ç»æ°æ®åºéç½®
spring.datasource.slave.trade.url=${spring.datasource.slave.trade.url}
spring.datasource.slave.trade.username=${spring.datasource.slave.trade.username}
spring.datasource.slave.trade.password=${spring.datasource.slave.trade.password}
slave.trade.mybatis.mapper-locations=classpath:/mapper/slave/trade/**/*.xml


## è½¦è¾ç³»ç»æ°æ®åºéç½®
spring.datasource.vehicle.url=${spring.datasource.vehicle.url}
spring.datasource.vehicle.username=${spring.datasource.vehicle.username}
spring.datasource.vehicle.password=${spring.datasource.vehicle.password}
vehicle.mybatis.mapper-locations=classpath:/mapper/vehicle/**/*.xml

## è½¦è¾ç³»ç»æ°æ®åºéç½®
spring.datasource.slave.vehicle.url=${spring.datasource.slave.vehicle.url}
spring.datasource.slave.vehicle.username=${spring.datasource.slave.vehicle.username}
spring.datasource.slave.vehicle.password=${spring.datasource.slave.vehicle.password}
slave.vehicle.mybatis.mapper-locations=classpath:/mapper/slave/vehicle/**/*.xml


## åå®¶ãç¨æ·ç®¡çãæéç³»ç»æ°æ®åºéç½®
spring.datasource.merchant.url=${spring.datasource.merchant.url}
spring.datasource.merchant.username=${spring.datasource.merchant.username}
spring.datasource.merchant.password=${spring.datasource.merchant.password}
merchant.mybatis.mapper-locations=classpath:/mapper/merchant/**/*.xml


## æ¯ä»ç³»ç»æ°æ®åºéç½®
spring.datasource.bill.url=${spring.datasource.bill.url}
spring.datasource.bill.username=${spring.datasource.bill.username}
spring.datasource.bill.password=${spring.datasource.bill.password}
bill.mybatis.mapper-locations=classpath:/mapper/bill/**/*.xml

## è³äºå«çæ°æ®åºæ§è½åæ°ï¼å¦è¿æ¥æ± å¤§å°ãæå¤§è¿æ¥æ¶é´ç­ï¼ï¼ç®åä½¿ç¨çæ¯ application-jdbcçä¸­çåæ°