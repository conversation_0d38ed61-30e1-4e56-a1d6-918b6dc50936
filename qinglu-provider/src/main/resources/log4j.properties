#log4j.appender.encoding=UTF-8
#log4j.rootLogger=DEBUG, stdout,ERROR, WARN, INFO, DEBUG, ALL
#
#log4j.appender.stdout=org.apache.log4j.ConsoleAppender
#log4j.appender.stdout.layout=PatternLayout
#log4j.appender.stdout.layout.ConversionPattern=%d{HH:mm:ss.SSS} [%t] %-5level ${PID} --- [%15.15t] %c{10}:%L trace_id=%X{trace_id} span_id=%X{span_id} trace_flags=%X{trace_flags} - %msg%n
#
#log4j.logger.ERROR=ERROR
#log4j.appender.ERROR=org.apache.log4j.DailyRollingFileAppender
#log4j.appender.ERROR.File=/deployments/logs/error.log
#log4j.appender.ERROR.DatePattern='.'yyyy-MM-dd
#
#log4j.appender.ERROR.Threshold=ERROR
#log4j.appender.ERROR.Append=true
#log4j.appender.ERROR.layout=org.apache.log4j.PatternLayout
#log4j.appender.ERROR.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss.SSS} %5p ${PID} --- [%15.15t] %c{10}:%L : %m%n
##-----------------------------------------------------------------------------------------------------
#log4j.logger.WARN=WARN
#log4j.appender.WARN=org.apache.log4j.DailyRollingFileAppender
#log4j.appender.WARN.File=/deployments/logs/warn.log
#log4j.appender.WARN.DatePattern='.'yyyy-MM-dd
#log4j.appender.WARN.Threshold=WARN
#log4j.appender.WARN.Append=true
#log4j.appender.WARN.layout=org.apache.log4j.PatternLayout
#log4j.appender.WARN.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss.SSS} %5p ${PID} --- [%15.15t] %c{10}:%L : %m%n
##-----------------------------------------------------------------------------------------------------
#log4j.logger.INFO=INFO
#log4j.appender.INFO=org.apache.log4j.DailyRollingFileAppender
#log4j.appender.INFO.File=/deployments/logs/info.log
#log4j.appender.INFO.DatePattern='.'yyyy-MM-dd
#log4j.appender.INFO.Threshold=INFO
#log4j.appender.INFO.Append=true
#log4j.appender.INFO.layout=org.apache.log4j.PatternLayout
#log4j.appender.INFO.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss.SSS} %5p ${PID} --- [%15.15t] %c{10}:%L : %m%n
##-----------------------------------------------------------------------------------------------------
#log4j.logger.DEBUG=DEBUG
#log4j.appender.DEBUG=org.apache.log4j.DailyRollingFileAppender
#log4j.appender.DEBUG.File=/deployments/logs/debugger.log
#log4j.appender.DEBUG.DatePattern='.'yyyy-MM-dd
#log4j.appender.DEBUG.Threshold=DEBUG
#log4j.appender.DEBUG.Append=true
#log4j.appender.DEBUG.layout=org.apache.log4j.PatternLayout
#log4j.appender.DEBUG.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss.SSS} %5p ${PID} --- [%15.15t] %c{10}:%L : %m%n
##-----------------------------------------------------------------------------------------------------
#log4j.logger.ALL=ALL
#log4j.appender.ALL=org.apache.log4j.DailyRollingFileAppender
#log4j.appender.ALL.File=/deployments/logs/all.log
#log4j.appender.ALL.DatePattern='.'yyyy-MM-dd
#log4j.appender.ALL.Threshold=ALL
#log4j.appender.ALL.Append=true
#log4j.appender.ALL.layout=org.apache.log4j.PatternLayout
#log4j.appender.ALL.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss.SSS} %5p ${PID} --- [%15.15t] %c{10}:%L : %m%n
#
##log4j.rootLogger=info
#log4j.logger.springfox=info
#log4j.logger.org.springframework=info
#log4j.logger.org.apache=info
#log4j.logger._org.springframework=info
#log4j.logger.Validator=info
#log4j.logger.io.netty=info
#log4j.logger.io.lettuce=info
#log4j.logger.org.mybatis=debug