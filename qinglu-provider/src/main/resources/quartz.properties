org.quartz.jobStore.useProperties=false
org.quartz.jobStore.tablePrefix=QRTZ_
org.quartz.jobStore.isClustered=true
org.quartz.jobStore.clusterCheckinInterval=5000
org.quartz.jobStore.class=org.quartz.impl.jdbcjobstore.JobStoreTX
org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.StdJDBCDelegate
org.quartz.scheduler.instanceId=AUTO
org.quartz.threadPool.class=org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadCount=5
org.quartz.threadPool.threadPriority=5



# ä½¿ç¨é¿éçdruidä½ä¸ºæ°æ®åºè¿æ¥æ± 
org.quartz.jobStore.dataSource=qzDS
org.quartz.dataSource.qzDS.connectionProvider.class=com.ql.rent.schedule.DruidConnectionProvider
org.quartz.dataSource.qzDS.URL=${spring.datasource.common.url}
org.quartz.dataSource.qzDS.user=${spring.datasource.common.username}
org.quartz.dataSource.qzDS.password=${spring.datasource.common.password}
org.quartz.dataSource.qzDS.driver=com.mysql.cj.jdbc.Driver
org.quartz.dataSource.qzDS.maxConnection=10
#è®¾ç½®ä¸ºâtrueâä»¥æå¼ç¾¤éåè½ãå¦ææ¨æå¤ä¸ªQuartzå®ä¾ä½¿ç¨åä¸ç»æ°æ®åºè¡¨ï¼åæ­¤å±æ§å¿é¡»è®¾ç½®ä¸ºâtrueâï¼å¦åæ¨å°éå°ç ´å
#org.quartz.jobStore.isClustered=false
