spring:
  datasource:
    name: carrent-server
    driver-class-name: com.mysql.cj.jdbc.Driver
    #以下为默认配置
    # Druid 数据源配置，继承spring.datasource.* 配置，相同则覆盖
    druid:
      initial-size: 1
      min-idle: 1                                     #最小连接池数量
      max-active: 100                                   #最大连接池数量
      max-wait: 5000                                   #获取连接时最大等待时间，单位毫秒。配置了maxWait之后，缺省启用公平锁，并发效率会有所下降，如果需要可以通过配置useUnfairLock属性为true使用非公平锁。
      time-between-eviction-runs-millis: 60000         #1分钟（1.0.14）    有两个含义：
      min-evictable-idle-time-millis: 300000           #30分钟（1.0.14）    连接保持空闲而不被驱逐的最长时间
      test-while-idle: true                            #建议配置为true，不影响性能，并且保证安全性。申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效。
      test-on-borrow: false                            #申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能。
      test-on-return: false                            #归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      validation-query: select 1                       #用来检测连接是否有效的sql，要求是一个查询语句。如果validationQuery为null，testOnBorrow、testOnReturn、testWhileIdle都不会其作用。
      validation-query-timeout: 1                      #单位：秒，检测连接是否有效的超时时间。底层调用jdbc Statement对象的void setQueryTimeout(int seconds)方法
      pool-prepared-statements: false                  #是否缓存preparedStatement，也就是PSCache。PSCache对支持游标的数据库性能提升巨大，比如说oracle。在mysql下建议关闭
      connection-init-sqls: set names utf8mb4
      max-open-prepared-statements: -1                 #要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true。在Druid中，不会存在Oracle下PSCache占用内存过多的问题，可以把这个数值配置大一些，比如说100
      #    1) Destroy线程会检测连接的间隔时间，如果连接空闲时间大于等于minEvictableIdleTimeMillis则关闭物理连接
      #    2) testWhileIdle的判断依据，详细看testWhileIdle属性的说明

      # Druid 数据源 datasource-one 配置，继承spring.datasource.druid.* 配置，相同则覆盖，不同配置具体再加
mybatis:
  mapper-locations: classpath:/mapper/**/**/*.xml
  typeAliasesPackage: com.ql.rent.entity.**
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  pagehelper:
    helperDialect: mysql
    reasonable: true
    supportMethodsArguments: true
    params: count=countSql

---
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    druid:
      initial-size: 20
      min-idle: 20