<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.VehicleIllegalOrderMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.VehicleIllegalOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="handle_store_id" jdbcType="BIGINT" property="handleStoreId" />
    <result column="license_no" jdbcType="VARCHAR" property="licenseNo" />
    <result column="vehicle_model_name" jdbcType="VARCHAR" property="vehicleModelName" />
    <result column="result_id" jdbcType="BIGINT" property="resultId" />
    <result column="illegal_time" jdbcType="TIMESTAMP" property="illegalTime" />
    <result column="illegal_city_id" jdbcType="BIGINT" property="illegalCityId" />
    <result column="illegal_addr" jdbcType="VARCHAR" property="illegalAddr" />
    <result column="illegal_action" jdbcType="VARCHAR" property="illegalAction" />
    <result column="fraction" jdbcType="INTEGER" property="fraction" />
    <result column="penalty_amount" jdbcType="BIGINT" property="penaltyAmount" />
    <result column="contract_damage_amount" jdbcType="BIGINT" property="contractDamageAmount" />
    <result column="deduction_type" jdbcType="TINYINT" property="deductionType" />
    <result column="deduction_status" jdbcType="TINYINT" property="deductionStatus" />
    <result column="refund_amount" jdbcType="BIGINT" property="refundAmount" />
    <result column="agency_fee" jdbcType="BIGINT" property="agencyFee" />
    <result column="illegal_type" jdbcType="TINYINT" property="illegalType" />
    <result column="handle_status" jdbcType="TINYINT" property="handleStatus" />
    <result column="real_handle_status" jdbcType="TINYINT" property="realHandleStatus" />
    <result column="from_source" jdbcType="TINYINT" property="fromSource" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="archive" jdbcType="VARCHAR" property="archive" />
    <result column="transfer_status" jdbcType="TINYINT" property="transferStatus" />
    <result column="transfer_id" jdbcType="BIGINT" property="transferId" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="partner_create_time" jdbcType="BIGINT" property="partnerCreateTime" />
    <result column="partner_op_time" jdbcType="BIGINT" property="partnerOpTime" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, vehicle_id, handle_store_id, license_no, vehicle_model_name, result_id, 
    illegal_time, illegal_city_id, illegal_addr, illegal_action, fraction, penalty_amount, 
    contract_damage_amount, deduction_type, deduction_status, refund_amount, agency_fee, 
    illegal_type, handle_status, real_handle_status, from_source, source, archive, transfer_status, 
    transfer_id, merchant_id, deleted, last_ver, op_user_id, partner_create_time, partner_op_time, 
    create_time, op_time, extra
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.VehicleIllegalOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_illegal_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_illegal_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_illegal_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.VehicleIllegalOrderExample">
    delete from vehicle_illegal_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.VehicleIllegalOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_illegal_order (order_id, vehicle_id, handle_store_id, 
      license_no, vehicle_model_name, result_id, 
      illegal_time, illegal_city_id, illegal_addr, 
      illegal_action, fraction, penalty_amount, 
      contract_damage_amount, deduction_type, deduction_status, 
      refund_amount, agency_fee, illegal_type, 
      handle_status, real_handle_status, from_source, 
      source, archive, transfer_status, 
      transfer_id, merchant_id, deleted, 
      last_ver, op_user_id, partner_create_time, 
      partner_op_time, create_time, op_time, 
      extra)
    values (#{orderId,jdbcType=BIGINT}, #{vehicleId,jdbcType=BIGINT}, #{handleStoreId,jdbcType=BIGINT}, 
      #{licenseNo,jdbcType=VARCHAR}, #{vehicleModelName,jdbcType=VARCHAR}, #{resultId,jdbcType=BIGINT}, 
      #{illegalTime,jdbcType=TIMESTAMP}, #{illegalCityId,jdbcType=BIGINT}, #{illegalAddr,jdbcType=VARCHAR}, 
      #{illegalAction,jdbcType=VARCHAR}, #{fraction,jdbcType=INTEGER}, #{penaltyAmount,jdbcType=BIGINT}, 
      #{contractDamageAmount,jdbcType=BIGINT}, #{deductionType,jdbcType=TINYINT}, #{deductionStatus,jdbcType=TINYINT}, 
      #{refundAmount,jdbcType=BIGINT}, #{agencyFee,jdbcType=BIGINT}, #{illegalType,jdbcType=TINYINT}, 
      #{handleStatus,jdbcType=TINYINT}, #{realHandleStatus,jdbcType=TINYINT}, #{fromSource,jdbcType=TINYINT}, 
      #{source,jdbcType=TINYINT}, #{archive,jdbcType=VARCHAR}, #{transferStatus,jdbcType=TINYINT}, 
      #{transferId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT}, #{deleted,jdbcType=TINYINT}, 
      #{lastVer,jdbcType=INTEGER}, #{opUserId,jdbcType=BIGINT}, #{partnerCreateTime,jdbcType=BIGINT}, 
      #{partnerOpTime,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, 
      #{extra,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.VehicleIllegalOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_illegal_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="handleStoreId != null">
        handle_store_id,
      </if>
      <if test="licenseNo != null">
        license_no,
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name,
      </if>
      <if test="resultId != null">
        result_id,
      </if>
      <if test="illegalTime != null">
        illegal_time,
      </if>
      <if test="illegalCityId != null">
        illegal_city_id,
      </if>
      <if test="illegalAddr != null">
        illegal_addr,
      </if>
      <if test="illegalAction != null">
        illegal_action,
      </if>
      <if test="fraction != null">
        fraction,
      </if>
      <if test="penaltyAmount != null">
        penalty_amount,
      </if>
      <if test="contractDamageAmount != null">
        contract_damage_amount,
      </if>
      <if test="deductionType != null">
        deduction_type,
      </if>
      <if test="deductionStatus != null">
        deduction_status,
      </if>
      <if test="refundAmount != null">
        refund_amount,
      </if>
      <if test="agencyFee != null">
        agency_fee,
      </if>
      <if test="illegalType != null">
        illegal_type,
      </if>
      <if test="handleStatus != null">
        handle_status,
      </if>
      <if test="realHandleStatus != null">
        real_handle_status,
      </if>
      <if test="fromSource != null">
        from_source,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="archive != null">
        archive,
      </if>
      <if test="transferStatus != null">
        transfer_status,
      </if>
      <if test="transferId != null">
        transfer_id,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="partnerCreateTime != null">
        partner_create_time,
      </if>
      <if test="partnerOpTime != null">
        partner_op_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="extra != null">
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="handleStoreId != null">
        #{handleStoreId,jdbcType=BIGINT},
      </if>
      <if test="licenseNo != null">
        #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelName != null">
        #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="resultId != null">
        #{resultId,jdbcType=BIGINT},
      </if>
      <if test="illegalTime != null">
        #{illegalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="illegalCityId != null">
        #{illegalCityId,jdbcType=BIGINT},
      </if>
      <if test="illegalAddr != null">
        #{illegalAddr,jdbcType=VARCHAR},
      </if>
      <if test="illegalAction != null">
        #{illegalAction,jdbcType=VARCHAR},
      </if>
      <if test="fraction != null">
        #{fraction,jdbcType=INTEGER},
      </if>
      <if test="penaltyAmount != null">
        #{penaltyAmount,jdbcType=BIGINT},
      </if>
      <if test="contractDamageAmount != null">
        #{contractDamageAmount,jdbcType=BIGINT},
      </if>
      <if test="deductionType != null">
        #{deductionType,jdbcType=TINYINT},
      </if>
      <if test="deductionStatus != null">
        #{deductionStatus,jdbcType=TINYINT},
      </if>
      <if test="refundAmount != null">
        #{refundAmount,jdbcType=BIGINT},
      </if>
      <if test="agencyFee != null">
        #{agencyFee,jdbcType=BIGINT},
      </if>
      <if test="illegalType != null">
        #{illegalType,jdbcType=TINYINT},
      </if>
      <if test="handleStatus != null">
        #{handleStatus,jdbcType=TINYINT},
      </if>
      <if test="realHandleStatus != null">
        #{realHandleStatus,jdbcType=TINYINT},
      </if>
      <if test="fromSource != null">
        #{fromSource,jdbcType=TINYINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="archive != null">
        #{archive,jdbcType=VARCHAR},
      </if>
      <if test="transferStatus != null">
        #{transferStatus,jdbcType=TINYINT},
      </if>
      <if test="transferId != null">
        #{transferId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="partnerCreateTime != null">
        #{partnerCreateTime,jdbcType=BIGINT},
      </if>
      <if test="partnerOpTime != null">
        #{partnerOpTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.VehicleIllegalOrderExample" resultType="java.lang.Long">
    select count(*) from vehicle_illegal_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_illegal_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleId != null">
        vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="record.handleStoreId != null">
        handle_store_id = #{record.handleStoreId,jdbcType=BIGINT},
      </if>
      <if test="record.licenseNo != null">
        license_no = #{record.licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleModelName != null">
        vehicle_model_name = #{record.vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="record.resultId != null">
        result_id = #{record.resultId,jdbcType=BIGINT},
      </if>
      <if test="record.illegalTime != null">
        illegal_time = #{record.illegalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.illegalCityId != null">
        illegal_city_id = #{record.illegalCityId,jdbcType=BIGINT},
      </if>
      <if test="record.illegalAddr != null">
        illegal_addr = #{record.illegalAddr,jdbcType=VARCHAR},
      </if>
      <if test="record.illegalAction != null">
        illegal_action = #{record.illegalAction,jdbcType=VARCHAR},
      </if>
      <if test="record.fraction != null">
        fraction = #{record.fraction,jdbcType=INTEGER},
      </if>
      <if test="record.penaltyAmount != null">
        penalty_amount = #{record.penaltyAmount,jdbcType=BIGINT},
      </if>
      <if test="record.contractDamageAmount != null">
        contract_damage_amount = #{record.contractDamageAmount,jdbcType=BIGINT},
      </if>
      <if test="record.deductionType != null">
        deduction_type = #{record.deductionType,jdbcType=TINYINT},
      </if>
      <if test="record.deductionStatus != null">
        deduction_status = #{record.deductionStatus,jdbcType=TINYINT},
      </if>
      <if test="record.refundAmount != null">
        refund_amount = #{record.refundAmount,jdbcType=BIGINT},
      </if>
      <if test="record.agencyFee != null">
        agency_fee = #{record.agencyFee,jdbcType=BIGINT},
      </if>
      <if test="record.illegalType != null">
        illegal_type = #{record.illegalType,jdbcType=TINYINT},
      </if>
      <if test="record.handleStatus != null">
        handle_status = #{record.handleStatus,jdbcType=TINYINT},
      </if>
      <if test="record.realHandleStatus != null">
        real_handle_status = #{record.realHandleStatus,jdbcType=TINYINT},
      </if>
      <if test="record.fromSource != null">
        from_source = #{record.fromSource,jdbcType=TINYINT},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=TINYINT},
      </if>
      <if test="record.archive != null">
        archive = #{record.archive,jdbcType=VARCHAR},
      </if>
      <if test="record.transferStatus != null">
        transfer_status = #{record.transferStatus,jdbcType=TINYINT},
      </if>
      <if test="record.transferId != null">
        transfer_id = #{record.transferId,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.partnerCreateTime != null">
        partner_create_time = #{record.partnerCreateTime,jdbcType=BIGINT},
      </if>
      <if test="record.partnerOpTime != null">
        partner_op_time = #{record.partnerOpTime,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_illegal_order
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      handle_store_id = #{record.handleStoreId,jdbcType=BIGINT},
      license_no = #{record.licenseNo,jdbcType=VARCHAR},
      vehicle_model_name = #{record.vehicleModelName,jdbcType=VARCHAR},
      result_id = #{record.resultId,jdbcType=BIGINT},
      illegal_time = #{record.illegalTime,jdbcType=TIMESTAMP},
      illegal_city_id = #{record.illegalCityId,jdbcType=BIGINT},
      illegal_addr = #{record.illegalAddr,jdbcType=VARCHAR},
      illegal_action = #{record.illegalAction,jdbcType=VARCHAR},
      fraction = #{record.fraction,jdbcType=INTEGER},
      penalty_amount = #{record.penaltyAmount,jdbcType=BIGINT},
      contract_damage_amount = #{record.contractDamageAmount,jdbcType=BIGINT},
      deduction_type = #{record.deductionType,jdbcType=TINYINT},
      deduction_status = #{record.deductionStatus,jdbcType=TINYINT},
      refund_amount = #{record.refundAmount,jdbcType=BIGINT},
      agency_fee = #{record.agencyFee,jdbcType=BIGINT},
      illegal_type = #{record.illegalType,jdbcType=TINYINT},
      handle_status = #{record.handleStatus,jdbcType=TINYINT},
      real_handle_status = #{record.realHandleStatus,jdbcType=TINYINT},
      from_source = #{record.fromSource,jdbcType=TINYINT},
      source = #{record.source,jdbcType=TINYINT},
      archive = #{record.archive,jdbcType=VARCHAR},
      transfer_status = #{record.transferStatus,jdbcType=TINYINT},
      transfer_id = #{record.transferId,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      deleted = #{record.deleted,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      partner_create_time = #{record.partnerCreateTime,jdbcType=BIGINT},
      partner_op_time = #{record.partnerOpTime,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      extra = #{record.extra,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.VehicleIllegalOrder">
    update vehicle_illegal_order
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="handleStoreId != null">
        handle_store_id = #{handleStoreId,jdbcType=BIGINT},
      </if>
      <if test="licenseNo != null">
        license_no = #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="resultId != null">
        result_id = #{resultId,jdbcType=BIGINT},
      </if>
      <if test="illegalTime != null">
        illegal_time = #{illegalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="illegalCityId != null">
        illegal_city_id = #{illegalCityId,jdbcType=BIGINT},
      </if>
      <if test="illegalAddr != null">
        illegal_addr = #{illegalAddr,jdbcType=VARCHAR},
      </if>
      <if test="illegalAction != null">
        illegal_action = #{illegalAction,jdbcType=VARCHAR},
      </if>
      <if test="fraction != null">
        fraction = #{fraction,jdbcType=INTEGER},
      </if>
      <if test="penaltyAmount != null">
        penalty_amount = #{penaltyAmount,jdbcType=BIGINT},
      </if>
      <if test="contractDamageAmount != null">
        contract_damage_amount = #{contractDamageAmount,jdbcType=BIGINT},
      </if>
      <if test="deductionType != null">
        deduction_type = #{deductionType,jdbcType=TINYINT},
      </if>
      <if test="deductionStatus != null">
        deduction_status = #{deductionStatus,jdbcType=TINYINT},
      </if>
      <if test="refundAmount != null">
        refund_amount = #{refundAmount,jdbcType=BIGINT},
      </if>
      <if test="agencyFee != null">
        agency_fee = #{agencyFee,jdbcType=BIGINT},
      </if>
      <if test="illegalType != null">
        illegal_type = #{illegalType,jdbcType=TINYINT},
      </if>
      <if test="handleStatus != null">
        handle_status = #{handleStatus,jdbcType=TINYINT},
      </if>
      <if test="realHandleStatus != null">
        real_handle_status = #{realHandleStatus,jdbcType=TINYINT},
      </if>
      <if test="fromSource != null">
        from_source = #{fromSource,jdbcType=TINYINT},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=TINYINT},
      </if>
      <if test="archive != null">
        archive = #{archive,jdbcType=VARCHAR},
      </if>
      <if test="transferStatus != null">
        transfer_status = #{transferStatus,jdbcType=TINYINT},
      </if>
      <if test="transferId != null">
        transfer_id = #{transferId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="partnerCreateTime != null">
        partner_create_time = #{partnerCreateTime,jdbcType=BIGINT},
      </if>
      <if test="partnerOpTime != null">
        partner_op_time = #{partnerOpTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.VehicleIllegalOrder">
    update vehicle_illegal_order
    set order_id = #{orderId,jdbcType=BIGINT},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      handle_store_id = #{handleStoreId,jdbcType=BIGINT},
      license_no = #{licenseNo,jdbcType=VARCHAR},
      vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      result_id = #{resultId,jdbcType=BIGINT},
      illegal_time = #{illegalTime,jdbcType=TIMESTAMP},
      illegal_city_id = #{illegalCityId,jdbcType=BIGINT},
      illegal_addr = #{illegalAddr,jdbcType=VARCHAR},
      illegal_action = #{illegalAction,jdbcType=VARCHAR},
      fraction = #{fraction,jdbcType=INTEGER},
      penalty_amount = #{penaltyAmount,jdbcType=BIGINT},
      contract_damage_amount = #{contractDamageAmount,jdbcType=BIGINT},
      deduction_type = #{deductionType,jdbcType=TINYINT},
      deduction_status = #{deductionStatus,jdbcType=TINYINT},
      refund_amount = #{refundAmount,jdbcType=BIGINT},
      agency_fee = #{agencyFee,jdbcType=BIGINT},
      illegal_type = #{illegalType,jdbcType=TINYINT},
      handle_status = #{handleStatus,jdbcType=TINYINT},
      real_handle_status = #{realHandleStatus,jdbcType=TINYINT},
      from_source = #{fromSource,jdbcType=TINYINT},
      source = #{source,jdbcType=TINYINT},
      archive = #{archive,jdbcType=VARCHAR},
      transfer_status = #{transferStatus,jdbcType=TINYINT},
      transfer_id = #{transferId,jdbcType=BIGINT},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      partner_create_time = #{partnerCreateTime,jdbcType=BIGINT},
      partner_op_time = #{partnerOpTime,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      extra = #{extra,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_illegal_order
    (order_id, vehicle_id, handle_store_id, license_no, vehicle_model_name, result_id, 
      illegal_time, illegal_city_id, illegal_addr, illegal_action, fraction, penalty_amount, 
      contract_damage_amount, deduction_type, deduction_status, refund_amount, agency_fee, 
      illegal_type, handle_status, real_handle_status, from_source, source, archive, 
      transfer_status, transfer_id, merchant_id, deleted, last_ver, op_user_id, partner_create_time, 
      partner_op_time, create_time, op_time, extra)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=BIGINT}, #{item.vehicleId,jdbcType=BIGINT}, #{item.handleStoreId,jdbcType=BIGINT}, 
        #{item.licenseNo,jdbcType=VARCHAR}, #{item.vehicleModelName,jdbcType=VARCHAR}, 
        #{item.resultId,jdbcType=BIGINT}, #{item.illegalTime,jdbcType=TIMESTAMP}, #{item.illegalCityId,jdbcType=BIGINT}, 
        #{item.illegalAddr,jdbcType=VARCHAR}, #{item.illegalAction,jdbcType=VARCHAR}, #{item.fraction,jdbcType=INTEGER}, 
        #{item.penaltyAmount,jdbcType=BIGINT}, #{item.contractDamageAmount,jdbcType=BIGINT}, 
        #{item.deductionType,jdbcType=TINYINT}, #{item.deductionStatus,jdbcType=TINYINT}, 
        #{item.refundAmount,jdbcType=BIGINT}, #{item.agencyFee,jdbcType=BIGINT}, #{item.illegalType,jdbcType=TINYINT}, 
        #{item.handleStatus,jdbcType=TINYINT}, #{item.realHandleStatus,jdbcType=TINYINT}, 
        #{item.fromSource,jdbcType=TINYINT}, #{item.source,jdbcType=TINYINT}, #{item.archive,jdbcType=VARCHAR}, 
        #{item.transferStatus,jdbcType=TINYINT}, #{item.transferId,jdbcType=BIGINT}, #{item.merchantId,jdbcType=BIGINT}, 
        #{item.deleted,jdbcType=TINYINT}, #{item.lastVer,jdbcType=INTEGER}, #{item.opUserId,jdbcType=BIGINT}, 
        #{item.partnerCreateTime,jdbcType=BIGINT}, #{item.partnerOpTime,jdbcType=BIGINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, #{item.extra,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_illegal_order (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_id'.toString() == column.value">
          #{item.vehicleId,jdbcType=BIGINT}
        </if>
        <if test="'handle_store_id'.toString() == column.value">
          #{item.handleStoreId,jdbcType=BIGINT}
        </if>
        <if test="'license_no'.toString() == column.value">
          #{item.licenseNo,jdbcType=VARCHAR}
        </if>
        <if test="'vehicle_model_name'.toString() == column.value">
          #{item.vehicleModelName,jdbcType=VARCHAR}
        </if>
        <if test="'result_id'.toString() == column.value">
          #{item.resultId,jdbcType=BIGINT}
        </if>
        <if test="'illegal_time'.toString() == column.value">
          #{item.illegalTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'illegal_city_id'.toString() == column.value">
          #{item.illegalCityId,jdbcType=BIGINT}
        </if>
        <if test="'illegal_addr'.toString() == column.value">
          #{item.illegalAddr,jdbcType=VARCHAR}
        </if>
        <if test="'illegal_action'.toString() == column.value">
          #{item.illegalAction,jdbcType=VARCHAR}
        </if>
        <if test="'fraction'.toString() == column.value">
          #{item.fraction,jdbcType=INTEGER}
        </if>
        <if test="'penalty_amount'.toString() == column.value">
          #{item.penaltyAmount,jdbcType=BIGINT}
        </if>
        <if test="'contract_damage_amount'.toString() == column.value">
          #{item.contractDamageAmount,jdbcType=BIGINT}
        </if>
        <if test="'deduction_type'.toString() == column.value">
          #{item.deductionType,jdbcType=TINYINT}
        </if>
        <if test="'deduction_status'.toString() == column.value">
          #{item.deductionStatus,jdbcType=TINYINT}
        </if>
        <if test="'refund_amount'.toString() == column.value">
          #{item.refundAmount,jdbcType=BIGINT}
        </if>
        <if test="'agency_fee'.toString() == column.value">
          #{item.agencyFee,jdbcType=BIGINT}
        </if>
        <if test="'illegal_type'.toString() == column.value">
          #{item.illegalType,jdbcType=TINYINT}
        </if>
        <if test="'handle_status'.toString() == column.value">
          #{item.handleStatus,jdbcType=TINYINT}
        </if>
        <if test="'real_handle_status'.toString() == column.value">
          #{item.realHandleStatus,jdbcType=TINYINT}
        </if>
        <if test="'from_source'.toString() == column.value">
          #{item.fromSource,jdbcType=TINYINT}
        </if>
        <if test="'source'.toString() == column.value">
          #{item.source,jdbcType=TINYINT}
        </if>
        <if test="'archive'.toString() == column.value">
          #{item.archive,jdbcType=VARCHAR}
        </if>
        <if test="'transfer_status'.toString() == column.value">
          #{item.transferStatus,jdbcType=TINYINT}
        </if>
        <if test="'transfer_id'.toString() == column.value">
          #{item.transferId,jdbcType=BIGINT}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'partner_create_time'.toString() == column.value">
          #{item.partnerCreateTime,jdbcType=BIGINT}
        </if>
        <if test="'partner_op_time'.toString() == column.value">
          #{item.partnerOpTime,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'extra'.toString() == column.value">
          #{item.extra,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>