<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.PickDriverExpenseMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.PickDriverExpense">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="expense_type" jdbcType="TINYINT" property="expenseType" />
    <result column="expense_amount" jdbcType="INTEGER" property="expenseAmount" />
    <result column="expense_status" jdbcType="TINYINT" property="expenseStatus" />
    <result column="expense_user_id" jdbcType="BIGINT" property="expenseUserId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, merchant_id, expense_type, expense_amount, expense_status, expense_user_id, 
    remark, deleted, create_time, op_time, create_user_id, op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.PickDriverExpenseExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from pick_driver_expense
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pick_driver_expense
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pick_driver_expense
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.PickDriverExpenseExample">
    delete from pick_driver_expense
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.PickDriverExpense">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pick_driver_expense (order_id, merchant_id, expense_type, 
      expense_amount, expense_status, expense_user_id, 
      remark, deleted, create_time, 
      op_time, create_user_id, op_user_id
      )
    values (#{orderId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT}, #{expenseType,jdbcType=TINYINT}, 
      #{expenseAmount,jdbcType=INTEGER}, #{expenseStatus,jdbcType=TINYINT}, #{expenseUserId,jdbcType=BIGINT}, 
      #{remark,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{createUserId,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.PickDriverExpense">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pick_driver_expense
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="expenseType != null">
        expense_type,
      </if>
      <if test="expenseAmount != null">
        expense_amount,
      </if>
      <if test="expenseStatus != null">
        expense_status,
      </if>
      <if test="expenseUserId != null">
        expense_user_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="expenseType != null">
        #{expenseType,jdbcType=TINYINT},
      </if>
      <if test="expenseAmount != null">
        #{expenseAmount,jdbcType=INTEGER},
      </if>
      <if test="expenseStatus != null">
        #{expenseStatus,jdbcType=TINYINT},
      </if>
      <if test="expenseUserId != null">
        #{expenseUserId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.PickDriverExpenseExample" resultType="java.lang.Long">
    select count(*) from pick_driver_expense
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update pick_driver_expense
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.expenseType != null">
        expense_type = #{record.expenseType,jdbcType=TINYINT},
      </if>
      <if test="record.expenseAmount != null">
        expense_amount = #{record.expenseAmount,jdbcType=INTEGER},
      </if>
      <if test="record.expenseStatus != null">
        expense_status = #{record.expenseStatus,jdbcType=TINYINT},
      </if>
      <if test="record.expenseUserId != null">
        expense_user_id = #{record.expenseUserId,jdbcType=BIGINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update pick_driver_expense
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      expense_type = #{record.expenseType,jdbcType=TINYINT},
      expense_amount = #{record.expenseAmount,jdbcType=INTEGER},
      expense_status = #{record.expenseStatus,jdbcType=TINYINT},
      expense_user_id = #{record.expenseUserId,jdbcType=BIGINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      create_user_id = #{record.createUserId,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.PickDriverExpense">
    update pick_driver_expense
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="expenseType != null">
        expense_type = #{expenseType,jdbcType=TINYINT},
      </if>
      <if test="expenseAmount != null">
        expense_amount = #{expenseAmount,jdbcType=INTEGER},
      </if>
      <if test="expenseStatus != null">
        expense_status = #{expenseStatus,jdbcType=TINYINT},
      </if>
      <if test="expenseUserId != null">
        expense_user_id = #{expenseUserId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.PickDriverExpense">
    update pick_driver_expense
    set order_id = #{orderId,jdbcType=BIGINT},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      expense_type = #{expenseType,jdbcType=TINYINT},
      expense_amount = #{expenseAmount,jdbcType=INTEGER},
      expense_status = #{expenseStatus,jdbcType=TINYINT},
      expense_user_id = #{expenseUserId,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      create_user_id = #{createUserId,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into pick_driver_expense
    (order_id, merchant_id, expense_type, expense_amount, expense_status, expense_user_id, 
      remark, deleted, create_time, op_time, create_user_id, op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=BIGINT}, #{item.merchantId,jdbcType=BIGINT}, #{item.expenseType,jdbcType=TINYINT}, 
        #{item.expenseAmount,jdbcType=INTEGER}, #{item.expenseStatus,jdbcType=TINYINT}, 
        #{item.expenseUserId,jdbcType=BIGINT}, #{item.remark,jdbcType=VARCHAR}, #{item.deleted,jdbcType=TINYINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, #{item.createUserId,jdbcType=BIGINT}, 
        #{item.opUserId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into pick_driver_expense (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'expense_type'.toString() == column.value">
          #{item.expenseType,jdbcType=TINYINT}
        </if>
        <if test="'expense_amount'.toString() == column.value">
          #{item.expenseAmount,jdbcType=INTEGER}
        </if>
        <if test="'expense_status'.toString() == column.value">
          #{item.expenseStatus,jdbcType=TINYINT}
        </if>
        <if test="'expense_user_id'.toString() == column.value">
          #{item.expenseUserId,jdbcType=BIGINT}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'create_user_id'.toString() == column.value">
          #{item.createUserId,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>