<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.ThirdAllocationTaskMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.ThirdAllocationTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_type" jdbcType="TINYINT" property="bizType" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="allocation_time" jdbcType="VARCHAR" property="allocationTime" />
    <result column="trans_order_no" jdbcType="VARCHAR" property="transOrderNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
  </resultMap>
  <sql id="Base_Column_List">
    id, biz_type, total_amount, status, remark, op_user_id, create_time, update_time, 
    allocation_time, trans_order_no, order_no, error_msg
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from third_allocation_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.ThirdAllocationTask">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into third_allocation_task (biz_type, total_amount, status, 
      remark, op_user_id, create_time, update_time, allocation_time, 
      trans_order_no, order_no, error_msg)
    values (#{bizType,jdbcType=TINYINT}, #{totalAmount,jdbcType=DECIMAL}, #{status,jdbcType=TINYINT}, 
      #{remark,jdbcType=VARCHAR}, #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT}, #{allocationTime,jdbcType=VARCHAR}, 
      #{transOrderNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{errorMsg,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.ThirdAllocationTask">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into third_allocation_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="allocationTime != null">
        allocation_time,
      </if>
      <if test="transOrderNo != null">
        trans_order_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="errorMsg != null">
        error_msg,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizType != null">
        #{bizType,jdbcType=TINYINT},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="allocationTime != null">
        #{allocationTime,jdbcType=VARCHAR},
      </if>
      <if test="transOrderNo != null">
        #{transOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        #{errorMsg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.ThirdAllocationTask">
    update third_allocation_task
    <set>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=TINYINT},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="allocationTime != null">
        allocation_time = #{allocationTime,jdbcType=VARCHAR},
      </if>
      <if test="transOrderNo != null">
        trans_order_no = #{transOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.ThirdAllocationTask">
    update third_allocation_task
    set biz_type = #{bizType,jdbcType=TINYINT},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      status = #{status,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      allocation_time = #{allocationTime,jdbcType=VARCHAR},
      trans_order_no = #{transOrderNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      error_msg = #{errorMsg,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into third_allocation_task
    (biz_type, total_amount, status, remark, op_user_id, create_time, update_time, 
      allocation_time, trans_order_no, order_no, error_msg)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bizType,jdbcType=TINYINT}, #{item.totalAmount,jdbcType=DECIMAL}, 
        #{item.status,jdbcType=TINYINT}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.opUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.updateTime,jdbcType=BIGINT}, #{item.allocationTime,jdbcType=VARCHAR}, 
        #{item.transOrderNo,jdbcType=VARCHAR}, #{item.orderNo,jdbcType=VARCHAR}, 
        #{item.errorMsg,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <select id="getAllByStatus" resultMap="BaseResultMap" parameterType="java.lang.Byte">
    select
    <include refid="Base_Column_List"/>
    from third_allocation_task 
    where status = #{status,jdbcType=TINYINT}
  </select>
  <!-- 根据商户订单号查询，可能返回多条记录 -->
  <select id="selectByTransOrderNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List"/>
    from third_allocation_task
    where trans_order_no = #{transOrderNo,jdbcType=VARCHAR}
  </select>
  <select id="selectByOrderNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List"/>
    from third_allocation_task
    where order_no = #{orderNo,jdbcType=VARCHAR}
  </select>
  
  <!-- 分页查询分账任务 -->
  <select id="selectPage" resultMap="BaseResultMap" parameterType="com.ql.rent.param.trade.ThirdAllocationTaskQueryParam">
    select
      <include refid="Base_Column_List"/>
    from third_allocation_task
    <where>
    </where>
    order by id desc
    limit #{param.offset}, #{param.pageSize}
  </select>

  <select id="countPage" resultType="long" parameterType="com.ql.rent.param.trade.ThirdAllocationTaskQueryParam">
    select count(1)
    from third_allocation_task
    <where>
    </where>
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from third_allocation_task where id = #{id,jdbcType=BIGINT}
  </delete>
</mapper> 