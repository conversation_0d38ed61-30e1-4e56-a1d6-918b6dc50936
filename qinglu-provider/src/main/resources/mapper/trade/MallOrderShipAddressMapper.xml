<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.MallOrderShipAddressMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.MallOrderShipAddress">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="item_type" jdbcType="TINYINT" property="itemType" />
    <result column="receive_user_name" jdbcType="VARCHAR" property="receiveUserName" />
    <result column="receive_address" jdbcType="VARCHAR" property="receiveAddress" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, order_no, item_type, receive_user_name, receive_address, mobile, 
    op_user_id, create_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.MallOrderShipAddressExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from mall_order_ship_address
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mall_order_ship_address
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mall_order_ship_address
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.MallOrderShipAddressExample">
    delete from mall_order_ship_address
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.MallOrderShipAddress">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mall_order_ship_address (merchant_id, order_no, item_type, 
      receive_user_name, receive_address, mobile, 
      op_user_id, create_time)
    values (#{merchantId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{itemType,jdbcType=TINYINT}, 
      #{receiveUserName,jdbcType=VARCHAR}, #{receiveAddress,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, 
      #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.MallOrderShipAddress">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mall_order_ship_address
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="itemType != null">
        item_type,
      </if>
      <if test="receiveUserName != null">
        receive_user_name,
      </if>
      <if test="receiveAddress != null">
        receive_address,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="itemType != null">
        #{itemType,jdbcType=TINYINT},
      </if>
      <if test="receiveUserName != null">
        #{receiveUserName,jdbcType=VARCHAR},
      </if>
      <if test="receiveAddress != null">
        #{receiveAddress,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.MallOrderShipAddressExample" resultType="java.lang.Long">
    select count(*) from mall_order_ship_address
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mall_order_ship_address
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.itemType != null">
        item_type = #{record.itemType,jdbcType=TINYINT},
      </if>
      <if test="record.receiveUserName != null">
        receive_user_name = #{record.receiveUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveAddress != null">
        receive_address = #{record.receiveAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.mobile != null">
        mobile = #{record.mobile,jdbcType=VARCHAR},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mall_order_ship_address
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      item_type = #{record.itemType,jdbcType=TINYINT},
      receive_user_name = #{record.receiveUserName,jdbcType=VARCHAR},
      receive_address = #{record.receiveAddress,jdbcType=VARCHAR},
      mobile = #{record.mobile,jdbcType=VARCHAR},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.MallOrderShipAddress">
    update mall_order_ship_address
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="itemType != null">
        item_type = #{itemType,jdbcType=TINYINT},
      </if>
      <if test="receiveUserName != null">
        receive_user_name = #{receiveUserName,jdbcType=VARCHAR},
      </if>
      <if test="receiveAddress != null">
        receive_address = #{receiveAddress,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.MallOrderShipAddress">
    update mall_order_ship_address
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      item_type = #{itemType,jdbcType=TINYINT},
      receive_user_name = #{receiveUserName,jdbcType=VARCHAR},
      receive_address = #{receiveAddress,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into mall_order_ship_address
    (merchant_id, order_no, item_type, receive_user_name, receive_address, mobile, op_user_id, 
      create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.orderNo,jdbcType=VARCHAR}, #{item.itemType,jdbcType=TINYINT}, 
        #{item.receiveUserName,jdbcType=VARCHAR}, #{item.receiveAddress,jdbcType=VARCHAR}, 
        #{item.mobile,jdbcType=VARCHAR}, #{item.opUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into mall_order_ship_address (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'order_no'.toString() == column.value">
          #{item.orderNo,jdbcType=VARCHAR}
        </if>
        <if test="'item_type'.toString() == column.value">
          #{item.itemType,jdbcType=TINYINT}
        </if>
        <if test="'receive_user_name'.toString() == column.value">
          #{item.receiveUserName,jdbcType=VARCHAR}
        </if>
        <if test="'receive_address'.toString() == column.value">
          #{item.receiveAddress,jdbcType=VARCHAR}
        </if>
        <if test="'mobile'.toString() == column.value">
          #{item.mobile,jdbcType=VARCHAR}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>