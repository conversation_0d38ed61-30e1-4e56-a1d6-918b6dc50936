<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.InvoiceBillRelationMapper">
    <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.InvoiceBillRelation">
        <!--@mbg.generated-->
        <!--@Table invoice_bill_relation-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo"/>
        <result column="bill_no" jdbcType="BIGINT" property="billNo"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="op_time" jdbcType="BIGINT" property="opTime"/>
        <result column="last_ver" jdbcType="TINYINT" property="lastVer"/>
        <result column="bill_amount" jdbcType="BIGINT" property="billAmount"/>
        <result column="bill_invoice_status" jdbcType="TINYINT" property="billInvoiceStatus"/>
        <result column="invoice_detail_id" jdbcType="BIGINT" property="invoiceDetailId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        invoice_no,
        bill_no,
        create_time,
        op_time,
        last_ver,
        bill_amount,
        bill_invoice_status,
        invoice_detail_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from invoice_bill_relation
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from invoice_bill_relation
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ql.rent.entity.trade.InvoiceBillRelation"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into invoice_bill_relation (invoice_no, bill_no, create_time,
                                           op_time, last_ver, bill_amount,
                                           bill_invoice_status, invoice_detail_id)
        values (#{invoiceNo,jdbcType=VARCHAR}, #{billNo,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT},
                #{opTime,jdbcType=BIGINT}, #{lastVer,jdbcType=TINYINT}, #{billAmount,jdbcType=BIGINT},
                #{billInvoiceStatus,jdbcType=TINYINT}, #{invoiceDetailId,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.ql.rent.entity.trade.InvoiceBillRelation" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into invoice_bill_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoiceNo != null">
                invoice_no,
            </if>
            <if test="billNo != null">
                bill_no,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="opTime != null">
                op_time,
            </if>
            <if test="lastVer != null">
                last_ver,
            </if>
            <if test="billAmount != null">
                bill_amount,
            </if>
            <if test="billInvoiceStatus != null">
                bill_invoice_status,
            </if>
            <if test="invoiceDetailId != null">
                invoice_detail_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="invoiceNo != null">
                #{invoiceNo,jdbcType=VARCHAR},
            </if>
            <if test="billNo != null">
                #{billNo,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="opTime != null">
                #{opTime,jdbcType=BIGINT},
            </if>
            <if test="lastVer != null">
                #{lastVer,jdbcType=TINYINT},
            </if>
            <if test="billAmount != null">
                #{billAmount,jdbcType=BIGINT},
            </if>
            <if test="billInvoiceStatus != null">
                #{billInvoiceStatus,jdbcType=TINYINT},
            </if>
            <if test="invoiceDetailId != null">
                #{invoiceDetailId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.InvoiceBillRelation">
        <!--@mbg.generated-->
        update invoice_bill_relation
        <set>
            <if test="invoiceNo != null">
                invoice_no = #{invoiceNo,jdbcType=VARCHAR},
            </if>
            <if test="billNo != null">
                bill_no = #{billNo,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="opTime != null">
                op_time = #{opTime,jdbcType=BIGINT},
            </if>
            <if test="lastVer != null">
                last_ver = #{lastVer,jdbcType=TINYINT},
            </if>
            <if test="billAmount != null">
                bill_amount = #{billAmount,jdbcType=BIGINT},
            </if>
            <if test="billInvoiceStatus != null">
                bill_invoice_status = #{billInvoiceStatus,jdbcType=TINYINT},
            </if>
            <if test="invoiceDetailId != null">
                invoice_detail_id = #{invoiceDetailId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByDetailSelective" parameterType="com.ql.rent.entity.trade.InvoiceBillRelation">
        <!--@mbg.generated-->
        update invoice_bill_relation
        <set>
            <if test="invoiceNo != null">
                invoice_no = #{invoiceNo,jdbcType=VARCHAR},
            </if>
            <if test="billNo != null">
                bill_no = #{billNo,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="opTime != null">
                op_time = #{opTime,jdbcType=BIGINT},
            </if>
            last_ver = last_ver + 1,
            <if test="billAmount != null">
                bill_amount = #{billAmount,jdbcType=BIGINT},
            </if>
            <if test="billInvoiceStatus != null">
                bill_invoice_status = #{billInvoiceStatus,jdbcType=TINYINT},
            </if>
            <if test="invoiceDetailId != null">
                invoice_detail_id = #{invoiceDetailId,jdbcType=BIGINT},
            </if>
        </set>
        where invoice_detail_id = #{invoiceDetailId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.InvoiceBillRelation">
        <!--@mbg.generated-->
        update invoice_bill_relation
        set invoice_no          = #{invoiceNo,jdbcType=VARCHAR},
            bill_no             = #{billNo,jdbcType=BIGINT},
            create_time         = #{createTime,jdbcType=BIGINT},
            op_time             = #{opTime,jdbcType=BIGINT},
            last_ver            = #{lastVer,jdbcType=TINYINT},
            bill_amount         = #{billAmount,jdbcType=BIGINT},
            bill_invoice_status = #{billInvoiceStatus,jdbcType=TINYINT},
            invoice_detail_id   = #{invoiceDetailId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="selectListByInvoiceNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from invoice_bill_relation
        where invoice_no = #{invoiceNo,jdbcType=VARCHAR}
        order by create_time desc;
    </select>

    <select id="selectListByDetailId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from invoice_bill_relation
        where invoice_detail_id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByInvoiceDetailId">
        delete
        from invoice_bill_relation
        where invoice_detail_id = #{id,jdbcType=BIGINT}
    </delete>
</mapper>