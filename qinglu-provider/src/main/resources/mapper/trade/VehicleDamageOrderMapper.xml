<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.VehicleDamageOrderMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.VehicleDamageOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="damage_time" jdbcType="TIMESTAMP" property="damageTime" />
    <result column="repair_fee" jdbcType="BIGINT" property="repairFee" />
    <result column="outage_fee" jdbcType="BIGINT" property="outageFee" />
    <result column="depreciation_fee" jdbcType="BIGINT" property="depreciationFee" />
    <result column="other_fee" jdbcType="BIGINT" property="otherFee" />
    <result column="deduction_type" jdbcType="TINYINT" property="deductionType" />
    <result column="deduction_status" jdbcType="TINYINT" property="deductionStatus" />
    <result column="refund_amount" jdbcType="BIGINT" property="refundAmount" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="partner_create_time" jdbcType="BIGINT" property="partnerCreateTime" />
    <result column="partner_op_time" jdbcType="BIGINT" property="partnerOpTime" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, vehicle_id, damage_time, repair_fee, outage_fee, depreciation_fee, 
    other_fee, deduction_type, deduction_status, refund_amount, source, deleted, last_ver, 
    op_user_id, partner_create_time, partner_op_time, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.VehicleDamageOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_damage_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_damage_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_damage_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.VehicleDamageOrderExample">
    delete from vehicle_damage_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.VehicleDamageOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_damage_order (order_id, vehicle_id, damage_time, 
      repair_fee, outage_fee, depreciation_fee, 
      other_fee, deduction_type, deduction_status, 
      refund_amount, source, deleted, 
      last_ver, op_user_id, partner_create_time, 
      partner_op_time, create_time, op_time
      )
    values (#{orderId,jdbcType=BIGINT}, #{vehicleId,jdbcType=BIGINT}, #{damageTime,jdbcType=TIMESTAMP}, 
      #{repairFee,jdbcType=BIGINT}, #{outageFee,jdbcType=BIGINT}, #{depreciationFee,jdbcType=BIGINT}, 
      #{otherFee,jdbcType=BIGINT}, #{deductionType,jdbcType=TINYINT}, #{deductionStatus,jdbcType=TINYINT}, 
      #{refundAmount,jdbcType=BIGINT}, #{source,jdbcType=TINYINT}, #{deleted,jdbcType=TINYINT}, 
      #{lastVer,jdbcType=INTEGER}, #{opUserId,jdbcType=BIGINT}, #{partnerCreateTime,jdbcType=BIGINT}, 
      #{partnerOpTime,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.VehicleDamageOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_damage_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="damageTime != null">
        damage_time,
      </if>
      <if test="repairFee != null">
        repair_fee,
      </if>
      <if test="outageFee != null">
        outage_fee,
      </if>
      <if test="depreciationFee != null">
        depreciation_fee,
      </if>
      <if test="otherFee != null">
        other_fee,
      </if>
      <if test="deductionType != null">
        deduction_type,
      </if>
      <if test="deductionStatus != null">
        deduction_status,
      </if>
      <if test="refundAmount != null">
        refund_amount,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="partnerCreateTime != null">
        partner_create_time,
      </if>
      <if test="partnerOpTime != null">
        partner_op_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="damageTime != null">
        #{damageTime,jdbcType=TIMESTAMP},
      </if>
      <if test="repairFee != null">
        #{repairFee,jdbcType=BIGINT},
      </if>
      <if test="outageFee != null">
        #{outageFee,jdbcType=BIGINT},
      </if>
      <if test="depreciationFee != null">
        #{depreciationFee,jdbcType=BIGINT},
      </if>
      <if test="otherFee != null">
        #{otherFee,jdbcType=BIGINT},
      </if>
      <if test="deductionType != null">
        #{deductionType,jdbcType=TINYINT},
      </if>
      <if test="deductionStatus != null">
        #{deductionStatus,jdbcType=TINYINT},
      </if>
      <if test="refundAmount != null">
        #{refundAmount,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="partnerCreateTime != null">
        #{partnerCreateTime,jdbcType=BIGINT},
      </if>
      <if test="partnerOpTime != null">
        #{partnerOpTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.VehicleDamageOrderExample" resultType="java.lang.Long">
    select count(*) from vehicle_damage_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_damage_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleId != null">
        vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="record.damageTime != null">
        damage_time = #{record.damageTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.repairFee != null">
        repair_fee = #{record.repairFee,jdbcType=BIGINT},
      </if>
      <if test="record.outageFee != null">
        outage_fee = #{record.outageFee,jdbcType=BIGINT},
      </if>
      <if test="record.depreciationFee != null">
        depreciation_fee = #{record.depreciationFee,jdbcType=BIGINT},
      </if>
      <if test="record.otherFee != null">
        other_fee = #{record.otherFee,jdbcType=BIGINT},
      </if>
      <if test="record.deductionType != null">
        deduction_type = #{record.deductionType,jdbcType=TINYINT},
      </if>
      <if test="record.deductionStatus != null">
        deduction_status = #{record.deductionStatus,jdbcType=TINYINT},
      </if>
      <if test="record.refundAmount != null">
        refund_amount = #{record.refundAmount,jdbcType=BIGINT},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=TINYINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.partnerCreateTime != null">
        partner_create_time = #{record.partnerCreateTime,jdbcType=BIGINT},
      </if>
      <if test="record.partnerOpTime != null">
        partner_op_time = #{record.partnerOpTime,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_damage_order
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      damage_time = #{record.damageTime,jdbcType=TIMESTAMP},
      repair_fee = #{record.repairFee,jdbcType=BIGINT},
      outage_fee = #{record.outageFee,jdbcType=BIGINT},
      depreciation_fee = #{record.depreciationFee,jdbcType=BIGINT},
      other_fee = #{record.otherFee,jdbcType=BIGINT},
      deduction_type = #{record.deductionType,jdbcType=TINYINT},
      deduction_status = #{record.deductionStatus,jdbcType=TINYINT},
      refund_amount = #{record.refundAmount,jdbcType=BIGINT},
      source = #{record.source,jdbcType=TINYINT},
      deleted = #{record.deleted,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      partner_create_time = #{record.partnerCreateTime,jdbcType=BIGINT},
      partner_op_time = #{record.partnerOpTime,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.VehicleDamageOrder">
    update vehicle_damage_order
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="damageTime != null">
        damage_time = #{damageTime,jdbcType=TIMESTAMP},
      </if>
      <if test="repairFee != null">
        repair_fee = #{repairFee,jdbcType=BIGINT},
      </if>
      <if test="outageFee != null">
        outage_fee = #{outageFee,jdbcType=BIGINT},
      </if>
      <if test="depreciationFee != null">
        depreciation_fee = #{depreciationFee,jdbcType=BIGINT},
      </if>
      <if test="otherFee != null">
        other_fee = #{otherFee,jdbcType=BIGINT},
      </if>
      <if test="deductionType != null">
        deduction_type = #{deductionType,jdbcType=TINYINT},
      </if>
      <if test="deductionStatus != null">
        deduction_status = #{deductionStatus,jdbcType=TINYINT},
      </if>
      <if test="refundAmount != null">
        refund_amount = #{refundAmount,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="partnerCreateTime != null">
        partner_create_time = #{partnerCreateTime,jdbcType=BIGINT},
      </if>
      <if test="partnerOpTime != null">
        partner_op_time = #{partnerOpTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.VehicleDamageOrder">
    update vehicle_damage_order
    set order_id = #{orderId,jdbcType=BIGINT},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      damage_time = #{damageTime,jdbcType=TIMESTAMP},
      repair_fee = #{repairFee,jdbcType=BIGINT},
      outage_fee = #{outageFee,jdbcType=BIGINT},
      depreciation_fee = #{depreciationFee,jdbcType=BIGINT},
      other_fee = #{otherFee,jdbcType=BIGINT},
      deduction_type = #{deductionType,jdbcType=TINYINT},
      deduction_status = #{deductionStatus,jdbcType=TINYINT},
      refund_amount = #{refundAmount,jdbcType=BIGINT},
      source = #{source,jdbcType=TINYINT},
      deleted = #{deleted,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      partner_create_time = #{partnerCreateTime,jdbcType=BIGINT},
      partner_op_time = #{partnerOpTime,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_damage_order
    (order_id, vehicle_id, damage_time, repair_fee, outage_fee, depreciation_fee, other_fee, 
      deduction_type, deduction_status, refund_amount, source, deleted, last_ver, op_user_id, 
      partner_create_time, partner_op_time, create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=BIGINT}, #{item.vehicleId,jdbcType=BIGINT}, #{item.damageTime,jdbcType=TIMESTAMP}, 
        #{item.repairFee,jdbcType=BIGINT}, #{item.outageFee,jdbcType=BIGINT}, #{item.depreciationFee,jdbcType=BIGINT}, 
        #{item.otherFee,jdbcType=BIGINT}, #{item.deductionType,jdbcType=TINYINT}, #{item.deductionStatus,jdbcType=TINYINT}, 
        #{item.refundAmount,jdbcType=BIGINT}, #{item.source,jdbcType=TINYINT}, #{item.deleted,jdbcType=TINYINT}, 
        #{item.lastVer,jdbcType=INTEGER}, #{item.opUserId,jdbcType=BIGINT}, #{item.partnerCreateTime,jdbcType=BIGINT}, 
        #{item.partnerOpTime,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_damage_order (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_id'.toString() == column.value">
          #{item.vehicleId,jdbcType=BIGINT}
        </if>
        <if test="'damage_time'.toString() == column.value">
          #{item.damageTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'repair_fee'.toString() == column.value">
          #{item.repairFee,jdbcType=BIGINT}
        </if>
        <if test="'outage_fee'.toString() == column.value">
          #{item.outageFee,jdbcType=BIGINT}
        </if>
        <if test="'depreciation_fee'.toString() == column.value">
          #{item.depreciationFee,jdbcType=BIGINT}
        </if>
        <if test="'other_fee'.toString() == column.value">
          #{item.otherFee,jdbcType=BIGINT}
        </if>
        <if test="'deduction_type'.toString() == column.value">
          #{item.deductionType,jdbcType=TINYINT}
        </if>
        <if test="'deduction_status'.toString() == column.value">
          #{item.deductionStatus,jdbcType=TINYINT}
        </if>
        <if test="'refund_amount'.toString() == column.value">
          #{item.refundAmount,jdbcType=BIGINT}
        </if>
        <if test="'source'.toString() == column.value">
          #{item.source,jdbcType=TINYINT}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'partner_create_time'.toString() == column.value">
          #{item.partnerCreateTime,jdbcType=BIGINT}
        </if>
        <if test="'partner_op_time'.toString() == column.value">
          #{item.partnerOpTime,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>