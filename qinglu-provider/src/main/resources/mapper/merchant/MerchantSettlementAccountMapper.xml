<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.merchant.MerchantSettlementAccountMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.merchant.MerchantSettlementAccount">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="account_type" jdbcType="TINYINT" property="accountType" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="bank_address" jdbcType="VARCHAR" property="bankAddress" />
    <result column="bank_detailed_address" jdbcType="VARCHAR" property="bankDetailedAddress" />
    <result column="opening_branch_bank" jdbcType="VARCHAR" property="openingBranchBank" />
    <result column="opening_bank_number" jdbcType="VARCHAR" property="openingBankNumber" />
    <result column="bank_deposit" jdbcType="VARCHAR" property="bankDeposit" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="bank_account" jdbcType="VARCHAR" property="bankAccount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, account_type, account_name, bank_address, bank_detailed_address, 
    opening_branch_bank, opening_bank_number, bank_deposit, last_ver, deleted, create_time, 
    op_time, op_user_id, bank_account
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.merchant.MerchantSettlementAccountExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from merchant_settlement_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_settlement_account
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_settlement_account
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.merchant.MerchantSettlementAccountExample">
    delete from merchant_settlement_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.merchant.MerchantSettlementAccount">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into merchant_settlement_account (merchant_id, account_type, account_name, 
      bank_address, bank_detailed_address, opening_branch_bank, 
      opening_bank_number, bank_deposit, last_ver, 
      deleted, create_time, op_time, 
      op_user_id, bank_account)
    values (#{merchantId,jdbcType=BIGINT}, #{accountType,jdbcType=TINYINT}, #{accountName,jdbcType=VARCHAR}, 
      #{bankAddress,jdbcType=VARCHAR}, #{bankDetailedAddress,jdbcType=VARCHAR}, #{openingBranchBank,jdbcType=VARCHAR}, 
      #{openingBankNumber,jdbcType=VARCHAR}, #{bankDeposit,jdbcType=VARCHAR}, #{lastVer,jdbcType=INTEGER}, 
      #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, 
      #{opUserId,jdbcType=BIGINT}, #{bankAccount,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.merchant.MerchantSettlementAccount">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into merchant_settlement_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="accountName != null">
        account_name,
      </if>
      <if test="bankAddress != null">
        bank_address,
      </if>
      <if test="bankDetailedAddress != null">
        bank_detailed_address,
      </if>
      <if test="openingBranchBank != null">
        opening_branch_bank,
      </if>
      <if test="openingBankNumber != null">
        opening_bank_number,
      </if>
      <if test="bankDeposit != null">
        bank_deposit,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="bankAccount != null">
        bank_account,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=TINYINT},
      </if>
      <if test="accountName != null">
        #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="bankAddress != null">
        #{bankAddress,jdbcType=VARCHAR},
      </if>
      <if test="bankDetailedAddress != null">
        #{bankDetailedAddress,jdbcType=VARCHAR},
      </if>
      <if test="openingBranchBank != null">
        #{openingBranchBank,jdbcType=VARCHAR},
      </if>
      <if test="openingBankNumber != null">
        #{openingBankNumber,jdbcType=VARCHAR},
      </if>
      <if test="bankDeposit != null">
        #{bankDeposit,jdbcType=VARCHAR},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="bankAccount != null">
        #{bankAccount,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.merchant.MerchantSettlementAccountExample" resultType="java.lang.Long">
    select count(*) from merchant_settlement_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update merchant_settlement_account
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.accountType != null">
        account_type = #{record.accountType,jdbcType=TINYINT},
      </if>
      <if test="record.accountName != null">
        account_name = #{record.accountName,jdbcType=VARCHAR},
      </if>
      <if test="record.bankAddress != null">
        bank_address = #{record.bankAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.bankDetailedAddress != null">
        bank_detailed_address = #{record.bankDetailedAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.openingBranchBank != null">
        opening_branch_bank = #{record.openingBranchBank,jdbcType=VARCHAR},
      </if>
      <if test="record.openingBankNumber != null">
        opening_bank_number = #{record.openingBankNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.bankDeposit != null">
        bank_deposit = #{record.bankDeposit,jdbcType=VARCHAR},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.bankAccount != null">
        bank_account = #{record.bankAccount,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update merchant_settlement_account
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      account_type = #{record.accountType,jdbcType=TINYINT},
      account_name = #{record.accountName,jdbcType=VARCHAR},
      bank_address = #{record.bankAddress,jdbcType=VARCHAR},
      bank_detailed_address = #{record.bankDetailedAddress,jdbcType=VARCHAR},
      opening_branch_bank = #{record.openingBranchBank,jdbcType=VARCHAR},
      opening_bank_number = #{record.openingBankNumber,jdbcType=VARCHAR},
      bank_deposit = #{record.bankDeposit,jdbcType=VARCHAR},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      bank_account = #{record.bankAccount,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.merchant.MerchantSettlementAccount">
    update merchant_settlement_account
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=TINYINT},
      </if>
      <if test="accountName != null">
        account_name = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="bankAddress != null">
        bank_address = #{bankAddress,jdbcType=VARCHAR},
      </if>
      <if test="bankDetailedAddress != null">
        bank_detailed_address = #{bankDetailedAddress,jdbcType=VARCHAR},
      </if>
      <if test="openingBranchBank != null">
        opening_branch_bank = #{openingBranchBank,jdbcType=VARCHAR},
      </if>
      <if test="openingBankNumber != null">
        opening_bank_number = #{openingBankNumber,jdbcType=VARCHAR},
      </if>
      <if test="bankDeposit != null">
        bank_deposit = #{bankDeposit,jdbcType=VARCHAR},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="bankAccount != null">
        bank_account = #{bankAccount,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.merchant.MerchantSettlementAccount">
    update merchant_settlement_account
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      account_type = #{accountType,jdbcType=TINYINT},
      account_name = #{accountName,jdbcType=VARCHAR},
      bank_address = #{bankAddress,jdbcType=VARCHAR},
      bank_detailed_address = #{bankDetailedAddress,jdbcType=VARCHAR},
      opening_branch_bank = #{openingBranchBank,jdbcType=VARCHAR},
      opening_bank_number = #{openingBankNumber,jdbcType=VARCHAR},
      bank_deposit = #{bankDeposit,jdbcType=VARCHAR},
      last_ver = #{lastVer,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      bank_account = #{bankAccount,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into merchant_settlement_account
    (merchant_id, account_type, account_name, bank_address, bank_detailed_address, opening_branch_bank, 
      opening_bank_number, bank_deposit, last_ver, deleted, create_time, op_time, op_user_id, 
      bank_account)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.accountType,jdbcType=TINYINT}, #{item.accountName,jdbcType=VARCHAR}, 
        #{item.bankAddress,jdbcType=VARCHAR}, #{item.bankDetailedAddress,jdbcType=VARCHAR}, 
        #{item.openingBranchBank,jdbcType=VARCHAR}, #{item.openingBankNumber,jdbcType=VARCHAR}, 
        #{item.bankDeposit,jdbcType=VARCHAR}, #{item.lastVer,jdbcType=INTEGER}, #{item.deleted,jdbcType=TINYINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT}, 
        #{item.bankAccount,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into merchant_settlement_account (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'account_type'.toString() == column.value">
          #{item.accountType,jdbcType=TINYINT}
        </if>
        <if test="'account_name'.toString() == column.value">
          #{item.accountName,jdbcType=VARCHAR}
        </if>
        <if test="'bank_address'.toString() == column.value">
          #{item.bankAddress,jdbcType=VARCHAR}
        </if>
        <if test="'bank_detailed_address'.toString() == column.value">
          #{item.bankDetailedAddress,jdbcType=VARCHAR}
        </if>
        <if test="'opening_branch_bank'.toString() == column.value">
          #{item.openingBranchBank,jdbcType=VARCHAR}
        </if>
        <if test="'opening_bank_number'.toString() == column.value">
          #{item.openingBankNumber,jdbcType=VARCHAR}
        </if>
        <if test="'bank_deposit'.toString() == column.value">
          #{item.bankDeposit,jdbcType=VARCHAR}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'bank_account'.toString() == column.value">
          #{item.bankAccount,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>