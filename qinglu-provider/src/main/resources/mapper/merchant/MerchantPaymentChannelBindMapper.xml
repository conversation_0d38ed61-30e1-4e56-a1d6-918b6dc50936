<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.merchant.MerchantPaymentChannelBindMapper">
    <resultMap id="BaseResultMap" type="com.ql.rent.param.merchant.MerchantPaymentChannelBindParam">
        <id column="id" property="id" />
        <result column="merchant_id" property="merchantId" />
        <result column="payment_channel_id" property="paymentChannelId" />
        <result column="priority" property="priority" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="op_time" property="opTime" />
        <result column="op_user_id" property="opUserId" />
        <result column="deleted" property="deleted" />
        <result column="discount" property="discount" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="VOResultMap" type="com.ql.rent.vo.merchant.MerchantPaymentChannelBindVO">
        <id column="id" property="id" />
        <result column="merchant_id" property="merchantId" />
        <result column="payment_channel_id" property="paymentChannelId" />
        <result column="entity_name" property="entityName" />
        <result column="priority" property="priority" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="discount" property="discount" jdbcType="INTEGER"/>
        <result column="payment_channel_type_code" property="paymentChannelTypeCode"/>
        <result column="merchant_name" property="merchantName"/>
    </resultMap>

    <insert id="insert" parameterType="com.ql.rent.param.merchant.MerchantPaymentChannelBindParam" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO merchant_payment_channel_bind (
            merchant_id, payment_channel_id, priority, status, remark,
            create_time, create_user_id, op_time, op_user_id, discount
        ) VALUES (
            #{merchantId}, #{paymentChannelId}, #{priority}, #{status}, #{remark},
            #{createTime}, #{createUserId}, #{opTime}, #{opUserId}, #{discount}
        )
    </insert>

    <select id="selectPage" resultMap="VOResultMap">
        SELECT 
            b.id, 
            b.merchant_id,
            b.payment_channel_id, 
            c.payment_channel_name,
            c.payment_channel_type_code,
            le.entity_name,
            b.discount,
            b.priority, 
            b.status, 
            b.create_time,
            m.name as merchant_name
        FROM merchant_payment_channel_bind b
        LEFT JOIN payment_channel c ON b.payment_channel_id = c.id
        LEFT JOIN legal_entity le ON c.legal_entity_id = le.id
        LEFT JOIN merchant_info m ON b.merchant_id = m.id
        <where>
            b.deleted = 0
            <if test="merchantId != null">AND b.merchant_id = #{merchantId}</if>
            <if test="paymentChannelTypeCode != null">AND c.payment_channel_type_code = #{paymentChannelTypeCode}</if>
            <if test="status != null">AND b.status = #{status}</if>
        </where>
        ORDER BY b.priority DESC, b.create_time DESC
    </select>

    <select id="selectByChannelIdAndMerchantId" resultType="com.ql.rent.param.merchant.MerchantPaymentChannelBindParam">
        SELECT 
            *
        FROM 
            merchant_payment_channel_bind
        WHERE 
            payment_channel_id = #{paymentChannelId}
            AND merchant_id = #{merchantId}
        LIMIT 1
    </select>

    <select id="selectByMerchantId" resultMap="BaseResultMap">
        SELECT * FROM merchant_payment_channel_bind 
        WHERE merchant_id = #{merchantId} 
        AND deleted = 0
    </select>

    <update id="updateByPrimaryKey" parameterType="com.ql.rent.param.merchant.MerchantPaymentChannelBindParam">
        UPDATE merchant_payment_channel_bind
        SET 
            merchant_id = #{merchantId},
            payment_channel_id = #{paymentChannelId},
            priority = #{priority},
            status = #{status},
            discount = #{discount},
            op_time = #{opTime}
        WHERE id = #{id}
    </update>

    <!-- 根据商户ID查询支付渠道绑定信息 -->
    <select id="selectChannelsByMerchantIds" resultMap="VOResultMap">
        SELECT 
            b.id, 
            b.merchant_id,
            b.payment_channel_id, 
            c.payment_channel_name,
            c.payment_channel_type_code,
            le.entity_name,
            b.discount,
            b.priority, 
            b.status, 
            b.create_time,
            m.name as merchant_name
        FROM merchant_payment_channel_bind b
        LEFT JOIN payment_channel c ON b.payment_channel_id = c.id
        LEFT JOIN legal_entity le ON c.legal_entity_id = le.id
        LEFT JOIN merchant_info m ON b.merchant_id = m.id
        <where>
            b.deleted = 0
            <if test="merchantIds != null and merchantIds.size() > 0">
                AND b.merchant_id IN
                <foreach collection="merchantIds" item="merchantId" open="(" separator="," close=")">
                    #{merchantId}
                </foreach>
            </if>
            <if test="paymentChannelTypeCode != null">AND c.payment_channel_type_code = #{paymentChannelTypeCode}</if>
            <if test="status != null">AND b.status = #{status}</if>
        </where>
        ORDER BY b.priority DESC, b.create_time DESC
    </select>

    <select id="countTotal" resultType="long">
        SELECT COUNT(*)
        FROM merchant_payment_channel_bind b
        LEFT JOIN payment_channel c ON b.payment_channel_id = c.id
        WHERE 1=1
        <if test="merchantId != null">
            AND b.merchant_id = #{merchantId}
        </if>
        <if test="paymentChannelTypeCode != null and paymentChannelTypeCode != ''">
            AND c.channel_type_code = #{paymentChannelTypeCode}
        </if>
        <if test="status != null">
            AND b.status = #{status}
        </if>
    </select>
</mapper> 