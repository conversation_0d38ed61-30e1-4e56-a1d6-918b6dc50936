package com.ql.rent.component.sms;

import com.ql.enums.SmsTemplateEnum;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 短信模板枚举单元测试
 */
public class SmsTemplateEnumTest {

    @Test
    public void testEnumValues() {
        // 验证枚举值数量
        assertThat(SmsTemplateEnum.values().length).isGreaterThan(0);
        
        // 验证常用枚举值存在
        assertThat(SmsTemplateEnum.USER_REG).isNotNull();
        assertThat(SmsTemplateEnum.USER_LOGIN).isNotNull();
        assertThat(SmsTemplateEnum.USER_FORGOT).isNotNull();
    }
    
    @Test
    public void testGetTemplateCode() {
        // 验证阿里云模板代码
        assertThat(SmsTemplateEnum.USER_REG.getAliTemplateCode()).isEqualTo("SMS_270855154");
        assertThat(SmsTemplateEnum.USER_LOGIN.getAliTemplateCode()).isEqualTo("SMS_270905144");
    }
    
    @Test
    public void testGetAliTemplateCode() {
        // 验证阿里云模板代码
        assertThat(SmsTemplateEnum.USER_REG.getAliTemplateCode()).isEqualTo("SMS_270855154");
        assertThat(SmsTemplateEnum.USER_LOGIN.getAliTemplateCode()).isEqualTo("SMS_270905144");
    }
    
    @Test
    public void testGetYunpianTemplateText() {
        // 验证云片网模板文本
        assertThat(SmsTemplateEnum.USER_REG.getYunpianTemplateText()).contains("擎路科技");
        assertThat(SmsTemplateEnum.USER_REG.getYunpianTemplateText()).contains("{code}");
        
        assertThat(SmsTemplateEnum.USER_LOGIN.getYunpianTemplateText()).contains("擎路科技");
        assertThat(SmsTemplateEnum.USER_LOGIN.getYunpianTemplateText()).contains("{code}");
    }
} 