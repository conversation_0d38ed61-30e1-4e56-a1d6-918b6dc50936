package com.ql.rent.component.sms.yunpian;

import com.ql.rent.api.aggregate.remote.yunpian.YunPianResponse;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 云片网API响应模型测试
 */
public class YunPianResponseTest {

    @Test
    public void testSuccessResponse() {
        YunPianResponse response = new YunPianResponse();
        response.setCode(0);
        response.setMsg("操作成功");
        response.setCount(1);
        response.setFee(1.0);
        response.setUnit("RMB");
        response.setMobile("13800138000");
        response.setSid("test-sid-123456");
        
        assertThat(response.isSuccess()).isTrue();
        assertThat(response.getCode()).isEqualTo(0);
        assertThat(response.getMsg()).isEqualTo("操作成功");
        assertThat(response.getCount()).isEqualTo(1);
        assertThat(response.getFee()).isEqualTo(1);
        assertThat(response.getUnit()).isEqualTo("RMB");
        assertThat(response.getMobile()).isEqualTo("13800138000");
        assertThat(response.getSid()).isEqualTo("test-sid-123456");
    }
    
    @Test
    public void testFailureResponse() {
        YunPianResponse response = new YunPianResponse();
        response.setCode(1);
        response.setMsg("签名不正确");
        
        assertThat(response.isSuccess()).isFalse();
        assertThat(response.getCode()).isEqualTo(1);
        assertThat(response.getMsg()).isEqualTo("签名不正确");
    }
    
    @Test
    public void testNullCodeHandling() {
        YunPianResponse response = new YunPianResponse();
        
        assertThat(response.isSuccess()).isFalse();
        assertThat(response.getCode()).isNull();
    }
    
    @Test
    public void testLombokDataAnnotation() {
        // 测试Lombok @Data生成的方法
        YunPianResponse response1 = new YunPianResponse();
        response1.setCode(0);
        response1.setMsg("操作成功");
        response1.setSid("123");
        
        YunPianResponse response2 = new YunPianResponse();
        response2.setCode(0);
        response2.setMsg("操作成功");
        response2.setSid("123");
        
        YunPianResponse response3 = new YunPianResponse();
        response3.setCode(1);
        response3.setMsg("失败");
        response3.setSid("456");
        
        // 测试equals和hashCode
        assertThat(response1).isEqualTo(response2);
        assertThat(response1).isNotEqualTo(response3);
        assertThat(response1.hashCode()).isEqualTo(response2.hashCode());
        assertThat(response1.hashCode()).isNotEqualTo(response3.hashCode());
        
        // 测试toString
        assertThat(response1.toString()).contains("code=0");
        assertThat(response1.toString()).contains("msg=操作成功");
        assertThat(response1.toString()).contains("sid=123");
    }
} 