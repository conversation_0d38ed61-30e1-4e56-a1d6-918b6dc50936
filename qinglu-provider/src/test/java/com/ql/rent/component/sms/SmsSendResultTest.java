package com.ql.rent.component.sms;

import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 短信发送结果封装类单元测试
 */
public class SmsSendResultTest {

    private static final String PROVIDER = "TestProvider";
    private static final String MESSAGE_ID = "test-message-id";
    private static final String ERROR_CODE = "ERROR_001";
    private static final String ERROR_MESSAGE = "Test error message";

    @Test
    public void testSuccessFactoryMethod() {
        // 测试成功结果工厂方法
        SmsSendResult result = SmsSendResult.success(PROVIDER, MESSAGE_ID);
        
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getProvider()).isEqualTo(PROVIDER);
        assertThat(result.getMessageId()).isEqualTo(MESSAGE_ID);
        assertThat(result.getTimestamp()).isGreaterThan(0);
        assertThat(result.getErrorCode()).isNull();
        assertThat(result.getErrorMessage()).isNull();
    }
    
    @Test
    public void testFailureFactoryMethod() {
        // 测试失败结果工厂方法
        SmsSendResult result = SmsSendResult.failure(PROVIDER, ERROR_CODE, ERROR_MESSAGE);
        
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getProvider()).isEqualTo(PROVIDER);
        assertThat(result.getMessageId()).isNull();
        assertThat(result.getTimestamp()).isGreaterThan(0);
        assertThat(result.getErrorCode()).isEqualTo(ERROR_CODE);
        assertThat(result.getErrorMessage()).isEqualTo(ERROR_MESSAGE);
    }
    
    @Test
    public void testBuilderAndGetterSetter() {
        // 测试Builder模式和Getter/Setter
        long timestamp = System.currentTimeMillis();
        
        SmsSendResult result = SmsSendResult.builder()
                .success(true)
                .provider(PROVIDER)
                .messageId(MESSAGE_ID)
                .errorCode(ERROR_CODE)
                .errorMessage(ERROR_MESSAGE)
                .timestamp(timestamp)
                .build();
        
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getProvider()).isEqualTo(PROVIDER);
        assertThat(result.getMessageId()).isEqualTo(MESSAGE_ID);
        assertThat(result.getErrorCode()).isEqualTo(ERROR_CODE);
        assertThat(result.getErrorMessage()).isEqualTo(ERROR_MESSAGE);
        assertThat(result.getTimestamp()).isEqualTo(timestamp);
        
        // 测试Setter
        result.setSuccess(false);
        result.setProvider("NewProvider");
        result.setMessageId("new-message-id");
        result.setErrorCode("NEW_ERROR");
        result.setErrorMessage("New error message");
        result.setTimestamp(timestamp + 1000);
        
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getProvider()).isEqualTo("NewProvider");
        assertThat(result.getMessageId()).isEqualTo("new-message-id");
        assertThat(result.getErrorCode()).isEqualTo("NEW_ERROR");
        assertThat(result.getErrorMessage()).isEqualTo("New error message");
        assertThat(result.getTimestamp()).isEqualTo(timestamp + 1000);
    }
    
    @Test
    public void testNoArgsConstructor() {
        // 测试无参构造函数
        SmsSendResult result = new SmsSendResult();
        
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getProvider()).isNull();
        assertThat(result.getMessageId()).isNull();
        assertThat(result.getErrorCode()).isNull();
        assertThat(result.getErrorMessage()).isNull();
        assertThat(result.getTimestamp()).isEqualTo(0);
    }
    
    @Test
    public void testAllArgsConstructor() {
        // 测试全参构造函数
        long timestamp = System.currentTimeMillis();
        
        SmsSendResult result = new SmsSendResult(
                true, ERROR_CODE, ERROR_MESSAGE, PROVIDER, timestamp, MESSAGE_ID);
        
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getErrorCode()).isEqualTo(ERROR_CODE);
        assertThat(result.getErrorMessage()).isEqualTo(ERROR_MESSAGE);
        assertThat(result.getProvider()).isEqualTo(PROVIDER);
        assertThat(result.getTimestamp()).isEqualTo(timestamp);
        assertThat(result.getMessageId()).isEqualTo(MESSAGE_ID);
    }
} 