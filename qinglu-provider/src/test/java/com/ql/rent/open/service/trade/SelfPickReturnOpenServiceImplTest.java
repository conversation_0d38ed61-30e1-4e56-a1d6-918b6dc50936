package com.ql.rent.open.service.trade;

import com.alibaba.fastjson.JSON;
import com.ql.dto.ResultResp;
import com.ql.dto.open.request.trade.ConfirmReturnRequest;
import com.ql.dto.open.request.trade.ScheduledCarRequest;
import com.ql.rent.AbstractTest;
import com.ql.rent.client.ISelfPickReturnOpenService;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

public class SelfPickReturnOpenServiceImplTest extends AbstractTest {
    @Resource
    ISelfPickReturnOpenService selfPickReturnOpenService;

    @Test
    public void testScheduledCar() {
        ScheduledCarRequest request = new ScheduledCarRequest();
//        request.setOrderId();
        request = JSON.parseObject("{\"orderId\":\"467368\",\"userName\":\"lalala\",\"startTime\":1717466939654,\"endTime\":\"\",\"energyType\":1,\"oilPercent\":0,\"batteryPercent\":37,\"carProblem\":\"\",\"carRemark\":\"\",\"att\":[{\"type\":1,\"url\":\"https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1717466945376_MT36nP40.jpg\"},{\"type\":2,\"url\":\"https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1717466947638_229dLSwf.jpg\"},{\"type\":3,\"url\":\"https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1717466950704_tncZYLRC.png\"},{\"type\":4,\"url\":\"https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1717466953162_yuFUs0Sr.png\"},{\"type\":5,\"url\":\"https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1717466955523_rNOBCU9p.png\"},{\"type\":6,\"url\":\"https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1717466958243_BKoDDchi.png\"},{\"type\":7,\"url\":\"https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1717466961079_Snpwmx8k.png\"},{\"type\":8,\"url\":\"https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1717466971506_SyLbCDlE.png\"},{\"type\":99,\"url\":\"https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1717466975506_EMYSBSef.png\"},{\"type\":11,\"url\":\"https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1717466978942_0oMjcMSM.png\"}],\"positionRemark\":\"123\",\"remindMileage\":0,\"milRange\":102}"
         , ScheduledCarRequest.class);
        request.setMerchantId(44L);
        ResultResp<Boolean> booleanResult = selfPickReturnOpenService.checkCar(request);
        System.out.println(JSON.toJSONString(booleanResult));
    }

    @Test
    public void testConfirmReturn() {
        ConfirmReturnRequest request = new ConfirmReturnRequest();
        request = JSON.parseObject("{\"orderId\":\"467369\",\"refundPayType\":\"1\",\"deductionPayType\":\"1\",\"refundItems\":[{\"amount\":100,\"expenseItemType\":14}],\"deductionItems\":[{\"amount\":100,\"expenseItemType\":3}]}"
        , ConfirmReturnRequest.class);
        request.setMerchantId(44L);
        ResultResp<Boolean> booleanResult = selfPickReturnOpenService.confirmReturn(request);
        System.out.println(JSON.toJSONString(booleanResult));
    }
}
