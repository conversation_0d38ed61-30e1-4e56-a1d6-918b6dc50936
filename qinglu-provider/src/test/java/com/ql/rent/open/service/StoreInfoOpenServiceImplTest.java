package com.ql.rent.open.service;

import com.alibaba.fastjson.JSON;
import com.ql.dto.open.request.store.*;
import com.ql.dto.open.response.store.OpenCircleListResponse;
import com.ql.dto.open.response.store.OpenStoreInfoResponse;
import com.ql.dto.open.response.store.OpenStoreListResponse;
import com.ql.rent.AbstractTest;
import com.ql.rent.client.IStoreInfoOpenService;
import com.ql.dto.ResultResp;
import com.ql.rent.service.store.ICtripStoreService;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class StoreInfoOpenServiceImplTest extends AbstractTest {
    @Resource
    IStoreInfoOpenService storeInfoOpenService;

    @Test
    public void testStoreInfoSync() {
        OpenStoreSyncRequest openStoreSyncRequest = new OpenStoreSyncRequest();
//        List<OpenStoreInfoDTO> list = new ArrayList<>();
//        OpenStoreInfoDTO storeInfoRequest = JSON.parseObject("{\"storeId\":\"1234qwer\",\"channelId\":3,\"storeName\":\"adfs\",\"countryId\":0,\"provinceId\":1,\"cityId\":35,\"areaCode\":110105,\"merchantId\":55,\"storeSize\":0,\"storeDecorate\":0,\"address\":\"1234124\",\"storePosType\":1,\"minAdvanceBookingTime\":1,\"maxAdvanceBookingTime\":1,\"minRentTerm\":0.04,\"maxRentTerm\":90,\"orderInterval\":1,\"allopatryReturnEnabled\":0,\"allopatryReturnFee\":0,\"storeType\":0,\"storeStatus\":1,\"longLat\":{\"longitude\":116.508455,\"latitude\":39.944762},\"pickupEnabled\":0,\"guidePickupList\":[{\"guideType\":0,\"step\":1,\"guideDesc\":\"123\",}],\"guideReturnList\":[{\"guideType\":1,\"step\":1,\"guideDesc\":\"123\",}],\"businessTimeList\":[{\"businessPeriod\":\"1111111\",\"nightService\":1,\"nightList\":[{\"fee\":100,\"businessFrom\":300,\"businessTo\":400}],\"businessFrom\":206,\"businessTo\":2200}],\"restTimeList\":[{\"startDate\":1695225600000,\"endDate\":1695398400000,\"startTime\":900,\"endTime\":2000}],\"contactList\":[{\"contactType\":0,\"linkName\":\"123\",\"countryCode\":\"+86\",\"mobile\":\"123\",\"telArea\":\"124\",\"tel\":\"4321\",\"telExt\":\"1234\",\"email\":\"1234\"}],\"hourlyChargeList\":[{\"scene\":2,\"chargeItem\":1,\"chargeValue\":\"0.35, 0.45, 0.55, 0.65, 0.75, 0.85, 0.9, 0.95\"},{\"scene\":1,\"chargeItem\":1,\"chargeValue\":\"0, 0.25, 0.4, 0.5, 0.65, 0.75, 0.9, 1\"}]}", OpenStoreInfoDTO.class);
//        storeInfoRequest.setDelFlg(false);
//        storeInfoRequest.setFreeShuttleEnabled((byte) 0);
//        storeInfoRequest.setPickupEnabled((byte) 0);
//        list.add(storeInfoRequest);
//        openStoreSyncRequest.setStoreInfoRequestList(list);
//        openStoreSyncRequest.setMerchantId(55L);
        OpenStoreSyncRequest openStoreSyncRequest1 = JSON.parseObject("{\"merchantId\":57,\"storeInfoList\":[{\"storeId\":\"test57_1\",\"channelId\":3,\"delFlg\":false,\"freeShuttleEnabled\":0,\"pickupEnabled\":0,\"storeName\":\"adfs\",\"countryId\":0,\"provinceId\":1,\"cityId\":35,\"merchantId\":57,\"storeSize\":0,\"storeDecorate\":0,\"address\":\"1234124\",\"storePosType\":1,\"minAdvanceBookingTime\":1,\"maxAdvanceBookingTime\":1,\"minRentTerm\":0.04,\"maxRentTerm\":90,\"orderInterval\":1,\"allopatryReturnEnabled\":0,\"allopatryReturnFee\":0,\"storeType\":0,\"storeStatus\":1,\"longLat\":{\"longitude\":116.508455,\"latitude\":39.944762},\"pickupEnabled\":0,\"guidePickupList\":[{\"guideType\":0,\"step\":1,\"guideDesc\":\"123\"}],\"guideReturnList\":[{\"guideType\":1,\"step\":1,\"guideDesc\":\"123\"}],\"businessTimeList\":[{\"businessPeriod\":\"1111111\",\"nightService\":1,\"nightList\":[{\"fee\":100,\"businessFrom\":300,\"businessTo\":400}],\"businessFrom\":206,\"businessTo\":2200}],\"restTimeList\":[{\"startDate\":1695225600000,\"endDate\":1695398400000,\"startTime\":900,\"endTime\":2000}],\"contactList\":[{\"contactType\":0,\"linkName\":\"123\",\"countryCode\":\"+86\",\"mobile\":\"123\",\"telArea\":\"124\",\"tel\":\"4321\",\"telExt\":\"1234\",\"email\":\"1234\"}],\"hourlyChargeList\":[{\"scene\":2,\"chargeItem\":1,\"chargeValue\":\"0.35, 0.45, 0.55, 0.65, 0.75, 0.85, 0.9, 0.95\"},{\"scene\":1,\"chargeItem\":1,\"chargeValue\":\"0, 0.25, 0.4, 0.5, 0.65, 0.75, 0.9, 1\"}]}]}"
                ,OpenStoreSyncRequest.class);
        openStoreSyncRequest1.setMerchantId(57L);
        ResultResp<Map<String, Long>> booleanResult = storeInfoOpenService.storeInfoSync(openStoreSyncRequest1);
        System.out.println(JSON.toJSONString(booleanResult));
    }

    @Test
    public void testGetStoreList() {
        OpenStoreListRequest openStoreListRequest = new OpenStoreListRequest();
        openStoreListRequest.setMerchantId(57L);
        openStoreListRequest.setPageIndex(1);
        openStoreListRequest.setPageSize(2);
        ResultResp<OpenStoreListResponse> storeList = storeInfoOpenService.getStoreList(openStoreListRequest);
        System.out.println(JSON.toJSONString(storeList));
    }


    @Test
    public void testGetStoreInfo() {
        OpenStoreInfoRequest openStoreListRequest = new OpenStoreInfoRequest();
        openStoreListRequest.setMerchantId(57L);
        openStoreListRequest.setSaasStoreId(477L);
        openStoreListRequest.setChannelId(1L);
        ResultResp<OpenStoreInfoResponse> storeInfo = storeInfoOpenService.getStoreInfo(openStoreListRequest);
        System.out.println(JSON.toJSONString(storeInfo));
    }


    @Test
    public void testGetCircleList() {
        OpenCircleListRequest openCircleListRequest = new OpenCircleListRequest();
        openCircleListRequest.setMerchantId(57L);
        openCircleListRequest.setStoreId("test57_hello");
        openCircleListRequest.setChannelId(2L);
        ResultResp<OpenCircleListResponse> circleList = storeInfoOpenService.getCircleList(openCircleListRequest);
        System.out.println(JSON.toJSONString(circleList));
    }

    @Test
    public void testOrderIntervalSync() {
        OpenIntervalSyncRequest openIntervalSyncRequest = new OpenIntervalSyncRequest();
        List<StoreIntervalRequest> list = new ArrayList<>();
        StoreIntervalRequest storeIntervalRequest = new StoreIntervalRequest();
        storeIntervalRequest.setStoreId("1234566785745");
        storeIntervalRequest.setChannelId(3L);
        List<OrderIntervalRequest> orderIntervalRequests = new ArrayList<>();
//        OrderIntervalRequest orderIntervalRequest = new OrderIntervalRequest();
        OrderIntervalRequest orderIntervalRequest = JSON.parseObject(
                "{\"orderInterval\":2,\"orderIntervalDetailList\":[{\"fuelForm\":\"汽油\",\"vehicleModelId\":1511},{\"fuelForm\":\"汽油\",\"vehicleModelId\":1617},{\"fuelForm\":\"汽油\",\"vehicleModelId\":1600}]}",
                OrderIntervalRequest.class
        );
        orderIntervalRequests.add(orderIntervalRequest);
        storeIntervalRequest.setOrderIntervalList(orderIntervalRequests);
        list.add(storeIntervalRequest);

        openIntervalSyncRequest.setStoreIntervalList(list);
        openIntervalSyncRequest.setMerchantId(55L);
        ResultResp<Boolean> booleanResult = storeInfoOpenService.orderIntervalSync(openIntervalSyncRequest);
        System.out.println(JSON.toJSONString(booleanResult));
    }

    @Test
    public void testCircleInfoSync() {
        OpenCircleSyncRequest openCircleSyncRequest = new OpenCircleSyncRequest();
//        openCircleSyncRequest.setMerchantId(55L);
//        List<CircleDTO> circleRequests = new ArrayList<>();
//        CircleDTO circleRequest = null;
////        circleRequest = JSON.parseObject(
////                "{\"storeId\":\"1234qwer\",\"name\":\"22'\",\"minAdvanceBookingTime\":11,\"color\":\"#722ed1\",\"longLats\":[{\"longitude\":120.176579,\"latitude\":30.345811},{\"longitude\":120.23872,\"latitude\":30.351736},{\"longitude\":120.190655,\"latitude\":30.318549},{\"longitude\":120.176579,\"latitude\":30.345811}],\"feeType\":1,\"fee\":12300,\"businessFrom\":200,\"businessTo\":1600,\"delFlg\":false}",
////                CircleDTO.class
////        );
//        circleRequest = JSON.parseObject(
//                "{\"storeId\":\"1234qwer\",\"name\":\"22'\",\"longLats\":[{\"longitude\":116.502619,\"latitude\":39.958944},{\"longitude\":116.473436,\"latitude\":39.941573},{\"longitude\":116.564073,\"latitude\":39.937888},{\"longitude\":116.502619,\"latitude\":39.958944}],\"feeType\":0,\"fee\":0,\"businessFrom\":200,\"businessTo\":1600,\"delFlg\":false}",
//                CircleDTO.class
//        );
//        circleRequest.setEnabled(YesOrNoEnum.YES.getValue());
//        circleRequest.setChannelId(OrderSourceEnum.FEIZHU.getSource().longValue());
//        circleRequest.setDeliveryServiceType(PickupTypeEnum.FREE_SHUTTLE.getValue());
//        circleRequest.setCircleId("weqrsafd");
//        circleRequest.setEnabled(YesOrNoEnum.YES.getValue());
//        circleRequests.add(circleRequest);
//        openCircleSyncRequest.setCircleList(circleRequests);
//        openCircleSyncRequest = JSON.parseObject(
//                "{\"circleList\":[{\"circleId\":\"1443\",\"storeId\":\"MD23102014513001\",\"channelId\":3,\"deliveryServiceType\":2,\"name\":\"南京圈飞猪\",\"feeType\":1,\"fee\":1023,\"longLats\":[{\"latitude\":31.332096,\"longitude\":121.353511},{\"latitude\":31.339668,\"longitude\":121.396294},{\"latitude\":31.317796,\"longitude\":121.39891},{\"latitude\":31.315218,\"longitude\":121.361827},{\"latitude\":31.315223,\"longitude\":121.362113},{\"latitude\":31.315223,\"longitude\":121.362113},{\"latitude\":31.332096,\"longitude\":121.353511}],\"minAdvanceBookingTime\":1,\"enabled\":1,\"delFlg\":false},{\"circleId\":\"1444\",\"storeId\":\"MD23102014513001\",\"channelId\":4,\"deliveryServiceType\":2,\"name\":\"南京圈哈喽\",\"feeType\":1,\"fee\":1023,\"longLats\":[{\"latitude\":31.332096,\"longitude\":121.353511},{\"latitude\":31.339668,\"longitude\":121.396294},{\"latitude\":31.317796,\"longitude\":121.39891},{\"latitude\":31.315218,\"longitude\":121.361827},{\"latitude\":31.315223,\"longitude\":121.362113},{\"latitude\":31.315223,\"longitude\":121.362113},{\"latitude\":31.332096,\"longitude\":121.353511}],\"minAdvanceBookingTime\":1,\"enabled\":1,\"delFlg\":false}]}"
//                , OpenCircleSyncRequest.class
//        );
        openCircleSyncRequest = JSON.parseObject(
                "{\"circleList\":[{\"storeId\":\"test_111\",\"name\":\"测试同步数据\",\"enabled\":1,\"channelId\":1,\"deliveryServiceType\":2,\"circleId\":\"weqrsafd111\",\"longLats\":[{\"longitude\":121.47475,\"latitude\":31.227415},{\"longitude\":121.374282,\"latitude\":31.295158},{\"longitude\":121.2304,\"latitude\":31.243001},{\"longitude\":121.453859,\"latitude\":31.031879}],\"feeType\":1,\"fee\":400,\"businessFrom\":1200,\"minAdvanceBookingTime\":2.50,\"businessTo\":1830,\"delFlg\":false}]}"
                , OpenCircleSyncRequest.class
        );
        openCircleSyncRequest.setMerchantId(57L);
        ResultResp<Boolean> booleanResult = storeInfoOpenService.circleInfoSync(openCircleSyncRequest);
        System.out.println(JSON.toJSONString(booleanResult));
    }

    @Test
    public void testUpdateStatus() {
        OpenStoreSyncRequest openStoreSyncRequest = new OpenStoreSyncRequest();
        List<OpenStoreInfoDTO> list = new ArrayList<>();
        OpenStoreInfoDTO storeInfoRequest = new OpenStoreInfoDTO();
        storeInfoRequest.setSaasStoreId(7242L);
        storeInfoRequest.setChannelId(1L);
        storeInfoRequest.setStoreStatus((byte) 0);
        openStoreSyncRequest.setMerchantId(57L);
        list.add(storeInfoRequest);
        storeInfoOpenService.updateStatus(openStoreSyncRequest);
    }

    @Test
    public void testDelCircle() {
        OpenCircleSyncRequest openCircleSyncRequest = new OpenCircleSyncRequest();
        openCircleSyncRequest = JSON.parseObject(
                "{\"circleList\":[{\"storeId\":\"test57_2\",\"channelId\":1}]}"
                , OpenCircleSyncRequest.class
        );
        openCircleSyncRequest.setMerchantId(57L);
        storeInfoOpenService.delCircle(openCircleSyncRequest);
    }


    @Resource
    private ICtripStoreService iCtripStoreService;

    @Test
    public void pushBookingLimit(){
     //   iCtripStoreService.pushBookingLimit(43L, 147L);
        iCtripStoreService.pushCarRentBookingPolicyDetail(1L);
    }

    @Test
    public void testTime(){
        Date pickupDate = new Date();
        Date lastReturnDate = DateUtils.addSeconds(pickupDate, 36000);
        // 计算pickupDate和lastReturnDate之间的小时数，向上取整
        long hour = (lastReturnDate.getTime() - pickupDate.getTime()) / (1000 * 60 * 60);
        System.out.println(hour);
    }

}
