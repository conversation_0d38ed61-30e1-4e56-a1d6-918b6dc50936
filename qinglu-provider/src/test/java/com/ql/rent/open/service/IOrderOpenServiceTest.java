package com.ql.rent.open.service;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.ql.dto.ResultResp;
import com.ql.dto.open.request.trade.OpenOrderDetailQueryRequest;
import com.ql.rent.AbstractTest;
import com.ql.rent.client.IOrderOpenService;
import com.ql.rent.open.service.util.SignUtils;
import com.ql.rent.service.trade.IVehicleReturnExpenseItemService;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: musi
 * @Date: 2024/4/6 14:49
 */
public class IOrderOpenServiceTest extends AbstractTest {

    @Resource
    private IOrderOpenService orderOpenService;
    @Resource
    private IVehicleReturnExpenseItemService vehicleReturnExpenseItemService;

    String url = "http://localhost:8081/open";

    static final String APP_KEY = "183e073cbb2cf03c9f2e7709d9ff5636";
    static final String APP_SECRET = "c2e381fcc4a5e9b935b953bea74f6344";

    @Test
    public void testQueryOrderDetail() {

        OpenOrderDetailQueryRequest request = new OpenOrderDetailQueryRequest();
        request.setMerchantId((long) 15);
        request.setIsFulfilled(true);
        request.setOrderSourceList(Arrays.asList((byte)2,(byte)10));
        request.setPageIndex(1);
        request.setPageSize(50);
//        request.setSourceOrderId("1128134481563001");
        ResultResp resultResp = orderOpenService.queryOrderDetail(request);
        System.out.println(JSON.toJSONString(resultResp));
    }


    @Test
    public void testOrderDetail() throws IOException {
        String method = "qinglu.open.order.detail.query";
        String biz = "{\"orderId\":null,\"sourceOrderId\":\"1128135728239036\",\"orderDate\":null,\"pageSize\":50,\"pageIndex\":1}";

        Map<String, Object> form = buildSignRequest(method, biz, 50L);
        System.out.println(JSON.toJSONString(form));
        String result = HttpUtil.post("http://open.qinglusaas.com/open", form);
        System.out.println(result);
    }



    @Test
    public void testOrderEvent() throws IOException {
        String method = "qinglu.open.order.event.sync";
        String biz = "{\"orderSource\":4,\"sourceMerchantId\":\"20000038603\",\"sourceOrderId\":\"RC202504127309494260863926415\"}";

        Map<String, Object> form = buildSignRequest(method, biz, 16L);
        System.out.println(JSON.toJSONString(form));
        String result = HttpUtil.post("http://openv2.qinglusaas-dev.com/open", form);
        System.out.println(result);
    }

    @Test
    public void testOrderEvent1() throws IOException {
        vehicleReturnExpenseItemService.saveExpense(1704270L, 130504L, 100L, 18L, 0L);
        System.out.println("success");
    }




    public static Map<String, Object> buildSignRequest(String method, String bizRequest, Long merchantId) throws IOException {
        // 设置接口参数
        Map<String, Object> form = new HashMap<>();
        // 排队取号
        form.put("appKey", APP_KEY);
        form.put("timestamp", System.currentTimeMillis());
        form.put("v", "1.0");
        form.put("lang", "zh_CN");
        form.put("merchantId", merchantId);
        form.put("method", method);
        form.put("bizRequest", bizRequest);

        // 对请求参数列表进行签名
        String sign = SignUtils.sign(form, APP_SECRET);
        form.put("sign", sign);
        return form;
    }
}
