package com.ql.rent.open.service;

import com.alibaba.fastjson2.JSON;
import com.ql.dto.open.request.vehicle.VehicleDeviceControlRequest;
import com.ql.dto.open.request.vehicle.VehicleDeviceInfoBatchRequest;
import com.ql.dto.open.request.vehicle.VehicleDeviceOpenQuery;
import com.ql.rent.AbstractTest;
import com.ql.rent.client.IVehicleDeviceOpenService;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class IVehicleDeviceOpenServiceTest extends AbstractTest {
    @Resource
    private IVehicleDeviceOpenService vehicleDeviceOpenService;

    @Test
    public void vehicleDeviceInfo() {
        VehicleDeviceControlRequest vehicleDeviceControlRequest = new VehicleDeviceControlRequest();
        vehicleDeviceControlRequest.setCommand("501");
        vehicleDeviceControlRequest.setThirdVehicleId("LSVUD6E4XN2044150");
        vehicleDeviceControlRequest.setOpTime(new Date(1731310187317L));
        vehicleDeviceControlRequest = JSON.parseObject("{\"opTime\":1731310103298,\"thirdVehicleId\":\"LSVUD6E4XN2044150\",\"command\":\"400\"}", VehicleDeviceControlRequest.class);
        vehicleDeviceControlRequest.setMerchantId(58L);
        vehicleDeviceOpenService.vehicleDeviceControl(vehicleDeviceControlRequest);
    }

    @Test
    public void vehicleDeviceInfoBatch() {
        VehicleDeviceInfoBatchRequest request = new VehicleDeviceInfoBatchRequest();
        List<VehicleDeviceOpenQuery> VehicleDeviceOpenQuery = new ArrayList<>();
        com.ql.dto.open.request.vehicle.VehicleDeviceOpenQuery vehicleDeviceOpenQuery = new VehicleDeviceOpenQuery();
        vehicleDeviceOpenQuery.setThirdVehicleId("LSVUD6E4XN2044150");
        vehicleDeviceOpenQuery.setDeviceNo("640031026");
        VehicleDeviceOpenQuery.add(vehicleDeviceOpenQuery);

        request.setMerchantId(58L);
        request.setList(VehicleDeviceOpenQuery);
        vehicleDeviceOpenService.vehicleDeviceInfoBatch(request);
    }
}
