package com.ql.rent.open.service.vehicle;

import com.ql.dto.ResultResp;
import com.ql.dto.open.request.vehicle.illegal.BindTrafficManagementOpenRequest;
import com.ql.dto.open.request.vehicle.illegal.BindVehicleOpenRequest;
import com.ql.dto.open.request.vehicle.illegal.TrafficCount;
import com.ql.rent.param.trade.MallBindAccountRequest;
import com.ql.rent.param.vehicle.VehicleInfoQueryParam;
import com.ql.rent.service.merchant.TrafficManagementBindService;
import com.ql.rent.service.trade.IVehicleIllegalTransferDetailService;
import com.ql.rent.service.vehicle.IVehicleInfoService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.vehicle.VehicleInfoVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * VehicleViolationOpenService单元测试类
 * 测试 bindVehicle 和 bindTrafficManagement 方法
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("车辆违章开放服务测试")
class VehicleViolationOpenServiceTest {

    @Mock
    private IVehicleIllegalTransferDetailService vehicleIllegalTransferDetailService;

    @Mock
    private IVehicleInfoService vehicleInfoService;

    @Mock
    private TrafficManagementBindService trafficManagementBindService;

    @InjectMocks
    private VehicleViolationOpenService vehicleViolationOpenService;

    private BindVehicleOpenRequest bindVehicleRequest;
    private BindTrafficManagementOpenRequest bindTrafficManagementRequest;
    private VehicleInfoVO vehicleInfo1;
    private VehicleInfoVO vehicleInfo2;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        bindVehicleRequest = new BindVehicleOpenRequest();
        bindVehicleRequest.setMerchantId(1001L);
        bindVehicleRequest.setMallOrderId(2001L);
        bindVehicleRequest.setCarNumberList(Arrays.asList("京A12345", "京B67890"));

        // 初始化车辆信息
        vehicleInfo1 = new VehicleInfoVO();
        vehicleInfo1.setId(3001L);
        vehicleInfo1.setLicense("京A12345");

        vehicleInfo2 = new VehicleInfoVO();
        vehicleInfo2.setId(3002L);
        vehicleInfo2.setLicense("京B67890");

        // 初始化交管绑定请求
        TrafficCount trafficCount = new TrafficCount();
        trafficCount.setUsername("testuser");
        trafficCount.setPassword("testpass");
        trafficCount.setCarNumberList(Arrays.asList("京A12345", "京B67890"));

        bindTrafficManagementRequest = new BindTrafficManagementOpenRequest();
        bindTrafficManagementRequest.setMerchantId(1001L);
        bindTrafficManagementRequest.setCountList(Collections.singletonList(trafficCount));
    }

    @Test
    @DisplayName("绑定车辆成功测试")
    void testBindVehicleSuccess() {
        // 准备Mock数据
        Result<List<VehicleInfoVO>> vehicleListResult = ResultUtil.successResult(Arrays.asList(vehicleInfo1, vehicleInfo2));
        Result<Boolean> bindResult = ResultUtil.successResult(true);

        // Mock服务调用
        when(vehicleInfoService.listVehicleInfo(any(VehicleInfoQueryParam.class))).thenReturn(vehicleListResult);
        when(vehicleIllegalTransferDetailService.saveTransferVehicleBind(any(LoginVo.class), anyList(), anyLong())).thenReturn(bindResult);

        // 执行测试
        ResultResp<Boolean> result = vehicleViolationOpenService.bindVehicle(bindVehicleRequest);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.success());
        assertTrue(result.getData());

        // 验证服务调用
        verify(vehicleInfoService).listVehicleInfo(any(VehicleInfoQueryParam.class));
        verify(vehicleIllegalTransferDetailService).saveTransferVehicleBind(any(LoginVo.class), anyList(), eq(2001L));
    }

    @Test
    @DisplayName("绑定车辆失败-商城订单ID为空")
    void testBindVehicleFailWithNullMallOrderId() {
        // 准备测试数据
        bindVehicleRequest.setMallOrderId(null);

        // 执行测试
        ResultResp<Boolean> result = vehicleViolationOpenService.bindVehicle(bindVehicleRequest);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.success());
        assertEquals("商城订单ID不能为空", result.getMessage());

        // 验证没有调用服务
        verify(vehicleInfoService, never()).listVehicleInfo(any());
        verify(vehicleIllegalTransferDetailService, never()).saveTransferVehicleBind(any(), any(), any());
    }

    @Test
    @DisplayName("绑定车辆失败-车牌号列表为空")
    void testBindVehicleFailWithEmptyCarNumberList() {
        // 准备测试数据
        bindVehicleRequest.setCarNumberList(Collections.emptyList());

        // 执行测试
        ResultResp<Boolean> result = vehicleViolationOpenService.bindVehicle(bindVehicleRequest);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.success());
        assertEquals("车牌号列表不能为空", result.getMessage());

        // 验证没有调用服务
        verify(vehicleInfoService, never()).listVehicleInfo(any());
        verify(vehicleIllegalTransferDetailService, never()).saveTransferVehicleBind(any(), any(), any());
    }

    @Test
    @DisplayName("绑定车辆失败-未找到对应车辆")
    void testBindVehicleFailWithVehicleNotFound() {
        // 准备Mock数据 - 只返回一个车辆，模拟另一个车辆未找到
        Result<List<VehicleInfoVO>> vehicleListResult = ResultUtil.successResult(Collections.singletonList(vehicleInfo1));

        // Mock服务调用
        when(vehicleInfoService.listVehicleInfo(any(VehicleInfoQueryParam.class))).thenReturn(vehicleListResult);

        // 执行测试
        ResultResp<Boolean> result = vehicleViolationOpenService.bindVehicle(bindVehicleRequest);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.success());
        assertTrue(result.getMessage().contains("未找到车牌号对应的车辆"));
        assertTrue(result.getMessage().contains("京B67890"));

        // 验证服务调用
        verify(vehicleInfoService).listVehicleInfo(any(VehicleInfoQueryParam.class));
        verify(vehicleIllegalTransferDetailService, never()).saveTransferVehicleBind(any(), any(), any());
    }

    @Test
    @DisplayName("绑定车辆失败-查询车辆信息失败")
    void testBindVehicleFailWithVehicleQueryError() {
        // 准备Mock数据
        Result<List<VehicleInfoVO>> vehicleListResult = ResultUtil.failResult("查询车辆信息失败");

        // Mock服务调用
        when(vehicleInfoService.listVehicleInfo(any(VehicleInfoQueryParam.class))).thenReturn(vehicleListResult);

        // 执行测试
        ResultResp<Boolean> result = vehicleViolationOpenService.bindVehicle(bindVehicleRequest);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.success());

        // 验证服务调用
        verify(vehicleInfoService).listVehicleInfo(any(VehicleInfoQueryParam.class));
        verify(vehicleIllegalTransferDetailService, never()).saveTransferVehicleBind(any(), any(), any());
    }

    @Test
    @DisplayName("绑定车辆失败-绑定服务调用失败")
    void testBindVehicleFailWithBindServiceError() {
        // 准备Mock数据
        Result<List<VehicleInfoVO>> vehicleListResult = ResultUtil.successResult(Arrays.asList(vehicleInfo1, vehicleInfo2));
        Result<Boolean> bindResult = ResultUtil.failResult("绑定失败");

        // Mock服务调用
        when(vehicleInfoService.listVehicleInfo(any(VehicleInfoQueryParam.class))).thenReturn(vehicleListResult);
        when(vehicleIllegalTransferDetailService.saveTransferVehicleBind(any(LoginVo.class), anyList(), anyLong())).thenReturn(bindResult);

        // 执行测试
        ResultResp<Boolean> result = vehicleViolationOpenService.bindVehicle(bindVehicleRequest);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.success());
        assertEquals("绑定失败", result.getMessage());

        // 验证服务调用
        verify(vehicleInfoService).listVehicleInfo(any(VehicleInfoQueryParam.class));
        verify(vehicleIllegalTransferDetailService).saveTransferVehicleBind(any(LoginVo.class), anyList(), eq(2001L));
    }

    @Test
    @DisplayName("绑定交管账号成功测试")
    void testBindTrafficManagementSuccess() {
        // 准备Mock数据
        Result<List<VehicleInfoVO>> vehicleListResult = ResultUtil.successResult(Arrays.asList(vehicleInfo1, vehicleInfo2));

        // Mock服务调用
        when(vehicleInfoService.listVehicleInfo(any(VehicleInfoQueryParam.class))).thenReturn(vehicleListResult);
        doNothing().when(trafficManagementBindService).replaceIllegalTransferAccount(any(LoginVo.class), any(MallBindAccountRequest.class));

        // 执行测试
        ResultResp<Boolean> result = vehicleViolationOpenService.bindTrafficManagement(bindTrafficManagementRequest);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.success());
        assertTrue(result.getData());

        // 验证服务调用
        verify(vehicleInfoService).listVehicleInfo(any(VehicleInfoQueryParam.class));
        verify(trafficManagementBindService).replaceIllegalTransferAccount(any(LoginVo.class), any(MallBindAccountRequest.class));
    }

    @Test
    @DisplayName("绑定交管账号失败-账号列表为空")
    void testBindTrafficManagementFailWithEmptyCountList() {
        // 准备测试数据
        bindTrafficManagementRequest.setCountList(Collections.emptyList());

        // 执行测试
        ResultResp<Boolean> result = vehicleViolationOpenService.bindTrafficManagement(bindTrafficManagementRequest);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.success());
        assertEquals("122账号信息列表不能为空", result.getMessage());

        // 验证没有调用服务
        verify(vehicleInfoService, never()).listVehicleInfo(any());
        verify(trafficManagementBindService, never()).replaceIllegalTransferAccount(any(), any());
    }

    @Test
    @DisplayName("绑定交管账号失败-用户名为空")
    void testBindTrafficManagementFailWithEmptyUsername() {
        // 准备测试数据
        TrafficCount trafficCount = new TrafficCount();
        trafficCount.setUsername(""); // 空用户名
        trafficCount.setPassword("testpass");
        trafficCount.setCarNumberList(Arrays.asList("京A12345"));

        bindTrafficManagementRequest.setCountList(Collections.singletonList(trafficCount));

        // 执行测试
        ResultResp<Boolean> result = vehicleViolationOpenService.bindTrafficManagement(bindTrafficManagementRequest);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.success());
        assertEquals("用户名不能为空", result.getMessage());

        // 验证没有调用服务
        verify(vehicleInfoService, never()).listVehicleInfo(any());
        verify(trafficManagementBindService, never()).replaceIllegalTransferAccount(any(), any());
    }

    @Test
    @DisplayName("绑定交管账号失败-密码为空")
    void testBindTrafficManagementFailWithEmptyPassword() {
        // 准备测试数据
        TrafficCount trafficCount = new TrafficCount();
        trafficCount.setUsername("testuser");
        trafficCount.setPassword(""); // 空密码
        trafficCount.setCarNumberList(Arrays.asList("京A12345"));

        bindTrafficManagementRequest.setCountList(Collections.singletonList(trafficCount));

        // 执行测试
        ResultResp<Boolean> result = vehicleViolationOpenService.bindTrafficManagement(bindTrafficManagementRequest);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.success());
        assertEquals("密码不能为空", result.getMessage());

        // 验证没有调用服务
        verify(vehicleInfoService, never()).listVehicleInfo(any());
        verify(trafficManagementBindService, never()).replaceIllegalTransferAccount(any(), any());
    }

    @Test
    @DisplayName("绑定交管账号成功-车牌号列表为空")
    void testBindTrafficManagementSuccessWithEmptyCarNumbers() {
        // 准备测试数据
        TrafficCount trafficCount = new TrafficCount();
        trafficCount.setUsername("testuser");
        trafficCount.setPassword("testpass");
        trafficCount.setCarNumberList(Collections.emptyList()); // 空车牌号列表，业务允许

        bindTrafficManagementRequest.setCountList(Collections.singletonList(trafficCount));

        // Mock服务调用 - 车牌号为空时不需要查询车辆信息，使用全量替换方式
        doNothing().when(trafficManagementBindService).replaceIllegalTransferAccount(any(LoginVo.class), any(MallBindAccountRequest.class));

        // 执行测试
        ResultResp<Boolean> result = vehicleViolationOpenService.bindTrafficManagement(bindTrafficManagementRequest);

        // 验证结果 - 车牌号列表为空应该允许成功
        assertNotNull(result);
        assertTrue(result.success());
        assertTrue(result.getData());

        // 验证服务调用 - 车牌号为空时不需要查询车辆信息，但会全量替换账号
        verify(vehicleInfoService, never()).listVehicleInfo(any());
        verify(trafficManagementBindService).replaceIllegalTransferAccount(any(LoginVo.class), any(MallBindAccountRequest.class));
    }

    @Test
    @DisplayName("绑定交管账号失败-未找到对应车辆")
    void testBindTrafficManagementFailWithVehicleNotFound() {
        // 准备Mock数据 - 只返回一个车辆，模拟另一个车辆未找到
        Result<List<VehicleInfoVO>> vehicleListResult = ResultUtil.successResult(Collections.singletonList(vehicleInfo1));

        // Mock服务调用
        when(vehicleInfoService.listVehicleInfo(any(VehicleInfoQueryParam.class))).thenReturn(vehicleListResult);

        // 执行测试
        ResultResp<Boolean> result = vehicleViolationOpenService.bindTrafficManagement(bindTrafficManagementRequest);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.success());
        assertTrue(result.getMessage().contains("未找到车牌号对应的车辆"));
        assertTrue(result.getMessage().contains("京B67890"));

        // 验证服务调用
        verify(vehicleInfoService).listVehicleInfo(any(VehicleInfoQueryParam.class));
        verify(trafficManagementBindService, never()).replaceIllegalTransferAccount(any(), any());
    }

    @Test
    @DisplayName("绑定交管账号失败-交管服务调用异常")
    void testBindTrafficManagementFailWithServiceException() {
        // 准备Mock数据
        Result<List<VehicleInfoVO>> vehicleListResult = ResultUtil.successResult(Arrays.asList(vehicleInfo1, vehicleInfo2));

        // Mock服务调用
        when(vehicleInfoService.listVehicleInfo(any(VehicleInfoQueryParam.class))).thenReturn(vehicleListResult);
        doThrow(new RuntimeException("交管服务异常")).when(trafficManagementBindService).replaceIllegalTransferAccount(any(LoginVo.class), any(MallBindAccountRequest.class));

        // 执行测试
        ResultResp<Boolean> result = vehicleViolationOpenService.bindTrafficManagement(bindTrafficManagementRequest);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.success());
        assertTrue(result.getMessage().contains("绑定122账号及车辆异常"));

        // 验证服务调用
        verify(vehicleInfoService).listVehicleInfo(any(VehicleInfoQueryParam.class));
        verify(trafficManagementBindService).replaceIllegalTransferAccount(any(LoginVo.class), any(MallBindAccountRequest.class));
    }

    @Test
    @DisplayName("绑定交管账号成功-多个账号测试")
    void testBindTrafficManagementSuccessWithMultipleAccounts() {
        // 准备测试数据 - 多个交管账号
        TrafficCount trafficCount1 = new TrafficCount();
        trafficCount1.setUsername("testuser1");
        trafficCount1.setPassword("testpass1");
        trafficCount1.setCarNumberList(Collections.singletonList("京A12345"));

        TrafficCount trafficCount2 = new TrafficCount();
        trafficCount2.setUsername("testuser2");
        trafficCount2.setPassword("testpass2");
        trafficCount2.setCarNumberList(Collections.singletonList("京B67890"));

        bindTrafficManagementRequest.setCountList(Arrays.asList(trafficCount1, trafficCount2));

        // 准备Mock数据
        Result<List<VehicleInfoVO>> vehicleListResult = ResultUtil.successResult(Arrays.asList(vehicleInfo1, vehicleInfo2));

        // Mock服务调用
        when(vehicleInfoService.listVehicleInfo(any(VehicleInfoQueryParam.class))).thenReturn(vehicleListResult);
        doNothing().when(trafficManagementBindService).replaceIllegalTransferAccount(any(LoginVo.class), any(MallBindAccountRequest.class));

        // 执行测试
        ResultResp<Boolean> result = vehicleViolationOpenService.bindTrafficManagement(bindTrafficManagementRequest);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.success());
        assertTrue(result.getData());

        // 验证服务调用 - 使用全量替换方式
        verify(vehicleInfoService).listVehicleInfo(any(VehicleInfoQueryParam.class));
        verify(trafficManagementBindService).replaceIllegalTransferAccount(any(LoginVo.class), any(MallBindAccountRequest.class));
    }

    @Test
    @DisplayName("绑定交管账号成功-全量替换验证")
    void testBindTrafficManagementFullReplace() {
        // 准备测试数据 - 模拟全量替换场景
        TrafficCount trafficCount1 = new TrafficCount();
        trafficCount1.setUsername("newuser1");
        trafficCount1.setPassword("newpass1");
        trafficCount1.setCarNumberList(Collections.singletonList("京A12345"));

        TrafficCount trafficCount2 = new TrafficCount();
        trafficCount2.setUsername("newuser2");
        trafficCount2.setPassword("newpass2");
        trafficCount2.setCarNumberList(Collections.emptyList()); // 无车牌号

        bindTrafficManagementRequest.setCountList(Arrays.asList(trafficCount1, trafficCount2));

        // 准备Mock数据
        Result<List<VehicleInfoVO>> vehicleListResult = ResultUtil.successResult(Collections.singletonList(vehicleInfo1));

        // Mock服务调用
        when(vehicleInfoService.listVehicleInfo(any(VehicleInfoQueryParam.class))).thenReturn(vehicleListResult);
        doNothing().when(trafficManagementBindService).replaceIllegalTransferAccount(any(LoginVo.class), any(MallBindAccountRequest.class));

        // 执行测试
        ResultResp<Boolean> result = vehicleViolationOpenService.bindTrafficManagement(bindTrafficManagementRequest);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.success());
        assertTrue(result.getData());

        // 验证服务调用 - 确保使用全量替换而非增量添加
        verify(vehicleInfoService).listVehicleInfo(any(VehicleInfoQueryParam.class));
        verify(trafficManagementBindService).replaceIllegalTransferAccount(any(LoginVo.class), any(MallBindAccountRequest.class));
        
        // 验证不会调用增量添加方法（如果存在的话）
        verify(trafficManagementBindService, never()).addIllegalTransferAccount(any(), any());
    }
}
