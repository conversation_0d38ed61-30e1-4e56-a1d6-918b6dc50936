package com.ql.rent.provider.common;

import com.alibaba.fastjson.JSON;
import com.ql.rent.param.vehicle.LicenseTypeParam;
import com.ql.rent.service.login.LoginService;
import com.ql.rent.service.vehicle.ILicenseTypeService;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.merchant.SysUserDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class LicenseTypeServiceImplTest {

    @Resource
    private ILicenseTypeService licenseTypeService;

    @Resource
    private LoginService loginService;

    @Test
    void saveLicenseType() {

        Result<SysUserDetailVo> userDetailVoResult = loginService.getUserInfo("a90cceaca80a43e28c95a8b49772ab06");

        LicenseTypeParam licenseTypeParam = new LicenseTypeParam();
        licenseTypeParam.setLicensePlateAreaId(3L);
        licenseTypeParam.setLicensePlateInitialsId(3L);
        licenseTypeParam.setMerchantId(10L);
        licenseTypeParam.setOpUserId(1L);
        Result<Integer> result = licenseTypeService.saveLicenseType(licenseTypeParam);
        log.info(JSON.toJSONString(result));
    }
}