package com.ql.rent.provider.merchant;

import com.ql.enums.ThirdPayEnum;
import com.ql.rent.AbstractTest;
import com.ql.rent.enums.merchant.RoyaltyRelationTypeEnum;
import com.ql.rent.param.merchant.RoyaltyRelationParam;
import com.ql.rent.service.merchant.IRoyaltyRelationService;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;

public class RoyaltyRelationServiceTest extends AbstractTest {
    @Resource
    private IRoyaltyRelationService royaltyRelationService;

    @Test
    public void testRoyaltyBind() {
        RoyaltyRelationParam royaltyRelationParam = new RoyaltyRelationParam();
        royaltyRelationParam.setMerchantId(44L);
        royaltyRelationParam.setRoyaltyRate(new BigDecimal("0.10"));
        royaltyRelationParam.setAccount("***********");
        royaltyRelationParam.setName("胡誉迈");
        royaltyRelationParam.setType(RoyaltyRelationTypeEnum.LOGIN_NAME.getType());
        royaltyRelationParam.setThirdSource(ThirdPayEnum.ThirdPaySource.ALI_PAY.getSource());
        royaltyRelationService.royaltyBind(royaltyRelationParam);
    }

    @Test
    public void testRoyaltyUnBind() {
        RoyaltyRelationParam royaltyRelationParam = new RoyaltyRelationParam();
        royaltyRelationParam.setId(3L);
        royaltyRelationParam.setMerchantId(44L);
        royaltyRelationParam.setThirdSource(ThirdPayEnum.ThirdPaySource.ALI_PAY.getSource());
        royaltyRelationService.royaltyUnbind(royaltyRelationParam);
    }
}
