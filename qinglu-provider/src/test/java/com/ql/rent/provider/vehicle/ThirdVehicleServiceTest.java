package com.ql.rent.provider.vehicle;

import com.alibaba.fastjson.JSON;
import com.ql.rent.api.aggregate.model.dto.*;
import com.ql.rent.param.vehicle.VehicleBusyParam;
import com.ql.rent.service.vehicle.IThirdVehicleService;
import com.ql.rent.service.vehicle.IVehicleBusyService;
import com.ql.rent.share.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
class ThirdVehicleServiceTest {

    @Resource
    private IThirdVehicleService thirdVehicleService;
    @Resource
    private IVehicleBusyService vehicleBusyService;

    @Test
    public void selectModelCalendarPrice() throws Exception {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        Long merchantId = 2L;
        Long channelId = 2L;
        Long vehicleModelId = 10L;
        Date pickUpTime = dateFormat.parse("2023-02-16 10:00");
        Date returnTime = dateFormat.parse("2023-02-16 14:00");
        VehicleModelUniDTO dto = new VehicleModelUniDTO();
        dto.setStoreId(1L);
        dto.setVehicleModelId(vehicleModelId);
        List<DailyPriceDTO> id = thirdVehicleService.selectModelCalendarPrice(merchantId, channelId, dto, pickUpTime, returnTime, false, null,false);
        System.out.println(JSON.toJSONString(id));
    }

    @Test
    public void selectModelCalendarPrices() throws Exception {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        Long merchantId = 2L;
        Long channelId = 2L;
        List<VehicleModelUniDTO> dtos = new ArrayList<>();
        VehicleModelUniDTO dto = new VehicleModelUniDTO();
//        dto.setStoreId(1L);
//        dto.setVehicleModelId(1L);
//        dtos.add(dto);
        dto = new VehicleModelUniDTO();
        dto.setStoreId(3L);
        dto.setVehicleModelId(14L);
        dtos.add(dto);

        Date pickUpTime = dateFormat.parse("2023-06-16 20:00");
        Date returnTime = dateFormat.parse("2023-06-18 21:00");
        List<VehicleModelUniDTO<List<DailyPriceDTO>>> id = thirdVehicleService.selectModelCalendarPrice(merchantId, channelId, dtos, pickUpTime, returnTime, false, null, null, null, false);
        System.out.println(JSON.toJSONString(id));
    }

    @Test
    public void selectModelService() throws Exception {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        Date pickUpTime = dateFormat.parse("2022-11-27 10:00");
        Date returnTime = dateFormat.parse("2022-11-29 12:00");

        List<VehicleModelUniDTO> dtos = new ArrayList<>();
        VehicleModelUniDTO dto = new VehicleModelUniDTO();
        dto.setStoreId(3L);
        dto.setVehicleModelId(14L);
        dtos.add(dto);
//        dto = new VehicleModelUniDTO();
//        dto.setStoreId(1001L);
//        dto.setVehicleModelId(18L);
//        dtos.add(dto);

        List<VehicleModelUniDTO<List<ServiceItemDTO>>> ret = thirdVehicleService.selectModelService(2L, dtos, 2L, pickUpTime, returnTime, true, null,null);
        System.out.println(JSON.toJSONString(ret));
    }

    @Test
    public void getStoreCharge() throws Exception {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        Date pickUpTime = dateFormat.parse("2022-11-27 10:00");
        Date returnTime = dateFormat.parse("2022-11-29 12:00");
        List<StoreChargeDTO<List<DailyPriceListDTO>>> ret = thirdVehicleService.getStoreCharge(45L, 2L, Arrays.asList(355L), pickUpTime, returnTime, null, "", false);
        System.out.println(JSON.toJSONString(ret));
    }

    @Test
    public void getVehicleAndLock() throws Exception {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        Date pickUpTime = dateFormat.parse("2025-11-27 10:00");
        Date returnTime = dateFormat.parse("2025-11-29 12:00");

        String json = "{\"endTime\":1677315600000,\"frcedScheduling\":0,\"merchantId\":2,\"sourceId\":1133,\"sourceType\":1,\"startTime\":1677290400000,\"storeId\":4,\"updTimeFlg\":true,\"vehicleModelId\":61}";

        json = "{\"channelId\":2,\"endTime\":1700145000000,\"frcedScheduling\":0,\"merchantId\":58,\"returnStoreId\":637,\"saasSync\":0,\"sourceId\":116158,\"sourceType\":1,\"startTime\":1700058600000,\"storeId\":637,\"thirdParentSourceId\":\"\",\"thirdSourceId\":\"\",\"updTimeFlg\":true,\"vehicleModelId\":2078}";

        json = "{\"channelId\":2,\"endTime\":1700145000000,\"frcedScheduling\":0,\"merchantId\":58,\"returnStoreId\":637,\"saasSync\":0,\"sourceId\":116158,\"sourceType\":1,\"startTime\":1700058600000,\"storeId\":637,\"thirdParentSourceId\":\"\",\"thirdSourceId\":\"\",\"updTimeFlg\":true,\"vehicleModelId\":2078}";

        json = "{\"channelId\":2,\"diffTime\":0,\"endTime\":1751522400000,\"frcedScheduling\":0,\"levelCorrEarliestRegisterTime\":1688364000000,\"merchantId\":45,\"openStockMerchantCheckReq\":{\"channelId\":2,\"endTime\":1751522400000,\"levelCorrEarliestRegisterTime\":1688364000000,\"mainOrder\":{\"idNo\":\"410523199206055015\",\"idType\":\"1\",\"mobile\":\"13162779073\",\"name\":\"孟远航\",\"payAmount\":21000,\"pickupAddrType\":-1,\"pickupCircleId\":\"-1\",\"receivableAmount\":21000,\"returnAddrType\":-1,\"returnCircleId\":\"-1\"},\"returnStoreId\":\"355\",\"sourceType\":2,\"startTime\":1751436000000,\"storeId\":\"355\",\"vehicleModelId\":\"1450\"},\"returnStoreId\":355,\"saasSync\":0,\"selfServiceReturn\":0,\"sourceId\":-1,\"sourceType\":1,\"startTime\":1751436000000,\"storeId\":355,\"thirdParentSourceId\":\"\",\"thirdSourceId\":\"\",\"tiIncludeEtc\":0,\"updTimeFlg\":true,\"vehicleModelId\":1450}";

        json = "{\"busyExt\":{\"createOffline\":1},\"channelId\":1,\"diffTime\":0,\"endTime\":1755648000000,\"frcedScheduling\":1,\"isAllopatry\":false,\"merchantId\":61,\"parentSourceId\":1706778,\"returnStoreId\":4707,\"saasSync\":0,\"selfServiceReturn\":0,\"sourceId\":33770844,\"sourceType\":3,\"startTime\":1755579600000,\"storeId\":4707,\"thirdParentSourceId\":\"\",\"thirdSourceId\":\"\",\"tiIncludeEtc\":0,\"updTimeFlg\":true,\"vehicleId\":74250,\"vehicleModelId\":12910}";

        VehicleBusyParam parm = JSON.parseObject(json, VehicleBusyParam.class);
        Result<Long> ret = vehicleBusyService.getVehicleAndLockCheck(parm);
        System.out.println(JSON.toJSONString(ret));
    }

    @Test
    public void getVehicleAndLockCheck() throws Exception {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        Date pickUpTime = dateFormat.parse("2022-11-27 10:00");
        Date returnTime = dateFormat.parse("2022-11-29 12:00");

        String json = "{\"endTime\":1677315600000,\"frcedScheduling\":0,\"merchantId\":2,\"sourceId\":1133,\"sourceType\":1,\"startTime\":1677290400000,\"storeId\":4,\"updTimeFlg\":true,\"vehicleModelId\":61}";

        json = "{\"endTime\":1677722400000,\"frcedScheduling\":0,\"merchantId\":2,\"sourceId\":1135,\"sourceType\":1,\"startTime\":1677636000000,\"storeId\":3,\"updTimeFlg\":true,\"vehicleModelId\":26}";

        json = "{\"channelId\":2,\"endTime\":1750471200000,\"frcedScheduling\":0,\"merchantId\":15,\"openStockMerchantCheckReq\":{\"channelId\":2,\"endTime\":1750471200000,\"mainOrder\":{\"idNo\":\"32092319980906632X\",\"idType\":\"1\",\"mobile\":\"17715199896\",\"name\":\"郑雨虹\",\"payAmount\":16000,\"pickupAddrType\":1,\"pickupCircleId\":\"9544\",\"receivableAmount\":16000,\"returnAddrType\":1,\"returnCircleId\":\"9544\"},\"returnStoreId\":\"18\",\"sourceType\":2,\"startTime\":1750384800000,\"storeId\":\"18\",\"vehicleModelId\":\"12954\"},\"returnStoreId\":18,\"saasSync\":0,\"selfServiceReturn\":0,\"sourceId\":-1,\"sourceType\":1,\"startTime\":1750384800000,\"storeId\":18,\"thirdParentSourceId\":\"\",\"thirdSourceId\":\"\",\"tiIncludeEtc\":0,\"updTimeFlg\":true,\"vehicleModelId\":12954}";

        json = "{\"busyExt\":{\"createOffline\":1},\"channelId\":1,\"endTime\":1735142400000,\"frcedScheduling\":0,\"isAllopatry\":false,\"merchantId\":44,\"returnStoreId\":353,\"saasSync\":0,\"selfServiceReturn\":0,\"sourceId\":1702597,\"sourceType\":1,\"startTime\":1735120440000,\"storeId\":353,\"thirdParentSourceId\":\"\",\"thirdSourceId\":\"\",\"tiIncludeEtc\":0,\"updTimeFlg\":true,\"vehicleId\":3585,\"vehicleModelId\":1376}";

        json = "{\"busyExt\":{\"createOffline\":1},\"channelId\":1,\"endTime\":1735642800000,\"frcedScheduling\":0,\"isAllopatry\":false,\"merchantId\":44,\"parentSourceId\":1702697,\"returnStoreId\":353,\"saasSync\":0,\"selfServiceReturn\":0,\"sourceId\":33534456,\"sourceType\":3,\"startTime\":1735383600000,\"storeId\":353,\"thirdParentSourceId\":\"\",\"thirdSourceId\":\"\",\"tiIncludeEtc\":0,\"updTimeFlg\":true,\"vehicleId\":3585,\"vehicleModelId\":1376}";

        json = "{\"channelId\":2,\"diffTime\":0,\"endTime\":1745562600000,\"frcedScheduling\":0,\"merchantId\":58,\"openStockMerchantCheckReq\":{\"channelId\":2,\"endTime\":1745562600000,\"mainOrder\":{\"idNo\":\"412823199811112012\",\"idType\":\"1\",\"mobile\":\"16639708523\",\"name\":\"吴瑞森\",\"payAmount\":16800,\"pickupAddrType\":-1,\"pickupCircleId\":\"-1\",\"receivableAmount\":16800,\"returnAddrType\":-1,\"returnCircleId\":\"-1\"},\"returnStoreId\":\"637\",\"sourceType\":2,\"startTime\":1745469000000,\"storeId\":\"637\",\"vehicleModelId\":\"2048\"},\"returnStoreId\":637,\"saasSync\":0,\"selfServiceReturn\":0,\"sourceId\":-1,\"sourceType\":1,\"startTime\":1745469000000,\"storeId\":637,\"thirdParentSourceId\":\"\",\"thirdSourceId\":\"\",\"tiIncludeEtc\":0,\"updTimeFlg\":true,\"vehicleModelId\":2048,\"levelCorrEarliestRegisterTime\":1745464346160}";
        VehicleBusyParam parm = JSON.parseObject(json, VehicleBusyParam.class);
        Result<Long> ret = vehicleBusyService.getVehicleAndLock(parm);
        System.out.println(JSON.toJSONString(ret));
    }

    @Test
    public void splitBusiDate() throws Exception {
//        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
//        Date pickUpTime = dateFormat.parse("2022-11-27 10:00");
//        Date returnTime = dateFormat.parse("2022-11-29 12:00");
//        List<SplitBusiDateVO> a = thirdVehicleService.splitBusiDate(148L, 1L, pickUpTime, returnTime);
//        System.out.println(a);
    }
}