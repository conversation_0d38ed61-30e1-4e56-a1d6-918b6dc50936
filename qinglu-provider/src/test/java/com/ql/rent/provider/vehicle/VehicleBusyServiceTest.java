package com.ql.rent.provider.vehicle;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.ql.dto.ResultResp;
import com.ql.dto.open.request.stock.*;
import com.ql.dto.open.response.OpenStockCheckResponse;
import com.ql.dto.open.response.stock.StockBusyResponse;
import com.ql.rent.client.IStockOpenService;
import com.ql.rent.param.vehicle.StockChangeParam;
import com.ql.rent.param.vehicle.StockOccupyQuery;
import com.ql.rent.param.vehicle.StockSummaryQuery;
import com.ql.rent.param.vehicle.VehicleBusyParam;
import com.ql.rent.service.vehicle.IVehicleBusyService;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.vehicle.StockOccupyVO;
import com.ql.rent.vo.vehicle.StockSummaryVO;
import com.ql.rent.vo.vehicle.StockVehicleBusyVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
class VehicleBusyServiceTest {

    @Resource
    private IVehicleBusyService vehicleBusyService;

    @Resource
    private IStockOpenService stockOpenService;

    @Test
    public void getVehicleAndLock() throws Exception {
        VehicleBusyParam parm = new VehicleBusyParam();
        parm.setMerchantId(10L);
        parm.setStoreId(1000L);
        parm.setVehicleModelId(18L);
        parm.setVehicleId(8L);
        parm.setSourceId(900L);
        parm.setSourceType(1);
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        parm.setStartTime(dateFormat.parse("2022-11-28 10:00:00").getTime());
        parm.setEndTime  (dateFormat.parse("2022-11-29 10:00:00").getTime());


//        parm = new VehicleBusyParam();
//        parm.setMerchantId(9L);
//        parm.setStoreId(1021L);
//        parm.setVehicleModelId(150L);
//        parm.setSourceId(70L);
//        parm.setSourceType(1);
//        parm.setVehicleId(65L);
//        parm.setStartTime(1669701600000L);
//        parm.setEndTime  (1669514400000L);

        String json = "{\"busyExt\":{\"createOffline\":0},\"channelId\":3,\"endTime\":1744434000000,\"frcedScheduling\":0,\"merchantId\":58,\"openStockMerchantCheckReq\":{\"mainOrder\":{\"idNo\":\"412823199811112012\",\"idType\":\"1\",\"mobile\":\"16639708523\",\"name\":\"吴瑞森\",\"payAmount\":6900,\"pickupAddrType\":1,\"pickupCircleId\":\"-1\",\"receivableAmount\":10900,\"returnAddrType\":1,\"returnCircleId\":\"-1\"},\"platOrderNo\":\"4295584657893744403\",\"sourceType\":2},\"returnStoreId\":637,\"saasSync\":0,\"selfServiceReturn\":0,\"sourceId\":1704279,\"sourceType\":1,\"startTime\":1744347600000,\"storeId\":637,\"thirdParentSourceId\":\"\",\"thirdSourceId\":\"\",\"tiIncludeEtc\":0,\"updTimeFlg\":true,\"vehicleModelId\":2096}";
        parm = JSON.parseObject(json, VehicleBusyParam.class);
        Result<Long> id = vehicleBusyService.getVehicleAndLock(parm);
        System.out.println("===========================================" + JSON.toJSONString(id));

        String a = "";
        a = "";

    }

    @Test
    public void cancelLock() throws Exception {
        vehicleBusyService.cancelLock(1, 124L);
    }

    @Test
    public void stockOccupy() throws Exception{
        StockOccupyQuery query = new StockOccupyQuery();
        query.setMerchantId(10L);
        query.setStoreId(1021L);
        query.setVehicleModelId(150L);
        query.setVehicleIds(Arrays.asList(2L,65L));
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        query.setStartTime(dateFormat.parse("2023-01-15").getTime());
        query.setEndTime  (dateFormat.parse("2023-01-29").getTime());
        Result<StockOccupyVO> stockSummary = vehicleBusyService.stockOccupy(false,  query, 10L, "");
        System.out.println(JSON.toJSONString(stockSummary));

    }

    @Test
    public void stockSummary() throws Exception{
        StockSummaryQuery query = new StockSummaryQuery();
        query.setMerchantId(10L);
        query.setStoreId(1000L);
        query.setVehicleModelId(18L);
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        query.setStartTime(dateFormat.parse("2022-11-02").getTime());
        query.setEndTime  (dateFormat.parse("2022-11-04").getTime());
        Result<StockSummaryVO> stockSummary = vehicleBusyService.stockSummary(query, "");
        System.out.println(JSON.toJSONString(stockSummary));

    }

    @Test
    public void selectBusyVehicleListForOrder() throws Exception {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        Date pickUpTime = dateFormat.parse("2023-01-13 18:35");
        Date returnTime = dateFormat.parse("2023-01-14 08:00");
        List<Long> vids = Arrays.asList(65L, 67L);
        Result<List<StockVehicleBusyVO>> id = vehicleBusyService.selectBusyVehicleByIdsForOrder(11L, vids, pickUpTime.getTime(), returnTime.getTime());
        System.out.println(JSON.toJSONString(id));
    }

    @Test
    public void prReturnVehicle() throws Exception {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date returnTime = dateFormat.parse("2023-02-12 22:56:28");
        vehicleBusyService.prReturnVehicle(647L, null, returnTime);
    }

    @Test
    public void testStockChange() throws Exception {
        StockChangeParam param = new StockChangeParam();
//        param.setMerchantId(44L);
//        param.setUserId(1L);
//        param.setFromVehicleId(3591L);
//        param.setStoreId(353L);
//        param.setToVehicleId(3590L);

        param.setMerchantId(55L);
        param.setUserId(112L);
        param.setFromVehicleId(4807L);
        param.setStoreId(375L);
        param.setToVehicleId(5008L);
        Result<Boolean> result = vehicleBusyService.stockChange(param);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    // open check stock
    public void checkStockFee() throws Exception {
            OpenStockCheckRequest param = new OpenStockCheckRequest();
            param.setStoreId("7298957988812161402");
            param.setReturnStoreId("7298957988812161402");
            param.setVehicleModelId("7301555578684571865");
            param.setStartTime(1698995195000L);
            param.setEndTime(1699945595000L);
            param.setDiffTime(60);
            param.setPlatOrderNo("test");
            param.setChannelId(4L);
            param.setSourceType(0);
            param.setVehicleId(null);
            param.setMerchantId(20000038603L);
            stockOpenService.checkStockFeeForHello(param);
    }

    @Test
    // open check stock
    public void stockSync() throws Exception {
        OpenStockSyncRequest param = new OpenStockSyncRequest();
        param.setMerchantId(57L);
        List<StockBusyRequest> stockBusyList = new ArrayList<>();

        StockBusyRequest busy = new StockBusyRequest();
        busy.setStoreId("test57_2");
        busy.setVehicleModelId("5555ccc");
        busy.setVehicleId("aaa444");
        busy.setStartTime(1710386917000l);
        busy.setEndTime(1710473317000l);
        busy.setEndIntervalTime(1710473317000l);
        busy.setThirdParentSourceId("A");
        busy.setThirdSourceId("A");
        busy.setStockId("qweasd");
        busy.setChannelId(1l);
        busy.setSourceType(1);
        busy.setDelFlg(true);
        stockBusyList.add(busy);

        param.setStockBusyList(stockBusyList);

        String json = "{\"merchantId\":58,\"stockBusyList\":[{\"channelId\":1,\"delFlg\":false,\"endIntervalTime\":1714147199999,\"endTime\":1714147200000,\"sourceType\":7,\"startTime\":1714060800000,\"stockId\":\"19021\",\"storeId\":\"MD123\",\"thirdParentSourceId\":\"\",\"thirdSourceId\":\"\",\"vehicleId\":\"VINTEST2311221000003\",\"vehicleModelId\":\"188\"}]}";
        param = JSON.parseObject(json, OpenStockSyncRequest.class);

        stockOpenService.stockSync(param);
    }

    @Test
    // open check stock
    public void getStockForOpen(){
        OpenGetStockRequest request = new OpenGetStockRequest();
        request.setMerchantId(15L);
        request.setStoreId("");
        request.setSaasStoreId(13L);
        request.setType((byte) 1);

        ResultResp<List<StockBusyResponse>> stock = stockOpenService.getStock(request);
        System.out.println(JSON.toJSONString(stock));
    }

    @Test
    public void testForceUpdate() {
        vehicleBusyService.forceUpdateByMainOrder(31L, 242187L, 445L, 259L);
    }
}