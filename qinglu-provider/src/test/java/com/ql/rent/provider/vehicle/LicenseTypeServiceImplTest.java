package com.ql.rent.provider.vehicle;

import com.ql.rent.dao.vehicle.LicenseTypeMapper;
import com.ql.rent.entity.vehicle.LicenseType;
import com.ql.rent.entity.vehicle.LicenseTypeExample;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.param.vehicle.LicenseTypeParam;
import com.ql.rent.service.vehicle.ILicensePlateAreaService;
import com.ql.rent.service.vehicle.ILicensePlateInitialsService;
import com.ql.rent.service.vehicle.IVehicleModelService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.common.LicensePlateAreaVO;
import com.ql.rent.vo.common.LicensePlateInitialsVO;
import com.ql.rent.vo.common.LicenseTypeVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class LicenseTypeServiceImplTest {

  @Mock
  private LicenseTypeMapper licenseTypeMapper;

  @Mock
  private ILicensePlateAreaService licensePlateAreaService;

  @Mock
  private ILicensePlateInitialsService licensePlateInitialsService;

  @Mock
  private IVehicleModelService vehicleModelService;

  @InjectMocks
  private LicenseTypeServiceImpl licenseTypeService;

  private LoginVo loginVo;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    loginVo = new LoginVo();
    loginVo.setUserId(1L);
    loginVo.setMerchantId(1L);
  }

  @Nested
  class ListLicenseTypeTests {
    @Test
    void shouldReturnFailWhenMerchantIdIsNull() {
      Result<PageListVo<LicenseTypeVO>> result = licenseTypeService.listLicenseType(null);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnPresetListWhenNoRecordsFound() {
      when(licenseTypeMapper.countByExample(any())).thenReturn(0L);

      Result<PageListVo<LicenseTypeVO>> result = licenseTypeService.listLicenseType(1L);

      assertTrue(result.isSuccess());
      assertNotNull(result.getModel());
      assertEquals(result.getModel().getList().size(), result.getModel().getCount());
    }

    @Test
    void shouldReturnCombinedListWhenRecordsExist() {
      LicenseType type = createLicenseType(1L, "测试牌照", false);
      when(licenseTypeMapper.countByExample(any())).thenReturn(1L);
      when(licenseTypeMapper.selectByExample(any())).thenReturn(Collections.singletonList(type));

      Result<PageListVo<LicenseTypeVO>> result = licenseTypeService.listLicenseType(1L);

      assertTrue(result.isSuccess());
      assertNotNull(result.getModel());
      assertTrue(result.getModel().getList().size() > 1); // 包含预设数据和新增数据
      assertTrue(result.getModel().getList().stream()
          .anyMatch(vo -> vo.getLicenseTypeName().equals("测试牌照")));
    }
  }

  @Nested
  class SaveLicenseTypeTests {
    @Test
    void shouldReturnFailWhenParamIsNull() {
      Result<Integer> result = licenseTypeService.saveLicenseType(null);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenRequiredFieldsAreNull() {
      LicenseTypeParam param = new LicenseTypeParam();

      Result<Integer> result = licenseTypeService.saveLicenseType(param);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenAreaNotFound() {
      LicenseTypeParam param = createValidParam();
      when(licensePlateAreaService.getById(any())).thenReturn(ResultUtil.failResult("区域不存在"));

      Result<Integer> result = licenseTypeService.saveLicenseType(param);

      assertFalse(result.isSuccess());
      assertEquals("区域不存在", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenLicenseTypeExists() {
      LicenseTypeParam param = createValidParam();
      when(licensePlateAreaService.getById(any()))
          .thenReturn(ResultUtil.successResult(createLicensePlateAreaVO(1L, "粤")));
      when(licenseTypeMapper.countByExample(any())).thenReturn(1L);

      Result<Integer> result = licenseTypeService.saveLicenseType(param);

      assertFalse(result.isSuccess());
      assertEquals("数据重复", result.getMessage());
    }

    @Test
    void shouldSaveSuccessfully() {
      LicenseTypeParam param = createValidParam();
      when(licensePlateAreaService.getById(any()))
          .thenReturn(ResultUtil.successResult(createLicensePlateAreaVO(1L, "粤")));
      when(licenseTypeMapper.countByExample(any())).thenReturn(0L);
      when(licenseTypeMapper.insertSelective(any())).thenReturn(1);

      Result<Integer> result = licenseTypeService.saveLicenseType(param);

      assertTrue(result.isSuccess());
      assertEquals(Integer.valueOf(1), result.getModel());

      ArgumentCaptor<LicenseType> captor = ArgumentCaptor.forClass(LicenseType.class);
      verify(licenseTypeMapper).insertSelective(captor.capture());
      LicenseType saved = captor.getValue();
      assertEquals(param.getMerchantId(), saved.getMerchantId());
      assertEquals("粤", saved.getLicenseTypeName());
      assertEquals(YesOrNoEnum.NO.getValue(), saved.getPreset());
    }
  }

  @Nested
  class DeleteLicenseTypeTests {
    @Test
    void shouldReturnFailWhenIdIsNull() {
      Result<Integer> result = licenseTypeService.deleteLicenseType(null, loginVo);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenLoginVoIsNull() {
      Result<Integer> result = licenseTypeService.deleteLicenseType(1L, null);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenLicenseTypeNotFound() {
      when(licenseTypeMapper.selectByPrimaryKey(1L)).thenReturn(null);

      Result<Integer> result = licenseTypeService.deleteLicenseType(1L, loginVo);

      assertFalse(result.isSuccess());
      assertEquals("牌照类型不存在，请刷新页面", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenDeletingPresetType() {
      LicenseType type = createLicenseType(1L, "测试牌照", true);
      when(licenseTypeMapper.selectByPrimaryKey(1L)).thenReturn(type);

      Result<Integer> result = licenseTypeService.deleteLicenseType(1L, loginVo);

      assertFalse(result.isSuccess());
      assertEquals("系统预设数据，无法删除", result.getMessage());
    }

    @Test
    void shouldDeleteSuccessfully() {
      LicenseType type = createLicenseType(1L, "测试牌照", false);
      when(licenseTypeMapper.selectByPrimaryKey(1L)).thenReturn(type);
      when(vehicleModelService.deleteVehicleModel(any(), any(), any(), any(), any()))
          .thenReturn(ResultUtil.successResult(1));
      when(licenseTypeMapper.updateByPrimaryKeySelective(any())).thenReturn(1);

      Result<Integer> result = licenseTypeService.deleteLicenseType(1L, loginVo);

      assertTrue(result.isSuccess());
      assertEquals(Integer.valueOf(1), result.getModel());

      ArgumentCaptor<LicenseType> captor = ArgumentCaptor.forClass(LicenseType.class);
      verify(licenseTypeMapper).updateByPrimaryKeySelective(captor.capture());
      LicenseType deleted = captor.getValue();
      assertEquals(YesOrNoEnum.YES.getValue(), deleted.getDeleted());
    }
  }

  @Nested
  class GetByIdsTests {
    @Test
    void shouldReturnEmptyListWhenIdsIsEmpty() {
      Result<List<LicenseTypeVO>> result = licenseTypeService.getByIds(Collections.emptyList());

      assertTrue(result.isSuccess());
      assertTrue(result.getModel().isEmpty());
    }

    @Test
    void shouldReturnLicenseTypes() {
      LicenseType type = createLicenseType(1L, "测试牌照", false);
      when(licenseTypeMapper.selectByExample(any())).thenReturn(Collections.singletonList(type));

      Result<List<LicenseTypeVO>> result = licenseTypeService.getByIds(Collections.singletonList(1L));

      assertTrue(result.isSuccess());
      assertEquals(1, result.getModel().size());
      assertEquals("测试牌照", result.getModel().get(0).getLicenseTypeName());
    }
  }

  private LicenseType createLicenseType(Long id, String name, boolean preset) {
    LicenseType type = new LicenseType();
    type.setId(id);
    type.setMerchantId(1L);
    type.setLicenseTypeName(name);
    type.setPreset(preset ? YesOrNoEnum.YES.getValue() : YesOrNoEnum.NO.getValue());
    type.setDeleted(YesOrNoEnum.NO.getValue());
    type.setCreateTime(System.currentTimeMillis());
    type.setOpTime(System.currentTimeMillis());
    type.setOpUserId(1L);
    return type;
  }

  private LicenseTypeParam createValidParam() {
    LicenseTypeParam param = new LicenseTypeParam();
    param.setMerchantId(1L);
    param.setOpUserId(1L);
    param.setLicensePlateAreaId(1L);
    return param;
  }

  private LicensePlateAreaVO createLicensePlateAreaVO(Long id, String name) {
    LicensePlateAreaVO vo = new LicensePlateAreaVO();
    vo.setId(id);
    vo.setAreaName(name);
    return vo;
  }
}