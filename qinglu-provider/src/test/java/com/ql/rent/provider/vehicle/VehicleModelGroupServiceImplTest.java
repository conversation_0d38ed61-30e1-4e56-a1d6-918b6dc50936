package com.ql.rent.provider.vehicle;

import com.ql.rent.dao.vehicle.VehicleModelGroupMapper;
import com.ql.rent.dao.vehicle.ex.VehicleModelMapperEx;
import com.ql.rent.entity.common.VehicleModelGroup;
import com.ql.rent.entity.vehicle.VehicleSubSery;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.param.vehicle.VehicleModelGroupParam;
import com.ql.rent.param.vehicle.VehicleModelGroupQueryParam;
import com.ql.rent.service.vehicle.IVehicleModelService;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.common.VehicleModelGroupVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
@DisplayName("VehicleModelGroupServiceImpl Tests")
class VehicleModelGroupServiceImplTest {

  @Mock
  private VehicleModelGroupMapper vehicleModelGroupMapper;

  @Mock
  private VehicleModelMapperEx vehicleModelMapperEx;

  @Mock
  private IVehicleModelService vehicleModelService;

  @InjectMocks
  private VehicleModelGroupServiceImpl vehicleModelGroupService;

  private static final Long VALID_MERCHANT_ID = 123L;
  private static final Long VALID_USER_ID = 456L;
  private static final Long VALID_GROUP_ID = 1L;

  private LoginVo validLoginVo;
  private VehicleModelGroupParam validGroupParam;
  private VehicleModelGroup validModelGroup;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);

    validLoginVo = new LoginVo();
    validLoginVo.setMerchantId(VALID_MERCHANT_ID);
    validLoginVo.setUserId(VALID_USER_ID);

    validGroupParam = new VehicleModelGroupParam();
    validGroupParam.setVehicleModelName("测试车型组");
    validGroupParam.setMerchantId(VALID_MERCHANT_ID);
    validGroupParam.setOpUserId(VALID_USER_ID);

    validModelGroup = new VehicleModelGroup();
    validModelGroup.setName("测试车型组");
    validModelGroup.setDeleted((byte) 0);
    validModelGroup.setMerchantId(VALID_MERCHANT_ID);
  }

  @Nested
  @DisplayName("SaveVehicleModelGroup Tests")
  class SaveVehicleModelGroupTests {

    @Test
    @DisplayName("Should successfully save vehicle model group")
    void shouldSuccessfullySaveVehicleModelGroup() {
      when(vehicleModelGroupMapper.insertSelective(any(VehicleModelGroup.class))).thenReturn(1);

      Result<Integer> result = vehicleModelGroupService.saveVehicleModelGroup(validGroupParam);

      assertThat(result.isSuccess()).isTrue();
      assertThat(result.getModel()).isEqualTo(1);
      verify(vehicleModelGroupMapper).insertSelective(any(VehicleModelGroup.class));
    }

    @Test
    @DisplayName("Should fail when group param is null")
    void shouldFailWhenGroupParamIsNull() {
      Result<Integer> result = vehicleModelGroupService.saveVehicleModelGroup(null);

      assertThat(result.isSuccess()).isFalse();
      verify(vehicleModelGroupMapper, never()).insertSelective(any(VehicleModelGroup.class));
    }

    @Test
    @DisplayName("Should fail when merchant id is null")
    void shouldFailWhenMerchantIdIsNull() {
      // Arrange
      validGroupParam.setMerchantId(null);

      // Act
      Result<Integer> result = vehicleModelGroupService.saveVehicleModelGroup(validGroupParam);

      // Assert
      assertFalse(result.isSuccess());
      verify(vehicleModelGroupMapper, never()).insertSelective(any());
    }

    @Test
    @DisplayName("Should fail when group name is empty")
    void shouldFailWhenGroupNameIsEmpty() {
      // Arrange
      validGroupParam.setVehicleModelName("");

      // Act
      Result<Integer> result = vehicleModelGroupService.saveVehicleModelGroup(validGroupParam);

      // Assert
      assertFalse(result.isSuccess());
      verify(vehicleModelGroupMapper, never()).insertSelective(any());
    }

    @Test
    @DisplayName("Should fail when group name exists")
    void shouldFailWhenNameExists() {
      when(vehicleModelGroupMapper.countByExample(any())).thenReturn(1L);

      Result<Integer> result = vehicleModelGroupService.saveVehicleModelGroup(validGroupParam);
      assertFalse(result.isSuccess());
      assertEquals("数据重复", result.getMessage());
    }
  }

  @Nested
  @DisplayName("ListVehicleModelGroupPage Tests")
  class ListVehicleModelGroupPageTests {

    @Test
    @DisplayName("Should successfully list vehicle model groups")
    void shouldSuccessfullyListVehicleModelGroups() {
      // Arrange
      VehicleModelGroupQueryParam queryParam = new VehicleModelGroupQueryParam();
      queryParam.setMerchantId(VALID_MERCHANT_ID);

      when(vehicleModelGroupMapper.selectByExample(any())).thenReturn(Collections.singletonList(validModelGroup));

      // Act
      Result<PageListVo<VehicleModelGroupVO>> result = vehicleModelGroupService.listVehicleModelGroupPage(queryParam);

      // Assert
      assertTrue(result.isSuccess());
      assertNotNull(result.getModel());
      assertEquals(1, result.getModel().getList().size());
      assertEquals(VALID_GROUP_ID, result.getModel().getList().get(0).getId());
    }

    @Test
    @DisplayName("Should return empty list when no groups found")
    void shouldReturnEmptyListWhenNoGroupsFound() {
      // Arrange
      VehicleModelGroupQueryParam queryParam = new VehicleModelGroupQueryParam();
      queryParam.setMerchantId(VALID_MERCHANT_ID);

      when(vehicleModelGroupMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      // Act
      Result<PageListVo<VehicleModelGroupVO>> result = vehicleModelGroupService.listVehicleModelGroupPage(queryParam);

      // Assert
      assertTrue(result.isSuccess());
      assertEquals(0, result.getModel().getList().size());
    }

    @Test
    @DisplayName("Should fail when query param is null")
    void shouldFailWhenQueryParamIsNull() {
      // Act
      Result<PageListVo<VehicleModelGroupVO>> result = vehicleModelGroupService.listVehicleModelGroupPage(null);

      // Assert
      assertFalse(result.isSuccess());
      verify(vehicleModelGroupMapper, never()).selectByExample(any());
    }
  }

  @Nested
  @DisplayName("DeleteVehicleModelGroup Tests")
  class DeleteVehicleModelGroupTests {

    @Test
    @DisplayName("Should successfully delete vehicle model group")
    void shouldSuccessfullyDeleteVehicleModelGroup() {
      when(vehicleModelGroupMapper.updateByPrimaryKeySelective(any())).thenReturn(1);

      Result<Integer> result = vehicleModelGroupService.deleteVehicleModelGroup(VALID_GROUP_ID, validLoginVo);

      assertThat(result.isSuccess()).isTrue();
      assertThat(result.getModel()).isEqualTo(1);
      verify(vehicleModelGroupMapper).updateByPrimaryKeySelective(any());
    }

    @Test
    @DisplayName("Should fail when group id is null")
    void shouldFailWhenGroupIdIsNull() {
      Result<Integer> result = vehicleModelGroupService.deleteVehicleModelGroup(null, validLoginVo);

      assertThat(result.isSuccess()).isFalse();
      verify(vehicleModelGroupMapper, never()).updateByPrimaryKeySelective(any());
    }

    @Test
    @DisplayName("Should fail when group does not exist")
    void shouldFailWhenGroupDoesNotExist() {
      // Arrange
      when(vehicleModelGroupMapper.selectByPrimaryKey(VALID_GROUP_ID)).thenReturn(null);

      // Act
      Result<Integer> result = vehicleModelGroupService.deleteVehicleModelGroup(VALID_GROUP_ID, validLoginVo);

      // Assert
      assertFalse(result.isSuccess());
      verify(vehicleModelGroupMapper, never()).updateByPrimaryKeySelective(any());
    }

    @Test
    @DisplayName("Should fail when group is already deleted")
    void shouldFailWhenGroupIsAlreadyDeleted() {
      // Arrange
      VehicleModelGroup deletedGroup = new VehicleModelGroup();
      deletedGroup.setId(VALID_GROUP_ID);
      deletedGroup.setDeleted(YesOrNoEnum.NO.getValue());

      when(vehicleModelGroupMapper.selectByPrimaryKey(VALID_GROUP_ID)).thenReturn(deletedGroup);

      // Act
      Result<Integer> result = vehicleModelGroupService.deleteVehicleModelGroup(VALID_GROUP_ID, validLoginVo);

      // Assert
      assertFalse(result.isSuccess());
      verify(vehicleModelGroupMapper, never()).updateByPrimaryKeySelective(any());
    }

    @Test
    @DisplayName("Should fail when group merchant id does not match")
    void shouldFailWhenMerchantIdNotMatch() {
      // Arrange
      VehicleModelGroup group = new VehicleModelGroup();
      group.setDeleted(YesOrNoEnum.NO.getValue());
      group.setMerchantId(2L);
      when(vehicleModelGroupMapper.selectByPrimaryKey(VALID_GROUP_ID)).thenReturn(group);

      // Act
      Result<Integer> result = vehicleModelGroupService.deleteVehicleModelGroup(VALID_GROUP_ID, validLoginVo);

      // Assert
      assertFalse(result.isSuccess());
      verify(vehicleModelGroupMapper, never()).updateByPrimaryKeySelective(any());
    }
  }

  @Nested
  @DisplayName("ListVehicleModelGroup Tests")
  class ListVehicleModelGroupTests {

    @Test
    @DisplayName("Should successfully list vehicle model groups")
    void shouldSuccessfullyListVehicleModelGroups() {
      VehicleModelGroupQueryParam queryParam = new VehicleModelGroupQueryParam();
      queryParam.setMerchantId(VALID_MERCHANT_ID);

      List<VehicleModelGroup> groups = Arrays.asList(validModelGroup);
      when(vehicleModelGroupMapper.selectByExample(any())).thenReturn(groups);

      Result<List<VehicleModelGroupVO>> result = vehicleModelGroupService
          .listVehicleModelGroup(Arrays.asList(VALID_GROUP_ID));

      assertThat(result.isSuccess()).isTrue();
      assertThat(result.getModel()).hasSize(1);
      VehicleModelGroupVO vo = result.getModel().get(0);
      assertThat(vo.getName()).isEqualTo(validModelGroup.getName());
    }

    @Test
    @DisplayName("Should return empty list when no groups found")
    void shouldReturnEmptyListWhenNoGroupsFound() {
      // Arrange
      List<Long> groupIds = Arrays.asList(VALID_GROUP_ID);
      when(vehicleModelGroupMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      // Act
      Result<List<VehicleModelGroupVO>> result = vehicleModelGroupService.listVehicleModelGroup(groupIds);

      // Assert
      assertTrue(result.isSuccess());
      assertTrue(result.getModel().isEmpty());
    }
  }

  @Nested
  @DisplayName("GetVehicleModelGroupById Tests")
  class GetVehicleModelGroupByIdTests {

    @Test
    @DisplayName("Should successfully get vehicle model group by id")
    void shouldSuccessfullyGetVehicleModelGroupById() {
      when(vehicleModelGroupMapper.selectByPrimaryKey(VALID_GROUP_ID)).thenReturn(validModelGroup);

      Result<VehicleModelGroupVO> result = vehicleModelGroupService.getById(VALID_GROUP_ID);

      assertThat(result.isSuccess()).isTrue();
      assertThat(result.getModel().getName()).isEqualTo(validModelGroup.getName());
      verify(vehicleModelGroupMapper).selectByPrimaryKey(VALID_GROUP_ID);
    }

    @Test
    @DisplayName("Should return null when group not found")
    void shouldReturnNullWhenGroupNotFound() {
      when(vehicleModelGroupMapper.selectByPrimaryKey(VALID_GROUP_ID)).thenReturn(null);

      Result<VehicleModelGroupVO> result = vehicleModelGroupService.getById(VALID_GROUP_ID);

      assertThat(result.isSuccess()).isTrue();
      assertThat(result.getModel()).isNull();
      verify(vehicleModelGroupMapper).selectByPrimaryKey(VALID_GROUP_ID);
    }
  }
}