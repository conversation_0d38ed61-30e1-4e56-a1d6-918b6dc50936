package com.ql.rent.provider.vehicle;

import com.alibaba.fastjson.JSON;
import com.ql.rent.param.price.RentServiceQuery;
import com.ql.rent.param.trade.MarketInfoQuery;
import com.ql.rent.service.price.IRentMainService;
import com.ql.rent.service.trade.IMarketService;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.price.AddedServiceVo;
import com.ql.rent.vo.price.InsuranceServicePriceVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
class RentlServiceImplTest {

    @Resource
    private IRentMainService rentMainService;

    @Resource
    private IMarketService marketService;

    @Test
    public void getMarketList() {
        MarketInfoQuery marketInfoQuery = new MarketInfoQuery();
        marketInfoQuery.setMerchantId(1L);
        marketInfoQuery.setStatus(2);
        marketService.getMarketList(marketInfoQuery);


    }

    @Test
    public void testListVehicleModel() {
        RentServiceQuery query = new RentServiceQuery();
        List<Long> ids = new ArrayList<>();
        ids.add(1L);
        query.setIds(ids);
        Result<List<InsuranceServicePriceVo>> listVoResult = rentMainService.getInsuranceServiceList(query);
        System.out.println(JSON.toJSONString(listVoResult));

        Result<List<AddedServiceVo>> listVoResult1 = rentMainService.getAddedServiceList(query);
        System.out.println(JSON.toJSONString(listVoResult1));

    }
}