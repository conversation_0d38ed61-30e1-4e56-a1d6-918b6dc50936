
package com.ql.rent.provider.vehicle;

import java.util.*;

import com.ql.rent.api.aggregate.model.dto.InsuranceServicePolicyDTO;
import com.ql.rent.service.price.IInsuranceServiceSettingService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.price.InsuranceServiceSettingVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ThirdVehicleServiceImplMockTest {

    @Mock
    private IInsuranceServiceSettingService insuranceServiceSettingService;

    @InjectMocks
    private ThirdVehicleServiceImpl thirdVehicleService;

    @BeforeEach
    public void setUp() {
        // Ensure that the ThirdVehicleServiceImpl instance is initialized with the correct map
        thirdVehicleService = new ThirdVehicleServiceImpl();
        thirdVehicleService.initInsuranceServiceNeedMap();
    }

    @Test
    public void getModelInsuranceServicePolicy_MerchantIdIsNull_ReturnsEmptyList() {
        List<InsuranceServicePolicyDTO> result = thirdVehicleService.getModelInsuranceServicePolicy(null);
        assertTrue(result.isEmpty());
    }

    @Test
    public void getModelInsuranceServicePolicy_ServiceSettingSuccess_ReturnsMappedList() {
        Long merchantId = 1L;

        // Mocking the response from insuranceServiceSettingService
        List<InsuranceServiceSettingVo> serviceSettingVoList = new ArrayList<>();
        InsuranceServiceSettingVo vo = new InsuranceServiceSettingVo();
        vo.setName("基本保障服务费");
        vo.setDamageInsurance((byte) 1);
        vo.setDamageInsuranceAmount(100);
        vo.setGlass((byte) 1);
        vo.setTire((byte) 1);
        vo.setThirdPartyInsurance(1000);
        vo.setOutageFee((byte) 1);
        vo.setOutageFeeRatio(new java.math.BigDecimal("0.1"));
        vo.setDepreciation((byte) 1);
        vo.setDepreciationFee(200);
        vo.setRepairFeeRatio(new java.math.BigDecimal("0.2"));
        serviceSettingVoList.add(vo);

        Result<List<InsuranceServiceSettingVo>> mockResult = ResultUtil.successResult(serviceSettingVoList);
        when(insuranceServiceSettingService.insuranceList(merchantId)).thenReturn(mockResult);

        List<InsuranceServicePolicyDTO> result = thirdVehicleService.getModelInsuranceServicePolicy(merchantId);

        assertNotNull(result);
        assertEquals(1, result.size());

        InsuranceServicePolicyDTO dto = result.get(0);
        assertEquals("02001", dto.getCode());
        assertEquals("基本保障服务费", dto.getName());
        assertEquals((byte) 1, dto.getDamageInsurance());
        assertEquals(100, dto.getDamageInsuranceAmount());
        assertEquals((byte) 1, dto.getGlass());
        assertEquals((byte) 1, dto.getTire());
        assertEquals(1000, dto.getThirdPartyInsurance());
        assertEquals((byte) 1, dto.getOutageFee());
        assertEquals(new java.math.BigDecimal("0.1"), dto.getOutageFeeRatio());
        assertEquals((byte) 1, dto.getDepreciation());
        assertEquals(200, dto.getDepreciationFee());
        assertEquals(new java.math.BigDecimal("0.2"), dto.getRepairFeeRatio());
    }

    @Test
    public void getModelInsuranceServicePolicy_ServiceSettingFailure_ThrowsBizException() {
        Long merchantId = 1L;

        // Mocking the response from insuranceServiceSettingService
        Result<List<InsuranceServiceSettingVo>> mockResult = ResultUtil.failResult("ERROR_CODE", "Error message");
        when(insuranceServiceSettingService.insuranceList(merchantId)).thenReturn(mockResult);

        assertThrows(BizException.class, () -> {
            thirdVehicleService.getModelInsuranceServicePolicy(merchantId);
        });
    }

    // Add a helper method to initialize the map in ThirdVehicleServiceImpl
    private static class ThirdVehicleServiceImplHelper extends ThirdVehicleServiceImpl {
        public ThirdVehicleServiceImplHelper() {
            initInsuranceServiceNeedMap();
        }

    }
}