package com.ql.rent.provider.vehicle;

import com.ql.rent.dao.vehicle.CityCodeMappingInfoMapper;
import com.ql.rent.entity.vehicle.CityCodeMappingInfo;
import com.ql.rent.entity.vehicle.CityCodeMappingInfoExample;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.share.result.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class CityCodeMappingInfoServiceImplTest {

  @Mock
  private CityCodeMappingInfoMapper codeMappingInfoMapper;

  @InjectMocks
  private CityCodeMappingInfoServiceImpl cityCodeMappingInfoService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Nested
  class GetCityCodeForSaasCityIdTests {
    @Test
    void shouldReturnFailWhenNoMappingFound() {
      when(codeMappingInfoMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      Result<Long> result = cityCodeMappingInfoService.getCityCodeForSaasCityId(1L, 1L);

      assertFalse(result.isSuccess());
      assertEquals("未找到城市映射关系，saasCityId:1", result.getMessage());

      ArgumentCaptor<CityCodeMappingInfoExample> exampleCaptor = ArgumentCaptor
          .forClass(CityCodeMappingInfoExample.class);
      verify(codeMappingInfoMapper).selectByExample(exampleCaptor.capture());
      CityCodeMappingInfoExample example = exampleCaptor.getValue();
      assertEquals(1, example.getOredCriteria().size());
      assertEquals(3, example.getOredCriteria().get(0).getCriteria().size());
    }

    @Test
    void shouldReturnChannelCityIdWhenMappingFound() {
      CityCodeMappingInfo mapping = createCityCodeMapping(1L, 1, 100L, 1L);
      when(codeMappingInfoMapper.selectByExample(any())).thenReturn(Collections.singletonList(mapping));

      Result<Long> result = cityCodeMappingInfoService.getCityCodeForSaasCityId(1L, 1L);

      assertTrue(result.isSuccess());
      assertEquals(Long.valueOf(100L), result.getModel());

      ArgumentCaptor<CityCodeMappingInfoExample> exampleCaptor = ArgumentCaptor
          .forClass(CityCodeMappingInfoExample.class);
      verify(codeMappingInfoMapper).selectByExample(exampleCaptor.capture());
      CityCodeMappingInfoExample example = exampleCaptor.getValue();
      assertEquals(1, example.getOredCriteria().size());
      assertEquals(3, example.getOredCriteria().get(0).getCriteria().size());
    }
  }

  private CityCodeMappingInfo createCityCodeMapping(Long id, Integer channel, Long channelCityId, Long sassCityId) {
    CityCodeMappingInfo mapping = new CityCodeMappingInfo();
    mapping.setId(id);
    mapping.setChannel(channel);
    mapping.setChannelCityId(channelCityId);
    mapping.setSassCityId(sassCityId);
    mapping.setDeleted(YesOrNoEnum.NO.getValue());
    mapping.setCreateTime(System.currentTimeMillis());
    mapping.setOpTime(System.currentTimeMillis());
    return mapping;
  }
}