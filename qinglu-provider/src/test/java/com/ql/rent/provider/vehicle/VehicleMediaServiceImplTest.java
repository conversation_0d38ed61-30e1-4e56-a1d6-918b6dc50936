package com.ql.rent.provider.vehicle;

import com.ql.rent.common.FileUploader;
import com.ql.rent.dao.vehicle.VehicleMediaMapper;
import com.ql.rent.entity.vehicle.VehicleMedia;
import com.ql.rent.entity.vehicle.VehicleMediaExample;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.param.vehicle.VehicleMediaParam;
import com.ql.rent.param.vehicle.VehicleMediaQueryParam;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.vehicle.VehicleMediaVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class VehicleMediaServiceImplTest {

  @Mock
  private VehicleMediaMapper vehicleMediaMapper;

  @InjectMocks
  private VehicleMediaServiceImpl vehicleMediaService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Nested
  class BatchSaveVehicleMediaTests {
    @Test
    void shouldFailWhenParamsAreEmpty() {
      Result<Integer> result = vehicleMediaService.batchSaveVehicleMedia(null, null, 1L);
      assertFalse(result.isSuccess());
      assertEquals("参数不能为空", result.getMessage());

      result = vehicleMediaService.batchSaveVehicleMedia(Collections.emptyList(), Collections.emptyList(), 1L);
      assertFalse(result.isSuccess());
      assertEquals("参数不能为空", result.getMessage());
    }

    @Test
    void shouldSuccessfullyBatchSaveVehicleMedia() {
      // 准备测试数据
      List<VehicleMediaParam> mediaParams = Arrays.asList(
          createMediaParam("/path1", (byte) 1),
          createMediaParam("/path2", (byte) 2));
      List<Long> modelIds = Arrays.asList(1L, 2L);
      Long opUserId = 1L;

      // Mock mapper方法
      when(vehicleMediaMapper.batchInsert(any())).thenReturn(4);

      // 执行测试
      Result<Integer> result = vehicleMediaService.batchSaveVehicleMedia(mediaParams, modelIds, opUserId);

      // 验证结果
      assertTrue(result.isSuccess());
      assertEquals(Integer.valueOf(1), result.getModel());

      // 验证mapper调用
      ArgumentCaptor<List<VehicleMedia>> captor = ArgumentCaptor.forClass(List.class);
      verify(vehicleMediaMapper).batchInsert(captor.capture());
      List<VehicleMedia> savedMedias = captor.getValue();
      assertEquals(4, savedMedias.size());
    }
  }

  @Nested
  class ListByParamTests {
    @Test
    void shouldFailWhenQueryParamIsNull() {
      Result<List<VehicleMediaVO>> result = vehicleMediaService.listByParam(null);
      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldSuccessfullyListVehicleMedia() {
      // 准备测试数据
      VehicleMediaQueryParam queryParam = new VehicleMediaQueryParam();
      queryParam.setVehicleModelId(1L);
      queryParam.setMediaType((byte) 1);
      queryParam.setVehicleModelIdList(Collections.singletonList(1L));

      VehicleMedia media = new VehicleMedia();
      media.setId(1L);
      media.setVehicleModelId(1L);
      media.setMediaType((byte) 1);
      media.setMediaPath("/test/path");

      // Mock mapper方法
      when(vehicleMediaMapper.selectByExample(any())).thenReturn(Collections.singletonList(media));

      // 执行测试
      Result<List<VehicleMediaVO>> result = vehicleMediaService.listByParam(queryParam);

      // 验证结果
      assertTrue(result.isSuccess());
      List<VehicleMediaVO> voList = result.getModel();
      assertEquals(1, voList.size());
      VehicleMediaVO vo = voList.get(0);
      assertEquals(Long.valueOf(1L), vo.getId());
      assertEquals(Long.valueOf(1L), vo.getVehicleModelId());
      assertEquals(Byte.valueOf((byte) 1), vo.getMediaType());
    }
  }

  @Nested
  class UpdateVehicleMediaByModelIdTests {
    @Test
    void shouldFailWhenParamsAreNull() {
      Result<Integer> result = vehicleMediaService.updateVehicleMediaByModelId(null, null, 1L);
      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldSuccessfullyUpdateVehicleMedia() {
      // 准备测试数据
      Long modelId = 1L;
      List<VehicleMediaParam> mediaParams = Arrays.asList(
          createMediaParam("/path1", (byte) 1),
          createMediaParam("/path2", (byte) 2));
      Long opUserId = 1L;

      VehicleMedia oldMedia = new VehicleMedia();
      oldMedia.setId(1L);
      oldMedia.setVehicleModelId(modelId);

      // Mock mapper方法
      when(vehicleMediaMapper.selectByExample(any())).thenReturn(Collections.singletonList(oldMedia));
      when(vehicleMediaMapper.updateByExampleSelective(any(), any())).thenReturn(1);
      when(vehicleMediaMapper.batchInsert(any())).thenReturn(2);

      // 执行测试
      Result<Integer> result = vehicleMediaService.updateVehicleMediaByModelId(modelId, mediaParams, opUserId);

      // 验证结果
      assertTrue(result.isSuccess());
      assertEquals(Integer.valueOf(1), result.getModel());

      // 验证mapper调用
      verify(vehicleMediaMapper).selectByExample(any());
      verify(vehicleMediaMapper).batchInsert(any());
    }

    @Test
    void shouldReturnSuccessWhenBothListsAreEmpty() {
      Long modelId = 1L;
      when(vehicleMediaMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      Result<Integer> result = vehicleMediaService.updateVehicleMediaByModelId(modelId, Collections.emptyList(), 1L);

      assertTrue(result.isSuccess());
      assertEquals(Integer.valueOf(1), result.getModel());
    }
  }

  private VehicleMediaParam createMediaParam(String path, Byte type) {
    VehicleMediaParam param = new VehicleMediaParam();
    param.setMediaPath(path);
    param.setMediaType(type);
    return param;
  }
}