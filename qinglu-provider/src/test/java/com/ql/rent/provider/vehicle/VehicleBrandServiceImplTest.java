package com.ql.rent.provider.vehicle;

import com.ql.rent.dao.vehicle.VehicleBrandMapper;
import com.ql.rent.entity.vehicle.VehicleBrand;
import com.ql.rent.entity.vehicle.VehicleBrandExample;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.param.vehicle.VehicleBrandParam;
import com.ql.rent.param.vehicle.VehicleBrandQuery;
import com.ql.rent.service.vehicle.IVehicleSeryService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.common.VehicleBrandVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class VehicleBrandServiceImplTest {

  @Mock
  private VehicleBrandMapper vehicleBrandMapper;

  @Mock
  private IVehicleSeryService vehicleSeryService;

  @InjectMocks
  private VehicleBrandServiceImpl vehicleBrandService;

  private LoginVo loginVo;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    loginVo = new LoginVo();
    loginVo.setUserId(1L);
    loginVo.setMerchantId(1L);
  }

  @Nested
  class SaveVehicleBrandTests {
    @Test
    void shouldReturnFailWhenParamIsNull() {
      Result<Integer> result = vehicleBrandService.saveVehicleBrand(null);

      assertFalse(result.isSuccess());
      assertEquals("参数不能为空", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenRequiredFieldsAreNull() {
      VehicleBrandParam param = new VehicleBrandParam();

      Result<Integer> result = vehicleBrandService.saveVehicleBrand(param);

      assertFalse(result.isSuccess());
      assertEquals("参数不能为空", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenBrandNameExists() {
      VehicleBrandParam param = createValidParam();
      when(vehicleBrandMapper.countByExample(any())).thenReturn(1L);

      Result<Integer> result = vehicleBrandService.saveVehicleBrand(param);

      assertFalse(result.isSuccess());
      assertEquals("数据重复", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenInsertFails() {
      VehicleBrandParam param = createValidParam();
      when(vehicleBrandMapper.countByExample(any())).thenReturn(0L);
      when(vehicleBrandMapper.insertSelective(any())).thenReturn(0);

      Result<Integer> result = vehicleBrandService.saveVehicleBrand(param);

      assertFalse(result.isSuccess());
      assertEquals("保存品牌数据失败", result.getMessage());
    }

    @Test
    void shouldSaveSuccessfully() {
      VehicleBrandParam param = createValidParam();
      when(vehicleBrandMapper.countByExample(any())).thenReturn(0L);
      when(vehicleBrandMapper.insertSelective(any())).thenReturn(1);

      Result<Integer> result = vehicleBrandService.saveVehicleBrand(param);

      assertTrue(result.isSuccess());
      assertEquals(Integer.valueOf(1), result.getModel());

      ArgumentCaptor<VehicleBrand> captor = ArgumentCaptor.forClass(VehicleBrand.class);
      verify(vehicleBrandMapper).insertSelective(captor.capture());
      VehicleBrand saved = captor.getValue();
      assertEquals(param.getBrandName(), saved.getBrandName());
      assertEquals(param.getMerchantId(), saved.getMerchantId());
      assertEquals(YesOrNoEnum.NO.getValue(), saved.getPreset());
    }
  }

  @Nested
  class DeleteVehicleBrandTests {
    @Test
    void shouldReturnFailWhenIdIsNull() {
      Result<Integer> result = vehicleBrandService.deleteVehicleBrand(null, loginVo);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenLoginVoIsNull() {
      Result<Integer> result = vehicleBrandService.deleteVehicleBrand(1L, null);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenBrandNotFound() {
      when(vehicleBrandMapper.selectByPrimaryKey(1L)).thenReturn(null);

      Result<Integer> result = vehicleBrandService.deleteVehicleBrand(1L, loginVo);

      assertFalse(result.isSuccess());
      assertEquals("品牌数据不存在，请刷新页面", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenBrandIsPreset() {
      VehicleBrand brand = createVehicleBrand(1L, "测试品牌", true);
      when(vehicleBrandMapper.selectByPrimaryKey(1L)).thenReturn(brand);

      Result<Integer> result = vehicleBrandService.deleteVehicleBrand(1L, loginVo);

      assertFalse(result.isSuccess());
      assertEquals("无法删除预设数据", result.getMessage());
    }

    @Test
    void shouldThrowExceptionWhenDeleteSeryFails() {
      VehicleBrand brand = createVehicleBrand(1L, "测试品牌", false);
      when(vehicleBrandMapper.selectByPrimaryKey(1L)).thenReturn(brand);
      when(vehicleSeryService.deleteByBrandId(any(), any())).thenReturn(ResultUtil.failResult("删除失败"));

      assertThrows(BizException.class, () -> vehicleBrandService.deleteVehicleBrand(1L, loginVo),
          "删除品牌关联数据失败");
    }

    @Test
    void shouldThrowExceptionWhenUpdateFails() {
      VehicleBrand brand = createVehicleBrand(1L, "测试品牌", false);
      when(vehicleBrandMapper.selectByPrimaryKey(1L)).thenReturn(brand);
      when(vehicleSeryService.deleteByBrandId(any(), any())).thenReturn(ResultUtil.successResult(1));
      when(vehicleBrandMapper.updateByPrimaryKeySelective(any())).thenReturn(0);

      assertThrows(BizException.class, () -> vehicleBrandService.deleteVehicleBrand(1L, loginVo),
          "删除品牌数据失败");
    }

    @Test
    void shouldDeleteSuccessfully() {
      VehicleBrand brand = createVehicleBrand(1L, "测试品牌", false);
      when(vehicleBrandMapper.selectByPrimaryKey(1L)).thenReturn(brand);
      when(vehicleSeryService.deleteByBrandId(any(), any())).thenReturn(ResultUtil.successResult(1));
      when(vehicleBrandMapper.updateByPrimaryKeySelective(any())).thenReturn(1);

      Result<Integer> result = vehicleBrandService.deleteVehicleBrand(1L, loginVo);

      assertTrue(result.isSuccess());
      assertEquals(Integer.valueOf(1), result.getModel());

      ArgumentCaptor<VehicleBrand> captor = ArgumentCaptor.forClass(VehicleBrand.class);
      verify(vehicleBrandMapper).updateByPrimaryKeySelective(captor.capture());
      VehicleBrand updated = captor.getValue();
      assertEquals(YesOrNoEnum.YES.getValue(), updated.getDeleted());
      assertEquals(loginVo.getUserId(), updated.getOpUserId());
    }
  }

  @Nested
  class ListVehicleBrandTests {
    @Test
    void shouldReturnFailWhenQueryIsNull() {
      Result<List<VehicleBrandVO>> result = vehicleBrandService.listVehicleBrand(null);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnEmptyListWhenNoRecordsFound() {
      VehicleBrandQuery query = new VehicleBrandQuery();
      when(vehicleBrandMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      Result<List<VehicleBrandVO>> result = vehicleBrandService.listVehicleBrand(query);

      assertTrue(result.isSuccess());
      assertTrue(result.getModel().isEmpty());
    }

    @Test
    void shouldReturnBrandList() {
      VehicleBrandQuery query = new VehicleBrandQuery();
      List<VehicleBrand> brands = Arrays.asList(
          createVehicleBrand(1L, "品牌1", false),
          createVehicleBrand(2L, "品牌2", false));
      when(vehicleBrandMapper.selectByExample(any())).thenReturn(brands);

      Result<List<VehicleBrandVO>> result = vehicleBrandService.listVehicleBrand(query);

      assertTrue(result.isSuccess());
      assertEquals(2, result.getModel().size());
      assertEquals("品牌1", result.getModel().get(0).getBrandName());
      assertEquals("品牌2", result.getModel().get(1).getBrandName());
    }
  }

  @Nested
  class ListVehicleBrandPageTests {
    @Test
    void shouldReturnFailWhenQueryIsNull() {
      Result<PageListVo<VehicleBrandVO>> result = vehicleBrandService.listVehicleBrandPage(null);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnEmptyPageWhenNoRecordsFound() {
      VehicleBrandQuery query = new VehicleBrandQuery();
      when(vehicleBrandMapper.countByExample(any())).thenReturn(0L);

      Result<PageListVo<VehicleBrandVO>> result = vehicleBrandService.listVehicleBrandPage(query);

      assertTrue(result.isSuccess());
      assertEquals(0L, result.getModel().getCount());
      assertTrue(result.getModel().getList().isEmpty());
    }

    @Test
    void shouldReturnPagedResults() {
      VehicleBrandQuery query = createValidQuery();
      List<VehicleBrand> brands = Arrays.asList(
          createVehicleBrand(1L, "品牌1", false),
          createVehicleBrand(2L, "品牌2", false));
      when(vehicleBrandMapper.countByExample(any())).thenReturn(2L);
      when(vehicleBrandMapper.selectByExample(any())).thenReturn(brands);

      Result<PageListVo<VehicleBrandVO>> result = vehicleBrandService.listVehicleBrandPage(query);

      assertTrue(result.isSuccess());
      assertEquals(2L, result.getModel().getCount());
      assertEquals(2, result.getModel().getList().size());
      assertEquals("品牌1", result.getModel().getList().get(0).getBrandName());
      assertEquals("品牌2", result.getModel().getList().get(1).getBrandName());
    }
  }

  private VehicleBrandParam createValidParam() {
    VehicleBrandParam param = new VehicleBrandParam();
    param.setBrandName("测试品牌");
    param.setMerchantId(1L);
    param.setOpUserId(1L);
    param.setStoreId(1L);
    return param;
  }

  private VehicleBrandQuery createValidQuery() {
    VehicleBrandQuery query = new VehicleBrandQuery();
    query.setMerchantId(1L);
    return query;
  }

  private VehicleBrand createVehicleBrand(Long id, String name, boolean preset) {
    VehicleBrand brand = new VehicleBrand();
    brand.setId(id);
    brand.setBrandName(name);
    brand.setMerchantId(1L);
    brand.setPreset(preset ? YesOrNoEnum.YES.getValue() : YesOrNoEnum.NO.getValue());
    brand.setDeleted(YesOrNoEnum.NO.getValue());
    brand.setCreateTime(System.currentTimeMillis());
    brand.setOpTime(System.currentTimeMillis());
    brand.setOpUserId(1L);
    brand.setStoreId(1L);
    brand.setInitials("T");
    brand.setEnglishName("");
    return brand;
  }
}