package com.ql.rent.provider.vehicle;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RemedialVehicleServiceImpl单元测试
 */
@ExtendWith(MockitoExtension.class)
public class RemedialVehicleServiceImplTest {

  @InjectMocks
  private RemedialVehicleServiceImpl remedialVehicleService;

  /**
   * 测试 initModelGroupDepositRanges 方法
   * 验证各个车型分组的价格区间和押金金额配置是否正确
   */
  @Test
  public void testInitModelGroupDepositRanges() throws Exception {
    // 直接调用公共方法，不再需要反射
    Map<Long, List<RemedialVehicleServiceImpl.PriceDepositRange>> result = remedialVehicleService
        .initModelGroupDepositRanges();

    // 验证返回的Map不为空
    assertNotNull(result);

    // 验证包含预期的车型分组
    Long[] expectedModelGroups = { 1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L, 9L };
    for (Long groupId : expectedModelGroups) {
      assertTrue(result.containsKey(groupId), "应包含车型分组ID: " + groupId);
      assertFalse(result.get(groupId).isEmpty(), "车型分组 " + groupId + " 的价格区间列表不应为空");
    }

    // 验证各分组的区间数量是否符合预期
    assertEquals(18, result.get(1L).size(), "SUV分组应有18个价格区间");
    assertEquals(12, result.get(2L).size(), "商务车分组应有12个价格区间");
    assertEquals(6, result.get(3L).size(), "经济型分组应有6个价格区间");
    assertEquals(7, result.get(4L).size(), "舒适型分组应有7个价格区间");
    assertEquals(14, result.get(5L).size(), "豪华型分组应有14个价格区间");
    assertEquals(8, result.get(6L).size(), "皮卡分组应有8个价格区间");
    assertEquals(23, result.get(7L).size(), "跑车分组应有23个价格区间");
    assertEquals(7, result.get(8L).size(), "小巴士分组应有7个价格区间");
    assertEquals(7, result.get(9L).size(), "房车分组应有7个价格区间");
  }

  /**
   * 测试 PriceDepositRange 内部类的功能
   */
  @Test
  public void testPriceDepositRange() {
    // 直接创建PriceDepositRange实例，不再需要反射
    RemedialVehicleServiceImpl.PriceDepositRange range1 = new RemedialVehicleServiceImpl.PriceDepositRange(
        new BigDecimal("10"), new BigDecimal("20"), 5000);
    RemedialVehicleServiceImpl.PriceDepositRange range2 = new RemedialVehicleServiceImpl.PriceDepositRange(
        new BigDecimal("20"), null, 10000);

    // 测试isInRange方法
    assertTrue(range1.isInRange(new BigDecimal("15")), "价格15应在10-20的区间内");
    assertFalse(range1.isInRange(new BigDecimal("20")), "价格20不应在10-20的区间内（不含上限）");
    assertFalse(range1.isInRange(new BigDecimal("5")), "价格5不应在10-20的区间内");
    assertTrue(range1.isInRange(new BigDecimal("10")), "价格10应在10-20的区间内（含下限）");

    // 测试无上限区间
    assertTrue(range2.isInRange(new BigDecimal("30")), "价格30应在20以上的无上限区间内");
    assertTrue(range2.isInRange(new BigDecimal("20")), "价格20应在20以上的无上限区间内（含下限）");
    assertFalse(range2.isInRange(new BigDecimal("19.99")), "价格19.99不应在20以上的无上限区间内");

    // 测试null价格
    assertFalse(range1.isInRange(null), "null价格不应在任何区间内");

    // 测试getDepositAmount方法
    assertEquals(5000, range1.getDepositAmount(), "第一个区间的押金金额应为5000元");
    assertEquals(10000, range2.getDepositAmount(), "第二个区间的押金金额应为10000元");
  }

  /**
   * 测试根据车型分组和价格获取对应的押金金额方法
   */
  @Test
  public void testGetDepositAmount() throws Exception {
    // 初始化车型分组押金配置
    Map<Long, List<RemedialVehicleServiceImpl.PriceDepositRange>> modelGroupDepositMap = remedialVehicleService
        .initModelGroupDepositRanges();

    // 使用反射获取私有方法
    Method method = RemedialVehicleServiceImpl.class.getDeclaredMethod("getDepositAmount",
        Long.class, BigDecimal.class, Map.class);
    method.setAccessible(true);

    // 测试SUV分组(1L)的不同价格区间
    assertEquals(300000, method.invoke(remedialVehicleService, 1L, new BigDecimal("3"), modelGroupDepositMap));
    assertEquals(400000, method.invoke(remedialVehicleService, 1L, new BigDecimal("12"), modelGroupDepositMap));
    assertEquals(500000, method.invoke(remedialVehicleService, 1L, new BigDecimal("18"), modelGroupDepositMap));
    assertEquals(600000, method.invoke(remedialVehicleService, 1L, new BigDecimal("35"), modelGroupDepositMap));
    assertEquals(800000, method.invoke(remedialVehicleService, 1L, new BigDecimal("45"), modelGroupDepositMap));
    assertEquals(1000000, method.invoke(remedialVehicleService, 1L, new BigDecimal("80"), modelGroupDepositMap));
    assertEquals(1500000, method.invoke(remedialVehicleService, 1L, new BigDecimal("120"), modelGroupDepositMap));
    assertEquals(1800000, method.invoke(remedialVehicleService, 1L, new BigDecimal("220"), modelGroupDepositMap));
    assertEquals(2000000, method.invoke(remedialVehicleService, 1L, new BigDecimal("280"), modelGroupDepositMap));
    assertEquals(2500000, method.invoke(remedialVehicleService, 1L, new BigDecimal("450"), modelGroupDepositMap));
    assertEquals(3000000, method.invoke(remedialVehicleService, 1L, new BigDecimal("650"), modelGroupDepositMap));

    // 测试经济型分组(3L)的不同价格区间
    assertEquals(300000, method.invoke(remedialVehicleService, 3L, new BigDecimal("3"), modelGroupDepositMap));
    assertEquals(300000, method.invoke(remedialVehicleService, 3L, new BigDecimal("7"), modelGroupDepositMap));
    assertEquals(300000, method.invoke(remedialVehicleService, 3L, new BigDecimal("9"), modelGroupDepositMap));
    assertEquals(300000, method.invoke(remedialVehicleService, 3L, new BigDecimal("12"), modelGroupDepositMap));
    assertEquals(400000, method.invoke(remedialVehicleService, 3L, new BigDecimal("18"), modelGroupDepositMap));
    assertEquals(500000, method.invoke(remedialVehicleService, 3L, new BigDecimal("25"), modelGroupDepositMap));

    // 测试豪华型分组(5L)的不同价格区间
    assertEquals(600000, method.invoke(remedialVehicleService, 5L, new BigDecimal("25"), modelGroupDepositMap));
    assertEquals(800000, method.invoke(remedialVehicleService, 5L, new BigDecimal("42"), modelGroupDepositMap));
    assertEquals(1000000, method.invoke(remedialVehicleService, 5L, new BigDecimal("60"), modelGroupDepositMap));
    assertEquals(1000000, method.invoke(remedialVehicleService, 5L, new BigDecimal("120"), modelGroupDepositMap));
    assertEquals(1500000, method.invoke(remedialVehicleService, 5L, new BigDecimal("250"), modelGroupDepositMap));
    assertEquals(2000000, method.invoke(remedialVehicleService, 5L, new BigDecimal("350"), modelGroupDepositMap));
    assertEquals(2500000, method.invoke(remedialVehicleService, 5L, new BigDecimal("550"), modelGroupDepositMap));
    assertEquals(5000000, method.invoke(remedialVehicleService, 5L, new BigDecimal("650"), modelGroupDepositMap));

    // 测试不存在的车型分组
    assertNull(method.invoke(remedialVehicleService, 999L, new BigDecimal("100"), modelGroupDepositMap));

    // 测试边界情况
    assertEquals(300000, method.invoke(remedialVehicleService, 3L, new BigDecimal("0"), modelGroupDepositMap));
    assertEquals(500000, method.invoke(remedialVehicleService, 3L, new BigDecimal("20"), modelGroupDepositMap));

    // 测试null价格
    assertNull(method.invoke(remedialVehicleService, 1L, null, modelGroupDepositMap));
  }

  /**
   * 集成测试 - 测试所有车型分组的边界价格区间
   * 这个测试确保押金计算系统对所有车型的所有边界价格都能正确处理
   */
  @Test
  public void testAllModelGroupBoundaries() throws Exception {
    // 初始化车型分组押金配置
    Map<Long, List<RemedialVehicleServiceImpl.PriceDepositRange>> modelGroupDepositMap = remedialVehicleService
        .initModelGroupDepositRanges();

    // 获取getDepositAmount方法
    Method getDepositAmountMethod = RemedialVehicleServiceImpl.class.getDeclaredMethod(
        "getDepositAmount", Long.class, BigDecimal.class, Map.class);
    getDepositAmountMethod.setAccessible(true);

    // 测试SUV分组(1L)的边界价格
    assertPriceToDeposit(getDepositAmountMethod, 1L, "0", 300000, modelGroupDepositMap); // 最小值
    assertPriceToDeposit(getDepositAmountMethod, 1L, "4.99", 300000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 1L, "5", 300000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 1L, "9.99", 300000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 1L, "10", 400000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 1L, "14.99", 400000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 1L, "15", 500000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 1L, "599.99", 2500000, modelGroupDepositMap); // 修正：599.99应该属于500-600区间，押金为25000元（2500000分）
    assertPriceToDeposit(getDepositAmountMethod, 1L, "600", 3000000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 1L, "1000", 3000000, modelGroupDepositMap); // 超过最大值

    // 测试商务车分组(2L)的边界价格
    assertPriceToDeposit(getDepositAmountMethod, 2L, "0", 300000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 2L, "7.99", 300000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 2L, "8", 400000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 2L, "9.99", 400000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 2L, "10", 400000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 2L, "199.99", 1000000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 2L, "200", 1000000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 2L, "300", 1000000, modelGroupDepositMap);

    // 测试经济型分组(3L)的边界价格
    assertPriceToDeposit(getDepositAmountMethod, 3L, "0", 300000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 3L, "4.99", 300000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 3L, "5", 300000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 3L, "7.99", 300000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 3L, "8", 300000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 3L, "19.99", 400000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 3L, "20", 500000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 3L, "50", 500000, modelGroupDepositMap);

    // 测试舒适型分组(4L)的边界价格
    assertPriceToDeposit(getDepositAmountMethod, 4L, "0", 300000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 4L, "9.99", 300000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 4L, "10", 500000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 4L, "14.99", 500000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 4L, "15", 500000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 4L, "39.99", 500000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 4L, "40", 500000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 4L, "50", 500000, modelGroupDepositMap);

    // 测试豪华型分组(5L)的边界价格
    assertPriceToDeposit(getDepositAmountMethod, 5L, "0", 600000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 5L, "29.99", 600000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 5L, "30", 600000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 5L, "34.99", 600000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 5L, "35", 600000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 5L, "599.99", 2500000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 5L, "600", 5000000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 5L, "1000", 5000000, modelGroupDepositMap);

    // 测试皮卡分组(6L)的边界价格
    assertPriceToDeposit(getDepositAmountMethod, 6L, "0", 300000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 6L, "4.99", 300000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 6L, "5", 300000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 6L, "9.99", 300000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 6L, "10", 500000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 6L, "49.99", 1000000, modelGroupDepositMap); // 修正：49.99应该属于40-50区间，押金为10000元（1000000分）
    assertPriceToDeposit(getDepositAmountMethod, 6L, "50", 1000000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 6L, "100", 1000000, modelGroupDepositMap);

    // 测试跑车分组(7L)的边界价格
    assertPriceToDeposit(getDepositAmountMethod, 7L, "0", 500000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 7L, "14.99", 500000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 7L, "15", 500000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 7L, "19.99", 500000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 7L, "20", 500000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 7L, "999.99", 5000000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 7L, "1000", 10000000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 7L, "3999.99", 20000000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 7L, "4000", 40000000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 7L, "5000", 40000000, modelGroupDepositMap);

    // 测试小巴士分组(8L)的边界价格
    assertPriceToDeposit(getDepositAmountMethod, 8L, "0", 300000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 8L, "4.99", 300000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 8L, "5", 300000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 8L, "9.99", 300000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 8L, "10", 500000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 8L, "49.99", 800000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 8L, "50", 1000000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 8L, "100", 1000000, modelGroupDepositMap);

    // 测试房车分组(9L)的边界价格
    assertPriceToDeposit(getDepositAmountMethod, 9L, "0", 600000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 9L, "9.99", 600000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 9L, "10", 600000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 9L, "19.99", 600000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 9L, "20", 1000000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 9L, "69.99", 1000000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 9L, "70", 1000000, modelGroupDepositMap);
    assertPriceToDeposit(getDepositAmountMethod, 9L, "100", 1000000, modelGroupDepositMap);
  }

  /**
   * 辅助方法：断言特定车型分组和价格对应的押金金额
   */
  private void assertPriceToDeposit(Method method, Long modelGroupId, String priceStr, Integer expectedDeposit,
      Map<Long, List<RemedialVehicleServiceImpl.PriceDepositRange>> modelGroupDepositMap) throws Exception {
    BigDecimal price = new BigDecimal(priceStr);
    Integer actualDeposit = (Integer) method.invoke(remedialVehicleService, modelGroupId, price, modelGroupDepositMap);
    assertEquals(expectedDeposit, actualDeposit,
        String.format("车型分组 %d, 价格 %s 的押金应为 %d", modelGroupId, priceStr, expectedDeposit));
  }
}