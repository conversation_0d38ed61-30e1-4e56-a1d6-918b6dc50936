package com.ql.rent.provider.common;

import com.ql.rent.constant.SendConsts;
import com.ql.rent.dao.trade.OrderInfoMapper;
import com.ql.rent.entity.trade.OrderInfo;
import com.ql.enums.SmsTemplateEnum;
import com.ql.rent.enums.merchant.PushTypeEnum;
import com.ql.rent.provider.trade.OrderComponent;
import com.ql.rent.service.common.IPushMsgService;
import com.ql.rent.service.login.LoginService;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.util.JPushUtil;
import com.ql.rent.util.MailUtil;
import com.ql.rent.vo.common.PushAddVO;
import com.ql.rent.vo.common.PushSyncVO;
import com.ql.rent.vo.common.PushVO;
import com.ql.rent.vo.login.WxMsgVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class JPushTest {

    @Resource
    private OrderComponent orderComponent;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private JPushUtil jPushUtil;
    @Resource
    private MailUtil mailUtil;
    @Resource
    private IPushMsgService pushMsgService;
    @Resource
    private LoginService loginService;
    @Value("${wechat.mpSendMsgUrl}")
    private String mpSendMsgUrl;


    @Test
    void appPush() {
        PushAddVO push = new PushAddVO();
        push.setSourceType(1);
        push.setSourceId(91L);
        push.setTitle("订单取消测试");
        push.setMsg("您的订单51已取消，详情请至APP或PC查看。");
        jPushUtil.jPush(push, new ArrayList<>(Arrays.asList(43L)));
    }

    @Test
    void sendMail() {
//        mailUtil.sendMail(Arrays.asList("<EMAIL>"), "订单取消通知", "您的订单112233已取消，详情请至APP或PC查看。");
        mailUtil.sendMail(Arrays.asList("<EMAIL>"), "订单取消通知", "您的订单112233已取消，详情请至APP或PC查看。");
    }

    @Test
    void settingPush() {
        String taskname = "送车任务";
        String time = "02月24日15;30";
        String address = "城站火车站";
        Long orderId = 222L;
        Long merchantId = 3L;
        Long storeId = 6L;
        List<Long> userIds = null;
        //userIds = Collections.singletonList(21L);

        // 数据组装
        PushVO pushVO = new PushVO();

        // ----APP 推送开始---------
        PushAddVO pushAddVO = new PushAddVO();
        pushAddVO.setTitle("司机任务通知");
        String msg = "您的订单${orderid}，有一个${taskname}任务，请于${time}至${address}完成${tasktype}";
        msg = msg.replace("${taskname}", taskname);
        msg = msg.replace("${orderid}", String.valueOf(orderId));
        msg = msg.replace("${time}", time);
        msg = msg.replace("${address}", address);
        msg = msg.replace("${tasktype}", taskname);
        pushAddVO.setMsg(msg);
        pushAddVO.setSourceId(orderId);
        pushAddVO.setSourceType(2);
        pushVO.setAppPushObj(pushAddVO);
        // ----APP推送结束----------

        // ---短信参数开始
        Map<String, Object> paramMap = new HashMap<>(5);
        paramMap.put("orderid", orderId);
        paramMap.put("taskname", taskname);
        paramMap.put("tasktype", taskname);
        paramMap.put("time", time);
        paramMap.put("address", address);
        pushVO.setSmsPushObj(paramMap);
        pushVO.setSmsTemplate(SmsTemplateEnum.DRIVER_TASK);
        // ------短信结束

        // 公众号
        WxMsgVo msgVo = new WxMsgVo();
        msgVo.setTouser("oIPdU6jLVFhjXFucnKz4Q6fYZTQo");
        msgVo.setTemplate_id("Rq_bkQAOzsgN9y2ilRlEYmGVOvyXN4efvqN9SvekaQ0");
        Map<String, WxMsgVo.Template> data = new HashMap<>();
        data.put("first", new WxMsgVo.Template("您的预约订单已成功"));
        data.put("thing2", new WxMsgVo.Template("联系人"));
        data.put("time3", new WxMsgVo.Template("2023-02-02"));
        data.put("remark", new WxMsgVo.Template("来前电话"));
        pushVO.setMpTemplate("Rq_bkQAOzsgN9y2ilRlEYmGVOvyXN4efvqN9SvekaQ0");
        pushVO.setMpPushObj(data);

        // ------通知对象参数--------
        pushVO.setMerchantId(merchantId);
        pushVO.setStoreId(storeId);
        pushVO.setPushTypeEnum(PushTypeEnum.TYPE_TASK);
        pushVO.setUserIds(userIds);
        pushMsgService.push(pushVO);
    }

    @Test
    void notSettingPush() {
        String taskname = "送车任务";
        String time = DateUtil.getFormatDateStr(new Date(), "MM月dd日");
        ;
        String address = "城站火车站";
        Long orderId = 222L;
        List<Long> userIds = Collections.singletonList(21L);

        // start ==================================================
        PushVO pushVO = new PushVO();
        // 需要推送的用户ID
        pushVO.setUserIds(userIds);

        // app 推送(如不需要不传这部分)
        PushAddVO pushAddVO = new PushAddVO();
        pushAddVO.setTitle("司机任务通知");
        String msg = "您的订单${orderid}，有一个${taskname}任务，请于${time}至${address}完成${tasktype}";
        msg = msg.replace("${taskname}", taskname);
        msg = msg.replace("${orderid}", String.valueOf(orderId));
        msg = msg.replace("${time}", time);
        msg = msg.replace("${address}", address);
        msg = msg.replace("${tasktype}", taskname);
        pushAddVO.setMsg(msg);
        pushAddVO.setSourceId(orderId); // app 对应业务数据id
        pushAddVO.setSourceType(2); // app 推送类型，app根据这个id，点击消息打开对应业务
        pushVO.setAppPushObj(pushAddVO);

        // 短信推送(如不需要不传这部分)
        Map<String, Object> paramMap = new HashMap<>(5);
        paramMap.put("orderid", orderId);  // 短信中需要替换的变量
        paramMap.put("taskname", taskname);
        paramMap.put("tasktype", taskname);
        paramMap.put("time", time);
        paramMap.put("address", address);
        pushVO.setSmsPushObj(paramMap);
        pushVO.setSmsTemplate(SmsTemplateEnum.DRIVER_TASK);

        // 公众号推送(如不需要不传这部分)
        Map<String, WxMsgVo.Template> data = new HashMap<>();
        data.put("first", new WxMsgVo.Template("您的预约订单已成功"));
        data.put("thing2", new WxMsgVo.Template("联系人"));
        data.put("time3", new WxMsgVo.Template("2023-02-02"));
        data.put("remark", new WxMsgVo.Template("来前电话"));
        pushVO.setMpTemplate("Rq_bkQAOzsgN9y2ilRlEYmGVOvyXN4efvqN9SvekaQ0");
        pushVO.setMpPushObj(data);

        // 邮箱推送(如不需要不传这部分)
        pushVO.setMailAddPlatUser(false);  // 是否发平台的系统管理员
        pushVO.setMailTitle(SendConsts.mailRegTitle); // 邮件标题
        String body = String.format(SendConsts.mailRegBody, "<EMAIL>", "123586"); // 邮件内容 替换的变量
        pushVO.setMailBody(body);

        pushMsgService.push(pushVO);
    }

    @Test
    void testPush(){
        String access_token = loginService.getMpAccessToken();
        String sendUrl = String.format(mpSendMsgUrl, access_token);
        WxMsgVo msgVo = new WxMsgVo();
        Map<String, WxMsgVo.Template> data = new HashMap<>();


//        msgVo.setTouser("oIPdU6jLVFhjXFucnKz4Q6fYZTQo");
//        msgVo.setTemplate_id("Rq_bkQAOzsgN9y2ilRlEYmGVOvyXN4efvqN9SvekaQ0");
//        data.put("first", new WxMsgVo.Template("您的预约订单已成功"));
//        data.put("thing2", new WxMsgVo.Template("联系人"));
//        data.put("time3", new WxMsgVo.Template("2023-02-02"));
//        data.put("remark", new WxMsgVo.Template("来前电话"));

        msgVo.setTouser("oIPdU6jLVFhjXFucnKz4Q6fYZTQo");
        msgVo.setTemplate_id("_e_dYp_CRDqLSvB4CLNlJJZA0BGC7IrydgYUojMbvMA");
        SimpleDateFormat sdf = new SimpleDateFormat("MM月dd日");
        String frist = String.format("来自%s，%s的用车订单已取消", "携程", sdf.format(new Date()));
        data.put("first", new WxMsgVo.Template(frist));
        data.put("keyword1", new WxMsgVo.Template("1324"));
        String keyword2 = String.format("客户姓名：%s，联系电话：%s","张三","13186960298");
        data.put("keyword2", new WxMsgVo.Template(keyword2));

        msgVo.setData(data);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(sendUrl, msgVo, String.class);
        System.out.println(responseEntity.getBody());
    }

    @Test
    void testCanelPush() {
        OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(951L);
        orderComponent.pushCancelOrder(orderInfo);
    }

    @Test
    void pushForSyncError(){
        PushSyncVO vo = new PushSyncVO();
        vo.setId(3269L);
        vo.setChannelId(2L);
        vo.setAction("ADD");
        vo.setRemark("错误");
//        pushMsgService.pushForSyncError(vo);
    }
}