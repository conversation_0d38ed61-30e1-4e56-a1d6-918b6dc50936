package com.ql.rent.provider.trade;

import com.alibaba.fastjson.JSON;
import com.ql.rent.AbstractTest;
import com.ql.rent.param.trade.VehiclePickReturnParam;
import com.ql.rent.service.trade.IOrderService;
import com.ql.rent.service.trade.IVehicleReturnExpenseItemService;
import com.ql.rent.share.result.Result;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

public class OrderTradeCodeTest extends AbstractTest {

    @Resource
    private IOrderService orderService;
    @Resource
    private IVehicleReturnExpenseItemService vehicleReturnExpenseItemService;


    @Test
    public void testCancelOrderPay() {
        Result<Boolean> result = orderService.cancelOrderPay(33770825L, "867f490706eb490c9e2337f58130d37c", 0L);
        System.out.println(result);
    }


    @Test
    public void testCancelService() {
        Result<Boolean> result = orderService.cancelService(1706384L, (byte) 0, 537356L, 1006L);
        System.out.println(result);
    }

    @Test
    public void testCancelService2() {
        VehiclePickReturnParam VehicleReturnParam = new VehiclePickReturnParam();
        VehicleReturnParam = JSON.parseObject("{\"attList\":[{\"attType\":0,\"attUrl\":\"https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1736055366374_dO7ESeck.jpg\"},{\"attType\":1,\"attUrl\":\"https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1736055364368_hTMdDbai.mp4\"}],\"contract\":{\"contractUrl\":\"https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1736055449829_MSBx5yKW.png\",\"inspectionUrl\":\"https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1736055452406_iIRVPgw9.png\"},\"contractSign\":0,\"deductionPayType\":1,\"id\":128514,\"inspection\":{\"appearance\":{\"appearanceList\":[],\"remark\":\"\"},\"interiorList\":[]},\"maxOilLiter\":20,\"mileage\":1,\"oilLiter\":8,\"orderId\":1702300,\"payKind\":99,\"prTime\":1736055361471,\"refundPayType\":1,\"returnDeductionItemList\":[{\"expenseAmount\":100,\"expenseItemPropId\":1,\"itemName\":\"ETC费用\",\"itemType\":1,\"returnPaid\":false}],\"returnRefundItemList\":[{\"expenseAmount\":10,\"expenseItemPropId\":19,\"itemName\":\"油费\",\"itemType\":2,\"returnPaid\":false}]}", VehiclePickReturnParam.class);
        Result<Integer> result = vehicleReturnExpenseItemService.saveVehicleReturnExpenseItemWhenReturn(VehicleReturnParam, 1L);
        System.out.println(result);
    }
}
