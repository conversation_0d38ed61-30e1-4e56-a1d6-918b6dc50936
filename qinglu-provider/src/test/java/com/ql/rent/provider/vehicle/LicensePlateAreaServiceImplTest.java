package com.ql.rent.provider.vehicle;

import com.ql.rent.common.IRedisService;
import com.ql.rent.dao.vehicle.LicensePlateAreaMapper;
import com.ql.rent.entity.common.LicensePlateArea;
import com.ql.rent.entity.common.LicensePlateAreaExample;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.common.LicensePlateAreaVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.ql.rent.constant.RedisConstant.CommonRedisKey.ALL_LICENSE_PLATE_AREA;
import static com.ql.rent.constant.RedisConstant.RedisExpireTime.HOUR_2;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class LicensePlateAreaServiceImplTest {

  @Mock
  private LicensePlateAreaMapper licensePlateAreaMapper;

  @Mock
  private IRedisService redisService;

  @InjectMocks
  private LicensePlateAreaServiceImpl licensePlateAreaService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Nested
  class ListLicensePlateAreaTests {
    @Test
    void shouldReturnFromCacheWhenExists() {
      List<LicensePlateAreaVO> cachedList = Collections.singletonList(createAreaVO(1L, "粤"));
      when(redisService.get(ALL_LICENSE_PLATE_AREA)).thenReturn(cachedList);

      Result<List<LicensePlateAreaVO>> result = licensePlateAreaService.listLicensePlateArea();

      assertTrue(result.isSuccess());
      assertEquals(cachedList, result.getModel());
      verify(licensePlateAreaMapper, never()).selectByExample(any());
    }

    @Test
    void shouldQueryFromDbAndCacheWhenNotInCache() {
      when(redisService.get(ALL_LICENSE_PLATE_AREA)).thenReturn(null);
      LicensePlateArea area = createArea(1L, "粤");
      when(licensePlateAreaMapper.selectByExample(any())).thenReturn(Collections.singletonList(area));

      Result<List<LicensePlateAreaVO>> result = licensePlateAreaService.listLicensePlateArea();

      assertTrue(result.isSuccess());
      assertEquals(1, result.getModel().size());
      assertEquals("粤", result.getModel().get(0).getAreaName());
      verify(redisService).set(eq(ALL_LICENSE_PLATE_AREA), any(), eq(HOUR_2));
    }

    @Test
    void shouldHandleEmptyDbResult() {
      when(redisService.get(ALL_LICENSE_PLATE_AREA)).thenReturn(null);
      when(licensePlateAreaMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      Result<List<LicensePlateAreaVO>> result = licensePlateAreaService.listLicensePlateArea();

      assertTrue(result.isSuccess());
      assertTrue(result.getModel().isEmpty());
    }
  }

  @Nested
  class GetByIdTests {
    @Test
    void shouldReturnFailWhenIdIsNull() {
      Result<LicensePlateAreaVO> result = licensePlateAreaService.getById(null);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFromCacheWhenExists() {
      List<LicensePlateAreaVO> cachedList = Arrays.asList(
          createAreaVO(1L, "粤"),
          createAreaVO(2L, "京"));
      when(redisService.get(ALL_LICENSE_PLATE_AREA)).thenReturn(cachedList);

      Result<LicensePlateAreaVO> result = licensePlateAreaService.getById(1L);

      assertTrue(result.isSuccess());
      assertEquals("粤", result.getModel().getAreaName());
      verify(licensePlateAreaMapper, never()).selectByPrimaryKey(any());
    }

    @Test
    void shouldClearCacheAndQueryDbWhenNotFoundInCache() {
      List<LicensePlateAreaVO> cachedList = Collections.singletonList(createAreaVO(2L, "京"));
      when(redisService.get(ALL_LICENSE_PLATE_AREA)).thenReturn(cachedList);

      LicensePlateArea area = createArea(1L, "粤");
      when(licensePlateAreaMapper.selectByPrimaryKey(1L)).thenReturn(area);

      Result<LicensePlateAreaVO> result = licensePlateAreaService.getById(1L);

      assertTrue(result.isSuccess());
      assertEquals("粤", result.getModel().getAreaName());
      verify(redisService).remove(ALL_LICENSE_PLATE_AREA);
    }

    @Test
    void shouldReturnFailWhenNotFoundInDb() {
      when(redisService.get(ALL_LICENSE_PLATE_AREA)).thenReturn(null);
      when(licensePlateAreaMapper.selectByPrimaryKey(1L)).thenReturn(null);

      Result<LicensePlateAreaVO> result = licensePlateAreaService.getById(1L);

      assertFalse(result.isSuccess());
      assertEquals("未查询到对应的区域牌照信息", result.getMessage());
    }

    @Test
    void shouldQueryDbDirectlyWhenCacheIsNull() {
      when(redisService.get(ALL_LICENSE_PLATE_AREA)).thenReturn(null);
      LicensePlateArea area = createArea(1L, "粤");
      when(licensePlateAreaMapper.selectByPrimaryKey(1L)).thenReturn(area);

      Result<LicensePlateAreaVO> result = licensePlateAreaService.getById(1L);

      assertTrue(result.isSuccess());
      assertEquals("粤", result.getModel().getAreaName());
    }
  }

  private LicensePlateArea createArea(Long id, String areaName) {
    LicensePlateArea area = new LicensePlateArea();
    area.setId(id);
    area.setAreaName(areaName);
    return area;
  }

  private LicensePlateAreaVO createAreaVO(Long id, String areaName) {
    LicensePlateAreaVO vo = new LicensePlateAreaVO();
    vo.setId(id);
    vo.setAreaName(areaName);
    return vo;
  }
}