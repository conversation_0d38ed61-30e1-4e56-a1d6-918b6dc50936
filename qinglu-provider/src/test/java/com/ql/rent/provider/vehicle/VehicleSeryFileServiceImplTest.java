package com.ql.rent.provider.vehicle;

import com.ql.rent.dao.vehicle.VehicleSeryFileMapper;
import com.ql.rent.entity.vehicle.VehicleSeryFile;
import com.ql.rent.entity.vehicle.VehicleSeryFileExample;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.vehicle.VehicleSeryFileVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class VehicleSeryFileServiceImplTest {

  @Mock
  private VehicleSeryFileMapper vehicleSeryFileMapper;

  @InjectMocks
  private VehicleSeryFileServiceImpl vehicleSeryFileService;

  private final String vehicleSeryFileUrl = "http://example.com/files/";

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    ReflectionTestUtils.setField(vehicleSeryFileService, "vehicleSeryFileUrl", vehicleSeryFileUrl);
  }

  @Nested
  class ListBySubSeryIdTests {
    @Test
    void shouldReturnFailWhenSubSeryIdIsNull() {
      // Act
      Result<List<VehicleSeryFileVO>> result = vehicleSeryFileService.listBySubSeryId(null);

      // Assert
      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
      verifyNoInteractions(vehicleSeryFileMapper);
    }

    @Test
    void shouldReturnEmptyListWhenNoFilesFound() {
      // Arrange
      Long subSeryId = 1L;
      when(vehicleSeryFileMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      // Act
      Result<List<VehicleSeryFileVO>> result = vehicleSeryFileService.listBySubSeryId(subSeryId);

      // Assert
      assertTrue(result.isSuccess());
      assertTrue(result.getModel().isEmpty());

      // Verify query criteria
      ArgumentCaptor<VehicleSeryFileExample> exampleCaptor = ArgumentCaptor.forClass(VehicleSeryFileExample.class);
      verify(vehicleSeryFileMapper).selectByExample(exampleCaptor.capture());
      VehicleSeryFileExample capturedExample = exampleCaptor.getValue();
      VehicleSeryFileExample.Criteria criteria = capturedExample.getOredCriteria().get(0);
      assertTrue(criteria.getAllCriteria().stream()
          .anyMatch(c -> c.getCondition().contains("sub_sery_id") && c.getValue().equals(subSeryId)));
      assertTrue(criteria.getAllCriteria().stream()
          .anyMatch(c -> c.getCondition().contains("deleted") && c.getValue().equals(YesOrNoEnum.NO.getValue())));
      assertEquals("sort asc", capturedExample.getOrderByClause());
    }

    @Test
    void shouldReturnFileListSuccessfully() {
      // Arrange
      Long subSeryId = 1L;
      VehicleSeryFile file1 = new VehicleSeryFile();
      file1.setId(1L);
      file1.setUrl("image1.jpg");
      file1.setSort((byte) 1);
      VehicleSeryFile file2 = new VehicleSeryFile();
      file2.setId(2L);
      file2.setUrl("image2.jpg");
      file2.setSort((byte) 2);

      when(vehicleSeryFileMapper.selectByExample(any())).thenReturn(Arrays.asList(file1, file2));

      // Act
      Result<List<VehicleSeryFileVO>> result = vehicleSeryFileService.listBySubSeryId(subSeryId);

      // Assert
      assertTrue(result.isSuccess());
      assertEquals(2, result.getModel().size());
      assertEquals(vehicleSeryFileUrl + "image1.jpg", result.getModel().get(0).getUrl());
      assertEquals(vehicleSeryFileUrl + "image2.jpg", result.getModel().get(1).getUrl());
    }

    @Test
    void shouldRemoveDuplicateUrls() {
      // Arrange
      Long subSeryId = 1L;
      VehicleSeryFile file1 = new VehicleSeryFile();
      file1.setId(1L);
      file1.setUrl("image1.jpg");
      file1.setSort((byte) 1);
      VehicleSeryFile file2 = new VehicleSeryFile();
      file2.setId(2L);
      file2.setUrl("image1.jpg"); // Same URL as file1
      file2.setSort((byte) 2);
      VehicleSeryFile file3 = new VehicleSeryFile();
      file3.setId(3L);
      file3.setUrl("image2.jpg");
      file3.setSort((byte) 3);

      when(vehicleSeryFileMapper.selectByExample(any())).thenReturn(Arrays.asList(file1, file2, file3));

      // Act
      Result<List<VehicleSeryFileVO>> result = vehicleSeryFileService.listBySubSeryId(subSeryId);

      // Assert
      assertTrue(result.isSuccess());
      assertEquals(2, result.getModel().size()); // Should only have 2 unique URLs
      assertEquals(vehicleSeryFileUrl + "image1.jpg", result.getModel().get(0).getUrl());
      assertEquals(vehicleSeryFileUrl + "image2.jpg", result.getModel().get(1).getUrl());
    }
  }
}