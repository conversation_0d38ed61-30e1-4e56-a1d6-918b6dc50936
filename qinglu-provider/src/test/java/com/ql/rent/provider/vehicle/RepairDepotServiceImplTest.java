package com.ql.rent.provider.vehicle;

import com.ql.rent.dao.vehicle.RepairDepotMapper;
import com.ql.rent.entity.vehicle.RepairDepot;
import com.ql.rent.entity.vehicle.RepairDepotExample;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.vehicle.RepairDepotVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class RepairDepotServiceImplTest {

  @Mock
  private RepairDepotMapper repairDepotMapper;

  @InjectMocks
  private RepairDepotServiceImpl repairDepotService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Nested
  class ListRepairDepotTests {
    @Test
    void shouldReturnFailWhenMerchantIdIsNull() {
      // Act
      Result<List<RepairDepotVO>> result = repairDepotService.listRepairDepot(null);

      // Assert
      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
      verifyNoInteractions(repairDepotMapper);
    }

    @Test
    void shouldReturnEmptyListWhenNoDepotsFound() {
      // Arrange
      Long merchantId = 1L;
      when(repairDepotMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      // Act
      Result<List<RepairDepotVO>> result = repairDepotService.listRepairDepot(merchantId);

      // Assert
      assertTrue(result.isSuccess());
      assertTrue(result.getModel().isEmpty());

      // Verify query criteria
      ArgumentCaptor<RepairDepotExample> exampleCaptor = ArgumentCaptor.forClass(RepairDepotExample.class);
      verify(repairDepotMapper).selectByExample(exampleCaptor.capture());
      RepairDepotExample capturedExample = exampleCaptor.getValue();
      RepairDepotExample.Criteria criteria = capturedExample.getOredCriteria().get(0);
      assertTrue(criteria.getAllCriteria().stream()
          .anyMatch(c -> c.getCondition().contains("merchant_id") && c.getValue().equals(merchantId)));
      assertTrue(criteria.getAllCriteria().stream()
          .anyMatch(c -> c.getCondition().contains("deleted") && c.getValue().equals(YesOrNoEnum.NO.getValue())));
    }

    @Test
    void shouldReturnDepotListSuccessfully() {
      // Arrange
      Long merchantId = 1L;
      RepairDepot depot1 = new RepairDepot();
      depot1.setId(1L);
      depot1.setDepotName("Depot 1");
      RepairDepot depot2 = new RepairDepot();
      depot2.setId(2L);
      depot2.setDepotName("Depot 2");

      when(repairDepotMapper.selectByExample(any())).thenReturn(Arrays.asList(depot1, depot2));

      // Act
      Result<List<RepairDepotVO>> result = repairDepotService.listRepairDepot(merchantId);

      // Assert
      assertTrue(result.isSuccess());
      assertEquals(2, result.getModel().size());
      assertEquals("Depot 1", result.getModel().get(0).getDepotName());
      assertEquals("Depot 2", result.getModel().get(1).getDepotName());
    }
  }

  @Nested
  class SaveRepairDepotTests {
    @Test
    void shouldReturnFailWhenParamsInvalid() {
      // Act & Assert
      Result<Integer> result1 = repairDepotService.saveRepairDepot(null, 1L, 1L);
      assertFalse(result1.isSuccess());
      assertEquals("新增维保厂参数错误", result1.getMessage());

      Result<Integer> result2 = repairDepotService.saveRepairDepot("Depot", null, 1L);
      assertFalse(result2.isSuccess());
      assertEquals("新增维保厂参数错误", result2.getMessage());

      Result<Integer> result3 = repairDepotService.saveRepairDepot("Depot", 1L, null);
      assertFalse(result3.isSuccess());
      assertEquals("新增维保厂参数错误", result3.getMessage());

      verifyNoInteractions(repairDepotMapper);
    }

    @Test
    void shouldSaveDepotSuccessfully() {
      // Arrange
      String depotName = "New Depot";
      Long merchantId = 1L;
      Long opUserId = 1L;
      when(repairDepotMapper.insert(any())).thenReturn(1);

      // Act
      Result<Integer> result = repairDepotService.saveRepairDepot(depotName, merchantId, opUserId);

      // Assert
      assertTrue(result.isSuccess());
      assertEquals(1, result.getModel());

      // Verify saved depot
      ArgumentCaptor<RepairDepot> depotCaptor = ArgumentCaptor.forClass(RepairDepot.class);
      verify(repairDepotMapper).insert(depotCaptor.capture());
      RepairDepot capturedDepot = depotCaptor.getValue();
      assertEquals(depotName, capturedDepot.getDepotName());
      assertEquals(merchantId, capturedDepot.getMerchantId());
      assertEquals(opUserId, capturedDepot.getOpUserId());
      assertEquals(YesOrNoEnum.NO.getValue(), capturedDepot.getDeleted());
      assertEquals(1, capturedDepot.getLastVer());
      assertNotNull(capturedDepot.getCreateTime());
      assertNotNull(capturedDepot.getOpTime());
    }

    @Test
    void shouldReturnFailWhenInsertFails() {
      // Arrange
      when(repairDepotMapper.insert(any())).thenReturn(0);

      // Act
      Result<Integer> result = repairDepotService.saveRepairDepot("Depot", 1L, 1L);

      // Assert
      assertFalse(result.isSuccess());
      assertEquals("新增维保厂失败", result.getMessage());
    }
  }

  @Nested
  class DeleteRepairDepotTests {
    @Test
    void shouldReturnFailWhenParamsInvalid() {
      // Act & Assert
      Result<Integer> result1 = repairDepotService.deleteRepairDepot(null, 1L, 1L);
      assertFalse(result1.isSuccess());
      assertEquals("删除维保厂参数错误", result1.getMessage());

      Result<Integer> result2 = repairDepotService.deleteRepairDepot(1L, null, 1L);
      assertFalse(result2.isSuccess());
      assertEquals("删除维保厂参数错误", result2.getMessage());

      Result<Integer> result3 = repairDepotService.deleteRepairDepot(1L, 1L, null);
      assertFalse(result3.isSuccess());
      assertEquals("删除维保厂参数错误", result3.getMessage());

      verifyNoInteractions(repairDepotMapper);
    }

    @Test
    void shouldReturnFailWhenDepotNotFound() {
      // Arrange
      when(repairDepotMapper.selectByPrimaryKey(any())).thenReturn(null);

      // Act
      Result<Integer> result = repairDepotService.deleteRepairDepot(1L, 1L, 1L);

      // Assert
      assertFalse(result.isSuccess());
      assertEquals("数据不存在，请刷新页面", result.getMessage());
    }

    @Test
    void shouldDeleteDepotSuccessfully() {
      // Arrange
      Long depotId = 1L;
      Long merchantId = 1L;
      Long opUserId = 1L;

      RepairDepot existingDepot = new RepairDepot();
      existingDepot.setId(depotId);
      existingDepot.setDeleted(YesOrNoEnum.NO.getValue());
      existingDepot.setLastVer(1);

      when(repairDepotMapper.selectByPrimaryKey(depotId)).thenReturn(existingDepot);
      when(repairDepotMapper.updateByPrimaryKey(any())).thenReturn(1);

      // Act
      Result<Integer> result = repairDepotService.deleteRepairDepot(depotId, merchantId, opUserId);

      // Assert
      assertTrue(result.isSuccess());
      assertEquals(1, result.getModel());

      // Verify update
      ArgumentCaptor<RepairDepot> depotCaptor = ArgumentCaptor.forClass(RepairDepot.class);
      verify(repairDepotMapper).updateByPrimaryKey(depotCaptor.capture());
      RepairDepot capturedDepot = depotCaptor.getValue();
      assertEquals(YesOrNoEnum.YES.getValue(), capturedDepot.getDeleted());
      assertEquals(2, capturedDepot.getLastVer());
      assertEquals(opUserId, capturedDepot.getOpUserId());
      assertNotNull(capturedDepot.getOpTime());
    }
  }

  @Nested
  class GetByIdTests {
    @Test
    void shouldReturnFailWhenIdIsNull() {
      // Act
      Result<RepairDepotVO> result = repairDepotService.getById(null);

      // Assert
      assertFalse(result.isSuccess());
      assertEquals("查询维保厂id不能为空", result.getMessage());
      verifyNoInteractions(repairDepotMapper);
    }

    @Test
    void shouldReturnFailWhenDepotNotFound() {
      // Arrange
      when(repairDepotMapper.selectByPrimaryKey(any())).thenReturn(null);

      // Act
      Result<RepairDepotVO> result = repairDepotService.getById(1L);

      // Assert
      assertFalse(result.isSuccess());
      assertEquals("未查询到对应的维保厂", result.getMessage());
    }

    @Test
    void shouldReturnDepotSuccessfully() {
      // Arrange
      Long depotId = 1L;
      RepairDepot depot = new RepairDepot();
      depot.setId(depotId);
      depot.setDepotName("Test Depot");
      depot.setDeleted(YesOrNoEnum.NO.getValue());

      when(repairDepotMapper.selectByPrimaryKey(depotId)).thenReturn(depot);

      // Act
      Result<RepairDepotVO> result = repairDepotService.getById(depotId);

      // Assert
      assertTrue(result.isSuccess());
      assertEquals(depotId, result.getModel().getId());
      assertEquals("Test Depot", result.getModel().getDepotName());
    }

    @Test
    void shouldReturnFailWhenDepotIsDeleted() {
      // Arrange
      Long depotId = 1L;
      RepairDepot depot = new RepairDepot();
      depot.setId(depotId);
      depot.setDeleted(YesOrNoEnum.YES.getValue());

      when(repairDepotMapper.selectByPrimaryKey(depotId)).thenReturn(depot);

      // Act
      Result<RepairDepotVO> result = repairDepotService.getById(depotId);

      // Assert
      assertFalse(result.isSuccess());
      assertEquals("未查询到对应的维保厂", result.getMessage());
    }
  }
}