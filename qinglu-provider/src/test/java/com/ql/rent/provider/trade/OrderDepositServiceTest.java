package com.ql.rent.provider.trade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ql.dto.ApiResultResp;
import com.ql.dto.open.request.PlatformBaseRequest;
import com.ql.rent.AbstractTest;
import com.ql.rent.api.aggregate.remote.didi.api.DidiApiClient;
import com.ql.rent.api.aggregate.remote.dto.DidiOrderDetailDTO;
import com.ql.rent.api.aggregate.remote.vo.request.OrderDepositUnfreezeReq;
import com.ql.rent.service.trade.IOrderDepositService;
import com.ql.rent.service.trade.IOrderFixDataService;
import com.ql.rent.service.trade.IOrderService;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.trade.OrderInfoVo;
import org.junit.jupiter.api.Test;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * @Author: musi
 * @Date: 2024/3/31 13:12
 */
public class OrderDepositServiceTest extends AbstractTest {

    @Resource
    private IOrderDepositService orderDepositService;
    @Resource
    private DidiApiClient didiApiClient;
    @Resource
    private IOrderService orderService;

    @Test
    public  void testDepositThawForJob() {
        Result result = orderDepositService.depositThawForJob();
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public  void testIllegalDepositThaw() {
        orderDepositService.illegalDepositThaw(467549L);
        System.out.println();
    }



    @Test
    public  void testOrderDetail() {

        /**
         * {
         * 	"code": "0",
         * 	"data": {
         * 		"supplier_info": {
         * 			"store_code": "25",
         * 			"third_order_id": "467549",
         * 			"supplier": 15,
         * 			"store_name": "骑仕租车"
         *                },
         * 		"booking_info": {
         * 			"driver_name": "赵航",
         * 			"car_name": "奥迪A3",
         * 			"driver_phone": "13000000000",
         * 			"driver_licence": "41110219650519051X",
         * 			"drop_off_info": {
         * 				"address": "哈尔滨市太平国际机场附近的门店",
         * 				"time": "2024-05-29 14:16:13",
         * 				"type": 4,
         * 				"pre_time": "2024-05-29 18:00:00",
         * 				"city_id": 5
         *            },
         * 			"pick_up_info": {
         * 				"address": "哈尔滨市太平国际机场附近的门店",
         * 				"time": "2024-05-29 14:15:23",
         * 				"type": 4,
         * 				"pre_time": "2024-05-29 17:00:00",
         * 				"city_id": 5
         *            }
         *        },
         * 		"amount_info": {
         * 			"deduct_amount": "-",
         * 			"renewal_amount": 0,
         * 			"replenish_amount": 0,
         * 			"replenish_pay": 0,
         * 			"ori_gmv": "3",
         * 			"real_pay_all": "3",
         * 			"replenish_detail": [],
         * 			"third_deduct_amount": "-",
         * 			"refund_detail": [{
         * 				"third_amount": 300,
         * 				"name": "能源费退款",
         * 				"didi_amount": 300,
         * 				"time": "2024-05-29 14:16:12"
         *            }],
         * 			"real_gmv": "3",
         * 			"coupon_info": {
         * 				"coupon_amount": 0,
         * 				"coupon_name": "",
         * 				"coupon_type": ""
         *            },
         * 			"real_pay_main": "3",
         * 			"promotions": [{
         * 				"config_id": "",
         * 				"deduction_amount": 0,
         * 				"title": ""
         *            }],
         * 			"fee_detail": [{
         * 				"target_amount": 1,
         * 				"name": "基本租车费"
         *            }, {
         * 				"target_amount": 1,
         * 				"name": "基础服务费"
         *            }, {
         * 				"target_amount": 1,
         * 				"name": "车行手续费"
         *            }, {
         * 				"target_amount": 0,
         * 				"name": "送车上门服务费"
         *            }, {
         * 				"target_amount": 0,
         * 				"name": "上门取车服务费"
         *            }, {
         * 				"target_amount": 0,
         * 				"name": "夜间服务费"
         *            }, {
         * 				"target_amount": 0,
         * 				"name": "异地还车费"
         *            }, {
         * 				"target_amount": 0,
         * 				"name": "零散小时费"
         *            }],
         * 			"refund_amount": 300,
         * 			"renewal_pay": 0,
         * 			"didi_refund_amount": 300
         *        },
         * 		"order_info": {
         * 			"cancel_time": "-",
         * 			"order_status": 7,
         * 			"replenish_pay_status": 0,
         * 			"cancel_reason": "-",
         * 			"create_time": "2024-05-29 14:14:41",
         * 			"paid_time": "2024-05-29 14:14:47",
         * 			"renewal_status": -1,
         * 			"main_order_status": 7,
         * 			"cancel_type": "-",
         * 			"order_id": "7201465956520114652",
         * 			"renewal_paid_time": ""
         *        },
         * 		"deposit_info": {
         * 			"deposit_amount": 200,
         * 			"deposit_op_list": [{
         * 				"amount": 200,
         * 				"time": "1970-01-01 08:00:00",
         * 				"type": -1
         *            }, {
         * 				"amount": 150,
         * 				"time": "2024-05-29 15:39:41",
         * 				"type": 3
         *            }],
         * 			"deduct_list": [{
         * 				"amount": 50,
         * 				"time": "2024-05-29 14:16:12",
         * 				"type": 3
         *            }],
         * 			"free_deposit_type": 1,
         * 			"deposit_status": 4,
         * 			"refund_list": [],
         * 			"broken_deposit_amount": 200,
         * 			"deposit_freezed_balance": 0,
         * 			"platform": "支付宝",
         * 			"illegal_deposit_amount": 100
         *        }* 	},
         * 	"message": "ok"
         * }
         */
        Result<OrderInfoVo> orderInfoVoResult = orderService.getOrderInfo(467549L);
        OrderDepositUnfreezeReq orderDepositUnfreezeReq = new OrderDepositUnfreezeReq();
        orderDepositUnfreezeReq.setOrderId(orderInfoVoResult.getModel().getId());
        orderDepositUnfreezeReq.setSourceOrderId(orderInfoVoResult.getModel().getSourceOrderId());
        PlatformBaseRequest<OrderDepositUnfreezeReq> request = new PlatformBaseRequest<>();
        request.setData(orderDepositUnfreezeReq);
        request.setChannelId(orderInfoVoResult.getModel().getOrderSource().longValue());
        request.setMerchantId(orderInfoVoResult.getModel().getMerchantId());
        ApiResultResp result = didiApiClient.orderDetail(request);
        DidiOrderDetailDTO didiOrderDetailDTO = JSON.parseObject(JSON.toJSONString(result.getData()), DidiOrderDetailDTO.class);
        System.out.println(JSON.toJSONString(didiOrderDetailDTO.getDeposit_info().getDeposit_freezed_balance()));
    }


}
