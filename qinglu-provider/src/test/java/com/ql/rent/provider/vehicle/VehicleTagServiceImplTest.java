package com.ql.rent.provider.vehicle;

import com.ql.enums.VehicleInfoEnums;
import com.ql.rent.dao.vehicle.VehicleInfoTagMapper;
import com.ql.rent.dao.vehicle.VehicleTagMapper;
import com.ql.rent.dao.vehicle.VehicleTagPropMapper;
import com.ql.rent.entity.vehicle.*;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.param.vehicle.VehicleTagPropParam;
import com.ql.rent.param.vehicle.VehicleTagPropQueryParam;
import com.ql.rent.param.vehicle.VehicleTagStoreVO;
import com.ql.rent.service.common.IPushDataTaskService;
import com.ql.rent.service.store.IStoreInfoService;
import com.ql.rent.service.vehicle.ICtripVehicleService;
import com.ql.rent.service.vehicle.IVehicleTagService;
import com.ql.rent.service.vehicle.IVehicleTagService.TagType;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.common.VehicleTagPropVO;
import com.ql.rent.vo.store.StoreSimpleVo;
import com.ql.rent.vo.vehicle.VehicleTagVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Executor;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("VehicleTagServiceImpl Tests")
class VehicleTagServiceImplTest {

  @Mock
  private VehicleTagMapper vehicleTagMapper;

  @Mock
  private IStoreInfoService storeInfoService;

  @Mock
  private IPushDataTaskService pushDataCtripTaskServiceImpl;

  @Mock
  private Executor asyncPromiseExecutor;

  @Mock
  private VehicleInfoTagMapper vehicleInfoTagMapper;

  @Mock
  private VehicleTagPropMapper vehicleTagPropMapper;

  @Mock
  private ICtripVehicleService ctripVehicleService;

  @InjectMocks
  private VehicleTagServiceImpl vehicleTagService;

  private LoginVo validLoginVo;
  private VehicleTagPropParam validTagPropParam;
  private VehicleTag validVehicleTag;
  private final Long VALID_MERCHANT_ID = 1L;
  private final Long VALID_USER_ID = 100L;
  private final Long VALID_TAG_ID = 1000L;
  private final Long VALID_MODEL_ID = 2000L;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);

    // Setup valid LoginVo
    validLoginVo = new LoginVo();
    validLoginVo.setMerchantId(VALID_MERCHANT_ID);
    validLoginVo.setUserId(VALID_USER_ID);

    // Setup valid VehicleTagPropParam
    validTagPropParam = new VehicleTagPropParam();
    validTagPropParam.setTagName("Test Tag");
    validTagPropParam.setTagType((byte) 1); // 1 for vehicle model tag

    // Setup valid VehicleTag
    validVehicleTag = new VehicleTag();
    validVehicleTag.setId(VALID_TAG_ID);
    validVehicleTag.setVehicleModelId(VALID_MODEL_ID);
    validVehicleTag.setDeleted(YesOrNoEnum.NO.getValue());
  }

  @Nested
  @DisplayName("listAllVehicleTagProp Tests")
  class ListAllVehicleTagPropTests {

    @Test
    @DisplayName("Should successfully list all vehicle tag properties")
    void shouldSuccessfullyListAllVehicleTagProperties() {
      // Arrange
      VehicleTagPropQueryParam queryParam = new VehicleTagPropQueryParam();
      queryParam.setMerchantId(VALID_MERCHANT_ID);

      VehicleTagProp tagProp = new VehicleTagProp();
      tagProp.setId(VALID_TAG_ID);
      tagProp.setTagName("Test Tag");
      tagProp.setPreset(YesOrNoEnum.NO.getValue());

      when(vehicleTagPropMapper.selectByExample(any())).thenReturn(Collections.singletonList(tagProp));

      // Act
      Result<List<VehicleTagPropVO>> result = vehicleTagService.listAllVehicleTagProp(queryParam, false, true);

      // Assert
      assertTrue(result.isSuccess());
      assertNotNull(result.getModel());
      assertEquals(1, result.getModel().size());
      assertEquals("Test Tag", result.getModel().get(0).getTagName());
    }

    @Test
    @DisplayName("Should fail when query param is null")
    void shouldFailWhenQueryParamIsNull() {
      var result = vehicleTagService.listAllVehicleTagProp(null, false, true);
      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    @DisplayName("Should return empty list when no tags found")
    void shouldReturnEmptyListWhenNoTagsFound() {
      var queryParam = new VehicleTagPropQueryParam();
      queryParam.setMerchantId(1L);
      when(vehicleTagPropMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      var result = vehicleTagService.listAllVehicleTagProp(queryParam, false, true);
      assertTrue(result.isSuccess());
      assertTrue(result.getModel().isEmpty());
    }

    @Test
    @DisplayName("Should return tags when found")
    void shouldReturnTagsWhenFound() {
      var queryParam = new VehicleTagPropQueryParam();
      queryParam.setMerchantId(1L);

      var tagProp = new VehicleTagProp();
      tagProp.setId(1L);
      tagProp.setTagName("测试标签");
      tagProp.setPreset(YesOrNoEnum.NO.getValue());

      when(vehicleTagPropMapper.selectByExample(any())).thenReturn(Collections.singletonList(tagProp));

      var result = vehicleTagService.listAllVehicleTagProp(queryParam, false, true);
      assertTrue(result.isSuccess());
      assertEquals(1, result.getModel().size());
      assertEquals("测试标签", result.getModel().get(0).getTagName());
    }
  }

  @Nested
  @DisplayName("listByVehicleModelId Tests")
  class ListByVehicleModelIdTests {

    @Test
    @DisplayName("Should successfully list tags by vehicle model id")
    void shouldSuccessfullyListTagsByVehicleModelId() {
      // Arrange
      List<Long> modelIds = Collections.singletonList(VALID_MODEL_ID);

      VehicleTag tag = new VehicleTag();
      tag.setTagId(VALID_TAG_ID);
      tag.setVehicleModelId(VALID_MODEL_ID);

      VehicleTagProp tagProp = new VehicleTagProp();
      tagProp.setId(VALID_TAG_ID);
      tagProp.setTagName("Test Tag");

      when(vehicleTagMapper.selectByExample(any())).thenReturn(Collections.singletonList(tag));
      when(vehicleTagPropMapper.selectByExample(any())).thenReturn(Collections.singletonList(tagProp));

      // Act
      Result<List<VehicleTagVO>> result = vehicleTagService.listByVehicleModelId(modelIds);

      // Assert
      assertTrue(result.isSuccess());
      assertNotNull(result.getModel());
      assertEquals(1, result.getModel().size());
      assertEquals(VALID_MODEL_ID, result.getModel().get(0).getVehicleModelId());
    }

    @Test
    @DisplayName("Should fail when model ids are empty")
    void shouldFailWhenModelIdsAreEmpty() {
      var result = vehicleTagService.listByVehicleModelId(Collections.emptyList());
      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    @DisplayName("Should fail when vehicle model ids is empty")
    void shouldFailWhenVehicleModelIdsIsEmpty() {
      var result = vehicleTagService.listByVehicleModelId(Collections.emptyList());
      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    @DisplayName("Should return empty list when no tags found")
    void shouldReturnEmptyListWhenNoTagsFound() {
      List<Long> vehicleModelIds = Arrays.asList(1L);
      when(vehicleTagMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      var result = vehicleTagService.listByVehicleModelId(vehicleModelIds);
      assertTrue(result.isSuccess());
      assertTrue(result.getModel().isEmpty());
    }

    @Test
    @DisplayName("Should return tags when found")
    void shouldReturnTagsWhenFound() {
      List<Long> vehicleModelIds = Arrays.asList(1L);

      var vehicleTag = new VehicleTag();
      vehicleTag.setVehicleModelId(1L);
      vehicleTag.setTagId(1L);
      vehicleTag.setStoreId(1L);

      var tagProp = new VehicleTagProp();
      tagProp.setId(1L);
      tagProp.setTagName("测试标签");

      when(vehicleTagMapper.selectByExample(any())).thenReturn(Collections.singletonList(vehicleTag));
      when(vehicleTagPropMapper.selectByExample(any())).thenReturn(Collections.singletonList(tagProp));

      var result = vehicleTagService.listByVehicleModelId(vehicleModelIds);
      assertTrue(result.isSuccess());
      assertEquals(1, result.getModel().size());
      assertEquals("测试标签", result.getModel().get(0).getTagName());
    }
  }

  @Nested
  @DisplayName("updateVehicleTag Tests")
  class UpdateVehicleTagTests {

    @Test
    @DisplayName("Should successfully update vehicle tags")
    void shouldSuccessfullyUpdateVehicleTags() {
      // Arrange
      List<Long> newTags = Arrays.asList(1L, 2L);

      VehicleTag existingTag = new VehicleTag();
      existingTag.setId(3L);
      existingTag.setTagId(3L);

      when(vehicleTagMapper.selectByExample(any())).thenReturn(Collections.singletonList(existingTag));
      when(vehicleTagMapper.updateByExampleSelective(any(), any())).thenReturn(1);
      when(vehicleTagMapper.batchInsert(anyList())).thenReturn(2);

      // Act
      Result<Integer> result = vehicleTagService.updateVehicleTag(VALID_MODEL_ID, newTags, VALID_USER_ID);

      // Assert
      assertTrue(result.isSuccess());
      assertEquals(1, result.getModel());
      verify(vehicleTagMapper).updateByExampleSelective(any(), any());
      verify(vehicleTagMapper).batchInsert(anyList());
    }

    @Test
    @DisplayName("Should fail when required parameters are missing")
    void shouldFailWhenRequiredParametersAreMissing() {
      // Act
      Result<Integer> result = vehicleTagService.updateVehicleTag(null, Collections.emptyList(), null);

      // Assert
      assertFalse(result.isSuccess());
      verify(vehicleTagMapper, never()).selectByExample(any());
    }

    @Test
    @DisplayName("Should fail when params are invalid")
    void shouldFailWhenParamsAreInvalid() {
      var result = vehicleTagService.updateVehicleTag(null, Arrays.asList(1L), 1L);
      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }
  }

  @Nested
  @DisplayName("saveVehicleTagProp Tests")
  class SaveVehicleTagPropTests {

    @Test
    @DisplayName("Should successfully save vehicle tag property")
    void shouldSuccessfullyCreateVehicleTagProperty() {
      // Arrange
      when(vehicleTagPropMapper.insertSelective(any())).thenReturn(1);

      // Act
      Result<Integer> result = vehicleTagService.saveVehicleTagProp(validTagPropParam, validLoginVo);

      // Assert
      assertTrue(result.isSuccess());
      assertEquals(1, result.getModel());
      verify(vehicleTagPropMapper).insertSelective(any());
    }

    @Test
    @DisplayName("Should fail when tag name is empty")
    void shouldFailWhenTagNameIsEmpty() {
      // Arrange
      validTagPropParam.setTagName("");

      // Act
      Result<Integer> result = vehicleTagService.saveVehicleTagProp(validTagPropParam, validLoginVo);

      // Assert
      assertFalse(result.isSuccess());
      verify(vehicleTagPropMapper, never()).insertSelective(any());
    }
  }

  @Nested
  class CreateVehicleTagPropTests {
    private VehicleTagPropParam validParam;
    private LoginVo validLoginVo;

    @BeforeEach
    void setUp() {
      validParam = new VehicleTagPropParam();
      validParam.setTagName("新标签");
      validParam.setTagType(TagType.VEHICLE_MODEL.getTagType());

      validLoginVo = new LoginVo();
      validLoginVo.setUserId(1L);
      validLoginVo.setMerchantId(1L);
    }

    @Test
    void shouldFailWhenParamIsNull() {
      var result = vehicleTagService.saveVehicleTagProp(null, validLoginVo);
      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldFailWhenLoginVoIsNull() {
      var result = vehicleTagService.saveVehicleTagProp(validParam, null);
      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldFailWhenTagNameExists() {
      when(vehicleTagPropMapper.selectByExample(any())).thenReturn(Collections.singletonList(new VehicleTagProp()));

      var result = vehicleTagService.saveVehicleTagProp(validParam, validLoginVo);
      assertFalse(result.isSuccess());
      assertEquals("标签名称已存在", result.getMessage());
    }

    @Test
    void shouldSuccessfullySaveNewTag() {
      when(vehicleTagPropMapper.selectByExample(any())).thenReturn(Collections.emptyList());
      when(vehicleTagPropMapper.insertSelective(any())).thenReturn(1);

      var result = vehicleTagService.saveVehicleTagProp(validParam, validLoginVo);
      assertTrue(result.isSuccess());
      assertEquals(1, result.getModel());

      verify(vehicleTagPropMapper).insertSelective(any());
    }
  }

  @Nested
  class DeleteVehicleTagPropTests {
    private LoginVo validLoginVo;

    @BeforeEach
    void setUp() {
      validLoginVo = new LoginVo();
      validLoginVo.setUserId(1L);
      validLoginVo.setMerchantId(1L);
    }

    @Test
    void shouldFailWhenIdIsNull() {
      var result = vehicleTagService.deleteVehicleTagProp(null, validLoginVo);
      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldFailWhenLoginVoIsNull() {
      var result = vehicleTagService.deleteVehicleTagProp(1L, null);
      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldFailWhenTagNotFound() {
      when(vehicleTagPropMapper.selectByPrimaryKey(any())).thenReturn(null);

      var result = vehicleTagService.deleteVehicleTagProp(1L, validLoginVo);
      assertFalse(result.isSuccess());
      assertEquals("标签不存在", result.getMessage());
    }

    @Test
    void shouldFailWhenTagIsPreset() {
      var tagProp = new VehicleTagProp();
      tagProp.setPreset(YesOrNoEnum.YES.getValue());
      when(vehicleTagPropMapper.selectByPrimaryKey(any())).thenReturn(tagProp);

      var result = vehicleTagService.deleteVehicleTagProp(1L, validLoginVo);
      assertFalse(result.isSuccess());
      assertEquals("预设标签不允许删除", result.getMessage());
    }

    @Test
    void shouldSuccessfullyDeleteTag() {
      var tagProp = new VehicleTagProp();
      tagProp.setPreset(YesOrNoEnum.NO.getValue());
      tagProp.setMerchantId(1L);

      when(vehicleTagPropMapper.selectByPrimaryKey(any())).thenReturn(tagProp);
      when(vehicleTagPropMapper.updateByPrimaryKeySelective(any())).thenReturn(1);

      var result = vehicleTagService.deleteVehicleTagProp(1L, validLoginVo);
      assertTrue(result.isSuccess());
      assertEquals(1, result.getModel());

      verify(vehicleTagPropMapper).updateByPrimaryKeySelective(any());
    }
  }

  @Nested
  class SaveVehicleInfoTagTests {
    private LoginVo validLoginVo;
    private List<Long> validTagIds;

    @BeforeEach
    void setUp() {
      validLoginVo = new LoginVo();
      validLoginVo.setUserId(1L);
      validLoginVo.setMerchantId(1L);

      validTagIds = Arrays.asList(1L, 2L);
    }

    @Test
    void shouldFailWhenVehicleIdIsNull() {
      var result = vehicleTagService.saveVehicleInfoTag(null, validTagIds, validLoginVo);
      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldFailWhenLoginVoIsNull() {
      var result = vehicleTagService.saveVehicleInfoTag(1L, validTagIds, null);
      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldSuccessfullySaveVehicleInfoTags() {
      when(vehicleInfoTagMapper.selectByExample(any())).thenReturn(Collections.emptyList());
      when(vehicleInfoTagMapper.batchInsert(any())).thenReturn(2);

      var result = vehicleTagService.saveVehicleInfoTag(1L, validTagIds, validLoginVo);
      assertTrue(result.isSuccess());
      assertEquals(1, result.getModel());

      verify(vehicleInfoTagMapper).batchInsert(any());
    }
  }
}