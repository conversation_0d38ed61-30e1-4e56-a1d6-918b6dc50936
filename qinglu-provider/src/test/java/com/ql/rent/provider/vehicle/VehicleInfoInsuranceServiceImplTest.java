package com.ql.rent.provider.vehicle;

import com.ql.enums.VehicleInfoEnums;
import com.ql.rent.dao.vehicle.VehicleInfoAttMapper;
import com.ql.rent.dao.vehicle.VehicleInfoInsuranceMapper;
import com.ql.rent.entity.vehicle.VehicleInfoAtt;
import com.ql.rent.entity.vehicle.VehicleInfoAttExample;
import com.ql.rent.entity.vehicle.VehicleInfoInsurance;
import com.ql.rent.entity.vehicle.VehicleInfoInsuranceExample;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.vehicle.VehicleInfoInsuranceVO;
import com.ql.rent.vo.vehicle.VehicleInfoVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class VehicleInfoInsuranceServiceImplTest {

  @Mock
  private VehicleInfoInsuranceMapper vehicleInfoInsuranceMapper;

  @Mock
  private VehicleInfoAttMapper vehicleInfoAttMapper;

  @InjectMocks
  private VehicleInfoInsuranceServiceImpl vehicleInfoInsuranceService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Nested
  class UpdateVehicleInfoInsuranceTests {
    @Test
    void shouldUpdateInsuranceSuccessfully() {
      // Prepare test data
      VehicleInfoVO vehicleInfoParam = new VehicleInfoVO();
      vehicleInfoParam.setId(1L);
      VehicleInfoVO.VehicleInfoLicenseVO licenseParam = new VehicleInfoVO.VehicleInfoLicenseVO();
      licenseParam.setEngineNum("ENG123");
      licenseParam.setFrameNum("FRM456");
      vehicleInfoParam.setLicenseParam(licenseParam);

      VehicleInfoInsuranceVO bizInsurance = new VehicleInfoInsuranceVO();
      bizInsurance.setInsuranceCompany("Test Insurance Co");
      bizInsurance.setInsuranceNum("INS123");
      bizInsurance.setInsuranceHolder("John Doe");
      bizInsurance.setInsuranceIdcard("ID123");
      bizInsurance.setInsuranceStartDate("2024-01-01");
      bizInsurance.setInsuranceEndDate("2025-01-01");
      bizInsurance.setThirdInsuranceType((byte) 1);
      bizInsurance.setDamageAmount(10000);

      VehicleInfoVO.AttParam attParam = new VehicleInfoVO.AttParam();
      attParam.setFilePath("test/path/insurance.jpg");
      bizInsurance.setFilePathList(Collections.singletonList(attParam));
      vehicleInfoParam.setBizInsuranceParamList(Collections.singletonList(bizInsurance));

      LoginVo opUser = new LoginVo();
      opUser.setUserId(1L);

      // Mock mapper responses
      when(vehicleInfoInsuranceMapper.selectByExample(any())).thenReturn(Collections.emptyList());
      when(vehicleInfoAttMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      // Execute
      vehicleInfoInsuranceService.updVehicleInfoInsurance(vehicleInfoParam, opUser);

      // Verify insurance update
      ArgumentCaptor<VehicleInfoInsurance> insuranceCaptor = ArgumentCaptor.forClass(VehicleInfoInsurance.class);
      verify(vehicleInfoInsuranceMapper).insertSelective(insuranceCaptor.capture());
      VehicleInfoInsurance savedInsurance = insuranceCaptor.getValue();
      assertEquals("Test Insurance Co", savedInsurance.getInsuranceCompany());
      assertEquals("INS123", savedInsurance.getInsuranceNum());
      assertEquals("John Doe", savedInsurance.getInsuranceHolder());
      assertEquals("ID123", savedInsurance.getInsuranceIdcard());
      assertEquals("2024-01-01", savedInsurance.getInsuranceStartDate());
      assertEquals("2025-01-01", savedInsurance.getInsuranceEndDate());
      assertEquals((byte) 1, savedInsurance.getThirdInsuranceType());
      assertEquals(10000, savedInsurance.getDamageAmount());
      assertEquals("ENG123", savedInsurance.getEngineNum());
      assertEquals("FRM456", savedInsurance.getFrameNum());
      assertEquals(YesOrNoEnum.NO.getValue(), savedInsurance.getDeleted());

      // Verify attachment update
      ArgumentCaptor<VehicleInfoAtt> attCaptor = ArgumentCaptor.forClass(VehicleInfoAtt.class);
      verify(vehicleInfoAttMapper).insertSelective(attCaptor.capture());
      VehicleInfoAtt savedAtt = attCaptor.getValue();
      assertEquals("test/path/insurance.jpg", savedAtt.getFilePath());
      assertEquals(VehicleInfoEnums.VehicleFileEnum.INSURANCE.getValue(), savedAtt.getFileType());
      assertEquals(YesOrNoEnum.NO.getValue(), savedAtt.getDeleted());
    }

    @Test
    void shouldDeleteExistingInsuranceAndAttachments() {
      // Prepare test data
      VehicleInfoVO vehicleInfoParam = new VehicleInfoVO();
      vehicleInfoParam.setId(1L);
      VehicleInfoVO.VehicleInfoLicenseVO licenseParam = new VehicleInfoVO.VehicleInfoLicenseVO();
      vehicleInfoParam.setLicenseParam(licenseParam);
      vehicleInfoParam.setBizInsuranceParamList(Collections.emptyList());

      LoginVo opUser = new LoginVo();
      opUser.setUserId(1L);

      // Mock existing data
      VehicleInfoInsurance existingInsurance = new VehicleInfoInsurance();
      existingInsurance.setId(1L);
      existingInsurance.setDeleted(YesOrNoEnum.NO.getValue());

      VehicleInfoAtt existingAtt = new VehicleInfoAtt();
      existingAtt.setId(1L);
      existingAtt.setDeleted(YesOrNoEnum.NO.getValue());

      when(vehicleInfoInsuranceMapper.selectByExample(any()))
          .thenReturn(Collections.singletonList(existingInsurance));
      when(vehicleInfoAttMapper.selectByExample(any()))
          .thenReturn(Collections.singletonList(existingAtt));

      // Execute
      vehicleInfoInsuranceService.updVehicleInfoInsurance(vehicleInfoParam, opUser);

      // Verify deletion
      ArgumentCaptor<VehicleInfoInsurance> insuranceCaptor = ArgumentCaptor.forClass(VehicleInfoInsurance.class);
      verify(vehicleInfoInsuranceMapper).updateByPrimaryKeySelective(insuranceCaptor.capture());
      VehicleInfoInsurance deletedInsurance = insuranceCaptor.getValue();
      assertEquals(YesOrNoEnum.YES.getValue(), deletedInsurance.getDeleted());

      ArgumentCaptor<VehicleInfoAtt> attCaptor = ArgumentCaptor.forClass(VehicleInfoAtt.class);
      verify(vehicleInfoAttMapper).updateByPrimaryKeySelective(attCaptor.capture());
      VehicleInfoAtt deletedAtt = attCaptor.getValue();
      assertEquals(YesOrNoEnum.YES.getValue(), deletedAtt.getDeleted());
    }
  }
}