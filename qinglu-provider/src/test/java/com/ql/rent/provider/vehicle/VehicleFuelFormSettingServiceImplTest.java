package com.ql.rent.provider.vehicle;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.ql.rent.converter.vehicle.VehicleFuelFormConverter;
import com.ql.rent.dao.vehicle.VehicleFuelFormSettingMapper;
import com.ql.rent.entity.vehicle.VehicleFuelFormSetting;
import com.ql.rent.entity.vehicle.VehicleFuelFormSettingExample;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.param.StoreBaseQuery;
import com.ql.rent.service.common.IAreaService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.common.AreaVo;
import com.ql.rent.vo.vehicle.VehicleFuelFormAddRequest;
import com.ql.rent.vo.vehicle.VehicleFuelFormSettingVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class VehicleFuelFormSettingServiceImplTest {

  @Mock
  private VehicleFuelFormSettingMapper vehicleFuelFormSettingMapper;

  @Mock
  private IAreaService areaService;

  @InjectMocks
  private VehicleFuelFormSettingServiceImpl vehicleFuelFormSettingService;

  private LoginVo loginVo;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    loginVo = new LoginVo();
    loginVo.setMerchantId(1L);
    loginVo.setUserId(1L);
  }

  @Nested
  class PageTests {
    @Test
    void shouldReturnEmptyListWhenNoRecordsFound() {
      StoreBaseQuery query = new StoreBaseQuery();
      query.setPageIndex(1);
      query.setPageSize(10);

      when(vehicleFuelFormSettingMapper.countByExample(any())).thenReturn(0L);

      PageListVo<VehicleFuelFormSettingVO> result = vehicleFuelFormSettingService.page(loginVo, query);

      assertEquals(0L, result.getCount());
      assertTrue(result.getList().isEmpty());
    }

    @Test
    void shouldReturnPagedListWhenRecordsExist() {
      StoreBaseQuery query = new StoreBaseQuery();
      query.setPageIndex(1);
      query.setPageSize(10);

      VehicleFuelFormSetting setting = createSetting(1L, (byte) 1, "92", 700L, 100L, Arrays.asList(1L));
      when(vehicleFuelFormSettingMapper.countByExample(any())).thenReturn(1L);
      when(vehicleFuelFormSettingMapper.selectByExample(any())).thenReturn(Collections.singletonList(setting));
      when(areaService.findByIds(any()))
          .thenReturn(ResultUtil.successResult(Collections.singletonList(createAreaVo(1L, "广州"))));

      PageListVo<VehicleFuelFormSettingVO> result = vehicleFuelFormSettingService.page(loginVo, query);

      assertEquals(1L, result.getCount());
      assertEquals(1, result.getList().size());
      VehicleFuelFormSettingVO vo = result.getList().get(0);
      assertEquals(Long.valueOf(1L), vo.getId());
      assertEquals(Byte.valueOf((byte) 1), vo.getFuelFormType());
      assertEquals("92", vo.getOilGrade());
      assertEquals(Long.valueOf(700L), vo.getSettingPrice());
      assertEquals(Long.valueOf(100L), vo.getServicePrice());
      assertEquals(Arrays.asList(1L), vo.getCitySetting());
      assertEquals(Arrays.asList("广州"), vo.getCityName());
    }

    @Test
    void shouldHandleAllCitiesCorrectly() {
      StoreBaseQuery query = new StoreBaseQuery();
      query.setPageIndex(1);
      query.setPageSize(10);

      VehicleFuelFormSetting setting = createSetting(1L, (byte) 1, "92", 700L, 100L, Arrays.asList(0L));
      when(vehicleFuelFormSettingMapper.countByExample(any())).thenReturn(1L);
      when(vehicleFuelFormSettingMapper.selectByExample(any())).thenReturn(Collections.singletonList(setting));

      PageListVo<VehicleFuelFormSettingVO> result = vehicleFuelFormSettingService.page(loginVo, query);

      assertEquals(1L, result.getCount());
      assertEquals(1, result.getList().size());
      VehicleFuelFormSettingVO vo = result.getList().get(0);
      assertEquals(Arrays.asList("全部"), vo.getCityName());
    }
  }

  @Nested
  class DetailTests {
    @Test
    void shouldReturnNullWhenRecordNotFound() {
      when(vehicleFuelFormSettingMapper.selectByPrimaryKey(1L)).thenReturn(null);

      VehicleFuelFormSettingVO result = vehicleFuelFormSettingService.detail(loginVo, 1L);

      assertNull(result);
    }

    @Test
    void shouldReturnDetailWhenExists() {
      VehicleFuelFormSetting setting = createSetting(1L, (byte) 1, "92", 700L, 100L, Arrays.asList(1L));
      when(vehicleFuelFormSettingMapper.selectByPrimaryKey(1L)).thenReturn(setting);
      when(areaService.findByIds(any()))
          .thenReturn(ResultUtil.successResult(Collections.singletonList(createAreaVo(1L, "广州"))));

      VehicleFuelFormSettingVO result = vehicleFuelFormSettingService.detail(loginVo, 1L);

      assertNotNull(result);
      assertEquals(Long.valueOf(1L), result.getId());
      assertEquals(Byte.valueOf((byte) 1), result.getFuelFormType());
      assertEquals("92", result.getOilGrade());
      assertEquals(Long.valueOf(700L), result.getSettingPrice());
      assertEquals(Long.valueOf(100L), result.getServicePrice());
      assertEquals(Arrays.asList(1L), result.getCitySetting());
      assertEquals(Arrays.asList("广州"), result.getCityName());
    }
  }

  @Nested
  class AddFuelFormSettingTests {
    @Test
    void shouldThrowExceptionWhenRequestIsNull() {
      assertThrows(BizException.class, () -> vehicleFuelFormSettingService.addFuelFormSetting(loginVo, null));
    }

    @Test
    void shouldThrowExceptionWhenFuelFormTypeIsInvalid() {
      VehicleFuelFormAddRequest request = new VehicleFuelFormAddRequest();
      request.setFuelFormType(3);

      assertThrows(BizException.class, () -> vehicleFuelFormSettingService.addFuelFormSetting(loginVo, request));
    }

    @Test
    void shouldThrowExceptionWhenOilGradeIsInvalid() {
      VehicleFuelFormAddRequest request = new VehicleFuelFormAddRequest();
      request.setFuelFormType(1);
      request.setOilGrade("98");

      assertThrows(BizException.class, () -> vehicleFuelFormSettingService.addFuelFormSetting(loginVo, request));
    }

    @Test
    void shouldThrowExceptionWhenInsertFails() {
      VehicleFuelFormAddRequest request = createValidRequest();
      when(vehicleFuelFormSettingMapper.selectByExample(any())).thenReturn(Collections.emptyList());
      when(vehicleFuelFormSettingMapper.insertSelective(any())).thenReturn(0);

      assertThrows(BizException.class, () -> vehicleFuelFormSettingService.addFuelFormSetting(loginVo, request));
    }

    @Test
    void shouldThrowExceptionWhenUpdateFails() {
      VehicleFuelFormAddRequest request = createValidRequest();
      request.setId(1L);
      when(vehicleFuelFormSettingMapper.selectByPrimaryKey(1L)).thenReturn(new VehicleFuelFormSetting());
      when(vehicleFuelFormSettingMapper.updateByPrimaryKeySelective(any())).thenReturn(0);

      assertThrows(BizException.class, () -> vehicleFuelFormSettingService.addFuelFormSetting(loginVo, request));
    }

    @Test
    void shouldSuccessfullyAddNewSetting() {
      VehicleFuelFormAddRequest request = createValidRequest();
      when(vehicleFuelFormSettingMapper.selectByExample(any())).thenReturn(Collections.emptyList());
      when(vehicleFuelFormSettingMapper.insertSelective(any())).thenReturn(1);

      Long id = vehicleFuelFormSettingService.addFuelFormSetting(loginVo, request);

      assertNotNull(id);
      ArgumentCaptor<VehicleFuelFormSetting> captor = ArgumentCaptor.forClass(VehicleFuelFormSetting.class);
      verify(vehicleFuelFormSettingMapper).insertSelective(captor.capture());
      VehicleFuelFormSetting saved = captor.getValue();
      assertEquals(loginVo.getMerchantId(), saved.getMerchantId());
      assertEquals(request.getFuelFormType().byteValue(), saved.getFuelFormType());
      assertEquals(request.getOilGrade(), saved.getOilGrade());
      assertEquals(request.getSettingPrice().longValue(), saved.getSettingPrice());
      assertEquals(request.getServicePrice().longValue(), saved.getServicePrice());
    }

    @Test
    void shouldSuccessfullyUpdateSetting() {
      VehicleFuelFormAddRequest request = createValidRequest();
      request.setId(1L);
      when(vehicleFuelFormSettingMapper.selectByPrimaryKey(1L)).thenReturn(new VehicleFuelFormSetting());
      when(vehicleFuelFormSettingMapper.updateByPrimaryKeySelective(any())).thenReturn(1);

      Long id = vehicleFuelFormSettingService.addFuelFormSetting(loginVo, request);

      assertEquals(request.getId(), id);
    }
  }

  @Nested
  class DeleteFuelFormSettingTests {
    @Test
    void shouldReturnFalseWhenSettingNotFound() {
      when(vehicleFuelFormSettingMapper.selectByPrimaryKey(1L)).thenReturn(null);

      Boolean result = vehicleFuelFormSettingService.deleteFuelFormSetting(loginVo, 1L);

      assertFalse(result);
    }

    @Test
    void shouldSuccessfullyDeleteSetting() {
      VehicleFuelFormSetting setting = createSetting(1L, (byte) 1, "92", 700L, 100L, Arrays.asList(1L));
      when(vehicleFuelFormSettingMapper.selectByPrimaryKey(1L)).thenReturn(setting);
      when(vehicleFuelFormSettingMapper.updateByPrimaryKey(any())).thenReturn(1);

      Boolean result = vehicleFuelFormSettingService.deleteFuelFormSetting(loginVo, 1L);

      assertTrue(result);
      ArgumentCaptor<VehicleFuelFormSetting> captor = ArgumentCaptor.forClass(VehicleFuelFormSetting.class);
      verify(vehicleFuelFormSettingMapper).updateByPrimaryKey(captor.capture());
      assertEquals(YesOrNoEnum.YES.getValue(), captor.getValue().getDeleted());
    }
  }

  @Nested
  class FuelFormSettingTests {
    @Test
    void shouldReturnNullWhenNoSettingsFound() {
      when(vehicleFuelFormSettingMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      List<VehicleFuelFormSettingVO> result = vehicleFuelFormSettingService.fuelFormSetting(1L, 1L);

      assertNull(result);
    }

    @Test
    void shouldReturnSettingsForCityOrDefault() {
      VehicleFuelFormSetting setting1 = createSetting(2L, (byte) 1, "92", 700L, 100L, Arrays.asList(1L));
      VehicleFuelFormSetting setting2 = createSetting(1L, (byte) 1, "95", 800L, 100L, Arrays.asList(0L));

      when(vehicleFuelFormSettingMapper.selectByExample(any()))
          .thenReturn(Arrays.asList(setting1, setting2));

      List<VehicleFuelFormSettingVO> result = vehicleFuelFormSettingService.fuelFormSetting(1L, 1L);

      assertNotNull(result);
      assertEquals(2, result.size());
      assertEquals(Long.valueOf(1L), result.get(0).getId());
      assertEquals(Long.valueOf(2L), result.get(1).getId());
    }

    @Test
    void shouldSortByIdWhenSameType() {
      VehicleFuelFormSetting setting1 = createSetting(1L, (byte) 1, "92", 700L, 100L, Arrays.asList(1L));
      VehicleFuelFormSetting setting2 = createSetting(2L, (byte) 1, "95", 800L, 100L, Arrays.asList(1L));

      when(vehicleFuelFormSettingMapper.selectByExample(any()))
          .thenReturn(Arrays.asList(setting1, setting2));

      List<VehicleFuelFormSettingVO> result = vehicleFuelFormSettingService.fuelFormSetting(1L, 1L);

      assertNotNull(result);
      assertEquals(2, result.size());
      assertEquals(Long.valueOf(1L), result.get(0).getId());
      assertEquals(Long.valueOf(2L), result.get(1).getId());
    }
  }

  @Nested
  class ValidationTests {
    @Test
    void shouldThrowExceptionWhenSettingPriceIsNull() {
      VehicleFuelFormAddRequest request = createValidRequest();
      request.setSettingPrice(null);

      assertThrows(BizException.class, () -> vehicleFuelFormSettingService.addFuelFormSetting(loginVo, request));
    }

    @Test
    void shouldThrowExceptionWhenServicePriceIsNull() {
      VehicleFuelFormAddRequest request = createValidRequest();
      request.setServicePrice(null);

      assertThrows(BizException.class, () -> vehicleFuelFormSettingService.addFuelFormSetting(loginVo, request));
    }

    @Test
    void shouldThrowExceptionWhenCitySettingIsEmpty() {
      VehicleFuelFormAddRequest request = createValidRequest();
      request.setCitySetting(Collections.emptyList());

      assertThrows(BizException.class, () -> vehicleFuelFormSettingService.addFuelFormSetting(loginVo, request));
    }
  }

  @Nested
  class ConflictCheckTests {
    @Test
    void shouldThrowExceptionWhenElectricCityConflict() {
      VehicleFuelFormAddRequest request = createValidRequest();
      request.setFuelFormType(2); // 电动车
      request.setOilGrade("");
      request.setCitySetting(Arrays.asList(0L));

      VehicleFuelFormSetting existingSetting = createSetting(2L, (byte) 2, "", 800L, 100L, Arrays.asList(0L));
      when(vehicleFuelFormSettingMapper.selectByExample(any()))
          .thenReturn(Collections.singletonList(existingSetting));

      BizException exception = assertThrows(BizException.class,
          () -> vehicleFuelFormSettingService.addFuelFormSetting(loginVo, request));
      assertEquals("电价/城市配置冲突", exception.getMessage());
    }

    @Test
    void shouldThrowExceptionWhenOilCityConflict() {
      VehicleFuelFormAddRequest request = createValidRequest();
      request.setCitySetting(Arrays.asList(1L));

      VehicleFuelFormSetting existingSetting = createSetting(2L, (byte) 1, "92", 800L, 100L, Arrays.asList(1L));
      when(vehicleFuelFormSettingMapper.selectByExample(any()))
          .thenReturn(Collections.singletonList(existingSetting));

      BizException exception = assertThrows(BizException.class,
          () -> vehicleFuelFormSettingService.addFuelFormSetting(loginVo, request));
      assertEquals("油号城市配置冲突", exception.getMessage());
    }

    @Test
    void shouldNotThrowExceptionWhenDifferentOilGrade() {
      VehicleFuelFormAddRequest request = createValidRequest();
      request.setOilGrade("95");
      request.setCitySetting(Arrays.asList(1L));

      VehicleFuelFormSetting existingSetting = createSetting(2L, (byte) 1, "92", 800L, 100L, Arrays.asList(1L));
      when(vehicleFuelFormSettingMapper.selectByExample(any()))
          .thenReturn(Collections.singletonList(existingSetting));
      when(vehicleFuelFormSettingMapper.insertSelective(any())).thenReturn(1);

      assertDoesNotThrow(() -> vehicleFuelFormSettingService.addFuelFormSetting(loginVo, request));
    }
  }

  @Nested
  class CityNameHandlingTests {
    @Test
    void shouldHandleEmptyAreaServiceResponse() {
      VehicleFuelFormSetting setting = createSetting(1L, (byte) 1, "92", 700L, 100L, Arrays.asList(1L));
      when(vehicleFuelFormSettingMapper.selectByPrimaryKey(1L)).thenReturn(setting);
      when(areaService.findByIds(any())).thenReturn(ResultUtil.successResult(Collections.emptyList()));

      VehicleFuelFormSettingVO result = vehicleFuelFormSettingService.detail(loginVo, 1L);

      assertNotNull(result);
      assertTrue(result.getCityName().isEmpty());
    }

    @Test
    void shouldHandleFailedAreaServiceResponse() {
      VehicleFuelFormSetting setting = createSetting(1L, (byte) 1, "92", 700L, 100L, Arrays.asList(1L));
      when(vehicleFuelFormSettingMapper.selectByPrimaryKey(1L)).thenReturn(setting);
      when(areaService.findByIds(any())).thenReturn(ResultUtil.failResult("Failed to get area info"));

      VehicleFuelFormSettingVO result = vehicleFuelFormSettingService.detail(loginVo, 1L);

      assertNotNull(result);
      assertTrue(result.getCityName().isEmpty());
    }
  }

  private VehicleFuelFormSetting createSetting(Long id, Byte fuelFormType, String oilGrade,
      Long settingPrice, Long servicePrice, List<Long> citySetting) {
    VehicleFuelFormSetting setting = new VehicleFuelFormSetting();
    setting.setId(id);
    setting.setMerchantId(1L);
    setting.setFuelFormType(fuelFormType);
    setting.setOilGrade(oilGrade);
    setting.setSettingPrice(settingPrice);
    setting.setServicePrice(servicePrice);
    setting.setCitySetting(JSON.toJSONString(citySetting));
    setting.setDeleted(YesOrNoEnum.NO.getValue());
    return setting;
  }

  private VehicleFuelFormAddRequest createValidRequest() {
    VehicleFuelFormAddRequest request = new VehicleFuelFormAddRequest();
    request.setFuelFormType(1);
    request.setOilGrade("92");
    request.setSettingPrice(700);
    request.setServicePrice(100);
    request.setCitySetting(Arrays.asList(1L));
    return request;
  }

  private AreaVo createAreaVo(Long id, String name) {
    AreaVo areaVo = new AreaVo();
    areaVo.setId(id);
    areaVo.setName(name);
    return areaVo;
  }
}