package com.ql.rent.provider.vehicle;

import com.ql.rent.dao.vehicle.VehicleSubSeryMapper;
import com.ql.rent.entity.vehicle.VehicleSubSery;
import com.ql.rent.entity.vehicle.VehicleSubSeryExample;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.param.vehicle.VehicleSubSeryParam;
import com.ql.rent.service.vehicle.IVehicleModelService;
import com.ql.rent.service.vehicle.IVehicleSeryService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.common.BaseSubVehicleSeryVO;
import com.ql.rent.vo.common.VehicleSeryVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class VehicleSubSeryServiceImplTest {

  @Mock
  private VehicleSubSeryMapper vehicleSubSeryMapper;

  @Mock
  private IVehicleModelService vehicleModelService;

  @Mock
  private IVehicleSeryService vehicleSeryService;

  @Mock
  private VehicleComponent vehicleComponent;

  @InjectMocks
  private VehicleSubSeryServiceImpl vehicleSubSeryService;

  private LoginVo loginVo;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    loginVo = new LoginVo();
    loginVo.setUserId(1L);
    loginVo.setMerchantId(1L);
  }

  @Nested
  class SaveVehicleSubSeryTests {
    @Test
    void shouldReturnFailWhenParamIsNull() {
      Result<Integer> result = vehicleSubSeryService.saveIVehicleSubSery(null);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenRequiredFieldsAreNull() {
      VehicleSubSeryParam param = new VehicleSubSeryParam();

      Result<Integer> result = vehicleSubSeryService.saveIVehicleSubSery(param);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenNameExists() {
      VehicleSubSeryParam param = createValidParam();
      when(vehicleSubSeryMapper.countByExample(any())).thenReturn(1L);

      Result<Integer> result = vehicleSubSeryService.saveIVehicleSubSery(param);

      assertFalse(result.isSuccess());
      assertEquals("数据重复", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenSeryNotFound() {
      VehicleSubSeryParam param = createValidParam();
      when(vehicleSubSeryMapper.countByExample(any())).thenReturn(0L);
      when(vehicleSeryService.getVehicleSeryById(any())).thenReturn(ResultUtil.failResult("未查询到对应的车系数据"));

      Result<Integer> result = vehicleSubSeryService.saveIVehicleSubSery(param);

      assertFalse(result.isSuccess());
      assertEquals("未查询到对应的车系数据，请刷新页面", result.getMessage());
    }

    @Test
    void shouldSaveSuccessfully() {
      VehicleSubSeryParam param = createValidParam();
      VehicleSeryVO seryVO = new VehicleSeryVO();
      seryVO.setBrandId(1L);

      when(vehicleSubSeryMapper.countByExample(any())).thenReturn(0L);
      when(vehicleSeryService.getVehicleSeryById(any())).thenReturn(ResultUtil.successResult(seryVO));
      when(vehicleSubSeryMapper.insertSelective(any())).thenReturn(1);

      Result<Integer> result = vehicleSubSeryService.saveIVehicleSubSery(param);

      assertTrue(result.isSuccess());
      assertEquals(Integer.valueOf(1), result.getModel());

      ArgumentCaptor<VehicleSubSery> captor = ArgumentCaptor.forClass(VehicleSubSery.class);
      verify(vehicleSubSeryMapper).insertSelective(captor.capture());
      VehicleSubSery saved = captor.getValue();
      assertEquals(param.getName(), saved.getName());
      assertEquals(param.getVehicleSeryId(), saved.getSeryId());
      assertEquals(YesOrNoEnum.NO.getValue(), saved.getPreset());
      assertEquals(YesOrNoEnum.NO.getValue(), saved.getDeleted());
      assertEquals(BigDecimal.ZERO, saved.getPrice());
    }
  }

  @Nested
  class DeleteByIdTests {
    @Test
    void shouldReturnFailWhenIdIsNull() {
      Result<Integer> result = vehicleSubSeryService.deleteById(null, loginVo);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenLoginVoIsNull() {
      Result<Integer> result = vehicleSubSeryService.deleteById(1L, null);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenSubSeryNotFound() {
      when(vehicleSubSeryMapper.selectByPrimaryKey(1L)).thenReturn(null);

      Result<Integer> result = vehicleSubSeryService.deleteById(1L, loginVo);

      assertFalse(result.isSuccess());
      assertEquals("数据不存在或者已被删除", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenSubSeryIsPreset() {
      VehicleSubSery subSery = createVehicleSubSery(1L, "测试子车系", true);
      when(vehicleSubSeryMapper.selectByPrimaryKey(1L)).thenReturn(subSery);

      Result<Integer> result = vehicleSubSeryService.deleteById(1L, loginVo);

      assertFalse(result.isSuccess());
      assertEquals("无法删除预设数据", result.getMessage());
    }

    @Test
    void shouldThrowExceptionWhenDeleteModelFails() {
      VehicleSubSery subSery = createVehicleSubSery(1L, "测试子车系", false);
      when(vehicleSubSeryMapper.selectByPrimaryKey(1L)).thenReturn(subSery);
      when(vehicleModelService.deleteVehicleModel(any(), any(), any(), any(), any()))
          .thenReturn(ResultUtil.failResult("删除失败"));

      Result<Integer> result = vehicleSubSeryService.deleteById(1L, loginVo);

      assertFalse(result.isSuccess());
      assertEquals("删除失败", result.getMessage());
    }

    @Test
    void shouldDeleteSuccessfully() {
      VehicleSubSery subSery = createVehicleSubSery(1L, "测试子车系", false);
      when(vehicleSubSeryMapper.selectByPrimaryKey(1L)).thenReturn(subSery);
      when(vehicleModelService.deleteVehicleModel(any(), any(), any(), any(), any()))
          .thenReturn(ResultUtil.successResult(1));
      when(vehicleSubSeryMapper.updateByPrimaryKeySelective(any())).thenReturn(1);

      Result<Integer> result = vehicleSubSeryService.deleteById(1L, loginVo);

      assertTrue(result.isSuccess());
      assertEquals(Integer.valueOf(1), result.getModel());

      ArgumentCaptor<VehicleSubSery> captor = ArgumentCaptor.forClass(VehicleSubSery.class);
      verify(vehicleSubSeryMapper).updateByPrimaryKeySelective(captor.capture());
      VehicleSubSery deleted = captor.getValue();
      assertEquals(YesOrNoEnum.YES.getValue(), deleted.getDeleted());
      assertEquals(loginVo.getUserId(), deleted.getOpUserId());
    }
  }

  @Nested
  class ListAllBySeryIdTests {
    @Test
    void shouldReturnFailWhenSeryIdIsNull() {
      Result<List<BaseSubVehicleSeryVO>> result = vehicleSubSeryService.listAllBySeryId(null, 1L);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnEmptyListWhenNoRecordsFound() {
      when(vehicleSubSeryMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      Result<List<BaseSubVehicleSeryVO>> result = vehicleSubSeryService.listAllBySeryId(1L, 1L);

      assertTrue(result.isSuccess());
      assertTrue(result.getModel().isEmpty());
    }

    @Test
    void shouldReturnSubSeryList() {
      List<VehicleSubSery> subSeryList = Arrays.asList(
          createVehicleSubSery(1L, "子车系1", false),
          createVehicleSubSery(2L, "子车系2", true));
      when(vehicleSubSeryMapper.selectByExample(any())).thenReturn(subSeryList);

      Result<List<BaseSubVehicleSeryVO>> result = vehicleSubSeryService.listAllBySeryId(1L, 1L);

      assertTrue(result.isSuccess());
      assertEquals(2, result.getModel().size());
      assertEquals("子车系1", result.getModel().get(0).getName());
      assertEquals("子车系2", result.getModel().get(1).getName());
    }
  }

  @Nested
  class GetByIdTests {
    @Test
    void shouldReturnFailWhenIdIsNull() {
      Result<BaseSubVehicleSeryVO> result = vehicleSubSeryService.getById(null);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenSubSeryNotFound() {
      when(vehicleSubSeryMapper.selectByPrimaryKey(1L)).thenReturn(null);

      Result<BaseSubVehicleSeryVO> result = vehicleSubSeryService.getById(1L);

      assertFalse(result.isSuccess());
      assertEquals("子车系数据不存在 或已被删除", result.getMessage());
    }

    @Test
    void shouldReturnSubSeryWithDetails() {
      VehicleSubSery subSery = createVehicleSubSery(1L, "测试子车系", false);
      subSery.setModelGroupId(1L);
      subSery.setModelGroupName("测试车型组");
      subSery.setSunroof("有天窗");

      when(vehicleSubSeryMapper.selectByPrimaryKey(1L)).thenReturn(subSery);
      when(vehicleComponent.transferDriveType(any())).thenReturn(2);

      Result<BaseSubVehicleSeryVO> result = vehicleSubSeryService.getById(1L);

      assertTrue(result.isSuccess());
      assertEquals("测试子车系", result.getModel().getName());
      assertEquals(Long.valueOf(1L), result.getModel().getVehicleModelGroupId());
      assertEquals("测试车型组", result.getModel().getVehicleModelGroupName());
      assertEquals(YesOrNoEnum.YES.getValue(), result.getModel().getHasSunroof());
      assertEquals((byte) 2, result.getModel().getDriveTypeNum());
    }
  }

  private VehicleSubSeryParam createValidParam() {
    VehicleSubSeryParam param = new VehicleSubSeryParam();
    param.setName("测试子车系");
    param.setVehicleSeryId(1L);
    param.setMerchantId(1L);
    param.setOpUserId(1L);
    return param;
  }

  private VehicleSubSery createVehicleSubSery(Long id, String name, boolean preset) {
    VehicleSubSery subSery = new VehicleSubSery();
    subSery.setId(id);
    subSery.setName(name);
    subSery.setSeryId(1L);
    subSery.setBrandId(1L);
    subSery.setMerchantId(1L);
    subSery.setPreset(preset ? YesOrNoEnum.YES.getValue() : YesOrNoEnum.NO.getValue());
    subSery.setDeleted(YesOrNoEnum.NO.getValue());
    subSery.setCreateTime(System.currentTimeMillis());
    subSery.setOpTime(System.currentTimeMillis());
    subSery.setOpUserId(1L);
    subSery.setLuxury((byte) 2);
    subSery.setPrice(BigDecimal.ZERO);
    return subSery;
  }
}