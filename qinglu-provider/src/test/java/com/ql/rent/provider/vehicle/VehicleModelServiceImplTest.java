package com.ql.rent.provider.vehicle;

import com.ql.rent.api.aggregate.remote.api.APIClient;
import com.ql.rent.api.aggregate.remote.collector.api.CollectorApiClient;
import com.ql.rent.api.aggregate.remote.collector.vo.dto.Vehicle;
import com.ql.rent.component.PlatformBiz;
import com.ql.rent.dao.vehicle.*;
import com.ql.rent.dao.vehicle.ex.VehicleModelMapperEx;
import com.ql.rent.entity.vehicle.*;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.vehicle.VehicleModelStatusEnum;
import com.ql.rent.param.vehicle.VehicleMediaParam;
import com.ql.rent.param.vehicle.VehicleModelInnerQuery;
import com.ql.rent.param.vehicle.VehicleModelParam.VehicleModelCreateParam;
import com.ql.rent.param.vehicle.VehicleModelParam.VehicleModelModifyParam;
import com.ql.rent.param.vehicle.VehicleModelQueryParam;
import com.ql.rent.service.price.AccessoryInventoryService;
import com.ql.rent.service.price.IPullDataTaskService;
import com.ql.rent.service.price.WkVehicleService;
import com.ql.rent.service.store.IStoreInfoService;
import com.ql.rent.service.vehicle.*;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.common.VehicleModelGroupVO;
import com.ql.rent.vo.vehicle.*;
import com.ql.rent.vo.vehicle.VehicleBindVO;
import com.ql.rent.vo.vehicle.VehicleModelListVO;
import com.ql.rent.vo.vehicle.VehicleModelVO;
import com.ql.rent.vo.vehicle.VehicleModelTagVO;
import com.ql.rent.vo.vehicle.VehicleTagVO;
import com.ql.rent.vo.store.StoreSimpleVo;
import com.ql.rent.vo.vehicle.InquiryVehicleModel;
import com.ql.rent.param.vehicle.ThirdModelSearchParam;
import com.ql.rent.vo.vehicle.ThirdVehicleModelSelectVO;
import com.ql.rent.vo.vehicle.ThirdVehicleModelVO;
import com.ql.rent.api.aggregate.remote.dto.ChannelVehicleModelMatchDTO;
import com.ql.rent.api.aggregate.remote.vo.response.ChannelVehicleModelMatchResp;
import com.ql.dto.ApiResultResp;
import com.ql.rent.entity.vehicle.VehicleSubSery;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
@DisplayName("VehicleModelServiceImpl Tests")
class VehicleModelServiceImplTest {

    @Mock
    private VehicleModelMapper vehicleModelMapper;

    @Mock
    private VehicleModelMapperEx vehicleModelMapperEx;

    @Mock
    private IVehicleTagService vehicleTagService;

    @Mock
    private VehicleSeryMapper vehicleSeryMapper;

    @Mock
    private IVehicleBindService vehicleBindService;

    @Mock
    private IVehicleMediaService vehicleMediaService;

    @Mock
    private VehicleSubSeryMapper vehicleSubSeryMapper;

    @Mock
    private IVehicleModelGroupService vehicleModelGroupService;

    @Mock
    private VehicleBrandMapper vehicleBrandMapper;

    @Mock
    private APIClient apiClient;

    @Mock
    private CollectorApiClient collectorApiClient;

    @Mock
    private IVehicleSeryService vehicleSeryService;

    @Mock
    private VehicleComponent vehicleComponent;

    @Mock
    private VehicleInfoMapper vehicleInfoMapper;

    @Mock
    private PlatformBiz platformBiz;

    @Mock
    private IStoreInfoService storeInfoService;

    @Mock
    private AccessoryInventoryService accessoryInventoryService;

    @Mock
    private IPullDataTaskService pullDataTaskService;

    @Mock
    private WkVehicleService wkVehicleService;

    @InjectMocks
    private VehicleModelServiceImpl vehicleModelService;

    private LoginVo validLoginVo;
    private VehicleModelCreateParam validCreateParam;
    private VehicleModel validVehicleModel;
    private final Long VALID_MERCHANT_ID = 1L;
    private final Long VALID_USER_ID = 100L;
    private final Long VALID_MODEL_ID = 1000L;
    private final Long VALID_SUBSERY_ID = 2000L;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // Setup valid LoginVo
        validLoginVo = new LoginVo();
        validLoginVo.setMerchantId(VALID_MERCHANT_ID);
        validLoginVo.setUserId(VALID_USER_ID);

        // Setup valid VehicleModelCreateParam
        validCreateParam = new VehicleModelCreateParam();
        validCreateParam.setVehicleSubSeryId(VALID_SUBSERY_ID);
        validCreateParam.setLicenseTypeIdList(Arrays.asList(1L, 2L));
        validCreateParam.setHasSnowTires(YesOrNoEnum.NO.getValue());
        validCreateParam.setSelfServiceReturn(YesOrNoEnum.NO.getValue());

        // Setup valid VehicleModel
        validVehicleModel = new VehicleModel();
        validVehicleModel.setId(VALID_MODEL_ID);
        validVehicleModel.setVehicleSubSeryId(VALID_SUBSERY_ID);
        validVehicleModel.setMerchantId(VALID_MERCHANT_ID);
        validVehicleModel.setStatus(VehicleModelStatusEnum.ON_SELLING.getStatus());
    }

    @Nested
    @DisplayName("saveVehicleModel Tests")
    class SaveVehicleModelTests {

        @Test
        @DisplayName("Should successfully save vehicle model")
        void shouldSuccessfullySaveVehicleModel() {
            // Arrange
            List<VehicleMediaParam> mediaParams = Collections.singletonList(new VehicleMediaParam());
            validCreateParam.setVehicleMediaList(mediaParams);

            List<VehicleBindVO> bindVOs = Collections.singletonList(new VehicleBindVO());
            validCreateParam.setVehicleBindList(bindVOs);

            when(vehicleModelMapper.batchInsert(anyList())).thenReturn(2);
            when(vehicleMediaService.batchSaveVehicleMedia(anyList(), anyList(), anyLong()))
                    .thenReturn(ResultUtil.successResult(1));
            when(vehicleBindService.updateOtherVehicleBind(anyList(), anyLong(), any()))
                    .thenReturn(ResultUtil.successResult(1));

            // Act
            Result<Integer> result = vehicleModelService.saveVehicleModel(validCreateParam, validLoginVo);

            // Assert
            assertTrue(result.isSuccess());
            assertEquals(1, result.getModel());
            verify(vehicleModelMapper).batchInsert(anyList());
            verify(vehicleMediaService).batchSaveVehicleMedia(anyList(), anyList(), anyLong());
            verify(vehicleBindService).updateOtherVehicleBind(anyList(), anyLong(), any());
        }

        @Test
        @DisplayName("Should fail when create param is null")
        void shouldFailWhenCreateParamIsNull() {
            // Act
            Result<Integer> result = vehicleModelService.saveVehicleModel(null, validLoginVo);

            // Assert
            assertFalse(result.isSuccess());
            verify(vehicleModelMapper, never()).batchInsert(anyList());
        }

        @Test
        @DisplayName("Should fail when license type list is empty")
        void shouldFailWhenLicenseTypeListIsEmpty() {
            // Arrange
            validCreateParam.setLicenseTypeIdList(Collections.emptyList());

            // Act
            Result<Integer> result = vehicleModelService.saveVehicleModel(validCreateParam, validLoginVo);

            // Assert
            assertFalse(result.isSuccess());
            verify(vehicleModelMapper, never()).batchInsert(anyList());
        }

        @Test
        @DisplayName("Should handle media service failure")
        void shouldHandleMediaServiceFailure() {
            // Arrange
            List<VehicleMediaParam> mediaParams = Collections.singletonList(new VehicleMediaParam());
            validCreateParam.setVehicleMediaList(mediaParams);

            when(vehicleModelMapper.batchInsert(anyList())).thenReturn(1);
            when(vehicleMediaService.batchSaveVehicleMedia(anyList(), anyList(), anyLong()))
                    .thenReturn(ResultUtil.failResult("Failed to save media"));

            // Act
            Result<Integer> result = vehicleModelService.saveVehicleModel(validCreateParam, validLoginVo);

            // Assert
            assertFalse(result.isSuccess());
            assertEquals("Failed to save media", result.getMessage());
            verify(vehicleModelMapper).batchInsert(anyList());
            verify(vehicleMediaService).batchSaveVehicleMedia(anyList(), anyList(), anyLong());
        }

        @Test
        @DisplayName("Should handle bind service failure")
        void shouldHandleBindServiceFailure() {
            // Arrange
            List<VehicleBindVO> bindVOs = Collections.singletonList(new VehicleBindVO());
            validCreateParam.setVehicleBindList(bindVOs);

            when(vehicleModelMapper.batchInsert(anyList())).thenReturn(1);
            when(vehicleBindService.updateOtherVehicleBind(anyList(), anyLong(), any()))
                    .thenReturn(ResultUtil.failResult("Failed to update bindings"));

            // Act
            Result<Integer> result = vehicleModelService.saveVehicleModel(validCreateParam, validLoginVo);

            // Assert
            assertFalse(result.isSuccess());
            assertEquals("Failed to update bindings", result.getMessage());
            verify(vehicleModelMapper).batchInsert(anyList());
            verify(vehicleBindService).updateOtherVehicleBind(anyList(), anyLong(), any());
        }
    }

    @Nested
    @DisplayName("obtainVehicleModeInfo Tests")
    class ObtainVehicleModeInfoTests {

        @Test
        @DisplayName("Should successfully obtain vehicle model info")
        void shouldSuccessfullyObtainVehicleModelInfo() {
            // Arrange
            List<Long> modelIds = Collections.singletonList(VALID_MODEL_ID);
            when(vehicleModelMapper.selectByExample(any())).thenReturn(Collections.singletonList(validVehicleModel));

            // Act
            Result<Map<Long, BaseVehicleModelVO>> result = vehicleModelService.obtainVehicleModeInfo(VALID_MERCHANT_ID,
                    modelIds);

            // Assert
            assertTrue(result.isSuccess());
            assertNotNull(result.getModel());
            assertEquals(1, result.getModel().size());
            assertTrue(result.getModel().containsKey(VALID_MODEL_ID));
        }

        @Test
        @DisplayName("Should fail when merchant id is null")
        void shouldFailWhenMerchantIdIsNull() {
            // Act
            Result<Map<Long, BaseVehicleModelVO>> result = vehicleModelService.obtainVehicleModeInfo(null,
                    Collections.singletonList(VALID_MODEL_ID));

            // Assert
            assertFalse(result.isSuccess());
            verify(vehicleModelMapper, never()).selectByExample(any());
        }

        @Test
        @DisplayName("Should fail when model ids are empty")
        void shouldFailWhenModelIdsAreEmpty() {
            // Act
            Result<Map<Long, BaseVehicleModelVO>> result = vehicleModelService.obtainVehicleModeInfo(VALID_MERCHANT_ID,
                    Collections.emptyList());

            // Assert
            assertFalse(result.isSuccess());
            verify(vehicleModelMapper, never()).selectByExample(any());
        }
    }

    @Nested
    @DisplayName("deleteVehicleModel Tests")
    class DeleteVehicleModelTests {

        @Test
        @DisplayName("Should successfully delete vehicle model")
        void shouldSuccessfullyDeleteVehicleModel() {
            // Arrange
            when(vehicleModelMapper.selectByPrimaryKey(VALID_MODEL_ID)).thenReturn(validVehicleModel);
            when(vehicleModelMapper.updateByPrimaryKeySelective(any())).thenReturn(1);

            // Act
            Result<Integer> result = vehicleModelService.deleteVehicleModel(VALID_MODEL_ID, validLoginVo);

            // Assert
            assertTrue(result.isSuccess());
            assertEquals(1, result.getModel());
            verify(vehicleModelMapper).updateByPrimaryKeySelective(any());
        }

        @Test
        @DisplayName("Should fail when model does not exist")
        void shouldFailWhenModelDoesNotExist() {
            // Arrange
            when(vehicleModelMapper.selectByPrimaryKey(VALID_MODEL_ID)).thenReturn(null);

            // Act
            Result<Integer> result = vehicleModelService.deleteVehicleModel(VALID_MODEL_ID, validLoginVo);

            // Assert
            assertFalse(result.isSuccess());
            verify(vehicleModelMapper, never()).updateByPrimaryKeySelective(any());
        }

        @Test
        @DisplayName("Should fail when model is already deleted")
        void shouldFailWhenModelIsAlreadyDeleted() {
            // Arrange
            VehicleModel deletedModel = new VehicleModel();
            when(vehicleModelMapper.selectByPrimaryKey(VALID_MODEL_ID)).thenReturn(deletedModel);

            // Act
            Result<Integer> result = vehicleModelService.deleteVehicleModel(VALID_MODEL_ID, validLoginVo);

            // Assert
            assertFalse(result.isSuccess());
            verify(vehicleModelMapper, never()).updateByPrimaryKeySelective(any());
        }
    }

    @Nested
    @DisplayName("updateVehicleModel Tests")
    class UpdateVehicleModelTests {

        @Test
        @DisplayName("Should successfully update vehicle model")
        void shouldSuccessfullyUpdateVehicleModel() {
            // Arrange
            VehicleModelModifyParam modifyParam = new VehicleModelModifyParam();
            modifyParam.setId(VALID_MODEL_ID);
            modifyParam.setHasSnowTires(YesOrNoEnum.YES.getValue());
            modifyParam.setSelfServiceReturn(YesOrNoEnum.YES.getValue());
            modifyParam.setFastChargeTime((byte) 30);

            when(vehicleModelMapper.selectByPrimaryKey(VALID_MODEL_ID)).thenReturn(validVehicleModel);
            when(vehicleModelMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
            when(vehicleMediaService.updateVehicleMediaByModelId(anyLong(), anyList(), anyLong()))
                    .thenReturn(ResultUtil.successResult(1));
            when(vehicleBindService.updateOtherVehicleBind(anyList(), anyLong(), any()))
                    .thenReturn(ResultUtil.successResult(1));

            // Act
            Result<Integer> result = vehicleModelService.updateVehicleModel(modifyParam, validLoginVo);

            // Assert
            assertTrue(result.isSuccess());
            assertEquals(1, result.getModel());
            verify(vehicleModelMapper).updateByPrimaryKeySelective(any());
            verify(vehicleMediaService).updateVehicleMediaByModelId(anyLong(), anyList(), anyLong());
            verify(vehicleBindService).updateOtherVehicleBind(anyList(), anyLong(), any());
        }

        @Test
        @DisplayName("Should fail when model id is null")
        void shouldFailWhenModelIdIsNull() {
            // Arrange
            VehicleModelModifyParam modifyParam = new VehicleModelModifyParam();

            // Act
            Result<Integer> result = vehicleModelService.updateVehicleModel(modifyParam, validLoginVo);

            // Assert
            assertFalse(result.isSuccess());
            verify(vehicleModelMapper, never()).updateByPrimaryKeySelective(any());
        }

        @Test
        @DisplayName("Should fail when model does not exist")
        void shouldFailWhenModelDoesNotExist() {
            // Arrange
            VehicleModelModifyParam modifyParam = new VehicleModelModifyParam();
            modifyParam.setId(VALID_MODEL_ID);
            modifyParam.setHasSnowTires(YesOrNoEnum.YES.getValue());
            modifyParam.setSelfServiceReturn(YesOrNoEnum.YES.getValue());

            when(vehicleModelMapper.selectByPrimaryKey(VALID_MODEL_ID)).thenReturn(null);

            // Act
            Result<Integer> result = vehicleModelService.updateVehicleModel(modifyParam, validLoginVo);

            // Assert
            assertFalse(result.isSuccess());
            verify(vehicleModelMapper, never()).updateByPrimaryKeySelective(any());
        }

        @Test
        @DisplayName("Should handle media service update failure")
        void shouldHandleMediaServiceUpdateFailure() {
            // Arrange
            VehicleModelModifyParam modifyParam = new VehicleModelModifyParam();
            modifyParam.setId(VALID_MODEL_ID);
            modifyParam.setVehicleMediaList(Collections.singletonList(new VehicleMediaParam()));

            when(vehicleModelMapper.selectByPrimaryKey(VALID_MODEL_ID)).thenReturn(validVehicleModel);
            when(vehicleModelMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
            when(vehicleMediaService.updateVehicleMediaByModelId(anyLong(), anyList(), anyLong()))
                    .thenReturn(ResultUtil.failResult("Failed to update media"));

            // Act
            Result<Integer> result = vehicleModelService.updateVehicleModel(modifyParam, validLoginVo);

            // Assert
            assertFalse(result.isSuccess());
            assertEquals("Failed to update media", result.getMessage());
            verify(vehicleModelMapper).updateByPrimaryKeySelective(any());
            verify(vehicleMediaService).updateVehicleMediaByModelId(anyLong(), anyList(), anyLong());
        }

        @Test
        @DisplayName("Should handle bind service update failure")
        void shouldHandleBindServiceUpdateFailure() {
            // Arrange
            VehicleModelModifyParam modifyParam = new VehicleModelModifyParam();
            modifyParam.setId(VALID_MODEL_ID);
            modifyParam.setVehicleBindList(Collections.singletonList(new VehicleBindVO()));

            when(vehicleModelMapper.selectByPrimaryKey(VALID_MODEL_ID)).thenReturn(validVehicleModel);
            when(vehicleModelMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
            when(vehicleBindService.updateOtherVehicleBind(anyList(), anyLong(), any()))
                    .thenReturn(ResultUtil.failResult("Failed to update bindings"));

            // Act
            Result<Integer> result = vehicleModelService.updateVehicleModel(modifyParam, validLoginVo);

            // Assert
            assertFalse(result.isSuccess());
            assertEquals("Failed to update bindings", result.getMessage());
            verify(vehicleModelMapper).updateByPrimaryKeySelective(any());
            verify(vehicleBindService).updateOtherVehicleBind(anyList(), anyLong(), any());
        }

        @Test
        @DisplayName("Should handle invalid status update")
        void shouldHandleInvalidStatusUpdate() {
            // Arrange
            VehicleModelModifyParam modifyParam = new VehicleModelModifyParam();
            modifyParam.setId(VALID_MODEL_ID);

            when(vehicleModelMapper.selectByPrimaryKey(VALID_MODEL_ID)).thenReturn(validVehicleModel);

            // Act
            Result<Integer> result = vehicleModelService.updateVehicleModel(modifyParam, validLoginVo);

            // Assert
            assertFalse(result.isSuccess());
            verify(vehicleModelMapper, never()).updateByPrimaryKeySelective(any());
        }
    }

    @Nested
    @DisplayName("getBaseVoById Tests")
    class GetBaseVoByIdTests {

        @Test
        @DisplayName("Should successfully get vehicle model base info")
        void shouldSuccessfullyGetVehicleModelBaseInfo() {
            // Arrange
            when(vehicleModelMapper.selectByPrimaryKey(VALID_MODEL_ID)).thenReturn(validVehicleModel);
            when(vehicleModelGroupService.listVehicleModelGroup(anyList()))
                    .thenReturn(ResultUtil.successResult(Collections.singletonList(new VehicleModelGroupVO())));

            // Act
            Result<BaseVehicleModelVO> result = vehicleModelService.getBaseVoById(VALID_MODEL_ID);

            // Assert
            assertTrue(result.isSuccess());
            assertNotNull(result.getModel());
            assertEquals(VALID_MODEL_ID, result.getModel().getId());
        }

        @Test
        @DisplayName("Should return null when model id is null")
        void shouldReturnNullWhenModelIdIsNull() {
            // Act
            Result<BaseVehicleModelVO> result = vehicleModelService.getBaseVoById(null);

            // Assert
            assertFalse(result.isSuccess());
            verify(vehicleModelMapper, never()).selectByPrimaryKey(any());
        }

        @Test
        @DisplayName("Should return null when model does not exist")
        void shouldReturnNullWhenModelDoesNotExist() {
            // Arrange
            when(vehicleModelMapper.selectByPrimaryKey(VALID_MODEL_ID)).thenReturn(null);

            // Act
            Result<BaseVehicleModelVO> result = vehicleModelService.getBaseVoById(VALID_MODEL_ID);

            // Assert
            assertTrue(result.isSuccess());
            assertNull(result.getModel());
        }
    }

    @Nested
    @DisplayName("listVehicleModel Tests")
    class ListVehicleModelTests {

        @Test
        @DisplayName("Should successfully list vehicle models")
        void shouldSuccessfullyListVehicleModels() {
            // Arrange
            VehicleModelQueryParam queryParam = new VehicleModelQueryParam();
            queryParam.setIdList(Arrays.asList(VALID_MODEL_ID));
            queryParam.setMerchantId(VALID_MERCHANT_ID);

            when(vehicleModelMapper.selectByExample(any())).thenReturn(Collections.singletonList(validVehicleModel));
            when(vehicleModelGroupService.listVehicleModelGroup(anyList()))
                    .thenReturn(ResultUtil.successResult(Collections.singletonList(new VehicleModelGroupVO())));

            // Act
            Result<PageListVo<VehicleModelListVO>> result = vehicleModelService.listVehicleModel(queryParam,
                    validLoginVo.getLoginName());

            // Assert
            assertTrue(result.isSuccess());
            assertNotNull(result.getModel());
            assertEquals(1, result.getModel().getList().size());
            assertEquals(VALID_MODEL_ID, result.getModel().getList().get(0).getId());
        }

        @Test
        @DisplayName("Should return empty list when query param is null")
        void shouldReturnEmptyListWhenQueryParamIsNull() {
            // Act
            Result<PageListVo<VehicleModelListVO>> result = vehicleModelService.listVehicleModel(null,
                    validLoginVo.getLoginName());

            // Assert
            assertFalse(result.isSuccess());
            verify(vehicleModelMapper, never()).selectByExample(any());
        }

        @Test
        @DisplayName("Should return empty list when login name is null")
        void shouldReturnEmptyListWhenLoginNameIsNull() {
            // Arrange
            VehicleModelQueryParam queryParam = new VehicleModelQueryParam();
            queryParam.setMerchantId(VALID_MERCHANT_ID);

            // Act
            Result<PageListVo<VehicleModelListVO>> result = vehicleModelService.listVehicleModel(queryParam, null);

            // Assert
            assertFalse(result.isSuccess());
            verify(vehicleModelMapper, never()).selectByExample(any());
        }
    }

    @Nested
    @DisplayName("listBaseVehicleModel Tests")
    class ListBaseVehicleModelTests {

        @Test
        @DisplayName("Should successfully list base vehicle models")
        void shouldSuccessfullyListBaseVehicleModels() {
            // Arrange
            VehicleModelInnerQuery innerQuery = new VehicleModelInnerQuery();
            innerQuery.setMerchantId(VALID_MERCHANT_ID);
            innerQuery.setIdList(Arrays.asList(VALID_MODEL_ID));

            when(vehicleModelMapper.selectByExample(any())).thenReturn(Collections.singletonList(validVehicleModel));
            when(vehicleModelGroupService.listVehicleModelGroup(anyList()))
                    .thenReturn(ResultUtil.successResult(Collections.singletonList(new VehicleModelGroupVO())));
            when(vehicleSeryMapper.selectByExample(any())).thenReturn(Collections.singletonList(new VehicleSery()));
            when(vehicleSubSeryMapper.selectByExample(any()))
                    .thenReturn(Collections.singletonList(new VehicleSubSery()));

            // Act
            Result<List<BaseVehicleModelVO>> result = vehicleModelService.listBaseVehicleModel(innerQuery);

            // Assert
            assertTrue(result.isSuccess());
            assertNotNull(result.getModel());
            assertEquals(1, result.getModel().size());
            assertEquals(VALID_MODEL_ID, result.getModel().get(0).getId());
        }

        @Test
        @DisplayName("Should fail when inner query is null")
        void shouldFailWhenInnerQueryIsNull() {
            // Act
            Result<List<BaseVehicleModelVO>> result = vehicleModelService.listBaseVehicleModel(null);

            // Assert
            assertFalse(result.isSuccess());
            verify(vehicleModelMapper, never()).selectByExample(any());
        }

        @Test
        @DisplayName("Should fail when all params in inner query are null")
        void shouldFailWhenAllParamsInInnerQueryAreNull() {
            // Arrange
            VehicleModelInnerQuery innerQuery = new VehicleModelInnerQuery();

            // Act
            Result<List<BaseVehicleModelVO>> result = vehicleModelService.listBaseVehicleModel(innerQuery);

            // Assert
            assertFalse(result.isSuccess());
            verify(vehicleModelMapper, never()).selectByExample(any());
        }

        @Test
        @DisplayName("Should return empty list when no models found")
        void shouldReturnEmptyListWhenNoModelsFound() {
            // Arrange
            VehicleModelInnerQuery innerQuery = new VehicleModelInnerQuery();
            innerQuery.setMerchantId(VALID_MERCHANT_ID);
            innerQuery.setIdList(Arrays.asList(VALID_MODEL_ID));

            when(vehicleModelMapper.selectByExample(any())).thenReturn(Collections.emptyList());

            // Act
            Result<List<BaseVehicleModelVO>> result = vehicleModelService.listBaseVehicleModel(innerQuery);

            // Assert
            assertTrue(result.isSuccess());
            assertTrue(result.getModel().isEmpty());
        }

        @Test
        @DisplayName("Should handle vehicle group service failure")
        void shouldHandleVehicleGroupServiceFailure() {
            // Arrange
            VehicleModelInnerQuery innerQuery = new VehicleModelInnerQuery();
            innerQuery.setMerchantId(VALID_MERCHANT_ID);
            innerQuery.setIdList(Arrays.asList(VALID_MODEL_ID));

            when(vehicleModelMapper.selectByExample(any())).thenReturn(Collections.singletonList(validVehicleModel));
            when(vehicleModelGroupService.listVehicleModelGroup(anyList()))
                    .thenReturn(ResultUtil.failResult("Failed to get vehicle groups"));

            // Act
            Result<List<BaseVehicleModelVO>> result = vehicleModelService.listBaseVehicleModel(innerQuery);

            // Assert
            assertTrue(result.isSuccess());
            assertNotNull(result.getModel());
            // assertTrue(result.getModel().get(0).getVehicleModelGroupVOList().isEmpty());
        }

        @Test
        @DisplayName("Should handle when sery data is missing")
        void shouldHandleWhenSeryDataIsMissing() {
            // Arrange
            VehicleModelInnerQuery innerQuery = new VehicleModelInnerQuery();
            innerQuery.setMerchantId(VALID_MERCHANT_ID);
            innerQuery.setIdList(Arrays.asList(VALID_MODEL_ID));

            when(vehicleModelMapper.selectByExample(any())).thenReturn(Collections.singletonList(validVehicleModel));
            when(vehicleModelGroupService.listVehicleModelGroup(anyList()))
                    .thenReturn(ResultUtil.successResult(Collections.singletonList(new VehicleModelGroupVO())));
            when(vehicleSeryMapper.selectByExample(any())).thenReturn(Collections.emptyList());
            when(vehicleSubSeryMapper.selectByExample(any())).thenReturn(Collections.emptyList());

            // Act
            Result<List<BaseVehicleModelVO>> result = vehicleModelService.listBaseVehicleModel(innerQuery);

            // Assert
            assertTrue(result.isSuccess());
            assertNotNull(result.getModel());
            assertEquals(1, result.getModel().size());
            assertNull(result.getModel().get(0).getVehicleSeryName());
            assertNull(result.getModel().get(0).getVehicleSubSeryName());
        }
    }

    @Nested
    @DisplayName("getVehicleModelBaseById Tests")
    class GetVehicleModelBaseByIdTests {

        @Test
        @DisplayName("Should successfully get vehicle model base by id")
        void shouldSuccessfullyGetVehicleModelBaseById() {
            // Arrange
            when(vehicleModelMapper.selectByPrimaryKey(VALID_MODEL_ID)).thenReturn(validVehicleModel);

            // Act
            Result<VehicleModelVO> result = vehicleModelService.getVehicleModelBaseById(VALID_MODEL_ID);

            // Assert
            assertTrue(result.isSuccess());
            assertNotNull(result.getModel());
            assertEquals(VALID_MODEL_ID, result.getModel().getId());
            assertEquals(validVehicleModel.getVehicleSeryId(), result.getModel().getVehicleSeryId());
            assertEquals(validVehicleModel.getVehicleSeryName(), result.getModel().getVehicleSeryName());
        }

        @Test
        @DisplayName("Should fail when model id is null")
        void shouldFailWhenModelIdIsNull() {
            // Act
            Result<VehicleModelVO> result = vehicleModelService.getVehicleModelBaseById(null);

            // Assert
            assertFalse(result.isSuccess());
            verify(vehicleModelMapper, never()).selectByPrimaryKey(any());
        }

        @Test
        @DisplayName("Should fail when model does not exist")
        void shouldFailWhenModelDoesNotExist() {
            // Arrange
            when(vehicleModelMapper.selectByPrimaryKey(VALID_MODEL_ID)).thenReturn(null);

            // Act
            Result<VehicleModelVO> result = vehicleModelService.getVehicleModelBaseById(VALID_MODEL_ID);

            // Assert
            assertFalse(result.isSuccess());
            assertEquals("车型数据不存在或已被删除，请刷新页面", result.getMessage());
        }

        @Test
        @DisplayName("Should fail when model is deleted")
        void shouldFailWhenModelIsDeleted() {
            // Arrange
            VehicleModel deletedModel = new VehicleModel();
            deletedModel.setId(VALID_MODEL_ID);
            deletedModel.setStatus(VehicleModelStatusEnum.DELETED.getStatus());

            when(vehicleModelMapper.selectByPrimaryKey(VALID_MODEL_ID)).thenReturn(deletedModel);

            // Act
            Result<VehicleModelVO> result = vehicleModelService.getVehicleModelBaseById(VALID_MODEL_ID);

            // Assert
            assertFalse(result.isSuccess());
            assertEquals("车型数据不存在或已被删除，请刷新页面", result.getMessage());
        }
    }

    @Nested
    @DisplayName("vehicleTagPage Tests")
    class VehicleTagPageTests {

        @Test
        @DisplayName("Should successfully get vehicle tag page")
        void shouldSuccessfullyGetVehicleTagPage() {
            // Arrange
            VehicleModelQueryParam queryParam = new VehicleModelQueryParam();
            queryParam.setMerchantId(VALID_MERCHANT_ID);

            VehicleModelListVO modelListVO = new VehicleModelListVO();
            modelListVO.setId(VALID_MODEL_ID);
            List<VehicleModelListVO> modelList = Collections.singletonList(modelListVO);
            PageListVo<VehicleModelListVO> pageListVo = PageListVo.buildPageList(1L, modelList);
            Result<PageListVo<VehicleModelListVO>> pageListVoResult = ResultUtil.successResult(pageListVo);

            when(vehicleModelService.listVehicleModel(any(), anyString())).thenReturn(pageListVoResult);
            when(vehicleTagService.listByVehicleModelId(anyList()))
                    .thenReturn(ResultUtil.successResult(Collections.singletonList(new VehicleTagVO())));
            when(storeInfoService.storeSampleForSelect(anyLong(), any()))
                    .thenReturn(ResultUtil.successResult(Collections.singletonList(new StoreSimpleVo())));

            // Act
            Result<PageListVo<VehicleModelTagVO>> result = vehicleModelService.vehicleTagPage(queryParam, validLoginVo);

            // Assert
            assertTrue(result.isSuccess());
            assertNotNull(result.getModel());
            assertEquals(1L, result.getModel().getCount());
            assertEquals(1, result.getModel().getList().size());
        }

        @Test
        @DisplayName("Should return empty page when no models found")
        void shouldReturnEmptyPageWhenNoModelsFound() {
            // Arrange
            VehicleModelQueryParam queryParam = new VehicleModelQueryParam();
            queryParam.setMerchantId(VALID_MERCHANT_ID);

            Result<PageListVo<VehicleModelListVO>> emptyPageResult = ResultUtil
                    .successResult(PageListVo.buildEmptyPage());

            when(vehicleModelService.listVehicleModel(any(), anyString())).thenReturn(emptyPageResult);

            // Act
            Result<PageListVo<VehicleModelTagVO>> result = vehicleModelService.vehicleTagPage(queryParam, validLoginVo);

            // Assert
            assertTrue(result.isSuccess());
            assertEquals(0L, result.getModel().getCount());
            assertTrue(result.getModel().getList().isEmpty());
        }

        @Test
        @DisplayName("Should fail when query param is null")
        void shouldFailWhenQueryParamIsNull() {
            // Act
            Result<PageListVo<VehicleModelTagVO>> result = vehicleModelService.vehicleTagPage(null, validLoginVo);

            // Assert
            assertFalse(result.isSuccess());
            verify(vehicleModelMapper, never()).selectByExample(any());
        }

        @Test
        @DisplayName("Should fail when login vo is null")
        void shouldFailWhenLoginVoIsNull() {
            // Arrange
            VehicleModelQueryParam queryParam = new VehicleModelQueryParam();
            queryParam.setMerchantId(VALID_MERCHANT_ID);

            // Act
            Result<PageListVo<VehicleModelTagVO>> result = vehicleModelService.vehicleTagPage(queryParam, null);

            // Assert
            assertFalse(result.isSuccess());
            verify(vehicleModelMapper, never()).selectByExample(any());
        }

        @Test
        @DisplayName("Should handle store service timeout")
        void shouldHandleStoreServiceTimeout() {
            // Arrange
            VehicleModelQueryParam queryParam = new VehicleModelQueryParam();
            queryParam.setMerchantId(VALID_MERCHANT_ID);

            VehicleModelListVO modelListVO = new VehicleModelListVO();
            modelListVO.setId(VALID_MODEL_ID);
            List<VehicleModelListVO> modelList = Collections.singletonList(modelListVO);
            PageListVo<VehicleModelListVO> pageListVo = PageListVo.buildPageList(1L, modelList);
            Result<PageListVo<VehicleModelListVO>> pageListVoResult = ResultUtil.successResult(pageListVo);

            when(vehicleModelService.listVehicleModel(any(), anyString())).thenReturn(pageListVoResult);
            when(vehicleTagService.listByVehicleModelId(anyList()))
                    .thenReturn(ResultUtil.successResult(Collections.singletonList(new VehicleTagVO())));
            when(storeInfoService.storeSampleForSelect(anyLong(), any()))
                    .thenThrow(new RuntimeException("Service timeout"));

            // Act
            Result<PageListVo<VehicleModelTagVO>> result = vehicleModelService.vehicleTagPage(queryParam, validLoginVo);

            // Assert
            assertTrue(result.isSuccess());
            assertNotNull(result.getModel());
            assertEquals(1, result.getModel().getList().size());
            // assertTrue(result.getModel().getList().get(0).getStoreList().isEmpty());
        }

        @Test
        @DisplayName("Should handle invalid page parameters")
        void shouldHandleInvalidPageParameters() {
            // Arrange
            VehicleModelQueryParam queryParam = new VehicleModelQueryParam();
            queryParam.setMerchantId(VALID_MERCHANT_ID);
            queryParam.setPageSize(-1);
            queryParam.setPageSize(-1);

            // Act
            Result<PageListVo<VehicleModelTagVO>> result = vehicleModelService.vehicleTagPage(queryParam, validLoginVo);

            // Assert
            assertFalse(result.isSuccess());
            assertEquals("分页参数错误", result.getMessage());
        }

        @Test
        @DisplayName("Should handle multiple vehicle tags")
        void shouldHandleMultipleVehicleTags() {
            // Arrange
            VehicleModelQueryParam queryParam = new VehicleModelQueryParam();
            queryParam.setMerchantId(VALID_MERCHANT_ID);

            VehicleModelListVO modelListVO = new VehicleModelListVO();
            modelListVO.setId(VALID_MODEL_ID);
            List<VehicleModelListVO> modelList = Collections.singletonList(modelListVO);
            PageListVo<VehicleModelListVO> pageListVo = PageListVo.buildPageList(1L, modelList);
            Result<PageListVo<VehicleModelListVO>> pageListVoResult = ResultUtil.successResult(pageListVo);

            List<VehicleTagVO> vehicleTags = Arrays.asList(
                    new VehicleTagVO(), new VehicleTagVO(), new VehicleTagVO());

            when(vehicleModelService.listVehicleModel(any(), anyString())).thenReturn(pageListVoResult);
            when(vehicleTagService.listByVehicleModelId(anyList()))
                    .thenReturn(ResultUtil.successResult(vehicleTags));
            when(storeInfoService.storeSampleForSelect(anyLong(), any()))
                    .thenReturn(ResultUtil.successResult(Collections.singletonList(new StoreSimpleVo())));

            // Act
            Result<PageListVo<VehicleModelTagVO>> result = vehicleModelService.vehicleTagPage(queryParam, validLoginVo);

            // Assert
            assertTrue(result.isSuccess());
            assertNotNull(result.getModel());
            assertEquals(1, result.getModel().getList().size());
            // assertEquals(3,
            // result.getModel().getList().get(0).getVehicleTagVOList().size());
        }
    }

    @Nested
    @DisplayName("listInquiryVehicleModels Tests")
    class ListInquiryVehicleModelsTests {

        @Test
        @DisplayName("Should successfully list inquiry vehicle models")
        void shouldSuccessfullyListInquiryVehicleModels() {
            // Arrange
            when(vehicleModelMapper.selectByExample(any())).thenReturn(Collections.singletonList(validVehicleModel));

            // Act
            List<InquiryVehicleModel> result = vehicleModelService.listInquiryVehicleModels(VALID_MERCHANT_ID);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(VALID_MODEL_ID, result.get(0).getId());
            assertEquals(validVehicleModel.getLicenseType(), result.get(0).getLicenseType());
            verify(vehicleModelMapper).selectByExample(any());
        }

        @Test
        @DisplayName("Should return empty list when no models found")
        void shouldReturnEmptyListWhenNoModelsFound() {
            // Arrange
            when(vehicleModelMapper.selectByExample(any())).thenReturn(Collections.emptyList());

            // Act
            List<InquiryVehicleModel> result = vehicleModelService.listInquiryVehicleModels(VALID_MERCHANT_ID);

            // Assert
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verify(vehicleModelMapper).selectByExample(any());
        }

        @Test
        @DisplayName("Should return empty list when merchant id is null")
        void shouldReturnEmptyListWhenMerchantIdIsNull() {
            // Act
            List<InquiryVehicleModel> result = vehicleModelService.listInquiryVehicleModels(null);

            // Assert
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verify(vehicleModelMapper, never()).selectByExample(any());
        }

        @Test
        @DisplayName("Should handle database error")
        void shouldHandleDatabaseError() {
            // Arrange
            when(vehicleModelMapper.selectByExample(any()))
                    .thenThrow(new RuntimeException("Database connection error"));

            // Act
            List<InquiryVehicleModel> result = vehicleModelService.listInquiryVehicleModels(VALID_MERCHANT_ID);

            // Assert
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verify(vehicleModelMapper).selectByExample(any());
        }

        @Test
        @DisplayName("Should filter out invalid models")
        void shouldFilterOutInvalidModels() {
            // Arrange
            VehicleModel invalidModel = new VehicleModel();
            invalidModel.setId(null);
            invalidModel.setStatus(VehicleModelStatusEnum.DELETED.getStatus());

            when(vehicleModelMapper.selectByExample(any()))
                    .thenReturn(Arrays.asList(validVehicleModel, invalidModel));

            // Act
            List<InquiryVehicleModel> result = vehicleModelService.listInquiryVehicleModels(VALID_MERCHANT_ID);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(VALID_MODEL_ID, result.get(0).getId());
            verify(vehicleModelMapper).selectByExample(any());
        }

        @Test
        @DisplayName("Should handle invalid merchant id format")
        void shouldHandleInvalidMerchantIdFormat() {
            // Arrange
            Long invalidMerchantId = -1L;

            // Act
            List<InquiryVehicleModel> result = vehicleModelService.listInquiryVehicleModels(invalidMerchantId);

            // Assert
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verify(vehicleModelMapper, never()).selectByExample(any());
        }
    }

    @Nested
    @DisplayName("GetModelPriceIds Tests")
    class GetModelPriceIdsTests {

        @Test
        @DisplayName("Should successfully get model price ids")
        void shouldSuccessfullyGetModelPriceIds() {
            // Arrange
            List<Long> vehicleModelIds = Arrays.asList(100L, 200L);

            VehicleModel model1 = new VehicleModel();
            model1.setId(100L);
            model1.setVehicleSeryId(1L);
            model1.setVehicleSubSeryId(10L);

            VehicleModel model2 = new VehicleModel();
            model2.setId(200L);
            model2.setVehicleSeryId(2L);
            model2.setVehicleSubSeryId(20L);

            when(vehicleModelMapper.selectByExample(any()))
                    .thenReturn(Arrays.asList(model1, model2));

            VehicleSubSery subSery1 = new VehicleSubSery();
            subSery1.setId(10L);
            subSery1.setName("测试子车系1");
            subSery1.setSunroof("全景天窗");
            subSery1.setPrice(new BigDecimal("200000"));
            subSery1.setLuxury((byte) 1);
            subSery1.setCtripVehicleModelId(1001L);

            VehicleSubSery subSery2 = new VehicleSubSery();
            subSery2.setId(20L);
            subSery2.setName("测试子车系2");
            subSery2.setSunroof("无天窗");
            subSery2.setPrice(new BigDecimal("150000"));
            subSery2.setLuxury((byte) 0);
            subSery2.setCtripVehicleModelId(1002L);

            when(vehicleSubSeryMapper.selectByExample(any()))
                    .thenReturn(Arrays.asList(subSery1, subSery2));

            // Act
            List<BaseVehicleModelVO> result = vehicleModelService.getModelPriceIds(vehicleModelIds);

            // Assert
            assertNotNull(result);
            assertEquals(2, result.size());

            BaseVehicleModelVO vo1 = result.get(0);
            assertEquals(100L, vo1.getId());
            assertEquals(10L, vo1.getVehicleSubSeryId());
            assertEquals("测试子车系1", vo1.getVehicleSubSeryName());
            assertEquals("全景天窗", vo1.getSunroof());
            assertEquals(new BigDecimal("200000"), vo1.getPrice());
            assertEquals((byte) 1, vo1.getLuxury());
            assertEquals(1001L, vo1.getCtripVehicleModelId());

            BaseVehicleModelVO vo2 = result.get(1);
            assertEquals(200L, vo2.getId());
            assertEquals(20L, vo2.getVehicleSubSeryId());
            assertEquals("测试子车系2", vo2.getVehicleSubSeryName());
            assertEquals("无天窗", vo2.getSunroof());
            assertEquals(new BigDecimal("150000"), vo2.getPrice());
            assertEquals((byte) 0, vo2.getLuxury());
            assertEquals(1002L, vo2.getCtripVehicleModelId());
        }

        @Test
        @DisplayName("Should return empty list when model ids is empty")
        void shouldReturnEmptyListWhenModelIdsIsEmpty() {
            // Act
            List<BaseVehicleModelVO> result = vehicleModelService.getModelPriceIds(Collections.emptyList());

            // Assert
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verify(vehicleModelMapper, never()).selectByExample(any());
        }

        @Test
        @DisplayName("Should return empty list when no models found")
        void shouldReturnEmptyListWhenNoModelsFound() {
            // Arrange
            List<Long> vehicleModelIds = Arrays.asList(100L, 200L);
            when(vehicleModelMapper.selectByExample(any())).thenReturn(Collections.emptyList());

            // Act
            List<BaseVehicleModelVO> result = vehicleModelService.getModelPriceIds(vehicleModelIds);

            // Assert
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verify(vehicleSubSeryMapper, never()).selectByExample(any());
        }

        @Test
        @DisplayName("Should handle when sub sery not found")
        void shouldHandleWhenSubSeryNotFound() {
            // Arrange
            List<Long> vehicleModelIds = Collections.singletonList(100L);

            VehicleModel model = new VehicleModel();
            model.setId(100L);
            model.setVehicleSubSeryId(10L);

            when(vehicleModelMapper.selectByExample(any()))
                    .thenReturn(Collections.singletonList(model));

            when(vehicleSubSeryMapper.selectByExample(any()))
                    .thenReturn(Collections.emptyList());

            // Act
            List<BaseVehicleModelVO> result = vehicleModelService.getModelPriceIds(vehicleModelIds);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());

            BaseVehicleModelVO vo = result.get(0);
            assertEquals(100L, vo.getId());
            assertEquals(10L, vo.getVehicleSubSeryId());
            assertNull(vo.getVehicleSubSeryName());
            assertNull(vo.getSunroof());
            assertNull(vo.getPrice());
            assertNull(vo.getLuxury());
            assertNull(vo.getCtripVehicleModelId());
        }
    }


    @Test
    public void testSearchThirdPlatformVehicle() {
    }

    @Nested
    @DisplayName("QueryV2VehicleModelListByStoreIds Tests")
    class QueryV2VehicleModelListByStoreIdsTests {

        @Test
        @DisplayName("Should successfully query v2 vehicle models by store ids")
        void shouldSuccessfullyQueryV2VehicleModelsByStoreIds() {
            // Arrange
            VehicleModelQueryParam queryParam = new VehicleModelQueryParam();
            queryParam.setStoreIdList(Arrays.asList(1L, 2L));
            queryParam.setVehicleModelGroup(300L);

            List<Long> vehicleModelIds = Arrays.asList(100L, 200L);
            when(vehicleModelMapperEx.selectVehicleModelIdStoreIds(queryParam.getStoreId(),
                    queryParam.getStoreIdList(), VALID_MERCHANT_ID))
                    .thenReturn(vehicleModelIds);

            VehicleModel model1 = new VehicleModel();
            model1.setId(100L);
            model1.setVehicleSeryName("测试车系1");
            model1.setVehicleModelGroupId(300L);
            model1.setStatus(VehicleModelStatusEnum.ON_SELLING.getStatus());

            VehicleModel model2 = new VehicleModel();
            model2.setId(200L);
            model2.setVehicleSeryName("测试车系2");
            model2.setVehicleModelGroupId(300L);
            model2.setStatus(VehicleModelStatusEnum.ON_SELLING.getStatus());

            when(vehicleModelMapper.selectByExample(any()))
                    .thenReturn(Arrays.asList(model1, model2));

            // Act
            Result<List<BaseVehicleModelVO>> result = vehicleModelService
                    .queryV2VehicleModelListByStoreIds(queryParam, VALID_MERCHANT_ID);

            // Assert
            assertTrue(result.isSuccess());
            assertNotNull(result.getModel());
            assertEquals(2, result.getModel().size());

            List<BaseVehicleModelVO> models = result.getModel();
            assertEquals(100L, models.get(0).getId());
            assertEquals("测试车系1", models.get(0).getVehicleSeryName());
            assertEquals(200L, models.get(1).getId());
            assertEquals("测试车系2", models.get(1).getVehicleSeryName());
        }

        @Test
        @DisplayName("Should fail when query param is null")
        void shouldFailWhenQueryParamIsNull() {
            // Act
            Result<List<BaseVehicleModelVO>> result = vehicleModelService
                    .queryV2VehicleModelListByStoreIds(null, VALID_MERCHANT_ID);

            // Assert
            assertFalse(result.isSuccess());
            verify(vehicleModelMapperEx, never()).selectVehicleModelIdStoreIds(any(), any(), any());
        }

        @Test
        @DisplayName("Should fail when merchant id is null")
        void shouldFailWhenMerchantIdIsNull() {
            // Arrange
            VehicleModelQueryParam queryParam = new VehicleModelQueryParam();
            queryParam.setStoreIdList(Arrays.asList(1L, 2L));

            // Act
            Result<List<BaseVehicleModelVO>> result = vehicleModelService
                    .queryV2VehicleModelListByStoreIds(queryParam, null);

            // Assert
            assertFalse(result.isSuccess());
            verify(vehicleModelMapperEx, never()).selectVehicleModelIdStoreIds(any(), any(), any());
        }

        @Test
        @DisplayName("Should handle single store id")
        void shouldHandleSingleStoreId() {
            // Arrange
            VehicleModelQueryParam queryParam = new VehicleModelQueryParam();
            queryParam.setStoreId(1L);

            List<Long> vehicleModelIds = Collections.singletonList(100L);
            when(vehicleModelMapperEx.selectVehicleModelIdStoreIds(eq(1L), any(), eq(VALID_MERCHANT_ID)))
                    .thenReturn(vehicleModelIds);

            VehicleModel model = new VehicleModel();
            model.setId(100L);
            model.setVehicleSeryName("测试车系");
            model.setStatus(VehicleModelStatusEnum.ON_SELLING.getStatus());

            when(vehicleModelMapper.selectByExample(any()))
                    .thenReturn(Collections.singletonList(model));

            // Act
            Result<List<BaseVehicleModelVO>> result = vehicleModelService
                    .queryV2VehicleModelListByStoreIds(queryParam, VALID_MERCHANT_ID);

            // Assert
            assertTrue(result.isSuccess());
            assertEquals(1, result.getModel().size());
            assertEquals(100L, result.getModel().get(0).getId());
            assertEquals("测试车系", result.getModel().get(0).getVehicleSeryName());
        }

        @Test
        @DisplayName("Should filter by vehicle model group")
        void shouldFilterByVehicleModelGroup() {
            // Arrange
            VehicleModelQueryParam queryParam = new VehicleModelQueryParam();
            queryParam.setStoreIdList(Arrays.asList(1L, 2L));
            queryParam.setVehicleModelGroup(300L);

            List<Long> vehicleModelIds = Arrays.asList(100L, 200L, 300L);
            when(vehicleModelMapperEx.selectVehicleModelIdStoreIds(any(), any(), eq(VALID_MERCHANT_ID)))
                    .thenReturn(vehicleModelIds);

            VehicleModel model1 = new VehicleModel();
            model1.setId(100L);
            model1.setVehicleModelGroupId(300L);
            model1.setStatus(VehicleModelStatusEnum.ON_SELLING.getStatus());

            VehicleModel model2 = new VehicleModel();
            model2.setId(200L);
            model2.setVehicleModelGroupId(400L);
            model2.setStatus(VehicleModelStatusEnum.ON_SELLING.getStatus());

            when(vehicleModelMapper.selectByExample(any()))
                    .thenReturn(Arrays.asList(model1, model2));

            // Act
            Result<List<BaseVehicleModelVO>> result = vehicleModelService
                    .queryV2VehicleModelListByStoreIds(queryParam, VALID_MERCHANT_ID);

            // Assert
            assertTrue(result.isSuccess());
            assertEquals(1, result.getModel().size());
            assertEquals(100L, result.getModel().get(0).getId());
        }
    }
}
