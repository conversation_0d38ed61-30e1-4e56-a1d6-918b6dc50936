package com.ql.rent.provider.common;

import com.ql.rent.AbstractTest;
import com.ql.rent.common.IMerchantAuthDetailsService;
import com.ql.rent.vo.common.MerchantAuthDetailsVO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

public class MerchantAuthDetailsServiceTest extends AbstractTest {
    @Resource
    private IMerchantAuthDetailsService merchantAuthDetailsService;

    @Test
    public void configEncryptKey() {
        merchantAuthDetailsService.configEncryptKey(3L, (byte) 21);
    }
    @Test
    public void updateMerchantAuthDetails() {
        MerchantAuthDetailsVO param = new MerchantAuthDetailsVO();
        merchantAuthDetailsService.updateMerchantAuthDetails(param);
    }
}
