package com.ql.rent.provider.trade;

import com.alibaba.fastjson.JSON;
import com.ql.rent.AbstractTest;
import com.ql.rent.component.ThirdInvoiceManager;
import com.ql.rent.component.invoice.res.BlankInvoiceResponse;
import com.ql.rent.component.invoice.res.CreateInvoiceSyncResponse;
import com.ql.rent.component.invoice.res.RedInvoiceResponse;
import com.ql.rent.enums.trade.InvoiceTypeEnum;
import com.ql.rent.param.trade.InvoiceCallbackParam;
import com.ql.rent.schedule.task.UpdateInvoiceRedStatusTask;
import com.ql.rent.service.trade.IInvoiceService;
import com.ql.rent.share.result.Result;
import com.ql.rent.util.MailUtil;
import com.ql.rent.vo.trade.InvoiceBaseEnumVo;
import org.junit.Test;
import org.quartz.JobExecutionException;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2023-01-16  15:00
 * Description
 */
public class InvoiceServiceTest extends AbstractTest {

    @Resource
    private ThirdInvoiceManager thirdInvoiceManager;

    @Resource
    IInvoiceService invoiceService;

    @Resource
    UpdateInvoiceRedStatusTask task;

    @Resource
    private MailUtil mailUtil;

    @Test
    public void getInvoiceBaseEnum() {
        Result<InvoiceBaseEnumVo> baseEnum = invoiceService.getInvoiceBaseEnum();
        System.out.println(baseEnum.getModel());
    }

    @Test
    public void testGetToken() {
        String invoiceToken = thirdInvoiceManager.getInvoiceToken();
        System.out.println(invoiceToken);
    }

    @Test
    public void createInvoice() {
        CreateInvoiceSyncResponse response = thirdInvoiceManager.createInvoiceSyncTest();
        System.out.println(response);
    }

    @Test
    public void redInvoiceSync() {
        RedInvoiceResponse invoiceInfo = thirdInvoiceManager.redInvoiceSyncMock();
        System.out.println(invoiceInfo);
    }

    @Test
    public void saveRedInvoiceInfo() {
        String invoiceInfo = thirdInvoiceManager.saveRedInvoiceInfoMock();
        System.out.println(invoiceInfo);
    }

    @Test
    public void blankInvoice() {
        BlankInvoiceResponse invoiceToken = thirdInvoiceManager.blankInvoice(String.valueOf(InvoiceTypeEnum.COMMON_ELECTRONIC_INVOICE.getType()));
        System.out.println(JSON.toJSONString(invoiceToken));
    }


    @Test
    public void response() {
        boolean result = mailUtil.sendMail(Arrays.asList("<EMAIL>"), "电子发票", "电子发票", "https://huisuiyun.oss-cn-shanghai.aliyuncs.com/pro/permanent/default/********/e109a28f-2d97-437d-bbbd-449b4b970134342********/************-********.pdf", "电子发票");

    }

    @Test
    public void execute() {
        try {
            task.execute(null);
        } catch (JobExecutionException e) {
            throw new RuntimeException(e);
        }

    }

    @Test
    public void callback() {
        try {
            String data = "{\"code\":\"200\",\"message\":\"开具成功\",\"data\":{\"applyType\":1,\"dept_code\":\"\",\"purchaserBankAccount\":\"\",\"exten10\":\"\",\"listFlag\":0,\"purchaserPhone\":\"\",\"createUserName\":\"吴昊东\",\"handlerName\":\"\",\"sellerAddress\":\"66+\",\"business_code\":\"\",\"drewDate\":*************,\"payee\":\"\",\"oldInvoiceNo\":\"\",\"logisticsFlag\":null,\"externalDocumentNo\":\"\",\"viewUrl\":\"\",\"invoiceType\":7,\"machineNo\":\"\",\"applyTime\":*************,\"invoiceDrewDate\":\"2023-03-13 21:18:01\",\"invoiceNo\":\"10000000000000001894\",\"business_name\":\"\",\"settlementNo\":\"\",\"machineCode\":\"\",\"elementCode\":\"\",\"taxCode\":\"\",\"invoiceCode\":\"\",\"purchaserName\":\"慧穗数字科技（上海）有限公司\",\"checkCode\":\"\",\"purchaserBankName\":\"\",\"serialNo\":\"820042976385540097\",\"xmlUrl\":\"https://huisuiyun.oss-cn-shanghai.aliyuncs.com/prod/permanent/electronicFile/electronicFile/********/allElectricInvoice_XML.zip\",\"taxRate\":\"0.06\",\"emailStatus\":0,\"sellerTaxNo\":\"*********\",\"purchaserEmail\":\"<EMAIL>\",\"purchaserTaxNo\":\"91310117MA1J30X297\",\"detailList\":[{\"invoiceLineType\":0,\"discountAmount\":0,\"createUserName\":\"吴昊东\",\"categoryName\":\"\",\"extend10\":\"\",\"id\":820042977161261000,\"goodsName\":\"软件服务费\",\"revenueCode\":\"3040203000000000000\",\"unitPrice\":0,\"amount\":-0.01,\"quantity\":0,\"goodsTypeCode\":null,\"taxPreType\":0,\"zeroTax\":0,\"specification\":\"\",\"extend7\":\"\",\"extend6\":\"\",\"taxRate\":0.06,\"extend9\":\"\",\"unit\":\"\",\"extend8\":\"\",\"deduction\":0,\"extend3\":\"\",\"extend2\":\"\",\"extend5\":\"\",\"extend4\":\"\",\"revenueVersion\":null,\"taxPre\":0,\"extend1\":\"\",\"goodsCode\":\"\",\"amountWithTax\":-0.01,\"shortName\":\"\",\"taxAmount\":0}],\"specialFlag\":0,\"taxAmount\":0,\"sellerBankName\":\"56563\",\"VoucherType\":\"\",\"exten4\":\"\",\"exten5\":\"\",\"exten2\":\"\",\"exten3\":\"\",\"matchFlag\":0,\"exten8\":\"\",\"exten9\":\"\",\"handlerCertificateCode\":\"\",\"oldInvoiceCode\":\"\",\"exten6\":\"\",\"exten7\":\"\",\"signStatus\":0,\"sellerName\":\"虚拟通道023\",\"remark\":\"顺丰加急-测试111111\",\"exten1\":\"\",\"purchaserAddress\":\"\",\"pdfUrl\":\"https://huisuiyun.oss-cn-shanghai.aliyuncs.com/prod/permanent/electronicFile/electronicFile/********/allElectricInvoice.pdf\",\"billingType\":1,\"specificElements\":[],\"phoneStatus\":0,\"sellerTel\":\"\",\"redReason\":null,\"redApplicationNo\":\"OELCmA2rxHKulYRK\",\"ctrlSerMachineCode\":null,\"amount\":-0.01,\"ciphertext\":\"\",\"additions\":null,\"handlerCertificateNo\":\"\",\"handlerTaxNo\":\"\",\"applyMsg\":\"\",\"drawer\":\"023测试\",\"dept_name\":\"\",\"purchaserTel\":\"\",\"reviewer\":\"\",\"lockFlag\":0,\"ofdUrl\":\"https://huisuiyun.oss-cn-shanghai.aliyuncs.com/prod/permanent/electronicFile/electronicFile/********/allElectricInvoice.ofd\",\"sellerBankAccount\":\"\",\"amountWithTax\":-0.01,\"invoiceStatus\":10},\"serialNo\":\"820042975899021312\"}";
            InvoiceCallbackParam param = new InvoiceCallbackParam();
            param.setData(data);
            param.setBusinessType("103");

            invoiceService.callback(param);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
}
