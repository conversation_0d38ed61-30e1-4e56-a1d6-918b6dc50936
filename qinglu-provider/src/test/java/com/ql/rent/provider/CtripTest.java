package com.ql.rent.provider;

import com.ql.rent.AbstractTest;
import com.ql.rent.service.price.ICtripPriceService;
import com.ql.rent.service.price.IThirdPriceService;
import com.ql.rent.service.store.IThirdStoreService;
import com.ql.rent.service.vehicle.ICtripVehicleService;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.CtripStoreVO;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @desc
 * @time 2023-08-01 20:23
 */
public class CtripTest extends AbstractTest {

    @Resource
    private IThirdStoreService thirdStoreService;
    @Resource
    private ICtripVehicleService ctripVehicleService;

    @Resource
    private IThirdPriceService priceService;
    @Resource
    private ICtripPriceService ctripPriceService;



    @Test
    public void testFreezeNotify() throws Exception {
        String vendorId = "30105";
        CtripStoreVO storeVo =  thirdStoreService.importStoreFormCtrip(vendorId);
        ctripVehicleService.pullCtripVehicleInfo(storeVo);
    }

    @Test
    public void testgetPushDataAfterUpdate(){
        String str = "{\"vehicleModelId\":1310,\"storeId\":0,\"merchantId\":43}";

        Long merchantId  = 43L;
        Long storeId = 0L; Long vehicleModelId = 1310L;
        try {
            ctripPriceService.getPushDataAfterUpdate(merchantId,storeId,vehicleModelId,2L);
        } catch (Exception e) {
            System.out.println(e);
        }
        System.out.println();
    }
//    @Test
//    public void loadPriceFilterDataFromCtrip() throws Exception {
//        String vendorId = "30105";
//        priceService.loadPriceFilterDataFromCtrip(Long.parseLong(vendorId),41L);
//    }


//    @Test
//    public void loadSkuPriceDataFromCtrip() throws Exception {
//        String vendorId = "30105";
//        priceService.loadSkuPriceDataFromCtrip(Long.parseLong(vendorId),41L);
//    }
}
