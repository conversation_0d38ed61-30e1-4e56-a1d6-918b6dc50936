package com.ql.rent.provider.store;

import com.alibaba.fastjson.JSON;
import com.ql.rent.service.store.ICtripStoreService;
import com.ql.rent.vo.store.StoreHourlyChargeVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.Collections;
import java.util.List;

@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
class CtripStoreServiceImplTest {
    @Resource
    ICtripStoreService ctripStoreService;

//    @Test
//    void pushHourlyCharge() {
//        List<StoreHourlyChargeVo> list = JSON.parseArray("[{\"id\":null,\"storeId\":148,\"channelId\":2,\"scene\":2,\"chargeItem\":1,\"chargeValue\":\"0.35, 0.45, 0.55, 0.65, 0.75, 0.85, 0.95, 1\",\"deleted\":0},{\"id\":null,\"storeId\":148,\"channelId\":2,\"scene\":1,\"chargeItem\":1,\"chargeValue\":\"0, 0.25, 0.4, 0.5, 0.65, 0.75, 0.9, 1\",\"deleted\":0}]"
//                ,StoreHourlyChargeVo.class);
//        ctripStoreService.pushHourlyCharge(list, 43L);
//    }
}