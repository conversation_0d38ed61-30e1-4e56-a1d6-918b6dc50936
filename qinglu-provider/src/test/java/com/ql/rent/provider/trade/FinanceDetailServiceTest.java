package com.ql.rent.provider.trade;

import com.ql.rent.AbstractTest;
import com.ql.rent.param.trade.FinanceQueryParam;
import com.ql.rent.service.trade.IFinanceDetailService;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.trade.FinanceDetailPageVO;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

public class FinanceDetailServiceTest extends AbstractTest {


    @Autowired
    private IFinanceDetailService financeDetailService;

    @Test
    public void test() {
        LoginVo loginVo = new LoginVo();
        loginVo.setUserId(1L);
        FinanceDetailPageVO financeDetailPageVO = new FinanceDetailPageVO();
        financeDetailPageVO.setType((byte)1);
        financeDetailPageVO.setFinanceType(1);
        financeDetailPageVO.setFinanceDetailType(1);
        financeDetailPageVO.setThirdPaySource(4);
        financeDetailPageVO.setTotalAmount(1123L);
        financeDetailPageVO.setStoreId(4665L);
        List<String> attUrls = new ArrayList<>();
        attUrls.add("https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/96f0869d-f1b9-49f7-b08e-c39067fcdc25.png");
        financeDetailPageVO.setAttUrls(attUrls);
        financeDetailService.saveFinanceDetail(financeDetailPageVO,loginVo);
    }

    @Test
    public void listPage() {
        FinanceQueryParam financeQueryParam = new FinanceQueryParam();
        financeQueryParam.setPageIndex(1);
        financeQueryParam.setPageSize(100);
        LoginVo loginVo = new LoginVo();
        loginVo.setMerchantId(61L);
        Result<PageListVo<FinanceDetailPageVO>> result = financeDetailService.listPage(financeQueryParam,loginVo);
        System.out.println(result.isSuccess());
    }
}
