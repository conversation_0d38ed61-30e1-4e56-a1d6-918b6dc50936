package com.ql.rent.provider.vehicle;

import com.ql.rent.dao.vehicle.VehicleColorMapper;
import com.ql.rent.entity.common.VehicleColor;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.vehicle.VehicleColorVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class VehicleColorServiceImplTest {

  @Mock
  private VehicleColorMapper vehicleColorMapper;

  @InjectMocks
  private VehicleColorServiceImpl vehicleColorService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Nested
  class ListAllVehicleColorTests {
    @Test
    void shouldReturnEmptyListWhenNoColorsExist() {
      when(vehicleColorMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      Result<List<VehicleColorVO>> result = vehicleColorService.listAllVehicleColor();

      assertTrue(result.isSuccess());
      assertTrue(result.getModel().isEmpty());
    }

    @Test
    void shouldReturnSingleColor() {
      VehicleColor color = createVehicleColor(1L, "白色");
      when(vehicleColorMapper.selectByExample(any())).thenReturn(Collections.singletonList(color));

      Result<List<VehicleColorVO>> result = vehicleColorService.listAllVehicleColor();

      assertTrue(result.isSuccess());
      assertEquals(1, result.getModel().size());
      VehicleColorVO vo = result.getModel().get(0);
      assertEquals(1L, vo.getId());
      assertEquals("白色", vo.getColorName());
    }

    @Test
    void shouldReturnMultipleColors() {
      List<VehicleColor> colors = Arrays.asList(
          createVehicleColor(1L, "白色"),
          createVehicleColor(2L, "黑色"),
          createVehicleColor(3L, "红色"));
      when(vehicleColorMapper.selectByExample(any())).thenReturn(colors);

      Result<List<VehicleColorVO>> result = vehicleColorService.listAllVehicleColor();

      assertTrue(result.isSuccess());
      assertEquals(3, result.getModel().size());
      assertEquals("白色", result.getModel().get(0).getColorName());
      assertEquals("黑色", result.getModel().get(1).getColorName());
      assertEquals("红色", result.getModel().get(2).getColorName());
    }

    @Test
    void shouldPreserveColorOrder() {
      List<VehicleColor> colors = Arrays.asList(
          createVehicleColor(3L, "红色"),
          createVehicleColor(1L, "白色"),
          createVehicleColor(2L, "黑色"));
      when(vehicleColorMapper.selectByExample(any())).thenReturn(colors);

      Result<List<VehicleColorVO>> result = vehicleColorService.listAllVehicleColor();

      assertTrue(result.isSuccess());
      assertEquals(3, result.getModel().size());
      assertEquals("红色", result.getModel().get(0).getColorName());
      assertEquals("白色", result.getModel().get(1).getColorName());
      assertEquals("黑色", result.getModel().get(2).getColorName());
    }

    @Test
    void shouldHandleNullColorName() {
      VehicleColor color = createVehicleColor(1L, null);
      when(vehicleColorMapper.selectByExample(any())).thenReturn(Collections.singletonList(color));

      Result<List<VehicleColorVO>> result = vehicleColorService.listAllVehicleColor();

      assertTrue(result.isSuccess());
      assertEquals(1, result.getModel().size());
      assertNull(result.getModel().get(0).getColorName());
    }
  }

  private VehicleColor createVehicleColor(Long id, String colorName) {
    VehicleColor color = new VehicleColor();
    color.setId(id);
    color.setColorName(colorName);
    return color;
  }
}