package com.ql.rent.provider.pay;


import com.alibaba.fastjson.JSON;
import com.ql.dto.pay.*;
import com.ql.enums.ThirdPayEnum;
import com.ql.rent.AbstractTest;
import com.ql.rent.param.pay.*;
import com.ql.rent.service.pay.IThirdPayService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.utils.UuidUtil;
import com.ql.rent.vo.bill.ThirdPayVO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.List;

public class ThirdPayServiceImplTest extends AbstractTest {

    @Resource
    private IThirdPayService thirdPayService;

    @Resource
    private ThirdPayHandler aliPayHandler;

    @Test
    public void getPayDetailList() {
        ThirdPayQuery query = new ThirdPayQuery();
        query.setId(771L);
        Result<List<ThirdPayVO>> result = thirdPayService.getPayDetailList(query);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void createThirdPay() {
        ThirdPayCreateParam query = new ThirdPayCreateParam();
        query.setAmount(1056L);
        query.setRelationId(0L);
        query.setRelationType(0);
        query.setMerchantId(44L);
        query.setThirdPaySource(ThirdPayEnum.ThirdPaySource.ALI_PAY.getSource());
        query.setSubject("测试支付");
        query.setPayNo(UuidUtil.getUUID());
        Result<ThirdPayResult> result = thirdPayService.createThirdPay(query);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void refund() {
        RefundParam param = new RefundParam();
        param.setPayId(604L);
        param.setThirdPaySource(ThirdPayEnum.ThirdPaySource.ALI_PAY.getSource());
        param.setAmount(165L);
        Result<RefundResult> result = thirdPayService.refund(param);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void orderSettle() {
        OrderSettleParam orderSettleParam = new OrderSettleParam();
        orderSettleParam.setPayId(635L);
        orderSettleParam.setRequestNo(UuidUtil.getUUID());
        orderSettleParam.setThirdSource(ThirdPayEnum.ThirdPaySource.ALI_PAY.getSource());
        Result<OrderSettleResult> result = thirdPayService.orderSettle(orderSettleParam);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void royaltyRateQuery() {
        aliPayHandler.royaltyRateQuery();
    }

    @Test
    public void tradeQuery() {
        TradeQueryParam param = new TradeQueryParam();
        param.setThirdPaySource(ThirdPayEnum.ThirdPaySource.ALI_PAY.getSource());
        param.setPayNo("89477d948d3a4a4aa9ff32c86e79fe45");
        TradeQueryResult tradeQueryResult = aliPayHandler.tradeQuery(param);
        System.out.println(JSON.toJSONString(tradeQueryResult));
    }
}
