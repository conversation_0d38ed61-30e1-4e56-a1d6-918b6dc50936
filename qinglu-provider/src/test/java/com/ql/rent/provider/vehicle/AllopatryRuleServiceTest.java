package com.ql.rent.provider.vehicle;

import com.alibaba.fastjson.JSON;
import com.ql.rent.AbstractTest;
import com.ql.rent.service.store.IAllopatryRuleService;
import com.ql.rent.vo.store.AllopatryRuleVO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

public class AllopatryRuleServiceTest extends AbstractTest {
    @Resource
    private IAllopatryRuleService allopatryRuleService;
    @Test
    public void allopatryRuleList() {
        List<AllopatryRuleVO> diffRuleVOS =
                allopatryRuleService.allopatryRuleList(57L, 3L, new Date());

        System.out.println("diffRuleVOS__" +JSON.toJSONString(diffRuleVOS));

    }
    @Test
    public void diffRuleStoreList() {
        List<AllopatryRuleVO> diffRuleVOS =
                allopatryRuleService.diffRuleStoreList(57L, 3L, new Date());

        System.out.println("diffRuleVOS__" +JSON.toJSONString(diffRuleVOS));

    }
}
