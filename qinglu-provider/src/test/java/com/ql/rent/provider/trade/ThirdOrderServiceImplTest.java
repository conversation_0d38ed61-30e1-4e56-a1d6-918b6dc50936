package com.ql.rent.provider.trade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.ql.rent.AbstractTest;
import com.ql.rent.api.aggregate.model.dto.*;
import com.ql.rent.api.aggregate.model.request.*;
import com.ql.rent.api.aggregate.model.response.RenewalPriceDetailResp;
import com.ql.rent.api.aggregate.service.VehicleAggregateService;
import com.ql.rent.api.aggregate.model.request.*;
import com.ql.rent.dao.trade.OrderInfoMapper;
import com.ql.rent.dao.trade.OrderSnapshotMapper;
import com.ql.rent.dao.vehicle.ThirdVehicleModelMatchInfoMapper;
import com.ql.rent.entity.third.GaodeAddressDTO;
import com.ql.rent.entity.vehicle.ThirdVehicleModelMatchInfo;
import com.ql.rent.entity.vehicle.ThirdVehicleModelMatchInfoExample;
import com.ql.rent.enums.third.ServiceItemEnum;
import com.ql.rent.enums.trade.OrderSourceEnum;
import com.ql.rent.provider.third.gaode.GaodeComponent;
import com.ql.rent.service.trade.IOrderFixDataService;
import com.ql.rent.service.trade.IThirdOrderService;
import com.ql.rent.service.vehicle.IVehicleInfoService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.vo.trade.CancelOrderDTO;
import com.ql.rent.vo.trade.OrderOptionServiceDTO;
import com.ql.rent.vo.trade.third.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @auther musi
 * @date 2022/10/31 23:36
 */
public class ThirdOrderServiceImplTest extends AbstractTest{

    @Resource
    private IThirdOrderService thirdOrderService;
    @Resource
    private ThirdVehicleModelMatchInfoMapper thirdVehicleModelMatchMapper;

    @Resource
    private OrderSnapshotMapper orderSnapshotMapper;

    @Resource
    private IVehicleInfoService vehicleInfoService;

    @Resource
    private OrderInfoMapper orderInfoMapper;

    @Resource
    private IOrderFixDataService orderFixDataService;
    @Resource
    private GaodeComponent gaodeComponent;

    @Resource
    private VehicleAggregateService vehicleAggregateService;


    @Test
    public void testPriceCalForThird() {
        Long start = 1714705200000L; Long end = 1714791600000L;
        String str = "[{\"activityAndCouponDTOList\":[],\"channelId\":7,\"id\":2173,\"merchantId\":29,\"priceDailyList\":[{\"date\":\"2024-05-03\",\"hour\":24,\"partDailyPrice\":10000,\"per\":1.0,\"price\":10000}],\"selectServiceIdList\":[],\"selfServiceReturn\":0,\"serviceItemList\":[{\"code\":\"01011\",\"description\":\"\",\"id\":-1,\"name\":\"到店取车服务\",\"onCharge\":1,\"price\":0,\"required\":1,\"type\":5,\"unit\":\"次\"},{\"code\":\"01021\",\"description\":\"\",\"id\":-1,\"name\":\"到店还车服务\",\"onCharge\":1,\"price\":0,\"required\":1,\"type\":5,\"unit\":\"次\"},{\"code\":\"02001\",\"description\":\"\",\"id\":3083,\"name\":\"基本保障服务费\",\"onCharge\":1,\"onHighestPrice\":0,\"price\":5000,\"required\":1,\"settingId\":37,\"type\":2,\"unit\":\"天\"},{\"code\":\"02003\",\"description\":\"\",\"id\":3084,\"name\":\"优享服务费\",\"onCharge\":1,\"onHighestPrice\":0,\"price\":200,\"required\":0,\"settingId\":38,\"type\":2,\"unit\":\"天\"},{\"code\":\"02002\",\"description\":\"\",\"id\":3360,\"name\":\"尊享服务费\",\"onCharge\":1,\"onHighestPrice\":0,\"price\":300,\"required\":0,\"settingId\":39,\"type\":2,\"unit\":\"天\"},{\"code\":\"03001\",\"description\":\"\",\"id\":2250,\"name\":\"手续费\",\"onCharge\":1,\"price\":5000,\"required\":1,\"settingId\":25,\"type\":3,\"unit\":\"次\"}],\"storeId\":25,\"vehicleModelId\":2173}]";
        List<VehicleModelPriceCalDTO> priceCalVoList = JSON.parseArray(str, VehicleModelPriceCalDTO.class);
        String listStr = "[{\"code\":\"02003\",\"high7Per\":1.0,\"list\":[{\"date\":\"2024-05-03\",\"hour\":24,\"per\":1.0}],\"name\":\"优享服务费\",\"totalPer\":1.0},{\"code\":\"02002\",\"high7Per\":1.0,\"list\":[{\"date\":\"2024-05-03\",\"hour\":24,\"per\":1.0}],\"name\":\"尊享服务费\",\"totalPer\":1.0}]";
        List<DailyPriceListDTO> list = JSON.parseArray(listStr, DailyPriceListDTO.class);

        Map<Long, List<DailyPriceListDTO>> storeChargeMap = new HashMap<>();
        storeChargeMap.put(25L, list);
        Result result = thirdOrderService.priceCalForThird(start, end, priceCalVoList, storeChargeMap);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public void testcreateOrder() {
        String str = "{\"activityAndCouponList\":[{\"code\":\"tripSingleMixPromotion-101393550217_1925728902212\",\"name\":\"40元租车优惠\",\"type\":2,\"vendorDiscountAmount\":40},{\"code\":\"coupon2tripCrossCoupon-107680617115_2052265347050-7708969442149\",\"name\":\"20元租车优惠券\",\"type\":2,\"vendorDiscountAmount\":0}],\"addedServiceItemCode\":[\"03001\",\"02001\",\"01011\",\"01021\",\"01004\",\"01005\"],\"anxin\":false,\"childrenSeatNum\":0,\"freeDepositDegree\":0,\"merchantId\":58,\"noWorriedOrder\":false,\"orderSource\":3,\"orderStatus\":3,\"orderTime\":1744268955785,\"payAmount\":6900,\"pickupAddr\":\"上海虹桥机场T1\",\"pickupAddrType\":1,\"pickupCircleId\":-1,\"pickupCityCode\":107,\"pickupDate\":\"2025-04-11 13:00:00\",\"pickupLongLatVo\":{\"latitude\":31.314404,\"longitude\":121.158198},\"pickupStoreId\":637,\"platformCal\":false,\"receivableAmount\":10900,\"replacePrice\":false,\"returnAddr\":\"上海虹桥机场T1\",\"returnAddrType\":1,\"returnCircleId\":-1,\"returnCityCode\":107,\"returnDate\":\"2025-04-12 13:00:00\",\"returnLongLatVo\":{\"latitude\":31.314404,\"longitude\":121.158198},\"returnStoreId\":637,\"selfPrOrder\":0,\"serviceItemDTOList\":[{\"amount\":0,\"calPer\":1.0,\"code\":\"01011\",\"id\":-1,\"includeInTotalAmount\":1,\"name\":\"到店取车服务\",\"price\":0,\"quantity\":1.0,\"type\":5,\"unit\":\"次\"},{\"amount\":0,\"calPer\":1.0,\"code\":\"01021\",\"id\":-1,\"includeInTotalAmount\":1,\"name\":\"到店还车服务\",\"price\":0,\"quantity\":1.0,\"type\":5,\"unit\":\"次\"},{\"amount\":100,\"calPer\":1.0,\"code\":\"01004\",\"id\":34264,\"includeInTotalAmount\":1,\"name\":\"夜间取车服务费\",\"price\":100,\"quantity\":1.0,\"type\":6,\"unit\":\"次\"},{\"amount\":100,\"calPer\":1.0,\"code\":\"01005\",\"id\":34264,\"includeInTotalAmount\":1,\"name\":\"夜间还车服务费\",\"price\":100,\"quantity\":1.0,\"type\":7,\"unit\":\"次\"},{\"amount\":400,\"calPer\":1.0,\"code\":\"02001\",\"id\":1992,\"includeInTotalAmount\":1,\"name\":\"基本保障服务费\",\"price\":400,\"quantity\":1.0,\"type\":2,\"unit\":\"天\"},{\"amount\":500,\"calPer\":1.0,\"code\":\"02003\",\"id\":1993,\"includeInTotalAmount\":0,\"name\":\"优享服务费\",\"price\":500,\"quantity\":1.0,\"type\":2,\"unit\":\"天\"},{\"amount\":600,\"calPer\":1.0,\"code\":\"02002\",\"id\":1994,\"includeInTotalAmount\":0,\"name\":\"尊享服务费\",\"price\":600,\"quantity\":1.0,\"type\":2,\"unit\":\"天\"},{\"amount\":300,\"calPer\":1.0,\"code\":\"03001\",\"id\":5104,\"includeInTotalAmount\":1,\"name\":\"手续费\",\"price\":300,\"quantity\":1.0,\"type\":3,\"unit\":\"次\"},{\"amount\":10000,\"calPer\":1.0,\"code\":\"04001\",\"includeInTotalAmount\":1,\"name\":\"租车费\",\"price\":10000,\"quantity\":1.0,\"type\":0,\"unit\":\"天\"}],\"skipCheckPrice\":false,\"sourceOrderId\":\"*******************\",\"thirdOrderUserDTO\":{\"idcardNo\":\"******************\",\"mobile\":\"16639708523\",\"userName\":\"吴瑞森\"},\"vehicleModelId\":2096,\"vehicleModelPriceAbbrDTO\":{\"banChildSeat\":false,\"discountAmount\":4000,\"discountBigDecimalAmount\":4000,\"id\":2096,\"merchantId\":58,\"priceDailyList\":[{\"allDay\":true,\"date\":\"2025-04-11\",\"hour\":24,\"partDailyPrice\":10000,\"per\":1.0,\"price\":10000}],\"rentalPrice\":{\"averageDailyPrice\":10000,\"days\":1,\"overtime\":0,\"overtimeAmount\":0,\"quantity\":1.0,\"standardAmount\":10000,\"standardTotalAmount\":10000,\"standardUnitPrice\":10000},\"serviceItemAmountList\":[{\"amount\":0,\"calPer\":1.0,\"code\":\"01011\",\"description\":\"\",\"id\":-1,\"includeInTotalAmount\":1,\"name\":\"到店取车服务\",\"onCharge\":1,\"price\":0,\"quantity\":1.0,\"required\":1,\"type\":5,\"unit\":\"次\"},{\"amount\":0,\"calPer\":1.0,\"code\":\"01021\",\"description\":\"\",\"id\":-1,\"includeInTotalAmount\":1,\"name\":\"到店还车服务\",\"onCharge\":1,\"price\":0,\"quantity\":1.0,\"required\":1,\"type\":5,\"unit\":\"次\"},{\"amount\":100,\"calPer\":1.0,\"code\":\"01004\",\"description\":\"\",\"id\":34264,\"includeInTotalAmount\":1,\"name\":\"夜间取车服务费\",\"onCharge\":1,\"price\":100,\"quantity\":1.0,\"required\":1,\"type\":6,\"unit\":\"次\"},{\"amount\":100,\"calPer\":1.0,\"code\":\"01005\",\"description\":\"\",\"id\":34264,\"includeInTotalAmount\":1,\"name\":\"夜间还车服务费\",\"onCharge\":1,\"price\":100,\"quantity\":1.0,\"required\":1,\"type\":7,\"unit\":\"次\"},{\"amount\":400,\"calPer\":1.0,\"code\":\"02001\",\"description\":\"\",\"id\":1992,\"includeInTotalAmount\":1,\"name\":\"基本保障服务费\",\"onCharge\":1,\"onHighestPrice\":0,\"price\":400,\"quantity\":1.0,\"required\":1,\"settingId\":119,\"type\":2,\"unit\":\"天\"},{\"amount\":500,\"calPer\":1.0,\"code\":\"02003\",\"description\":\"\",\"id\":1993,\"includeInTotalAmount\":0,\"name\":\"优享服务费\",\"onCharge\":1,\"onHighestPrice\":1,\"price\":500,\"quantity\":1.0,\"required\":0,\"settingId\":120,\"type\":2,\"unit\":\"天\"},{\"amount\":600,\"calPer\":1.0,\"code\":\"02002\",\"description\":\"\",\"id\":1994,\"includeInTotalAmount\":0,\"name\":\"尊享服务费\",\"onCharge\":1,\"onHighestPrice\":1,\"price\":600,\"quantity\":1.0,\"required\":0,\"settingId\":121,\"type\":2,\"unit\":\"天\"},{\"amount\":300,\"calPer\":1.0,\"code\":\"03001\",\"description\":\"\",\"id\":5104,\"includeInTotalAmount\":1,\"name\":\"手续费\",\"onCharge\":1,\"price\":300,\"quantity\":1.0,\"required\":1,\"settingId\":90,\"type\":3,\"unit\":\"次\"},{\"amount\":10000,\"calPer\":1.0,\"code\":\"04001\",\"includeInTotalAmount\":1,\"name\":\"租车费\",\"onCharge\":1,\"price\":10000,\"quantity\":1.0,\"required\":1,\"type\":0,\"unit\":\"天\"}],\"storeId\":637,\"totalAmount\":10900,\"vehicleModelId\":2096}}";
        ThirdOrderDTO thirdOrderDTO = JSON.parseObject(str, ThirdOrderDTO.class);

        Result<Long> result = thirdOrderService.createOrder(thirdOrderDTO);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testMatch() {
        ThirdVehicleModelMatchInfoExample example = new ThirdVehicleModelMatchInfoExample();
        example.createCriteria().andChannelIdEqualTo(4L).andMerchantIdEqualTo(15L).
                andThirdVehicleModelIdEqualTo("1519618958284685313").andMatchedEqualTo((byte) 1).andDeletedEqualTo((byte) 0);
        List<ThirdVehicleModelMatchInfo> match = thirdVehicleModelMatchMapper.selectByExample(example);
        System.out.println(match);
    }

    @Test
    public void testCancelOrder() {
        CancelOrderDTO cancelOrderDTO = new CancelOrderDTO();
        cancelOrderDTO.setCancelType(6);
        cancelOrderDTO.setCancelReason("老板服务态度不好");
        Result<ThirdCancelPenaltyDTO> result = thirdOrderService.cancelOrder(1690009L, null,0, cancelOrderDTO);
        System.out.println(result);
    }

    @Test
    public void testValidateCancelOrder() {
        Result<ThirdCancelPenaltyDTO> result = thirdOrderService.validateCancelOrder(null, "2859591118946");
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testPriceCal() {
        String str = "[{\n" +
                "\t\"priceDailyList\": [{\n" +
                "\t\t\"date\": \"2022-12-17\",\n" +
                "\t\t\"hour\": 24,\n" +
                "\t\t\"partDailyPrice\": 38800,\n" +
                "\t\t\"per\": 1.0,\n" +
                "\t\t\"price\": 38800\n" +
                "\t}],\n" +
                "\t\"serviceItemList\": [{\n" +
                "\t\t\"code\": \"02001\",\n" +
                "\t\t\"description\": \"\",\n" +
                "\t\t\"id\": 30,\n" +
                "\t\t\"name\": \"???????\",\n" +
                "\t\t\"onCharge\": 1,\n" +
                "\t\t\"onHighestPrice\": 0,\n" +
                "\t\t\"price\": 11000,\n" +
                "\t\t\"required\": 1,\n" +
                "\t\t\"select\": false,\n" +
                "\t\t\"type\": 2,\n" +
                "\t\t\"unit\": \"?\"\n" +
                "\t}, {\n" +
                "\t\t\"code\": \"02003\",\n" +
                "\t\t\"description\": \"\",\n" +
                "\t\t\"id\": 31,\n" +
                "\t\t\"name\": \"?????\",\n" +
                "\t\t\"onCharge\": 1,\n" +
                "\t\t\"onHighestPrice\": 0,\n" +
                "\t\t\"price\": 5000,\n" +
                "\t\t\"required\": 0,\n" +
                "\t\t\"select\": false,\n" +
                "\t\t\"type\": 2,\n" +
                "\t\t\"unit\": \"?\"\n" +
                "\t}, {\n" +
                "\t\t\"code\": \"02002\",\n" +
                "\t\t\"description\": \"\",\n" +
                "\t\t\"id\": 35,\n" +
                "\t\t\"name\": \"???????\",\n" +
                "\t\t\"onCharge\": 1,\n" +
                "\t\t\"onHighestPrice\": 0,\n" +
                "\t\t\"price\": 10000,\n" +
                "\t\t\"required\": 0,\n" +
                "\t\t\"select\": false,\n" +
                "\t\t\"type\": 2,\n" +
                "\t\t\"unit\": \"?\"\n" +
                "\t}, {\n" +
                "\t\t\"code\": \"03001\",\n" +
                "\t\t\"description\": \"\",\n" +
                "\t\t\"id\": 23,\n" +
                "\t\t\"name\": \"???\",\n" +
                "\t\t\"onCharge\": 1,\n" +
                "\t\t\"price\": 3500,\n" +
                "\t\t\"required\": 1,\n" +
                "\t\t\"select\": false,\n" +
                "\t\t\"type\": 3,\n" +
                "\t\t\"unit\": \"?\"\n" +
                "\t}, {\n" +
                "\t\t\"code\": \"03002\",\n" +
                "\t\t\"description\": \"\",\n" +
                "\t\t\"id\": 24,\n" +
                "\t\t\"name\": \"????\",\n" +
                "\t\t\"onCharge\": 1,\n" +
                "\t\t\"price\": 3000,\n" +
                "\t\t\"required\": 0,\n" +
                "\t\t\"select\": false,\n" +
                "\t\t\"type\": 3,\n" +
                "\t\t\"unit\": \"?\"\n" +
                "\t}, {\n" +
                "\t\t\"code\": \"01001\",\n" +
                "\t\t\"description\": \"\",\n" +
                "\t\t\"id\": 15,\n" +
                "\t\t\"name\": \"???????\",\n" +
                "\t\t\"onCharge\": 1,\n" +
                "\t\t\"price\": 0,\n" +
                "\t\t\"required\": 1,\n" +
                "\t\t\"select\": false,\n" +
                "\t\t\"type\": 4,\n" +
                "\t\t\"unit\": \"?\"\n" +
                "\t}, {\n" +
                "\t\t\"code\": \"01002\",\n" +
                "\t\t\"description\": \"\",\n" +
                "\t\t\"id\": 21,\n" +
                "\t\t\"name\": \"???????\",\n" +
                "\t\t\"onCharge\": 1,\n" +
                "\t\t\"price\": 0,\n" +
                "\t\t\"required\": 1,\n" +
                "\t\t\"select\": false,\n" +
                "\t\t\"type\": 5,\n" +
                "\t\t\"unit\": \"?\"\n" +
                "\t}],\n" +
                "\t\"storeId\": 1021,\n" +
                "\t\"vehicleModelId\": 150\n" +
                "}, {\n" +
                "\t\"priceDailyList\": [{\n" +
                "\t\t\"date\": \"2022-12-17\",\n" +
                "\t\t\"hour\": 24,\n" +
                "\t\t\"partDailyPrice\": 32000,\n" +
                "\t\t\"per\": 1.0,\n" +
                "\t\t\"price\": 32000\n" +
                "\t}],\n" +
                "\t\"serviceItemList\": [{\n" +
                "\t\t\"code\": \"02001\",\n" +
                "\t\t\"description\": \"\",\n" +
                "\t\t\"id\": 32,\n" +
                "\t\t\"name\": \"???????\",\n" +
                "\t\t\"onCharge\": 1,\n" +
                "\t\t\"onHighestPrice\": 0,\n" +
                "\t\t\"price\": 6600,\n" +
                "\t\t\"required\": 1,\n" +
                "\t\t\"select\": false,\n" +
                "\t\t\"type\": 2,\n" +
                "\t\t\"unit\": \"?\"\n" +
                "\t}, {\n" +
                "\t\t\"code\": \"02003\",\n" +
                "\t\t\"description\": \"\",\n" +
                "\t\t\"id\": 33,\n" +
                "\t\t\"name\": \"?????\",\n" +
                "\t\t\"onCharge\": 1,\n" +
                "\t\t\"onHighestPrice\": 0,\n" +
                "\t\t\"price\": 10000,\n" +
                "\t\t\"required\": 0,\n" +
                "\t\t\"select\": false,\n" +
                "\t\t\"type\": 2,\n" +
                "\t\t\"unit\": \"?\"\n" +
                "\t}, {\n" +
                "\t\t\"code\": \"02002\",\n" +
                "\t\t\"description\": \"\",\n" +
                "\t\t\"id\": 34,\n" +
                "\t\t\"name\": \"???????\",\n" +
                "\t\t\"onCharge\": 1,\n" +
                "\t\t\"onHighestPrice\": 0,\n" +
                "\t\t\"price\": 15000,\n" +
                "\t\t\"required\": 0,\n" +
                "\t\t\"select\": false,\n" +
                "\t\t\"type\": 2,\n" +
                "\t\t\"unit\": \"?\"\n" +
                "\t}, {\n" +
                "\t\t\"code\": \"03001\",\n" +
                "\t\t\"description\": \"\",\n" +
                "\t\t\"id\": 26,\n" +
                "\t\t\"name\": \"???\",\n" +
                "\t\t\"onCharge\": 1,\n" +
                "\t\t\"price\": 3000,\n" +
                "\t\t\"required\": 1,\n" +
                "\t\t\"select\": false,\n" +
                "\t\t\"type\": 3,\n" +
                "\t\t\"unit\": \"?\"\n" +
                "\t}, {\n" +
                "\t\t\"code\": \"03002\",\n" +
                "\t\t\"description\": \"\",\n" +
                "\t\t\"id\": 27,\n" +
                "\t\t\"name\": \"????\",\n" +
                "\t\t\"onCharge\": 1,\n" +
                "\t\t\"price\": 3000,\n" +
                "\t\t\"required\": 0,\n" +
                "\t\t\"select\": false,\n" +
                "\t\t\"type\": 3,\n" +
                "\t\t\"unit\": \"?\"\n" +
                "\t}, {\n" +
                "\t\t\"code\": \"01001\",\n" +
                "\t\t\"description\": \"\",\n" +
                "\t\t\"id\": 15,\n" +
                "\t\t\"name\": \"???????\",\n" +
                "\t\t\"onCharge\": 1,\n" +
                "\t\t\"price\": 0,\n" +
                "\t\t\"required\": 1,\n" +
                "\t\t\"select\": false,\n" +
                "\t\t\"type\": 4,\n" +
                "\t\t\"unit\": \"?\"\n" +
                "\t}, {\n" +
                "\t\t\"code\": \"01002\",\n" +
                "\t\t\"description\": \"\",\n" +
                "\t\t\"id\": 21,\n" +
                "\t\t\"name\": \"???????\",\n" +
                "\t\t\"onCharge\": 1,\n" +
                "\t\t\"price\": 0,\n" +
                "\t\t\"required\": 1,\n" +
                "\t\t\"select\": false,\n" +
                "\t\t\"type\": 5,\n" +
                "\t\t\"unit\": \"?\"\n" +
                "\t}],\n" +
                "\t\"storeId\": 1021,\n" +
                "\t\"vehicleModelId\": 153\n" +
                "}]";
        List<VehicleModelPriceCalDTO> priceCalDTO = JSON.parseArray(str, VehicleModelPriceCalDTO.class);
        priceCalDTO.get(0).setId(19l);
        Long start = DateUtil.getFormatDate("2022-11-27 10:00:00", DateUtil.yyyyMMddHHmmss).getTime();
        Long end = DateUtil.getFormatDate("2022-11-29 12:00:00", DateUtil.yyyyMMddHHmmss).getTime();

        Result<List<VehicleModelPriceAbbrDTO>> result = thirdOrderService.priceCalForThird(start, end, priceCalDTO, new HashMap<>());
        System.out.println(result);
    }


    @Test
    public void testRerentPriceCal() {
        Result<VehicleModelPriceAbbrDTO> result = thirdOrderService.rerentPriceCal(1705332L, 1749263400000L, OrderSourceEnum.CTRIP.getSource(), true);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }


    @Test
    public void testRenewalPriceDetail() {
        RenewalPriceDetailReq req = new RenewalPriceDetailReq();
        req.setOrderId(1704842L);
        req.setReturnDate("2025-06-01 13:00");
        req.setSourceOrderId("1234563333");
        req.setNewProcess(true);
        req.setChannelId(2L);
        RenewalPriceDetailResp result = vehicleAggregateService.renewalPriceDetail(45L, req);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }


    @Test
    public void testCreateRerentOrder() {
        ThirdRerentOrderDTO thirdRerentOrderDTO = new ThirdRerentOrderDTO();
        thirdRerentOrderDTO.setMerchantId(1L);
        thirdRerentOrderDTO.setOrderId(948L);
        thirdRerentOrderDTO.setPayAmount(25000);
        thirdRerentOrderDTO.setReceivableAmount(25000);
        thirdRerentOrderDTO.setSourceOrderId("3259678609368862302");
        thirdRerentOrderDTO.setOrderSource(OrderSourceEnum.FEIZHU.getSource());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date rerentTime = sdf.parse("2022-12-21 10:30:00");
            thirdRerentOrderDTO.setRerentTime(1679106600000L);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        Result<Long> result = thirdOrderService.createRerentOrder(thirdRerentOrderDTO);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public void testRerentCheck() {
        Result<Boolean> result = thirdOrderService.rerentCheck(465767L, null, 1714536000000L);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public void testGetRerentPenalty() {
        Result<ThirdCancelPenaltyDTO> result = thirdOrderService.getRerentPenalty(124520L, 32097253L, null);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public void testCancelRerentOrder() {
        Result<Boolean> result = thirdOrderService.cancelRerentOrder(124520L, 32097253L, 2500, Long.valueOf(OrderSourceEnum.CTRIP.getSource()));
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public void testGetVehicleModelPriceAbbr() {
        String str = "{\n" +
                "    \"vehicleModelId\": 153,\n" +
                "    \"payAmount\": 94300,\n" +
                "    \"receivableAmount\": 94300,\n" +
                "    \"priceKey\": \"6e04886a-231e-47b0-a06b-71d08ce39f44::153\",\n" +
                "    \"partnerUser\": {\n" +
                "        \"name\": \"王硕\",\n" +
                "        \"mobile\": \"15556365108\",\n" +
                "        \"idType\": 1,\n" +
                "        \"idNo\": \"341225199110012431\"\n" +
                "    },\n" +
                "    \"pickUpStore\": {\n" +
                "        \"datetime\": \"2023-03-21 15:15:00\",\n" +
                "        \"pickUpDropOffType\": 1,\n" +
                "        \"storeId\": 1021,\n" +
                "        \"cityId\": 121,\n" +
                "        \"address\": \"浙江省杭州市上城区彭埠街道杭州东站\",\n" +
                "        \"point\": {\n" +
                "            \"longitude\": 120.2126,\n" +
                "            \"latitude\": 30.290851\n" +
                "        },\n" +
                "        \"serviceCircleIds\": [\n" +
                "            15\n" +
                "        ]\n" +
                "    },\n" +
                "    \"dropOffStore\": {\n" +
                "        \"datetime\": \"2023-03-23 18:45:00\",\n" +
                "        \"pickUpDropOffType\": 1,\n" +
                "        \"storeId\": 1021,\n" +
                "        \"cityId\": 121,\n" +
                "        \"address\": \"浙江省杭州市上城区彭埠街道杭州东站\",\n" +
                "        \"point\": {\n" +
                "            \"longitude\": 120.2126,\n" +
                "            \"latitude\": 30.290851\n" +
                "        },\n" +
                "        \"serviceCircleIds\": [\n" +
                "            15\n" +
                "        ]\n" +
                "    },\n" +
                "    \"thirdOrderId\": \"RC202301031610124883944198144\",\n" +
                "    \"freeDepositDegree\": 1\n" +
                "}";
        ValidateCreateOrderReq checkOrderReq = JSON.parseObject(str, ValidateCreateOrderReq.class);

        VehicleModelPriceAbbrDTO result = thirdOrderService.getVehicleModelPriceAbbr(4L, 9L, checkOrderReq);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public void testGetVehicleModelPriceAbbr2() {
        String str = "{\"vehicleModelId\":12,\"payMode\":2,\"payAmount\":23000,\"receivableAmount\":23000,\"priceKey\":\"001ff10f-44cb-455c-85a5-95cb768906a8\",\"partnerUser\":{\"name\":\"谢鹏\",\"mobile\":\"18658846812\",\"idType\":1,\"idNo\":\"330501198703121017\"},\"pickUpStore\":{\"datetime\":\"2023-03-08 10:00:00\",\"pickUpDropOffType\":2,\"storeId\":2,\"cityId\":107,\"address\":\"上海市虹桥机场门店\",\"point\":{\"longitude\":121.345416,\"latitude\":31.204532},\"serviceCircleIds\":[4]},\"dropOffStore\":{\"datetime\":\"2023-03-09 10:00:00\",\"pickUpDropOffType\":2,\"storeId\":2,\"cityId\":107,\"address\":\"上海市虹桥机场门店\",\"point\":{\"longitude\":121.345416,\"latitude\":31.204532},\"serviceCircleIds\":[4]},\"addedServices\":[\"03001\",\"02001\",\"01012\",\"01022\"]}";
        ValidateCreateOrderReq validateCreateOrderReq = JSON.parseObject(str, ValidateCreateOrderReq.class);
        VehicleModelPriceAbbrDTO dto = thirdOrderService.getVehicleModelPriceAbbr(3l, 1l, validateCreateOrderReq);
        System.out.println(JSON.toJSONString(dto));
    }

    @Test
    public void testGetOrderDetail() {
        GetOrderDetailReq getOrderDetailReq = new GetOrderDetailReq();
        getOrderDetailReq.setOrderId(466478L);
        getOrderDetailReq.setMerchantId(29L);
        Result<ThirdOrderDetailDTO> result = thirdOrderService.getOrderDetail(getOrderDetailReq);
        System.out.println(JSON.toJSONString(result));
    }
    @Test
    public void testCreateOrderForReptile() {
        ReptileOrderDTO obj = new ReptileOrderDTO();
        obj.setMerchantId(1l);
        obj.setVehicleNo("");
        obj.setPickupDate("2023-04-01 00:00:00");
        obj.setReturnDate("2023-04-02 00:00:00");
        obj.setPickupStoreId(1l);
        obj.setReturnStoreId(1l);
        obj.setPickupCityCode(1l);
        obj.setReturnCityCode(1l);
        obj.setPickupAddr("");
        obj.setReturnAddr("");
        obj.setPickupAddrType((byte) 1);
        obj.setReturnAddrType((byte) 1);
//        obj.setPickupLongLatVo();
//        obj.setReturnLongLatVo();
        obj.setPayAmount(10000);
        obj.setReceivableAmount(10000);
        obj.setFreeDepositDegree(0);
        obj.setFreeDepositWay(String.valueOf(0));
        obj.setSourceOrderId("12345");
        obj.setOrderSource((byte) 2);
        obj.setOrderStatus((byte) 4);
        obj.setRemark("remark");
        obj.setOrderTime(System.currentTimeMillis());
        OrderMemberDTO orderMemberDTO = new OrderMemberDTO();
        orderMemberDTO.setMobile("12345671234");
        orderMemberDTO.setIdType(1);
        orderMemberDTO.setIdNo("2324");
        orderMemberDTO.setName("musi");
        obj.setOrderMemberDTO(orderMemberDTO);
        obj.setServiceItem("");
        String s = "{\n" +
                "\t\"freeDepositDegree\": 2,\n" +
                "\t\"freeDepositWay\": \"3\",\n" +
                "\t\"merchantId\": 3,\n" +
                "\t\"orderMemberDTO\": {\n" +
                "\t\t\"idNo\": \"622621200010020012\",\n" +
                "\t\t\"idType\": 1,\n" +
                "\t\t\"mobile\": \"18928236002\",\n" +
                "\t\t\"name\": \"李泊江\"\n" +
                "\t},\n" +
                "\t\"orderSource\": 4,\n" +
                "\t\"orderStatus\": 4,\n" +
                "\t\"orderTime\": 1680570214000,\n" +
                "\t\"payAmount\": 11500,\n" +
                "\t\"pickupAddr\": \"哈尔滨市\",\n" +
                "\t\"pickupAddrType\": 1,\n" +
                "\t\"pickupCityCode\": 121,\n" +
                "\t\"pickupDate\": \"2023-04-04 21:00:00\",\n" +
                "\t\"pickupLongLatVo\": {\n" +
                "\t\t\"latitude\": 45.780045,\n" +
                "\t\t\"longitude\": 126.646061\n" +
                "\t},\n" +
                "\t\"pickupStoreId\": 6,\n" +
                "\t\"receivableAmount\": 11500,\n" +
                "\t\"returnAddr\": \"哈尔滨市\",\n" +
                "\t\"returnAddrType\": 1,\n" +
                "\t\"returnCityCode\": 121,\n" +
                "\t\"returnDate\": \"2023-04-05 21:00:00\",\n" +
                "\t\"returnLongLatVo\": {\n" +
                "\t\t\"latitude\": 45.780045,\n" +
                "\t\t\"longitude\": 126.646061\n" +
                "\t},\n" +
                "\t\"returnStoreId\": 6,\n" +
                "\t\"serviceItem\": \"[{\\\"price\\\":18,\\\"code\\\":\\\"基本费用\\\"},{\\\"price\\\":45,\\\"code\\\":\\\"基础保障服务费\\\"},{\\\"price\\\":-18,\\\"code\\\":\\\"优惠金额\\\"},{\\\"price\\\":50,\\\"code\\\":\\\"优享服务费\\\"},{\\\"price\\\":20,\\\"code\\\":\\\"车行手续费\\\"}]\",\n" +
                "\t\"sourceOrderId\": \"RC202304041643056642213937152\",\n" +
                "\t\"tagList\": [\"本地热门\", \"支付宝精选\", \"芝麻免押金\", \"先租后付\", \"哈啰精选\", \"免费送取车\", \"免费取消\", \"免费留车\", \"押金极速退\", \"疫情无忧退\", \"消毒后交车\", \"一车一洗\", \"哈尔滨销量第1名\", \"自动挡\", \"支持电子驾照\", \"倒车影像\", \"手机支架\", \"倒车雷达\", \"不限里程\", \"两年内新车\"],\n" +
                "\t\"vehicleNo\": \"\"\n" +
                "}";
        obj = JSON.parseObject(s, ReptileOrderDTO.class);
        Result<Long> result = thirdOrderService.createOrderForReptile(obj);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testCreateCtripHistoryOrder() {
        CtripHistoryOrderDTO ctripHistoryOrderDTO;
        ctripHistoryOrderDTO = JSON.parseObject("{\"ctripHistoryRerentOrderDTOList\":[],\"freeDepositDegree\":0,\"freeDepositWay\":\"5\",\"illegalDepositAmount\":200000,\"merchantId\":29,\"orderMemberDTO\":{\"idType\":1,\"idcardNo\":\"******************\",\"mobile\":\"18342578588\",\"name\":\"曹永健\"},\"orderSource\":7,\"orderStatus\":8,\"orderTime\":1748572058000,\"payAmount\":600,\"pickupAddr\":\"杭州东站\",\"pickupAddrType\":1,\"pickupDate\":\"2025-06-05 15:00:00\",\"pickupLongLatVo\":{\"latitude\":30.29145,\"longitude\":120.21307},\"pickupStoreId\":7446,\"receivableAmount\":600,\"rentDepositAmount\":100000,\"returnAddr\":\"桃仙机场T1航站楼\",\"returnAddrType\":1,\"returnDate\":\"2025-06-06 17:30:00\",\"returnLongLatVo\":{\"latitude\":41.639823,\"longitude\":123.49709},\"returnStoreId\":2248,\"serviceItemDTOList\":[{\"amount\":200,\"code\":\"04001\",\"name\":\"租车费\",\"price\":100,\"quantity\":1.03},{\"amount\":100,\"code\":\"02001\",\"name\":\"基本保障服务费\",\"price\":100,\"quantity\":1.03},{\"amount\":100,\"code\":\"01003\",\"name\":\"异门店还车费\",\"price\":100,\"quantity\":1.0},{\"amount\":200,\"code\":\"03001\",\"name\":\"手续费\",\"price\":200,\"quantity\":1.0}],\"sourceOrderId\":\"M7476148330474180608\",\"vehicleId\":\"85360\",\"vehicleModelId\":\"13337\",\"vehicleNo\":\"川AG58708\"}", CtripHistoryOrderDTO.class);
        Result reslut = thirdOrderService.createCtripHistoryOrder(ctripHistoryOrderDTO);
        System.out.println(reslut);
    }


    @Test
    public void testUpdateOrder() {
        ThirdUpdateOrderDTO thirdUpdateOrderDTO;
        thirdUpdateOrderDTO = JSON.parseObject("{\"amountMargin\":true,\"channelId\":7,\"hourlyChargeSnapShot\":[{\"chargeFormula\":1,\"chargeItem\":1,\"chargeType\":1,\"chargeWay\":1,\"range\":[{\"rangeEnd\":1,\"rangeStart\":0,\"ratio\":0.00},{\"rangeEnd\":3,\"rangeStart\":1,\"ratio\":0.25},{\"rangeEnd\":5,\"rangeStart\":3,\"ratio\":0.50},{\"rangeEnd\":7,\"rangeStart\":5,\"ratio\":0.75},{\"rangeEnd\":23,\"rangeStart\":7,\"ratio\":1.00}],\"scene\":1},{\"chargeFormula\":1,\"chargeItem\":2,\"chargeType\":2,\"chargeWay\":1,\"range\":[{\"rangeEnd\":5,\"rangeStart\":0,\"ratio\":0},{\"rangeEnd\":23,\"rangeStart\":6,\"ratio\":1}],\"scene\":1}],\"merchantId\":99,\"payAmount\":2100,\"pickupAddr\":\"合肥站\",\"pickupDate\":\"2025-06-20 17:30:00\",\"pickupLongLatVo\":{\"latitude\":31.88565,\"longitude\":117.31735},\"receivableAmount\":2100,\"returnAddr\":\"合肥站\",\"returnDate\":\"2025-06-21 18:30:00\",\"returnLongLatVo\":{\"latitude\":31.88565,\"longitude\":117.31735},\"saasAmount\":2100,\"serviceItemDTOList\":[{\"amount\":1000,\"calPer\":1.0,\"code\":\"04001\",\"ctripStandardFee\":false,\"includeInTotalAmount\":1,\"name\":\"租车费\",\"price\":1000,\"quantity\":1.01,\"type\":0},{\"amount\":1000,\"calPer\":1.0,\"code\":\"02001\",\"ctripStandardFee\":false,\"includeInTotalAmount\":1,\"name\":\"基本保障服务费\",\"price\":1000,\"quantity\":1.01,\"type\":2},{\"amount\":100,\"calPer\":1.0,\"code\":\"03001\",\"ctripStandardFee\":false,\"includeInTotalAmount\":1,\"name\":\"手续费\",\"price\":100,\"quantity\":1.0,\"type\":3}],\"thirdOrderId\":\"M7495823495680749568\"}", ThirdUpdateOrderDTO.class);
        Result reslut = thirdOrderService.updateOrder(thirdUpdateOrderDTO);
        System.out.println(reslut);
    }

    @Test
    public void testGetOptionServiceList(){
//        Result result = thirdOrderService.getOptionServiceList(2L, 45L, "36520329989");

        Result result = thirdOrderService.getOptionServiceListV2(2L, 45L, "1128168945025312");
        System.out.println(JSON.toJSONString(result));
    }


    @Test
    public void testAddOptionService() {
        String str = "[{\"code\":\"02002\",\"name\":\"尊享服务\",\"price\":100.0000,\"quantity\":1.0,\"totalAmount\":60.00}]";
        List<OrderOptionServiceDTO> list = JSON.parseArray(str, OrderOptionServiceDTO.class);
        Result result = thirdOrderService.addOptionService(2L, 45L, "36520568938", null, list);
        System.out.println(JSON.toJSONString(result));
    }

//    @Test
//    public void testImportExcelOrder() {
//        String str = "{\"freeDepositDegree\":2,\"merchantId\":57,\"mobile\":\"13862767517\",\"orderSource\":3,\"orderStatus\":5,\"payAmount\":36500,\"pickupAddr\":\"中华女子学院\",\"pickupAddrType\":0,\"pickupCityCode\":110000,\"pickupDate\":\"2024-03-10 12:00:00\",\"pickupStoreId\":494,\"receivableAmount\":36500,\"returnAddr\":\"中华女子学院\",\"returnAddrType\":0,\"returnCityCode\":110000,\"returnDate\":\"2024-03-11 12:00:00\",\"returnStoreId\":494,\"sourceOrderId\":\"3792167065544266528\",\"username\":\"徐书林\",\"vehicleModelId\":2119,\"vehicleNo\":\"沪MD0000\"}";
//
//        ExcelOrderReq req = JSON.parseObject(str, ExcelOrderReq.class);
//        ThirdOrderDTO thirdOrderDTO = ThirdOrderDTO.builder().build();
//        BeanUtils.copyProperties(req, thirdOrderDTO);
//        ThirdOrderUserDTO thirdOrderUserDTO = ThirdOrderUserDTO.builder().userName(req.getUsername()).mobile(req.getMobile()).build();
//        thirdOrderDTO.setThirdOrderUserDTO(thirdOrderUserDTO);
//        thirdOrderDTO.setIsExcel(1);
//        thirdOrderDTO.setPickupAddrType((byte) (thirdOrderDTO.getPickupAddrType() + 1));
//        thirdOrderDTO.setReturnAddrType((byte) (thirdOrderDTO.getReturnAddrType() + 1));
//        thirdOrderDTO.setOrderTime(System.currentTimeMillis());
//
//        Result result = thirdOrderService.importExcelOrder(thirdOrderDTO);
//        System.out.println(JSON.toJSONString(result));
//    }

    @Test
    public void testImportExcelOrder() {
        String str =
            "{\"freeDepositDegree\":2,\"merchantId\":57,\"mobile\":\"13862767517\",\"orderSource\":3,\"orderStatus\":5,\"payAmount\":36500,\"pickupAddr\":\"中华女子学院\",\"pickupAddrType\":0,\"pickupCityCode\":110000,\"pickupDate\":\"2024-03-10 12:00:00\",\"pickupStoreId\":494,\"receivableAmount\":36500,\"returnAddr\":\"中华女子学院\",\"returnAddrType\":0,\"returnCityCode\":110000,\"returnDate\":\"2024-03-11 12:00:00\",\"returnStoreId\":494,\"sourceOrderId\":\"3792167065544266528\",\"username\":\"徐书林\",\"vehicleModelId\":2119,\"vehicleNo\":\"沪MD0000\"}";

        ExcelOrderReq req = JSON.parseObject(str, ExcelOrderReq.class);
        ThirdOrderDTO thirdOrderDTO = new ThirdOrderDTO();
        BeanUtils.copyProperties(req, thirdOrderDTO);

        ThirdOrderUserDTO thirdOrderUserDTO = new ThirdOrderUserDTO();
        thirdOrderUserDTO.setUserName(req.getUsername());
        thirdOrderUserDTO.setMobile(req.getMobile());

        thirdOrderDTO.setThirdOrderUserDTO(thirdOrderUserDTO);
        thirdOrderDTO.setIsExcel(1);
        thirdOrderDTO.setPickupAddrType((byte)(thirdOrderDTO.getPickupAddrType() + 1));
        thirdOrderDTO.setReturnAddrType((byte)(thirdOrderDTO.getReturnAddrType() + 1));
        thirdOrderDTO.setOrderTime(System.currentTimeMillis());

        Result result = thirdOrderService.importExcelOrder(thirdOrderDTO);
    }

    @Test
    public void testCreateRerentOrderV2() {
        String str =
                "{\"discountAmount\":1800,\"mainSourceOrderId\":\"2858303626216\",\"merchantId\":29,\"orderSource\":6,\"payAmount\":71000,\"receivableAmount\":72800,\"rerentTime\":1715349600000,\"serviceItemList\":[{\"calPer\":1.0,\"code\":\"03001\",\"name\":\"手续费\",\"price\":2200.0,\"quantity\":1.0,\"totalAmount\":2200.0},{\"calPer\":4.57,\"code\":\"04001\",\"name\":\"租车费\",\"price\":11200.0,\"quantity\":4.04,\"totalAmount\":51200.0},{\"calPer\":4.57,\"code\":\"02001\",\"name\":\"基本保障服务费\",\"price\":18400.0,\"quantity\":4.04,\"totalAmount\":18400.0},{\"calPer\":1.0,\"code\":\"01004\",\"name\":\"夜间服务费\",\"price\":1000.0,\"quantity\":1.0,\"totalAmount\":1000.0}]}";
        ThirdRerentOrderDTO thirdRerentOrderDTO = JSON.parseObject(str, ThirdRerentOrderDTO.class);
        Result result = thirdOrderService.createRerentOrderV2(thirdRerentOrderDTO);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void test() {
        UpdateOrderReq updateOrderReq = new UpdateOrderReq();
        String str = "{\"amountMargin\":false,\"hourlyChargeSnapShot\":[],\"payAmount\":9600,\"pickupAddr\":\"杭州东站\",\"pickupDate\":\"2025-06-03 10:00:00\",\"pickupLongLatVo\":{\"latitude\":30.29145,\"longitude\":120.21307},\"receivableAmount\":9600,\"returnAddr\":\"杭州东站\",\"returnDate\":\"2025-06-06 17:45:00\",\"returnLongLatVo\":{\"latitude\":30.29145,\"longitude\":120.21307},\"saasAmount\":16800,\"serviceItemDTOList\":[{\"amount\":7000,\"code\":\"04001\",\"includeInTotalAmount\":1,\"name\":\"租车费\",\"type\":0},{\"amount\":400,\"code\":\"02001\",\"includeInTotalAmount\":1,\"name\":\"基本保障服务费\",\"type\":2},{\"amount\":2000,\"code\":\"04001\",\"includeInTotalAmount\":1,\"name\":\"租车费\",\"type\":0},{\"amount\":200,\"code\":\"03001\",\"includeInTotalAmount\":1,\"name\":\"手续费\",\"type\":3}],\"thirdOrderId\":\"M7476480240496873472\"}";
        updateOrderReq = JSON.parseObject(str, UpdateOrderReq.class);
        System.out.println(JSON.toJSONString(updateOrderReq));
    }



    @Test
    public void testCreateRerentOrderV3() {
        String str =
                "{\"discountAmount\":0,\"mainSourceOrderId\":\"M7472444581767270400\",\"merchantId\":29,\"noWorriedOrder\":false,\"orderId\":1705229,\"orderSource\":7,\"payAmount\":300,\"receivableAmount\":300,\"rerentTime\":1750420800000,\"serviceItemList\":[{\"amount\":0.0,\"code\":\"04001\",\"name\":\"租车费\",\"platformCal\":false},{\"amount\":0.0,\"code\":\"02001\",\"name\":\"基本保障服务费\",\"platformCal\":false},{\"amount\":0.0,\"code\":\"02003\",\"name\":\"优享服务费\",\"platformCal\":false},{\"amount\":300.0,\"code\":\"04001\",\"name\":\"租车费\",\"platformCal\":false},{\"amount\":0.0,\"code\":\"01002\",\"name\":\"上门还车服务费\",\"platformCal\":false},{\"amount\":0.0,\"code\":\"01005\",\"name\":\"夜间还车服务费\",\"platformCal\":false}],\"sourceOrderId\":\"R7472472266119827456\"}";
        ThirdRerentOrderDTO thirdRerentOrderDTO = JSON.parseObject(str, ThirdRerentOrderDTO.class);
        Result result = thirdOrderService.createRerentOrderV3(thirdRerentOrderDTO);
        System.out.println(JSON.toJSONString(result));
    }




    @Test
    public void testCancelRerentOrderV2() {
//        Result result = thirdOrderService.cancelRerentOrderV2("2024013001", 6L);
//        System.out.println(JSON.toJSONString(result));
        double a = 4.04d;
        double b = 3d;
        int daysA = (int) a;
        int daysB = (int) b;
//        int hoursA = (int) ((a - daysA) * 100);
//        int hoursB = (int) ((b - daysB) * 100);
        int hoursA = BigDecimal.valueOf(a * 100).subtract(BigDecimal.valueOf(daysA * 100)).intValue();
        int hoursB = BigDecimal.valueOf(b * 100).subtract(BigDecimal.valueOf(daysB * 100)).intValue();

        int totalDays = daysA - daysB;
        int totalHours = hoursA - hoursB;

        if (totalHours < 0) {
            totalDays -= 1;
            totalHours += 24;
        }

        if (totalDays < 0) {
            totalDays = 0;
            totalHours = 0;
        }

        System.out.println(totalDays + (double) totalHours / 100);
    }

    @Test
    public void testGetInsuranceList() {
        Result result = thirdOrderService.getInsuranceList(7l, 29L, "2024030301");
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testOrderSnapshot() {
        Result result = orderFixDataService.addOrderSnapshot(124416l);
    }

    @Test
    public void testFndWukongErrorAmountOrderIds() {
        Result result = orderFixDataService.findWukongErrorAmountOrderIds(1L, 1696867200000L, 1728489600000L);
        System.out.println(JSON.toJSONString(result));
    }



    @Test
    public void testFixData() {
        double pickupLongitude = 19.93966d; double pickupLatitude = 110.469366d;
        GaodeAddressDTO pickupGaodeAddressDTO = gaodeComponent.regeo(pickupLongitude, pickupLatitude);
        if (pickupGaodeAddressDTO != null) {
            String pickupStr = pickupGaodeAddressDTO.getProvince() + "||" + pickupGaodeAddressDTO.getCity() + "||"
                    + pickupGaodeAddressDTO.getDistrict() + "||" + pickupGaodeAddressDTO.getTownship() + "||"
                    + pickupGaodeAddressDTO.getAddress();
            System.out.println(pickupStr);
        }
    }

    @Test
    public void test6() {

        Map<String, String> map = ServiceItemEnum.CtripMapping.getForwardMapping();

        ThirdOrderDTO thirdOrderDTO = new ThirdOrderDTO();
        List<StandardFeeDTO> standardFeeList = JSON.parseArray("[{\"feeAmount\":164.00,\"feeCode\":\"04001\",\"quantity\":3,\"unitAmount\":68.00},{\"feeAmount\":20.00,\"feeCode\":\"03001\",\"quantity\":1,\"unitAmount\":20.00},{\"feeAmount\":120.00,\"feeCode\":\"02001\",\"quantity\":3,\"unitAmount\":50.00}]", StandardFeeDTO.class);
        thirdOrderDTO.setStandardFeeList(standardFeeList);
        List<ServiceItemAmountDTO> serviceItemAmountList = JSON.parseArray("[{\"amount\":0,\"calPer\":1.0,\"code\":\"01001\",\"description\":\"\",\"id\":890,\"includeInTotalAmount\":1,\"name\":\"送车上门服务费\",\"onCharge\":1,\"price\":0,\"quantity\":1.0,\"required\":1,\"type\":5,\"unit\":\"次\"},{\"amount\":0,\"calPer\":1.0,\"code\":\"01002\",\"description\":\"\",\"id\":890,\"includeInTotalAmount\":1,\"name\":\"上门还车服务费\",\"onCharge\":1,\"price\":0,\"quantity\":1.0,\"required\":1,\"type\":5,\"unit\":\"次\"},{\"amount\":3000,\"calPer\":1.0,\"code\":\"02001\",\"description\":\"\",\"id\":744,\"includeInTotalAmount\":1,\"name\":\"基本保障服务费\",\"onCharge\":1,\"onHighestPrice\":0,\"price\":3000,\"quantity\":1.0,\"required\":1,\"settingId\":75,\"type\":2,\"unit\":\"天\"},{\"amount\":4000,\"calPer\":1.0,\"code\":\"02003\",\"description\":\"\",\"id\":745,\"includeInTotalAmount\":1,\"name\":\"优享服务费\",\"onCharge\":1,\"onHighestPrice\":0,\"price\":4000,\"quantity\":1.0,\"required\":0,\"settingId\":76,\"type\":2,\"unit\":\"天\"},{\"amount\":8000,\"calPer\":1.0,\"code\":\"02002\",\"description\":\"\",\"id\":746,\"includeInTotalAmount\":0,\"name\":\"尊享服务费\",\"onCharge\":1,\"onHighestPrice\":0,\"price\":8000,\"quantity\":1.0,\"required\":0,\"settingId\":77,\"type\":2,\"unit\":\"天\"},{\"amount\":2000,\"calPer\":1.0,\"code\":\"03001\",\"description\":\"\",\"id\":941,\"includeInTotalAmount\":1,\"name\":\"手续费\",\"onCharge\":1,\"price\":2000,\"quantity\":1.0,\"required\":1,\"settingId\":53,\"type\":3,\"unit\":\"次\"},{\"amount\":1500,\"calPer\":1.0,\"code\":\"03002\",\"description\":\"\",\"includeInTotalAmount\":1,\"name\":\"儿童座椅\",\"onCharge\":1,\"price\":1500,\"quantity\":1.0,\"required\":1,\"settingId\":54,\"type\":3,\"unit\":\"次\"},{\"amount\":9800,\"calPer\":1.0,\"code\":\"04001\",\"includeInTotalAmount\":1,\"name\":\"租车费\",\"onCharge\":1,\"price\":9800,\"quantity\":1.0,\"required\":1,\"type\":0,\"unit\":\"天\"}]", ServiceItemAmountDTO.class);
        replaceServiceItemDTOS(thirdOrderDTO, serviceItemAmountList);
        System.out.println(JSON.toJSONString(thirdOrderDTO.getServiceItemDTOList()));

    }

    @Test
    public void test7() {
        SettlementOrderRequest settlementOrderRequest = new SettlementOrderRequest();
        settlementOrderRequest.setThirdOrderId("2853276064004");
        settlementOrderRequest.setChannelId(OrderSourceEnum.WUKONG.getSource().longValue());
        Result result = thirdOrderService.settlementOrder(settlementOrderRequest);
        System.out.println(JSON.toJSONString(result));
    }


    private void replaceServiceItemDTOS(ThirdOrderDTO thirdOrderDTO,  List<ServiceItemAmountDTO> serviceItemAmountList) {
        if (CollectionUtils.isNotEmpty(thirdOrderDTO.getStandardFeeList()) && CollectionUtils.isNotEmpty(serviceItemAmountList)) {
            Map<String, ServiceItemAmountDTO> serviceItemDTOMap = serviceItemAmountList.stream().collect(Collectors.toMap(ServiceItemAmountDTO::getCode, item -> item));
            List<ServiceItemAmountDTO> serviceItemDTOS = new ArrayList<>();
            for (StandardFeeDTO item : thirdOrderDTO.getStandardFeeList()) {
                ServiceItemAmountDTO serviceItemAmountDTO = new ServiceItemAmountDTO();
                serviceItemAmountDTO.setAmount(item.getFeeAmount().intValue() * 100);
                serviceItemAmountDTO.setPrice(item.getUnitAmount().intValue() * 100);
                serviceItemAmountDTO.setCount(item.getQuantity().intValue());
                serviceItemAmountDTO.setIncludeInTotalAmount(1);
                serviceItemAmountDTO.setQuantity(item.getQuantity().doubleValue());
                serviceItemAmountDTO.setCode(item.getFeeCode());
                serviceItemAmountDTO.setCalPer(item.getFeeAmount().divide(item.getUnitAmount(), 2, RoundingMode.UP).doubleValue());

//                String serviceItemCode = ServiceItemEnum.CtripMapping.getBackwardMapping().get(item.getFeeCode());
                if (serviceItemDTOMap.get(item.getFeeCode()) != null) {
                    ServiceItemAmountDTO serviceItemAmountDTOFromMap = serviceItemDTOMap.get(item.getFeeCode());
                    serviceItemAmountDTO.setName(serviceItemAmountDTOFromMap.getName());
                    serviceItemAmountDTO.setUnit(serviceItemAmountDTOFromMap.getUnit());
                    serviceItemAmountDTO.setId(serviceItemAmountDTOFromMap.getId());
                    serviceItemAmountDTO.setType(serviceItemAmountDTOFromMap.getType());
                    serviceItemDTOS.add(serviceItemAmountDTO);
                } else {
                    Map<String, String> map = ServiceItemEnum.CtripMapping.getBackwardMapping();
                    if (map.get(item.getFeeCode()) != null) {
                        ServiceItemEnum.ServiceItemCode serviceItemCode = ServiceItemEnum.CtripMapping.getItemCodeByCode(map.get(item.getFeeCode()));
                        serviceItemAmountDTO.setName(serviceItemCode.getDesc());
                        serviceItemAmountDTO.setUnit("次");
                        serviceItemAmountDTO.setId(0L);
                        serviceItemAmountDTO.setType(serviceItemCode.getType().byteValue());
                        serviceItemDTOS.add(serviceItemAmountDTO);
                    } else {
                        System.out.println(000);
                    }
                }
            }
            thirdOrderDTO.setServiceItemDTOList(serviceItemDTOS);
        }

    }

    @Test
    public void test8() {
        String filePath = "C:\\Users\\<USER>\\Desktop\\测试用\\工作簿3.xlsx"; // 替换为实际文件路径
        int sheetIndex = 0; // 工作表索引 (0表示第一个工作表)
        int headerRows = 0; // 表头行数 (根据实际调整)
        int columnIndex = 0; // 要提取的列索引 (A列=0, B列=1...)

        List<String> columnData = extractFirstColumn(filePath, sheetIndex, headerRows, columnIndex);

        // 打印结果
        System.out.println("提取到 " + columnData.size() + " 条数据：");
        System.out.println(JSON.toJSONString(columnData));
    }

    public static List<String> extractFirstColumn(
            String filePath,
            int sheetIndex,
            int headerRows,
            int columnIndex) {

        List<String> result = new ArrayList<>();

        try (FileInputStream file = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(file)) {

            Sheet sheet = workbook.getSheetAt(sheetIndex);

            // 跳过表头，从数据行开始处理
            for (int i = headerRows; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue; // 跳过空行

                Cell cell = row.getCell(columnIndex);
                if (cell == null) continue; // 跳过空单元格

                // 根据单元格类型处理数据
                String cellValue;

                cellValue = cell.getStringCellValue().trim();

                if (!cellValue.isEmpty()) {
                    result.add(cellValue);
                }
            }

        } catch (Exception e) {
            throw new RuntimeException("解析Excel文件时出错: " + e.getMessage(), e);
        }

        return result;
    }
}
