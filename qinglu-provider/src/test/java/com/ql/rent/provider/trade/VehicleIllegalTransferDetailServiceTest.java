package com.ql.rent.provider.trade;

import com.ql.rent.dao.trade.VehicleIllegalTransferDetailMapper;
import com.ql.rent.dao.merchant.TrafficManagementBindingMapper;
import com.ql.rent.dao.merchant.TrafficManagementBindingDetailMapper;
import com.ql.rent.entity.trade.VehicleIllegalTransferDetail;
import com.ql.rent.entity.trade.VehicleIllegalTransferDetailExample;
import com.ql.rent.entity.trade.MallServiceOrderInfo;
import com.ql.rent.entity.merchant.TrafficManagementBinding;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.trade.MallServiceOrderStatusEnum;
import com.ql.rent.service.trade.IMallServiceOrderInfoService;
import com.ql.rent.service.vehicle.IVehicleInfoService;
import com.ql.rent.service.merchant.TrafficManagementBindService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.trade.MallServiceOrderInfoDTO;
import com.ql.rent.vo.trade.MallItemServiceGroupDTO;
import com.ql.rent.vo.trade.VehicleIllegalTransferDetailVo;
import com.ql.rent.vo.trade.VehicleIllegalTransferImportResultVO;
import com.ql.rent.vo.vehicle.VehicleInfoVO;
import com.ql.rent.param.trade.TransferVehicleBindQuery;
import com.ql.rent.param.vehicle.VehicleInfoQueryParam;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * VehicleIllegalTransferDetailService 单元测试
 */
@ExtendWith(MockitoExtension.class)
class VehicleIllegalTransferDetailServiceTest {

    @Mock
    private VehicleIllegalTransferDetailMapper vehicleIllegalTransferDetailMapper;

    @Mock
    private IMallServiceOrderInfoService mallServiceOrderInfoService;

    @Mock
    private IVehicleInfoService vehicleInfoService;

    @Mock
    private TrafficManagementBindService trafficManagementBindService;

    @Mock
    private TrafficManagementBindingMapper trafficManagementBindingMapper;

    @Mock
    private TrafficManagementBindingDetailMapper trafficManagementBindingDetailMapper;

    @InjectMocks
    private VehicleIllegalTransferDetailService vehicleIllegalTransferDetailService;

    private LoginVo loginVo;
    private MallServiceOrderInfoDTO mockOrderInfo;
    private List<VehicleInfoVO> mockVehicleList;

    @BeforeEach
    void setUp() {
        // 设置登录用户
        loginVo = new LoginVo();
        loginVo.setUserId(1L);
        loginVo.setMerchantId(100L);
        loginVo.setLoginName("testuser");

        // 设置模拟订单信息
        mockOrderInfo = new MallServiceOrderInfoDTO();
        mockOrderInfo.setId(1001L);
        mockOrderInfo.setMerchantId(100L);
        mockOrderInfo.setOrderStatus(MallServiceOrderStatusEnum.ALL_PAID.getStatus());
        mockOrderInfo.setExpirationDate(new Date(System.currentTimeMillis() + 86400000L)); // 明天过期
        mockOrderInfo.setItemCount(10);

        // 设置模拟车辆信息
        mockVehicleList = new ArrayList<>();
        VehicleInfoVO vehicle1 = new VehicleInfoVO();
        vehicle1.setId(1L);
        vehicle1.setLicense("京A12345");
        mockVehicleList.add(vehicle1);

        VehicleInfoVO vehicle2 = new VehicleInfoVO();
        vehicle2.setId(2L);
        vehicle2.setLicense("京B67890");
        mockVehicleList.add(vehicle2);
    }

    @Test
    void testSaveTransferVehicleBind_Success() {
        // 准备测试数据
        List<Long> vehicleIds = Arrays.asList(1L, 2L);
        Long orderId = 1001L;

        // 准备Mock的Service Group数据
        List<MallItemServiceGroupDTO> serviceGroupList = new ArrayList<>();
        MallItemServiceGroupDTO serviceGroup = new MallItemServiceGroupDTO();
        
        List<MallServiceOrderInfoDTO> serviceOrderList = new ArrayList<>();
        MallServiceOrderInfoDTO serviceOrder = new MallServiceOrderInfoDTO();
        serviceOrder.setId(orderId);
        serviceOrder.setItemSubPackage("ILLEGAL_TRANSFER"); // 设置为违章转移类型
        serviceOrderList.add(serviceOrder);
        
        serviceGroup.setServiceOrderList(serviceOrderList);
        serviceGroupList.add(serviceGroup);

        // Mock 依赖服务
        when(mallServiceOrderInfoService.detail(loginVo.getMerchantId(), orderId, null))
                .thenReturn(mockOrderInfo);
        when(mallServiceOrderInfoService.availableItemOrderList(any(), any()))
                .thenReturn(serviceGroupList);
        when(vehicleIllegalTransferDetailMapper.selectByExample(any()))
                .thenReturn(Collections.emptyList());
        when(vehicleIllegalTransferDetailMapper.batchInsert(any()))
                .thenReturn(2);

        // 执行测试
        Result<Boolean> result = vehicleIllegalTransferDetailService.saveTransferVehicleBind(
                loginVo, vehicleIds, orderId);

        // 验证结果
        assertTrue(result.isSuccess());
        assertTrue(result.getModel());

        // 验证调用
        verify(mallServiceOrderInfoService).detail(loginVo.getMerchantId(), orderId, null);
        verify(vehicleIllegalTransferDetailMapper).batchInsert(any());
    }

    @Test
    void testSaveTransferVehicleBind_OrderNotPaid() {
        // 准备测试数据
        List<Long> vehicleIds = Arrays.asList(1L, 2L);
        Long orderId = 1001L;

        // 设置订单为未支付状态
        mockOrderInfo.setOrderStatus((byte) 0);

        // Mock 依赖服务
        when(mallServiceOrderInfoService.detail(loginVo.getMerchantId(), orderId, null))
                .thenReturn(mockOrderInfo);

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            vehicleIllegalTransferDetailService.saveTransferVehicleBind(loginVo, vehicleIds, orderId);
        });
    }

    @Test
    void testListTransferVehicleBind_Success() {
        // 准备测试数据
        TransferVehicleBindQuery query = new TransferVehicleBindQuery();
        query.setMerchantId(100L);
        query.setOrderId(1001L);

        List<VehicleIllegalTransferDetail> mockDetails = new ArrayList<>();
        VehicleIllegalTransferDetail detail = new VehicleIllegalTransferDetail();
        detail.setId(1L);
        detail.setOrderId(1001L);
        detail.setVehicleId(1L);
        detail.setMerchantId(100L);
        mockDetails.add(detail);

        // Mock 依赖服务
        when(vehicleIllegalTransferDetailMapper.selectByExample(any()))
                .thenReturn(mockDetails);

        // 执行测试
        Result<List<VehicleIllegalTransferDetailVo>> result = 
                vehicleIllegalTransferDetailService.listTransferVehicleBind(query);

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getModel());
        assertEquals(1, result.getModel().size());
        assertEquals(1L, result.getModel().get(0).getId());
    }

    @Test
    void testImportTransferVehicleBind_EmptyFile() {
        // 准备测试数据
        MultipartFile emptyFile = new MockMultipartFile("file", "", "text/plain", new byte[0]);
        Long orderId = 1001L;

        // 执行测试
        Result<VehicleIllegalTransferImportResultVO> result = 
                vehicleIllegalTransferDetailService.importTransferVehicleBind(emptyFile, orderId, loginVo);

        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals("请选择要导入的文件", result.getMessage());
    }

    @Test
    void testImportTransferVehicleBind_NullOrderId() {
        // 准备测试数据
        MultipartFile file = new MockMultipartFile("file", "test.xlsx", 
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
                "test content".getBytes());

        // 执行测试
        Result<VehicleIllegalTransferImportResultVO> result = 
                vehicleIllegalTransferDetailService.importTransferVehicleBind(file, null, loginVo);

        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals("订单ID不能为空", result.getMessage());
    }

    @Test
    void testImportTransferVehicleBind_ValidFile() {
        // 准备Excel文件内容（模拟）
        String excelContent = "企业账号,企业名称,绑定车牌号\ntest123,测试企业,京A12345，京B67890";
        
        MultipartFile file = new MockMultipartFile("file", "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                excelContent.getBytes());
        Long orderId = 1001L;

        // 由于EasyExcel的复杂性，这里主要测试参数验证部分
        // 实际测试会因为Excel解析失败而返回错误，这是预期的行为
        Result<VehicleIllegalTransferImportResultVO> result = 
                vehicleIllegalTransferDetailService.importTransferVehicleBind(file, orderId, loginVo);

        // 验证结果 - 预期会失败，因为模拟的Excel内容无法被正确解析
        assertFalse(result.isSuccess());
        assertNotNull(result.getMessage());
    }

    @Test
    void testSaveTransferVehicleBind_ExceedsItemCount() {
        // 准备测试数据 - 车辆数量超过订单限制
        List<Long> vehicleIds = new ArrayList<>();
        for (int i = 1; i <= 15; i++) { // 超过订单限制的10辆
            vehicleIds.add((long) i);
        }
        Long orderId = 1001L;

        // Mock 依赖服务
        when(mallServiceOrderInfoService.detail(loginVo.getMerchantId(), orderId, null))
                .thenReturn(mockOrderInfo);

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            vehicleIllegalTransferDetailService.saveTransferVehicleBind(loginVo, vehicleIds, orderId);
        });
    }

    @Test
    void testSaveTransferVehicleBind_ExpiredOrder() {
        // 准备测试数据
        List<Long> vehicleIds = Arrays.asList(1L, 2L);
        Long orderId = 1001L;

        // 设置订单为已过期
        mockOrderInfo.setExpirationDate(new Date(System.currentTimeMillis() - 86400000L)); // 昨天过期

        // Mock 依赖服务
        when(mallServiceOrderInfoService.detail(loginVo.getMerchantId(), orderId, null))
                .thenReturn(mockOrderInfo);

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            vehicleIllegalTransferDetailService.saveTransferVehicleBind(loginVo, vehicleIds, orderId);
        });
    }

    @Test
    void testListTransferVehicleBind_NullMerchantId() {
        // 准备测试数据
        TransferVehicleBindQuery query = new TransferVehicleBindQuery();
        query.setMerchantId(null); // 商户ID为空

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            vehicleIllegalTransferDetailService.listTransferVehicleBind(query);
        });
    }

    @Test
    void testListTransferVehicleBind_EmptyResult() {
        // 准备测试数据
        TransferVehicleBindQuery query = new TransferVehicleBindQuery();
        query.setMerchantId(100L);

        // Mock 依赖服务返回空结果
        when(vehicleIllegalTransferDetailMapper.selectByExample(any()))
                .thenReturn(Collections.emptyList());

        // 执行测试
        Result<List<VehicleIllegalTransferDetailVo>> result = 
                vehicleIllegalTransferDetailService.listTransferVehicleBind(query);

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getModel());
        assertTrue(result.getModel().isEmpty());
    }
} 