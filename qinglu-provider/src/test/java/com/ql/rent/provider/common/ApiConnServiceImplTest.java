package com.ql.rent.provider.common;

import com.alibaba.fastjson.JSON;
import com.ql.rent.api.aggregate.model.remote.ctrip.dto.CtripStandardFeeStatusDTO;
import com.ql.rent.dao.common.ApiConnMapper;
import com.ql.rent.service.common.IApiConnService;
import com.ql.rent.share.result.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ApiConnServiceImpl测试类
 */
@SpringBootTest
public class ApiConnServiceImplTest {

//    @InjectMocks
//    private ApiConnServiceImpl apiConnService;

//    @Mock
//    private ApiConnMapper apiConnMapper;

    @Resource
    private IApiConnService apiConnService;

    // 这里需要添加ApiConnServiceImpl中依赖的服务或组件
    // @Mock
    // private SomeDependency dependency;

//    @BeforeEach
//    public void setUp() {
//        MockitoAnnotations.openMocks(this);
//    }
//
//    /**
//     * 测试更新携程标准状态方法
//     */
//    @Test
//    public void testUpdateCtripStandardStatus() {
//        // 准备测试数据 - 根据CtripStandardFeeStatusDTO结构生成JSON
//        String jsonData = "[{\"switchType\":\"saasUpload\",\"swicth\":true,\"grayVendor\":\"22222,33333\",\"blackVendor\":\"44444,55555\"}]";
//
//        // 如果有依赖服务，配置mock行为
//        // when(dependency.someMethod(any())).thenReturn(expectedResult);
//
//        // 执行被测试方法
//        Result<Boolean> result = apiConnService.updateCtripStandardStatus(jsonData);
//
//        // 验证结果
//        assertTrue(result.isSuccess(), "更新携程标准状态应该成功");
//        assertTrue(result.getModel(), "更新结果应该为true");
//
//        // 验证依赖方法是否被正确调用
//        // verify(dependency, times(1)).someMethod(any());
//    }
//
//    /**
//     * 测试更新携程标准状态方法 - 空数据情况
//     */
//    @Test
//    public void testUpdateCtripStandardStatus_EmptyData() {
//        // 准备空数据
//        String emptyJson = "[]";
//
//        // 执行被测试方法
//        Result<Boolean> result = apiConnService.updateCtripStandardStatus(emptyJson);
//
//        // 验证结果
//        assertFalse(result.isSuccess() && result.getModel(), "空数据更新应该返回失败");
//    }
//
//    /**
//     * 测试更新携程标准状态方法 - 无效数据情况
//     */
//    @Test
//    public void testUpdateCtripStandardStatus_InvalidData() {
//        // 准备无效数据
//        String invalidJson = "invalid json data";
//
//        // 执行被测试方法并验证异常
//        Exception exception = assertThrows(RuntimeException.class, () -> {
//            apiConnService.updateCtripStandardStatus(invalidJson);
//        });
//
//        // 验证异常信息
//        assertTrue(exception.getMessage().contains("解析异常") ||
//                  exception.getMessage().contains("格式错误"),
//                  "应该抛出JSON解析相关的异常");
//    }
//
//    /**
//     * 测试更新携程标准状态方法 - 异常情况
//     */
//    @Test
//    public void testUpdateCtripStandardStatus_Exception() {
//        // 准备测试数据
//        String jsonData = "[{\"switchType\":\"saasUpload\",\"swicth\":true,\"grayVendor\":\"22222,33333\",\"blackVendor\":\"44444,55555\"}]";
//
//        // 如果有依赖服务，配置mock行为抛出异常
//        // when(dependency.someMethod(any())).thenThrow(new RuntimeException("模拟异常"));
//
//        // 执行被测试方法并验证异常
//        Exception exception = assertThrows(RuntimeException.class, () -> {
//            apiConnService.updateCtripStandardStatus(jsonData);
//        });
//
//        // 验证异常信息
//        assertTrue(exception.getMessage().contains("模拟异常"), "应该抛出预期的异常");
//    }

    @Test
    public void updateCtripStandardStatus() {
        String content ="";

        List<CtripStandardFeeStatusDTO> statusList = new ArrayList<>();
        CtripStandardFeeStatusDTO dto = null;
//        dto = new CtripStandardFeeStatusDTO();
//        dto.setSwitchType("saasUpload");
//        dto.setSwitchStatus(true);
//        dto.setBlackVendor("37573");
//        dto.setGrayVendor("");
//        statusList.add(dto);
//
//        dto = new CtripStandardFeeStatusDTO();
//        dto.setSwitchType("saasUpload");
//        dto.setSwitchStatus(false);
//        dto.setBlackVendor("");
//        dto.setGrayVendor("");
//        statusList.add(dto);

        dto = new CtripStandardFeeStatusDTO();
        dto.setSwitchType("saasApi");
        dto.setSwitchStatus(false);
        dto.setBlackVendor("37573");
        dto.setGrayVendor("37573");
        statusList.add(dto);

//        dto = new CtripStandardFeeStatusDTO();
//        dto.setSwitchType("saasUpload");
//        dto.setSwitchStatus(false);
//        dto.setBlackVendor("37573");
//        dto.setGrayVendor("37573,30105");
//        statusList.add(dto);

//        dto = new CtripStandardFeeStatusDTO();
//        dto.setSwitchType("saasUpload");
//        dto.setSwitchStatus(true);
//        dto.setBlackVendor("37573");
//        dto.setGrayVendor("");
//        statusList.add(dto);

        apiConnService.updateCtripStandardStatus(JSON.toJSONString(statusList));
    }

} 