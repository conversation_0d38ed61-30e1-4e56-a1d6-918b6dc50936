package com.ql.rent.provider.vehicle;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ql.rent.api.aggregate.remote.ctrip.api.CtripApiClient;
import com.ql.rent.component.CtripRequestSignBuilder;
import com.ql.rent.service.vehicle.ICtripVehicleService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import java.util.Arrays;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023/8/1 14:46
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@RunWith(SpringRunner.class)
public class CtripVehicleServiceImplTests {
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private CtripRequestSignBuilder ctripRequestSignBuilder;
    @Resource
    private CtripApiClient ctripApiClient;
    @Resource
    private ICtripVehicleService ctripVehicleService;

    @Test
    public void getUsageInfoTest() throws JsonProcessingException {
        long merchantId = 41l;
//        GetUsageInfoRequest getUsageInfoRequest = new GetUsageInfoRequest(null, storeId, Collections.singletonList("沪ETE837"), localTime);
//        SignRequest sign = ctripRequestSignBuilder.build(merchantId, getUsageInfoRequest);
//        System.err.println(objectMapper.writeValueAsString(sign));
//        GetUsageInfoResp usageInfo = ctripApiClient.getUsageInfo(sign);
//        Assertions.assertThat(usageInfo.getUsageInfoList()).isNotEmpty();
        System.err.println(ctripVehicleService.getUsageInfo(41L));
    }

    @Test
    public void pushStock() {
        ctripVehicleService.pushStock(Arrays.asList(5044L), 43L);
    }
}
