package com.ql.rent.provider.vehicle;

import com.ql.rent.dao.vehicle.VehicleWorkOrderExpensePropMapper;
import com.ql.rent.entity.vehicle.VehicleWorkOrderExpenseProp;
import com.ql.rent.entity.vehicle.VehicleWorkOrderExpensePropExample;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.vehicle.VehicleWorkOrderExpensePropVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class VehicleWorkOrderExpensePropServiceImplTest {

  @Mock
  private VehicleWorkOrderExpensePropMapper vehicleWorkOrderExpensePropMapper;

  @InjectMocks
  private VehicleWorkOrderExpensePropServiceImpl vehicleWorkOrderExpensePropService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Nested
  class ListWorkOrderExpensePropTests {
    @Test
    void shouldReturnFailWhenMerchantIdIsNull() {
      Result<List<VehicleWorkOrderExpensePropVO>> result = vehicleWorkOrderExpensePropService
          .listWorkOrderExpenseProp(null);

      assertFalse(result.isSuccess());
      assertEquals("查询车辆工单费用项选项失败", result.getMessage());
    }

    @Test
    void shouldReturnEmptyListWhenNoExpensePropFound() {
      when(vehicleWorkOrderExpensePropMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      Result<List<VehicleWorkOrderExpensePropVO>> result = vehicleWorkOrderExpensePropService
          .listWorkOrderExpenseProp(1L);

      assertTrue(result.isSuccess());
      assertTrue(result.getModel().isEmpty());
    }

    @Test
    void shouldReturnExpensePropList() {
      VehicleWorkOrderExpenseProp prop1 = createExpenseProp(1L, "费用1");
      VehicleWorkOrderExpenseProp prop2 = createExpenseProp(2L, "费用2");

      when(vehicleWorkOrderExpensePropMapper.selectByExample(any()))
          .thenReturn(Arrays.asList(prop1, prop2));

      Result<List<VehicleWorkOrderExpensePropVO>> result = vehicleWorkOrderExpensePropService
          .listWorkOrderExpenseProp(1L);

      assertTrue(result.isSuccess());
      assertEquals(2, result.getModel().size());

      assertEquals(1L, result.getModel().get(0).getId());
      assertEquals("费用1", result.getModel().get(0).getExpenseName());
      assertEquals(2L, result.getModel().get(1).getId());
      assertEquals("费用2", result.getModel().get(1).getExpenseName());
    }
  }

  @Nested
  class SaveItemTests {
    @Test
    void shouldReturnFailWhenItemNameIsBlank() {
      Result<Integer> result = vehicleWorkOrderExpensePropService.saveItem("", 1L, 1L);

      assertFalse(result.isSuccess());
      assertEquals("新增工单费用项参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenMerchantIdIsNull() {
      Result<Integer> result = vehicleWorkOrderExpensePropService.saveItem("费用1", null, 1L);

      assertFalse(result.isSuccess());
      assertEquals("新增工单费用项参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenOpUserIdIsNull() {
      Result<Integer> result = vehicleWorkOrderExpensePropService.saveItem("费用1", 1L, null);

      assertFalse(result.isSuccess());
      assertEquals("新增工单费用项参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenInsertFails() {
      when(vehicleWorkOrderExpensePropMapper.insert(any())).thenReturn(0);

      Result<Integer> result = vehicleWorkOrderExpensePropService.saveItem("费用1", 1L, 1L);

      assertFalse(result.isSuccess());
      assertEquals("新增工单费用项参数失败", result.getMessage());
    }

    @Test
    void shouldSaveItemSuccessfully() {
      when(vehicleWorkOrderExpensePropMapper.insert(any())).thenReturn(1);

      Result<Integer> result = vehicleWorkOrderExpensePropService.saveItem("费用1", 1L, 1L);

      assertTrue(result.isSuccess());
      assertEquals(Integer.valueOf(1), result.getModel());

      ArgumentCaptor<VehicleWorkOrderExpenseProp> captor = ArgumentCaptor.forClass(VehicleWorkOrderExpenseProp.class);
      verify(vehicleWorkOrderExpensePropMapper).insert(captor.capture());
      VehicleWorkOrderExpenseProp saved = captor.getValue();
      assertEquals("费用1", saved.getExpenseName());
      assertEquals(1L, saved.getMerchantId());
      assertEquals(1L, saved.getOpUserId());
      assertEquals(YesOrNoEnum.NO.getValue(), saved.getDeleted());
    }
  }

  @Nested
  class DeleteItemTests {
    @Test
    void shouldReturnFailWhenIdIsNull() {
      Result<Integer> result = vehicleWorkOrderExpensePropService.deleteItem(null, 1L, 1L);

      assertFalse(result.isSuccess());
      assertEquals("新增工单费用项参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenMerchantIdIsNull() {
      Result<Integer> result = vehicleWorkOrderExpensePropService.deleteItem(1L, null, 1L);

      assertFalse(result.isSuccess());
      assertEquals("新增工单费用项参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenOpUserIdIsNull() {
      Result<Integer> result = vehicleWorkOrderExpensePropService.deleteItem(1L, 1L, null);

      assertFalse(result.isSuccess());
      assertEquals("新增工单费用项参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenItemNotFound() {
      when(vehicleWorkOrderExpensePropMapper.selectByPrimaryKey(1L)).thenReturn(null);

      Result<Integer> result = vehicleWorkOrderExpensePropService.deleteItem(1L, 1L, 1L);

      assertFalse(result.isSuccess());
      assertEquals("数据不存在，请刷新页面", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenItemAlreadyDeleted() {
      VehicleWorkOrderExpenseProp prop = createExpenseProp(1L, "费用1");
      prop.setDeleted(YesOrNoEnum.YES.getValue());
      when(vehicleWorkOrderExpensePropMapper.selectByPrimaryKey(1L)).thenReturn(prop);

      Result<Integer> result = vehicleWorkOrderExpensePropService.deleteItem(1L, 1L, 1L);

      assertFalse(result.isSuccess());
      assertEquals("数据不存在，请刷新页面", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenUpdateFails() {
      VehicleWorkOrderExpenseProp prop = createExpenseProp(1L, "费用1");
      when(vehicleWorkOrderExpensePropMapper.selectByPrimaryKey(1L)).thenReturn(prop);
      when(vehicleWorkOrderExpensePropMapper.updateByPrimaryKey(any())).thenReturn(0);

      Result<Integer> result = vehicleWorkOrderExpensePropService.deleteItem(1L, 1L, 1L);

      assertFalse(result.isSuccess());
      assertEquals("新增工单费用项参数失败", result.getMessage());
    }

    @Test
    void shouldDeleteItemSuccessfully() {
      VehicleWorkOrderExpenseProp prop = createExpenseProp(1L, "费用1");
      when(vehicleWorkOrderExpensePropMapper.selectByPrimaryKey(1L)).thenReturn(prop);
      when(vehicleWorkOrderExpensePropMapper.updateByPrimaryKey(any())).thenReturn(1);

      Result<Integer> result = vehicleWorkOrderExpensePropService.deleteItem(1L, 1L, 1L);

      assertTrue(result.isSuccess());
      assertEquals(Integer.valueOf(1), result.getModel());

      ArgumentCaptor<VehicleWorkOrderExpenseProp> captor = ArgumentCaptor.forClass(VehicleWorkOrderExpenseProp.class);
      verify(vehicleWorkOrderExpensePropMapper).updateByPrimaryKey(captor.capture());
      VehicleWorkOrderExpenseProp updated = captor.getValue();
      assertEquals(YesOrNoEnum.YES.getValue(), updated.getDeleted());
      assertEquals(1L, updated.getOpUserId());
    }
  }

  private VehicleWorkOrderExpenseProp createExpenseProp(Long id, String name) {
    VehicleWorkOrderExpenseProp prop = new VehicleWorkOrderExpenseProp();
    prop.setId(id);
    prop.setExpenseName(name);
    prop.setMerchantId(1L);
    prop.setDeleted(YesOrNoEnum.NO.getValue());
    prop.setCreateTime(System.currentTimeMillis());
    prop.setOpTime(System.currentTimeMillis());
    prop.setOpUserId(1L);
    return prop;
  }
}