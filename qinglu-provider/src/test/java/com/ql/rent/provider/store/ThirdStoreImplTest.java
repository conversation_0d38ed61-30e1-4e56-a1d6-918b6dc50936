package com.ql.rent.provider.store;

import com.alibaba.fastjson.JSON;
import com.ql.rent.api.aggregate.model.dto.*;
import com.ql.rent.api.aggregate.model.request.StoreSearchReq;
import com.ql.rent.api.aggregate.model.response.StoreSearchResp;
import com.ql.rent.param.common.PullDataTaskQuery;
import com.ql.rent.param.store.StoreThirdParam;
import com.ql.rent.service.price.IThirdPriceService;
import com.ql.rent.service.store.IThirdStoreService;
import com.ql.rent.service.vehicle.ICtripVehicleService;
import com.ql.rent.vo.store.StoreDistanceVo;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.CtripStoreVO;
import com.ql.rent.vo.store.thirdSync.ThirdStoreInfosVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
class ThirdStoreImplTest {

    @Resource
    private IThirdStoreService thirdStoreService;

    @Resource
    private IThirdPriceService thirdPriceService;

    @Resource
    private ICtripVehicleService ctripVehicleService;

    @Test
    public void storeSearch() {
        StoreSearchReq req = new StoreSearchReq();
        req.setPickUpCityId(121L);
        req.setReturnCityId(121L);
        PointDTO gis = new PointDTO();
        gis.setLongitude(120.127747);
        gis.setLatitude(30.285369);
        req.setPickUpPoint(gis);
        gis = new PointDTO();
        gis.setLongitude(120.126747);
        gis.setLatitude(30.286369);
        req.setReturnPoint(gis);

//        req.setPickUpStoreId(1000L);
//        req.setReturnStoreId(1000L);
//        req.setPickUpDeliveryServiceType(1);
//        req.setReturnDeliveryServiceType(1);
        String json = "{\n"
            + "    \"channelId\": 4,\n"
            + "    \"pickUpStoreId\": 1021,\n"
            + "    \"pickUpPoint\": {\n"
            + "        \"longitude\": 120.2126,\n"
            + "        \"latitude\": 30.290851\n"
            + "    },\n"
            + "    \"pickUpDeliveryServiceType\": 1,\n"
            + "    \"pickUpCityId\": 121,\n"
            + "    \"returnStoreId\": 1021,\n"
            + "    \"returnPoint\": {\n"
            + "        \"longitude\": 120.2126,\n"
            + "        \"latitude\": 30.290851\n"
            + "    },\n"
            + "    \"returnCityId\": 121,\n"
            + "    \"returnDeliveryServiceType\": 2\n"
            + "}";


        json = " {\"channelId\":4,\"pickUpStoreId\":1021,\"pickUpPoint\":{\"longitude\":120.2126,\"latitude\":30.290851},\"pickUpDeliveryServiceType\":-1,\"pickUpCityId\":121,\"returnStoreId\":1021,\"returnPoint\":{\"longitude\":120.2126,\"latitude\":30.290851},\"returnCityId\":121,\"returnDeliveryServiceType\":-1}";
        json = "{\"channelId\":3,\"pickUpPoint\":{\"longitude\":121.345416,\"latitude\":31.204532},\"pickUpCityId\":107,\"returnPoint\":{\"longitude\":121.345416,\"latitude\":31.204532},\"returnCityId\":107}";
        json = "{\"channelId\":3,\"pickUpPoint\":{\"longitude\":121.241236,\"latitude\":31.250378},\"pickUpCityId\":107,\"returnPoint\":{\"longitude\":121.241236,\"latitude\":31.250378},\"returnCityId\":107}";
        req = JSON.parseObject(json, StoreSearchReq.class);

        StoreSearchResp ret = thirdStoreService.storeSearch(1L,  req);
        System.out.println(JSON.toJSONString(ret));
    }

    @Test
    public void getCancelRule() {
        CancelPolicyDTO ret = thirdStoreService.getCancelRule(9L, new Date());
        System.out.println(JSON.toJSONString(ret));
    }

    @Test
    public void selectOptionalVehicleModels() throws Exception{
        List<StorePairDTO> pairDTOList = new ArrayList<>();
        StorePairDTO dto = new StorePairDTO();
        StoreCircleIdDTO pickupStore = new StoreCircleIdDTO();
        pickupStore.setId(375L);
        StoreCircleIdDTO returnStore = new StoreCircleIdDTO();
        returnStore.setId(375L);
        dto.setPickUpStore(pickupStore);
        dto.setReturnStore(returnStore);
        pairDTOList.add(dto);

//        dto = new StorePairDTO();
//        pickupStore = new StoreCircleIdDTO();
//        pickupStore.setId(1000L);
//        returnStore = new StoreCircleIdDTO();
//        returnStore.setId(1001L);
//        dto.setPickUpStore(pickupStore);
//        dto.setReturnStore(returnStore);
//        pairDTOList.add(dto);

        PointDTO pickUpGis = new PointDTO();
        PointDTO returnGis = new PointDTO();

        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        Date pickUpTime =  dateFormat.parse("2023-10-32 20:00");
        Date returnTime =  dateFormat.parse("2023-10-32 20:00");
//        List<StorePairUniDTO<List<VehicleModelAbbrDTO>>> listVoResult = thirdStoreService.selectOptionalVehicleModels(55L, 1L, pairDTOList,pickUpGis, returnGis, pickUpTime, returnTime, null);
//        System.out.println(JSON.toJSONString(listVoResult));
    }

//    @Test
//    public void selectServiceCirclePrice() throws Exception{
//        List<StorePairDTO> pairDTOList = new ArrayList<>();
//
//        StorePairDTO dto = new StorePairDTO();
//
//        StoreCircleIdDTO pickupStore = new StoreCircleIdDTO();
//        pickupStore.setId(2L);
//        List<Long> serviceCircleIdList = new ArrayList<>(Arrays.asList(-1L,1L,4L));
//        pickupStore.setServiceCircleIdList(serviceCircleIdList);
//        dto.setPickUpStore(pickupStore);
//
//        StoreCircleIdDTO returnStore = new StoreCircleIdDTO();
//        returnStore.setId(2L);
//        List<Long> serviceCircleIdList1 = new ArrayList<>(Arrays.asList(-1L,1L,4L));
//        returnStore.setServiceCircleIdList(serviceCircleIdList1);
//        dto.setReturnStore(returnStore);
//
//        pairDTOList.add(dto);
//
//        PointDTO pickUpGis = new PointDTO();
//        PointDTO returnGis = new PointDTO();
//
//        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
//        Date pickUpTime =  dateFormat.parse("2023-03-23 21:00");
//        Date returnTime =  dateFormat.parse("2023-03-24 21:00");
//
//        Long channelId = 2L;
//
//        List<StorePairServiceCircleDTO> listVoResult = thirdStoreService.selectServiceCirclePrice(channelId, pairDTOList, pickUpGis, returnGis, pickUpTime, returnTime);
//        System.out.println(JSON.toJSONString(listVoResult));
//    }

//    @Test
//    public void selectStoreService() throws Exception{
//        List<StorePairDTO> pairDTOList = new ArrayList<>();
//
//        StorePairDTO dto = new StorePairDTO();
//
//        StoreCircleIdDTO pickupStore = new StoreCircleIdDTO();
//        pickupStore.setId(3L);
//        List<Long> serviceCircleIdList = new ArrayList<>(Arrays.asList(-1L, 14L, 18L));
//        pickupStore.setServiceCircleIdList(serviceCircleIdList);
//        dto.setPickUpStore(pickupStore);
//
//        StoreCircleIdDTO returnStore = new StoreCircleIdDTO();
//        returnStore.setId(4L);
//        List<Long> serviceCircleIdList1 = new ArrayList<>(Arrays.asList(-1L, 14L, 18L));
//        returnStore.setServiceCircleIdList(serviceCircleIdList1);
//        dto.setReturnStore(returnStore);
//
//        pairDTOList.add(dto);
//
//        PointDTO pickUpGis = new PointDTO();
//        pickUpGis.setLongitude(121.333765);
//        pickUpGis.setLatitude(31.23909);
//
//        PointDTO returnGis = new PointDTO();
//        returnGis.setLongitude(121.333765);
//        returnGis.setLatitude(31.23909);
//
//        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
//        Date pickUpTime =  dateFormat.parse("2022-12-05 21:00");
//        Date returnTime =  dateFormat.parse("2022-12-11 22:00");
//
//        Long channelId = 2L;
//
//        List<StorePairUniDTO<List<ServiceItemDTO>>> listVoResult = thirdStoreService.selectStoreService(2L, channelId, pairDTOList,pickUpGis, returnGis, pickUpTime, returnTime);
//        System.out.println(JSON.toJSONString(listVoResult));
//    }

    @Test
    public void selectStoreNightFee() throws Exception{
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        Date returnTime =  dateFormat.parse("2023-08-13 22:00");
        Integer fee = thirdStoreService.selectStoreNightFee(23L,  1L, returnTime);
        System.out.println(JSON.toJSONString(fee));
    }

    @Test
    public void selectByDistance() throws Exception{
        List<StoreDistanceVo> listVoResult = thirdStoreService.selectByDistance(null,  10L, 1000L, 1L,120.15017,30.280134, 5000);
        System.out.println(JSON.toJSONString(listVoResult));
    }

    @Test
    public void selectStoreInfos() throws Exception{
        StoreThirdParam param = new StoreThirdParam();
        param.setMerchantId(57L);
        param.setChannelId(3L);
        param.setStoreId(486L);
        List<ThirdStoreInfosVo> list = thirdStoreService.selectStoreInfos(param);
        System.out.println(JSON.toJSONString(list));
    }

    @Test
    public void importStoreFormCtrip() throws Exception{
        CtripStoreVO storeVo =  thirdStoreService.importStoreFormCtrip("30027");

        thirdStoreService.importgetHourlyChargeFormCtrip(storeVo);
    }

    @Test
    public void importDiffStoreFormCtrip() throws Exception{
        thirdStoreService.importDiffStoreFormCtrip("30105");
    }

    @Test
    public void getChargeList() throws Exception{
        thirdStoreService.getChargeList(Arrays.asList(6L), 2L);
    }

    @Test
    public void importgetHourlyChargeFormCtrip() throws Exception{
        CtripStoreVO vo = new CtripStoreVO();
        vo.setVendorId(30105L);
        vo.setMerchantId(41L);
        vo.setStoreList(new ArrayList<>());
        thirdStoreService.importgetHourlyChargeFormCtrip(vo);
    }

    @Test
    public void loadSkuPriceDataFromCtrip() throws Exception{
        CtripStoreVO storeVo =  thirdStoreService.importStoreFormCtrip("61365");

        // 零散小时规则
        thirdStoreService.importgetHourlyChargeFormCtrip(storeVo);
        log.info("携程上货,零散小时;处理成功");

        // 车辆信息 和 多媒体信息
        ctripVehicleService.pullCtripVehicleInfo(storeVo);
        log.info("携程上货,车辆;处理成功");

        thirdPriceService.loadSkuPriceDataFromCtrip(storeVo);
//        thirdPriceService.loadPriceFilterDataFromCtrip(storeVo);


//        CtripStoreVO.StoreVO store = new CtripStoreVO.StoreVO();
//        store.setStoreId(107343L);
//        store.setCtripStoreCode("66527");
//        store.setCtripCityId(3330L);
//
//        CtripStoreVO storeVo = new CtripStoreVO();
//        storeVo.setMerchantId(44L);
//        storeVo.setVendorId(61365L);
//        storeVo.setStoreList(Arrays.asList(store));
//        thirdPriceService.loadSkuPriceDataFromCtrip(storeVo);
    }

    @Test
    public void loadPriceFilterDataFromCtripByJob() throws Exception{
        PullDataTaskQuery param = new PullDataTaskQuery();
        param.setOpUserId(294L);
        param.setVehicleModelId(1248L);
        param.setMerchantId(45L);
        //拉取全量数据
        thirdPriceService.loadPriceFilterDataFromCtripByJob(param);
    }

}