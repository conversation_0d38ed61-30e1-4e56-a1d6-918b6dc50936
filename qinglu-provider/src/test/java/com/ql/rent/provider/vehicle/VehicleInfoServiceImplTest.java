package com.ql.rent.provider.vehicle;

import com.alibaba.fastjson.JSON;
import com.ql.dto.vehicle.request.GetlVehicleRequest;
import com.ql.enums.VehicleInfoEnums;
import com.ql.rent.api.aggregate.model.request.CtripVehicleStatusNotifyReq;
import com.ql.rent.common.IRedisService;
import com.ql.rent.dao.vehicle.VehicleInfoMapper;
import com.ql.rent.dao.vehicle.VehicleInfoInsuranceMapper;
import com.ql.rent.dao.vehicle.VehicleModelMapper;
import com.ql.rent.dao.vehicle.ex.VehicleInfoMapperEx;
import com.ql.rent.entity.vehicle.VehicleInfo;
import com.ql.rent.entity.vehicle.VehicleInfoExample;
import com.ql.rent.enums.common.ResultEnum;
import com.ql.rent.service.price.IRentMainService;
import com.ql.rent.service.vehicle.ICtripVehicleService;
import com.ql.rent.service.vehicle.IVehicleInfoService;
import com.ql.rent.vo.vehicle.VehicleInfoVO;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.enums.YesOrNoEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("VehicleInfoServiceImpl Tests")
class VehicleInfoServiceImplTest {


    @Mock
    private VehicleInfoMapper vehicleInfoMapper;

    @Mock
    private VehicleInfoMapperEx vehicleInfoMapperEx;

    @Mock
    private VehicleModelMapper vehicleModelMapper;

    @Mock
    private VehicleInfoInsuranceMapper vehicleInfoInsuranceMapper;

    @Mock
    private IRedisService redisServer;

    @Mock
    private ICtripVehicleService ctripVehicleServiceMock;

    @Mock
    private IRentMainService rentMainService;

    @InjectMocks
    private VehicleInfoServiceImpl vehicleInfoServiceImpl;

    private VehicleInfo testVehicle;

    @BeforeEach
    void setUp() {
        // Setup test data
        testVehicle = new VehicleInfo();
        testVehicle.setId(1L);
        testVehicle.setLicense("TEST123");
        testVehicle.setMerchantId(100L);
        testVehicle.setStoreId(200L);
        testVehicle.setDeleted(YesOrNoEnum.NO.getValue());
    }




    @Test
    void saveVehicleInfo() {
        VehicleInfoVO vehicleInfoParam = new VehicleInfoVO();
        vehicleInfoParam.setMerchantId(10L);
        vehicleInfoParam.setStoreId(1000L);
        vehicleInfoParam.setMileage(300);
        vehicleInfoParam.setVehicleColorId(2L);
        vehicleInfoParam.setVehicleSource(VehicleInfoEnums.VehicleSourceEnum.SELF_OWN.getSource());
        vehicleInfoParam.setVehicleModelId(17L);
    }

    @Test
    @DisplayName("Should return vehicle info when vehicle exists")
    void shouldReturnVehicleInfoWhenVehicleExists() {
        // Arrange
        when(vehicleInfoMapper.selectByPrimaryKey(1L)).thenReturn(testVehicle);

        // Act
        Result<VehicleInfoVO> result = vehicleInfoServiceImpl.getBaseById(1L, false);

        // Assert
        assertTrue(result.isSuccess());
        assertNotNull(result.getModel());
        assertEquals(testVehicle.getId(), result.getModel().getId());
        assertEquals(testVehicle.getLicense(), result.getModel().getLicense());

        verify(vehicleInfoMapper, times(1)).selectByPrimaryKey(1L);
    }

    @Test
    @DisplayName("Should return error when vehicle does not exist")
    void shouldReturnErrorWhenVehicleDoesNotExist() {
        // Arrange
        when(vehicleInfoMapper.selectByPrimaryKey(1L)).thenReturn(null);

        // Act
        Result<VehicleInfoVO> result = vehicleInfoServiceImpl.getBaseById(1L, false);

        // Assert
        assertFalse(result.isSuccess());
        assertEquals(ResultEnum.e004.getCode(), result.getResultCode());

        verify(vehicleInfoMapper, times(1)).selectByPrimaryKey(1L);
    }

    @Test
    @DisplayName("Should return error when vehicle is deleted and includeDeleted is false")
    void shouldReturnErrorWhenVehicleIsDeletedAndIncludeDeletedIsFalse() {
        // Arrange
        testVehicle.setDeleted(YesOrNoEnum.YES.getValue());
        when(vehicleInfoMapper.selectByPrimaryKey(1L)).thenReturn(testVehicle);

        // Act
        Result<VehicleInfoVO> result = vehicleInfoServiceImpl.getBaseById(1L, false);

        // Assert
        assertFalse(result.isSuccess());
        assertEquals(ResultEnum.e004.getCode(), result.getResultCode());

        verify(vehicleInfoMapper, times(1)).selectByPrimaryKey(1L);
    }

    @Test
    @DisplayName("Should return vehicle info when vehicle is deleted and includeDeleted is true")
    void shouldReturnVehicleInfoWhenVehicleIsDeletedAndIncludeDeletedIsTrue() {
        // Arrange
        testVehicle.setDeleted(YesOrNoEnum.YES.getValue());
        when(vehicleInfoMapper.selectByPrimaryKey(1L)).thenReturn(testVehicle);

        // Act
        Result<VehicleInfoVO> result = vehicleInfoServiceImpl.getBaseById(1L, true);

        // Assert
        assertTrue(result.isSuccess());
        assertNotNull(result.getModel());
        assertEquals(testVehicle.getId(), result.getModel().getId());
        assertEquals(testVehicle.getLicense(), result.getModel().getLicense());

        verify(vehicleInfoMapper, times(1)).selectByPrimaryKey(1L);
    }

    @Test
    @DisplayName("Should save vehicle info when all parameters are valid")
    void shouldSaveVehicleInfoWhenAllParametersAreValid() {
        // Arrange
        VehicleInfoVO vehicleInfoParam = new VehicleInfoVO();
        vehicleInfoParam.setStoreId(200L);
        vehicleInfoParam.setMileage(10000);
        vehicleInfoParam.setMerchantId(100L);
        vehicleInfoParam.setVehicleSource((byte) 1);
        vehicleInfoParam.setVehicleColorId(1L);
        vehicleInfoParam.setVehicleModelId(1L);

        VehicleInfoVO.VehicleInfoLicenseVO licenseVO = new VehicleInfoVO.VehicleInfoLicenseVO();
        licenseVO.setLicense("TEST123");
        vehicleInfoParam.setLicenseParam(licenseVO);

        LoginVo opUser = new LoginVo();
        opUser.setUserId(1L);

        when(vehicleInfoMapper.countByExample(any(VehicleInfoExample.class))).thenReturn(0L);
        when(vehicleInfoMapper.insertSelective(any(VehicleInfo.class))).thenReturn(1);
        doNothing().when(ctripVehicleServiceMock).registerCtripRealVehicle(anyLong(), anyLong());
        when(rentMainService.insRelationModelForAddVehicle(anyLong(), anyLong(), anyLong()));

        // Act
        Result<Long> result = vehicleInfoServiceImpl.saveVehicleInfo(vehicleInfoParam, opUser);

        // Assert
        assertTrue(result.isSuccess());
        assertEquals(1L, result.getModel());
        verify(vehicleInfoMapper, times(1)).countByExample(any(VehicleInfoExample.class));
        verify(vehicleInfoMapper, times(1)).insertSelective(any(VehicleInfo.class));
        verify(ctripVehicleServiceMock, times(1)).registerCtripRealVehicle(anyLong(), anyLong());
        verify(rentMainService, times(1)).insRelationModelForAddVehicle(anyLong(), anyLong(), anyLong());
    }

    @Test
    @DisplayName("Should return error when required parameters are missing")
    void shouldReturnErrorWhenRequiredParametersAreMissing() {
        // Arrange
        VehicleInfoVO vehicleInfoParam = new VehicleInfoVO();
        LoginVo opUser = new LoginVo();

        // Act
        Result<Long> result = vehicleInfoServiceImpl.saveVehicleInfo(vehicleInfoParam, opUser);

        // Assert
        assertFalse(result.isSuccess());
        assertEquals("参数错误", result.getMessage());
        verifyNoInteractions(vehicleInfoMapper, ctripVehicleServiceMock, rentMainService);
    }

    @Test
    @DisplayName("Should return error when license plate is missing")
    void shouldReturnErrorWhenLicensePlateMissing() {
        // Arrange
        VehicleInfoVO vehicleInfoParam = new VehicleInfoVO();
        vehicleInfoParam.setStoreId(200L);
        vehicleInfoParam.setMileage(10000);
        vehicleInfoParam.setMerchantId(100L);
        vehicleInfoParam.setVehicleSource((byte) 1);
        vehicleInfoParam.setVehicleColorId(1L);
        vehicleInfoParam.setVehicleModelId(1L);

        LoginVo opUser = new LoginVo();
        opUser.setUserId(1L);

        // Act
        Result<Long> result = vehicleInfoServiceImpl.saveVehicleInfo(vehicleInfoParam, opUser);

        // Assert
        assertFalse(result.isSuccess());
        assertEquals("车牌必须输入", result.getMessage());
        verifyNoInteractions(vehicleInfoMapper, ctripVehicleServiceMock, rentMainService);
    }

    @Test
    @DisplayName("Should return error when license plate already exists")
    void shouldReturnErrorWhenLicensePlateExists() {
        // Arrange
        VehicleInfoVO vehicleInfoParam = new VehicleInfoVO();
        vehicleInfoParam.setStoreId(200L);
        vehicleInfoParam.setMileage(10000);
        vehicleInfoParam.setMerchantId(100L);
        vehicleInfoParam.setVehicleSource((byte) 1);
        vehicleInfoParam.setVehicleColorId(1L);
        vehicleInfoParam.setVehicleModelId(1L);

        VehicleInfoVO.VehicleInfoLicenseVO licenseVO = new VehicleInfoVO.VehicleInfoLicenseVO();
        licenseVO.setLicense("TEST123");
        vehicleInfoParam.setLicenseParam(licenseVO);

        LoginVo opUser = new LoginVo();
        opUser.setUserId(1L);

        when(vehicleInfoMapper.countByExample(any(VehicleInfoExample.class))).thenReturn(1L);

        // Act
        Result<Long> result = vehicleInfoServiceImpl.saveVehicleInfo(vehicleInfoParam, opUser);

        // Assert
        assertFalse(result.isSuccess());
        assertEquals("车牌已存在", result.getMessage());
        verify(vehicleInfoMapper, times(1)).countByExample(any(VehicleInfoExample.class));
        verifyNoInteractions(ctripVehicleServiceMock, rentMainService);
    }
}