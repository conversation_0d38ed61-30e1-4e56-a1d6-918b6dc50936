//package com.ql.rent.provider.trade;
//
//import com.ql.Constant;
//import com.ql.dto.open.request.trade.ContractInspectionRequest;
//import com.ql.dto.open.request.trade.ContractSmsRequest;
//import com.ql.dto.open.request.trade.ContractStatusRequest;
//import com.ql.dto.open.response.trade.ContractStatusResponse;
//import com.ql.rent.bizdata.order.VehicleInspectionDataReq;
//import com.ql.rent.bizdata.order.SendOrderContractReq;
//import com.ql.rent.component.PlatformBiz;
//import com.ql.rent.dao.common.ElectronicContractStatusMapper;
//import com.ql.rent.dao.trade.OrderInfoMapper;
//import com.ql.rent.entity.common.ElectronicContractStatus;
//import com.ql.rent.entity.trade.OrderInfo;
//import com.ql.rent.enums.ElectronicContractStatusEnum;
//import com.ql.rent.share.result.Result;
//import com.ql.rent.share.result.ResultUtil;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.List;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.ArgumentMatchers.*;
//import static org.mockito.Mockito.*;
//
///**
// * 第三方订单电子合同服务测试类
// *
// * <AUTHOR>
// */
//class ThirdOrderContractServiceImplTest {
//
//    @InjectMocks
//    private ThirdOrderContractServiceImpl thirdOrderContractService;
//
//    @Mock
//    private PlatformBiz platformBiz;
//
//    @Mock
//    private OrderInfoMapper orderInfoMapper;
//
//    @Mock
//    private ElectronicContractStatusMapper electronicContractStatusMapper;
//
//    private OrderInfo mockOrderInfo;
//    private ContractInspectionRequest mockInspectionRequest;
//    private ContractSmsRequest mockSmsRequest;
//    private ContractStatusRequest mockStatusRequest;
//
//    @BeforeEach
//    void setUp() {
//        MockitoAnnotations.openMocks(this);
//
//        // 初始化测试数据
//        mockOrderInfo = new OrderInfo();
//        mockOrderInfo.setOrderId(1001L);
//        mockOrderInfo.setMerchantId(100L);
//        mockOrderInfo.setSourceOrderId("CTRIP_ORDER_123");
//        mockOrderInfo.setOrderSource((byte) 1); // 携程订单
//
//        mockInspectionRequest = new ContractInspectionRequest();
//        mockInspectionRequest.setOrderId(1001L);
//        mockInspectionRequest.setMerchantId(100L);
//        mockInspectionRequest.setMileage(1000);
//        mockInspectionRequest.setOilLiter(50);
//        mockInspectionRequest.setMaxOilLiter(60);
//
//        mockSmsRequest = new ContractSmsRequest();
//        mockSmsRequest.setOrderId(1001L);
//        mockSmsRequest.setMerchantId(100L);
//        mockSmsRequest.setPrType((byte) 1);
//
//        mockStatusRequest = new ContractStatusRequest();
//        mockStatusRequest.setOrderId(1001L);
//        mockStatusRequest.setMerchantId(100L);
//    }
//
//    @Test
//    void testProcessInspectionData_Success() {
//        // Given
//        when(orderInfoMapper.selectByPrimaryKey(1001L)).thenReturn(mockOrderInfo);
//        when(platformBiz.uploadContract(any(VehicleInspectionDataReq.class), eq(100L), eq(Constant.ChannelId.CTRIP)))
//                .thenReturn(ResultUtil.successResult(true));
//        when(electronicContractStatusMapper.insertSelective(any(ElectronicContractStatus.class))).thenReturn(1);
//
//        // When
//        Result<Boolean> result = thirdOrderContractService.processInspectionData(mockInspectionRequest);
//
//        // Then
//        assertTrue(ResultUtil.isResultSuccess(result));
//        assertTrue(result.getModel());
//        verify(orderInfoMapper).selectByPrimaryKey(1001L);
//        verify(platformBiz).uploadContract(any(VehicleInspectionDataReq.class), eq(100L), eq(Constant.ChannelId.CTRIP));
//        verify(electronicContractStatusMapper).insertSelective(any(ElectronicContractStatus.class));
//    }
//
//    @Test
//    void testProcessInspectionData_OrderNotFound() {
//        // Given
//        when(orderInfoMapper.selectByPrimaryKey(1001L)).thenReturn(null);
//
//        // When
//        Result<Boolean> result = thirdOrderContractService.processInspectionData(mockInspectionRequest);
//
//        // Then
//        assertFalse(ResultUtil.isResultSuccess(result));
//        assertEquals("订单不存在或无权限", result.getMessage());
//        verify(orderInfoMapper).selectByPrimaryKey(1001L);
//        verifyNoInteractions(platformBiz);
//    }
//
//    @Test
//    void testProcessInspectionData_MerchantNotMatch() {
//        // Given
//        mockOrderInfo.setMerchantId(999L); // 不匹配的商家ID
//        when(orderInfoMapper.selectByPrimaryKey(1001L)).thenReturn(mockOrderInfo);
//
//        // When
//        Result<Boolean> result = thirdOrderContractService.processInspectionData(mockInspectionRequest);
//
//        // Then
//        assertFalse(ResultUtil.isResultSuccess(result));
//        assertEquals("订单不存在或无权限", result.getMessage());
//        verify(orderInfoMapper).selectByPrimaryKey(1001L);
//        verifyNoInteractions(platformBiz);
//    }
//
//    @Test
//    void testProcessInspectionData_NotCtripOrder() {
//        // Given
//        mockOrderInfo.setOrderSource((byte) 2); // 非携程订单
//        when(orderInfoMapper.selectByPrimaryKey(1001L)).thenReturn(mockOrderInfo);
//
//        // When
//        Result<Boolean> result = thirdOrderContractService.processInspectionData(mockInspectionRequest);
//
//        // Then
//        assertFalse(ResultUtil.isResultSuccess(result));
//        assertEquals("当前订单不支持电子合同功能", result.getMessage());
//        verify(orderInfoMapper).selectByPrimaryKey(1001L);
//        verifyNoInteractions(platformBiz);
//    }
//
//    @Test
//    void testSendContractSms_Success() {
//        // Given
//        when(orderInfoMapper.selectByPrimaryKey(1001L)).thenReturn(mockOrderInfo);
//        when(platformBiz.sendContractMsg(any(SendOrderContractReq.class), eq(100L), eq(Constant.ChannelId.CTRIP)))
//                .thenReturn(ResultUtil.successResult(1));
//        when(electronicContractStatusMapper.insertSelective(any(ElectronicContractStatus.class))).thenReturn(1);
//
//        // When
//        Result<Boolean> result = thirdOrderContractService.sendContractSms(mockSmsRequest);
//
//        // Then
//        assertTrue(ResultUtil.isResultSuccess(result));
//        assertTrue(result.getModel());
//        verify(orderInfoMapper).selectByPrimaryKey(1001L);
//        verify(platformBiz).sendContractMsg(any(SendOrderContractReq.class), eq(100L), eq(Constant.ChannelId.CTRIP));
//        verify(electronicContractStatusMapper).insertSelective(any(ElectronicContractStatus.class));
//    }
//
//    @Test
//    void testSendContractSms_PlatformBizFailed() {
//        // Given
//        when(orderInfoMapper.selectByPrimaryKey(1001L)).thenReturn(mockOrderInfo);
//        when(platformBiz.sendContractMsg(any(SendOrderContractReq.class), eq(100L), eq(Constant.ChannelId.CTRIP)))
//                .thenReturn(ResultUtil.failResult("携程接口调用失败"));
//
//        // When
//        Result<Boolean> result = thirdOrderContractService.sendContractSms(mockSmsRequest);
//
//        // Then
//        assertFalse(ResultUtil.isResultSuccess(result));
//        assertTrue(result.getMessage().contains("发送短信失败"));
//        verify(orderInfoMapper).selectByPrimaryKey(1001L);
//        verify(platformBiz).sendContractMsg(any(SendOrderContractReq.class), eq(100L), eq(Constant.ChannelId.CTRIP));
//        verifyNoInteractions(electronicContractStatusMapper);
//    }
//
//    @Test
//    void testQueryContractStatus_WithStatus() {
//        // Given
//        when(orderInfoMapper.selectByPrimaryKey(1001L)).thenReturn(mockOrderInfo);
//
//        ElectronicContractStatus mockStatus = new ElectronicContractStatus();
//        mockStatus.setSourceOrderId("CTRIP_ORDER_123");
//        mockStatus.setStatus(ElectronicContractStatusEnum.SMS_SIGNING.getStatus());
//        mockStatus.setElectronicContractUrl("http://example.com/contract.pdf");
//        mockStatus.setSignTime(System.currentTimeMillis());
//
//        List<ElectronicContractStatus> statusList = new ArrayList<>();
//        statusList.add(mockStatus);
//
//        when(electronicContractStatusMapper.selectByExample(any())).thenReturn(statusList);
//
//        // When
//        Result<ContractStatusResponse> result = thirdOrderContractService.queryContractStatus(mockStatusRequest);
//
//        // Then
//        assertTrue(ResultUtil.isResultSuccess(result));
//        ContractStatusResponse response = result.getModel();
//        assertNotNull(response);
//        assertEquals(1001L, response.getOrderId());
//        assertEquals(ElectronicContractStatusEnum.SMS_SIGNING.getStatus(), response.getStatus());
//        assertEquals("http://example.com/contract.pdf", response.getContractUrl());
//        assertNotNull(response.getSignTime());
//
//        verify(orderInfoMapper).selectByPrimaryKey(1001L);
//        verify(electronicContractStatusMapper).selectByExample(any());
//    }
//
//    @Test
//    void testQueryContractStatus_NoStatus() {
//        // Given
//        when(orderInfoMapper.selectByPrimaryKey(1001L)).thenReturn(mockOrderInfo);
//        when(electronicContractStatusMapper.selectByExample(any())).thenReturn(Collections.emptyList());
//
//        // When
//        Result<ContractStatusResponse> result = thirdOrderContractService.queryContractStatus(mockStatusRequest);
//
//        // Then
//        assertTrue(ResultUtil.isResultSuccess(result));
//        ContractStatusResponse response = result.getModel();
//        assertNotNull(response);
//        assertEquals(1001L, response.getOrderId());
//        assertEquals(ElectronicContractStatusEnum.WAIT_UPLOAD_INSPECTION_DATA.getStatus(), response.getStatus());
//        assertEquals(ElectronicContractStatusEnum.WAIT_UPLOAD_INSPECTION_DATA.getDesc(), response.getStatusDesc());
//
//        verify(orderInfoMapper).selectByPrimaryKey(1001L);
//        verify(electronicContractStatusMapper).selectByExample(any());
//    }
//
//    @Test
//    void testQueryContractStatus_InvalidParameters() {
//        // Given
//        mockStatusRequest.setOrderId(null);
//
//        // When
//        Result<ContractStatusResponse> result = thirdOrderContractService.queryContractStatus(mockStatusRequest);
//
//        // Then
//        assertFalse(ResultUtil.isResultSuccess(result));
//        assertEquals("订单ID和商家ID不能为空", result.getMessage());
//        verifyNoInteractions(orderInfoMapper);
//        verifyNoInteractions(electronicContractStatusMapper);
//    }
//}