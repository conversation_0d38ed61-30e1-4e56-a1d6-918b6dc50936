package com.ql.rent.provider.trade;

import com.alibaba.fastjson.JSON;
import com.ql.rent.AbstractTest;
import com.ql.rent.param.trade.OrderInfoParam;
import com.ql.rent.service.slave.IOrderSlaveService;
import com.ql.rent.service.trade.IOrderService;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.trade.OrderInfoListVo;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

/**
 * @auther musi
 * @date 2023/12/8 10:45
 */
public class OrderSlaveServiceImplTest extends AbstractTest {

    @Resource
    private IOrderSlaveService orderSlaveService;

    @Test
    public void testGetOrderList() {
        LoginVo loginVo = new LoginVo();
        loginVo.setLoginName("merchant10");
        loginVo.setMerchantId(2L);

        OrderInfoParam orderInfoParam = new OrderInfoParam();
        String a = "{\n" +
                "  \"pageIndex\": 1,\n" +
                "  \"pageSize\": 5000,\n" +
                "  \"sortField\": \"create_time\",\n" +
                "  \"sortType\": \"desc\",\n" +
                "  \"orderType\": 0\n" +
                "}";
        orderInfoParam = JSON.parseObject(a, OrderInfoParam.class);
        orderInfoParam.setOrderType((byte) 0);
        orderInfoParam.setMerchantId(2L);
        Result<OrderInfoListVo> result = orderSlaveService.getOrderList(orderInfoParam, loginVo);

        System.out.println(JSON.toJSONString(result));
    }
}
