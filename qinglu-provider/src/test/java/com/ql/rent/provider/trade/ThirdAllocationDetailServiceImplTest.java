package com.ql.rent.provider.trade;

import com.ql.rent.param.trade.ThirdAllocationDetailQueryParam;
import com.ql.rent.service.trade.IThirdAllocationDetailService;
import com.ql.rent.AbstractTest;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.trade.ThirdAllocationDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * 分账明细服务测试类
 */
@Slf4j
public class ThirdAllocationDetailServiceImplTest extends AbstractTest {

    @Resource
    private IThirdAllocationDetailService thirdAllocationDetailService;


    @Test
    public void testPageList() {
        log.info("=== 测试分页查询分账明细 ===");
        ThirdAllocationDetailQueryParam param = new ThirdAllocationDetailQueryParam();
        param.setPageIndex(1);
        param.setPageSize(10);
        param.setStoreId(7665L);
        PageListVo<ThirdAllocationDetailVo> result = thirdAllocationDetailService.pageList(param);
        log.info("分页查询结果: {}", com.alibaba.fastjson.JSON.toJSONString(result));
        assert result != null : "分页查询失败";
        assert result.getCount() >= 0 : "总数不能为负数";
    }
} 