package com.ql.rent.provider.vehicle;

import com.ql.rent.dao.vehicle.OperateDeviceRecordMapper;
import com.ql.rent.entity.vehicle.OperateDeviceRecord;
import com.ql.rent.service.third.IThirdVehicleDeviceService;
import com.ql.rent.service.vehicle.IVehicleDeviceSettingService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.third.vo.request.OperateDeviceReq;
import com.ql.rent.third.vo.response.RemoteControlResp;
import com.ql.rent.vo.vehicle.VehicleDeviceVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class OperateDeviceRecordServiceImplTest {

  @Mock
  private OperateDeviceRecordMapper operateDeviceRecordMapper;

  @Mock
  private IThirdVehicleDeviceService thirdVehicleDeviceService;

  @Mock
  private IVehicleDeviceSettingService vehicleDeviceSettingService;

  @InjectMocks
  private OperateDeviceRecordServiceImpl operateDeviceRecordService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Nested
  class AddOperateRecordTests {
    @Test
    void shouldThrowExceptionWhenDeviceNotFound() {
      // Arrange
      Long merchantId = 1L;
      Long orderId = 1L;
      Long vehicleId = 1L;
      String command = "START";
      Date operateTime = new Date();
      Long opUserId = 1L;

      when(vehicleDeviceSettingService.selectDevice(merchantId, vehicleId, null))
          .thenReturn(null);

      // Act & Assert
      assertThrows(BizException.class, () -> operateDeviceRecordService.addOperateRecord(merchantId, orderId, vehicleId,
          command, operateTime, opUserId));

      verify(vehicleDeviceSettingService).selectDevice(merchantId, vehicleId, null);
      verifyNoInteractions(thirdVehicleDeviceService);
      verifyNoInteractions(operateDeviceRecordMapper);
    }

    @Test
    void shouldAddOperateRecordSuccessfully() {
      // Arrange
      Long merchantId = 1L;
      Long orderId = 1L;
      Long vehicleId = 1L;
      String command = "START";
      Date operateTime = new Date();
      Long opUserId = 1L;

      VehicleDeviceVO deviceVO = new VehicleDeviceVO();
      deviceVO.setDeviceNo("DEVICE001");
      deviceVO.setCodeNo("CODE001");

      RemoteControlResp controlResp = new RemoteControlResp();
      controlResp.setResult(1);

      when(vehicleDeviceSettingService.selectDevice(merchantId, vehicleId, null))
          .thenReturn(deviceVO);
      when(thirdVehicleDeviceService.controlEngine(any()))
          .thenReturn(controlResp);

      // Act
      Boolean result = operateDeviceRecordService.addOperateRecord(merchantId, orderId, vehicleId, command, operateTime,
          opUserId);

      // Assert
      assertTrue(result);

      // Verify device control request
      ArgumentCaptor<OperateDeviceReq> deviceReqCaptor = ArgumentCaptor.forClass(OperateDeviceReq.class);
      verify(thirdVehicleDeviceService).controlEngine(deviceReqCaptor.capture());
      OperateDeviceReq capturedReq = deviceReqCaptor.getValue();
      assertEquals(command, capturedReq.getValue());
      assertEquals("DEVICE001", capturedReq.getSn());
      assertEquals("CODE001", capturedReq.getCode());
      assertEquals(operateTime.getTime(), capturedReq.getTimeStamp());

      // Verify record insertion
      ArgumentCaptor<OperateDeviceRecord> recordCaptor = ArgumentCaptor.forClass(OperateDeviceRecord.class);
      verify(operateDeviceRecordMapper).insert(recordCaptor.capture());
      OperateDeviceRecord capturedRecord = recordCaptor.getValue();
      assertEquals(merchantId, capturedRecord.getMerchantId());
      assertEquals(orderId, capturedRecord.getOrderId());
      assertEquals("DEVICE001", capturedRecord.getDeviceNo());
      assertEquals(vehicleId, capturedRecord.getVehicleId());
      assertEquals(command, capturedRecord.getControlCommand());
      assertEquals(operateTime.getTime(), capturedRecord.getOpTime());
      assertEquals(operateTime.getTime(), capturedRecord.getCurrTime());
      assertEquals(opUserId, capturedRecord.getOpUserId());
      assertEquals("1", capturedRecord.getResult());
    }

    @Test
    void shouldHandleFailedDeviceControl() {
      // Arrange
      Long merchantId = 1L;
      Long orderId = 1L;
      Long vehicleId = 1L;
      String command = "START";
      Date operateTime = new Date();
      Long opUserId = 1L;

      VehicleDeviceVO deviceVO = new VehicleDeviceVO();
      deviceVO.setDeviceNo("DEVICE001");
      deviceVO.setCodeNo("CODE001");

      RemoteControlResp controlResp = new RemoteControlResp();
      controlResp.setResult(0);

      when(vehicleDeviceSettingService.selectDevice(merchantId, vehicleId, null))
          .thenReturn(deviceVO);
      when(thirdVehicleDeviceService.controlEngine(any()))
          .thenReturn(controlResp);

      // Act
      Boolean result = operateDeviceRecordService.addOperateRecord(merchantId, orderId, vehicleId, command, operateTime,
          opUserId);

      // Assert
      assertFalse(result);

      // Verify record insertion with failed result
      ArgumentCaptor<OperateDeviceRecord> recordCaptor = ArgumentCaptor.forClass(OperateDeviceRecord.class);
      verify(operateDeviceRecordMapper).insert(recordCaptor.capture());
      OperateDeviceRecord capturedRecord = recordCaptor.getValue();
      assertEquals("0", capturedRecord.getResult());
    }

    @Test
    void shouldHandleNullOrderId() {
      // Arrange
      Long merchantId = 1L;
      Long orderId = null;
      Long vehicleId = 1L;
      String command = "START";
      Date operateTime = new Date();
      Long opUserId = 1L;

      VehicleDeviceVO deviceVO = new VehicleDeviceVO();
      deviceVO.setDeviceNo("DEVICE001");
      deviceVO.setCodeNo("CODE001");

      RemoteControlResp controlResp = new RemoteControlResp();
      controlResp.setResult(1);

      when(vehicleDeviceSettingService.selectDevice(merchantId, vehicleId, null))
          .thenReturn(deviceVO);
      when(thirdVehicleDeviceService.controlEngine(any()))
          .thenReturn(controlResp);

      // Act
      Boolean result = operateDeviceRecordService.addOperateRecord(merchantId, orderId, vehicleId, command, operateTime,
          opUserId);

      // Assert
      assertTrue(result);

      // Verify record insertion with default order ID
      ArgumentCaptor<OperateDeviceRecord> recordCaptor = ArgumentCaptor.forClass(OperateDeviceRecord.class);
      verify(operateDeviceRecordMapper).insert(recordCaptor.capture());
      OperateDeviceRecord capturedRecord = recordCaptor.getValue();
      assertEquals(0L, capturedRecord.getOrderId());
    }
  }
}