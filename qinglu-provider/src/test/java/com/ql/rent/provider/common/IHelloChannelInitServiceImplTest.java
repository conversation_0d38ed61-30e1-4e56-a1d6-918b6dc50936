package com.ql.rent.provider.common;

import com.ql.rent.AbstractTest;
import com.ql.rent.vo.common.ThirdBatchAddVo;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

public class IHelloChannelInitServiceImplTest extends AbstractTest {
    @Resource
    private HelloChannelInitServiceImpl helloChannelInitService;

    @Test
    public void handleStoreMatchData() {
        ThirdBatchAddVo param = new ThirdBatchAddVo();
        param.setChannelId(4L);
        param.setMerchantId(6L);
//        param.setInitTaskId(1L);
        List<ThirdBatchAddVo.ThirdStoreAddVo> mockList = new ArrayList<>();
        // 匹配上的门店
        ThirdBatchAddVo.ThirdStoreAddVo mock1 = new ThirdBatchAddVo.ThirdStoreAddVo();
        mock1.setMatch((byte) 1);
        mock1.setStoreId(15L);
        mock1.setCreated((byte) 0);
        mock1.setThirdStoreId("7306753023750045700");
        mockList.add(mock1);
        // 创建的门店
        ThirdBatchAddVo.ThirdStoreAddVo mock2 = new ThirdBatchAddVo.ThirdStoreAddVo();
        mock2.setMatch((byte) 0);
        mock2.setCreated((byte) 1);
        mock2.setThirdStoreId("7306926289173414814");
        mockList.add(mock2);
        // 下线的门店
        ThirdBatchAddVo.ThirdStoreAddVo mock3 = new ThirdBatchAddVo.ThirdStoreAddVo();
        mock3.setMatch((byte) 0);
        mock3.setCreated((byte) 0);
        mock3.setThirdStoreId("7306752944293216259");
        mockList.add(mock3);
        param.setStoreAddVos(mockList);
//        helloChannelInitService.handleStoreMatchData(param);
    }
}
