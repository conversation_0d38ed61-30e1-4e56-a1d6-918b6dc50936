package com.ql.rent.provider.common;

import com.aliyuncs.IAcsClient;
import com.aliyuncs.afs.model.v20180112.AuthenticateSigRequest;
import com.aliyuncs.afs.model.v20180112.AuthenticateSigResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class AliAfsTest {

    @Resource
    private IAcsClient client;

    @Test
    public void test(){
        AuthenticateSigRequest request = new AuthenticateSigRequest();
        request.setSessionId("xxx");// 必填参数，从前端获取，不可更改，android和ios只传这个参数即可
        request.setSig("xxx");// 必填参数，从前端获取，不可更改
        request.setToken("xxx");// 必填参数，从前端获取，不可更改
        request.setScene("xxx");// 必填参数，从前端获取，不可更改
        request.setAppKey("FFFF0N0000000000B2D9");// 必填参数，后端填写
        request.setRemoteIp(request.getSourceIp());// 必填参数，后端填写

        try {
            AuthenticateSigResponse response = client.getAcsResponse(request);
            if(response.getCode() == 100) {
                System.out.println("验签通过");
            } else {
                System.out.println("验签失败");
            }
            // TODO
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}