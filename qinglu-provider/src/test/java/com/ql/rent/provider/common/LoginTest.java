package com.ql.rent.provider.common;

import com.alibaba.fastjson.JSON;
import com.ql.rent.param.login.RegisterParam;
import com.ql.rent.service.login.LoginService;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.merchant.SysUserDetailVo;

import javax.annotation.Resource;

public class LoginTest {

    public static void main(String[] args) {

        RegisterParam regParam = new RegisterParam();
        regParam.setMail("<EMAIL>");
        regParam.setCountryCode("86");
        regParam.setMobile("13000000001");
        regParam.setPassword("123456");
        regParam.setCode("123456");
        regParam.setSource("123");

        System.out.println(JSON.toJSONString(regParam));
    }


}
