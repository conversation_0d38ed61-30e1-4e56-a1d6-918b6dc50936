package com.ql.rent.provider.common;

import com.ql.dto.ResultResp;
import com.ql.dto.open.request.common.OpenMappingRequest;
import com.ql.dto.open.response.OpenMappingResponse;
import com.ql.rent.StartSpringBootApplication;
import com.ql.rent.client.ICommonOpenService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * @desc:
 * @author: pj
 * @time: 2022-09-17 14:11
 * @Version: 1.0
 */
@SpringBootTest(classes = StartSpringBootApplication.class)
@RunWith(SpringRunner.class)
@Slf4j
public class CommonMappingServiceImplTest {

    @Resource
    private ICommonOpenService commonOpenService;

    @Test
    public void testMappingPull() {
        ResultResp<List<OpenMappingResponse>> resp;
        OpenMappingRequest request = new OpenMappingRequest();
        request.setMerchantId(58L);
        request.setModel(1);
        request.setPageSize(5L);

        request.setLastMaxId(0L);
        resp = commonOpenService.getMappingByPage(request);
        System.out.println(resp);

        request.setLastMaxId(3148L);
        resp = commonOpenService.getMappingByPage(request);
        System.out.println(resp);
    }

}
