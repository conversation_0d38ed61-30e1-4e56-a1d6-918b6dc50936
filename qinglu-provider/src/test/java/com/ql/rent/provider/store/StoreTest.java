package com.ql.rent.provider.store;

import com.alibaba.fastjson.JSON;
import com.ql.rent.dao.store.StoreInfoMapper;
import com.ql.rent.entity.store.StoreInfo;
import com.ql.rent.entity.store.StoreInfoExample;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.param.store.StoreCopyParam;
import com.ql.rent.param.store.StoreInfoParam;
import com.ql.rent.service.store.IStoreInfoService;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.common.AreaVo;
import com.ql.rent.vo.store.StoreInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
class StoreTest {
    @Resource
    private IStoreInfoService storeInfoService;
    @Resource
    private StoreInfoMapper storeInfoMapper;

    @Test
    public void storeSearch() {
        StoreInfoParam param = new StoreInfoParam();
        param.setMerchantId(2L);
        System.out.println(JSON.toJSONString(storeInfoService.storeInfoChannelList(param, false)));
    }

    @Test
    public void storeCopy() {
        StoreCopyParam param = new StoreCopyParam();
        param.setUserId(2L);
        param.setStoreId(6L);
        param.setFromChannelId(1L);
        param.setToChannelId(10L);
        System.out.println(JSON.toJSONString(storeInfoService.storeInfoCopy(param)));
    }
    @Test
    public void storeInfoSave() {
        StoreInfoVo param = new StoreInfoVo();
        param = JSON.parseObject("{\"id\":7716,\"source\":null,\"sortNo\":null,\"deleted\":0,\"lastVer\":113,\"createTime\":1739263685236,\"createUserId\":null,\"opTime\":1741603371768,\"opUserId\":0,\"oldData\":null,\"storeId\":7309,\"channelId\":1,\"channelName\":null,\"storeName\":\"qtest1\",\"countryId\":0,\"provinceId\":11,\"provinceName\":null,\"cityId\":126,\"cityCode\":\"330600\",\"areaCode\":330602,\"cityName\":\"绍兴市\",\"merchantId\":16,\"storeSize\":0,\"storeDecorate\":0,\"address\":\"12132131212412312124124123213213124123213131231241\",\"storePosType\":1,\"minAdvanceBookingTime\":0.5,\"minAdvanceBookingUnit\":1,\"maxAdvanceBookingTime\":1,\"maxAdvanceBookingUnit\":4,\"minRentTerm\":0,\"maxRentTerm\":0,\"orderInterval\":20,\"orderIntervalUnit\":1,\"allopatryReturnEnabled\":0,\"allopatryReturnFee\":0,\"storeType\":0,\"storeStatus\":1,\"isTest\":0,\"longLat\":{\"longitude\":120.582112,\"latitude\":29.997117},\"freeShuttleEnabled\":1,\"freeShuttle\":5,\"pickupEnabled\":1,\"guidePickupList\":[],\"guideReturnList\":[],\"selfServiceReturn\":false,\"selfServiceDistance\":1,\"selfGuidePickupList\":[],\"selfGuideReturnList\":[],\"businessTimeList\":null,\"businessTimeV2List\":[{\"businessPeriod\":\"1111111\",\"periodTimeVoList\":[{\"fromTime\":400,\"toTime\":900,\"fee\":null},{\"fromTime\":901,\"toTime\":1000,\"fee\":null},{\"fromTime\":1001,\"toTime\":2359,\"fee\":null},{\"fromTime\":0,\"toTime\":359,\"fee\":null}],\"nightService\":1,\"nightList\":[{\"id\":34870,\"fee\":100,\"feeType\":1,\"businessFrom\":400,\"businessTo\":500},{\"id\":34871,\"fee\":100,\"feeType\":1,\"businessFrom\":501,\"businessTo\":600},{\"id\":34872,\"fee\":100,\"feeType\":1,\"businessFrom\":0,\"businessTo\":359},{\"id\":34874,\"fee\":100,\"feeType\":1,\"businessFrom\":2300,\"businessTo\":2359}],\"businessFrom\":1046,\"businessTo\":1046}],\"restTimeList\":null,\"restTimeV2List\":[],\"orderIntervalList\":[],\"contactList\":[{\"id\":94013,\"contactType\":0,\"linkName\":\"qqqq\",\"countryCode\":\"+86\",\"mobile\":\"***********\",\"telArea\":\"\",\"tel\":\"\",\"telExt\":\"\",\"email\":\"\"}],\"hourlyChargeList\":[{\"id\":42575,\"storeId\":7309,\"channelId\":1,\"scene\":2,\"chargeItem\":1,\"chargeValue\":\"0.35, 0.45, 0.55, 0.65, 0.75, 0.85, 0.95, 1\",\"deleted\":0},{\"id\":42576,\"storeId\":7309,\"channelId\":1,\"scene\":1,\"chargeItem\":1,\"chargeValue\":\"0, 0.25, 0.4, 0.5, 0.65, 0.75, 0.9, 1\",\"deleted\":0},{\"id\":42577,\"storeId\":7309,\"channelId\":1,\"scene\":2,\"chargeItem\":2,\"chargeValue\":\"0,0,0,0,0,0,0,0\",\"deleted\":0},{\"id\":42578,\"storeId\":7309,\"channelId\":1,\"scene\":2,\"chargeItem\":3,\"chargeValue\":\"0,0,0,0,0,0,0,0\",\"deleted\":0},{\"id\":42579,\"storeId\":7309,\"channelId\":1,\"scene\":1,\"chargeItem\":2,\"chargeValue\":\"0,0,0,0,0,0,0,0\",\"deleted\":0},{\"id\":42580,\"storeId\":7309,\"channelId\":1,\"scene\":1,\"chargeItem\":3,\"chargeValue\":\"0,0,0,0,0,0,0,0\",\"deleted\":0},{\"id\":42581,\"storeId\":7309,\"channelId\":1,\"scene\":1,\"chargeItem\":4,\"chargeValue\":\"1,1,1,1,1,1,1,1\",\"deleted\":0},{\"id\":42582,\"storeId\":7309,\"channelId\":1,\"scene\":2,\"chargeItem\":4,\"chargeValue\":\"1,1,1,1,1,1,1,1\",\"deleted\":0}],\"isSync\":null,\"thirdStoreId\":null,\"posHub\":0,\"productList\":[\"0,0,0,0,0,0,0,0\",\"0,0,0,0,0,0,0,0\"],\"productList1\":[\"0,0,0,0,0,0,0,0\",\"0,0,0,0,0,0,0,0\"],\"isInitMap\":true}"
                ,StoreInfoVo.class);
        param.setIsSync(false);
        param.setMerchantId(16L);
        Result<Boolean> booleanResult = storeInfoService.storeInfoSave(param, 0L);
        System.out.println(JSON.toJSONString(booleanResult));
    }

    @Test
    public void storeOffine() {
        Long merchantId = 72L;
        StoreInfoExample storeInfoExample = new StoreInfoExample();
        storeInfoExample.createCriteria().andMerchantIdEqualTo(merchantId)
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<StoreInfo> storeInfos = storeInfoMapper.selectByExample(storeInfoExample);
        for (StoreInfo storeInfo : storeInfos) {
            storeInfoService.storeOffine(merchantId, storeInfo.getId(), 1L);
        }

    }

    @Test
    public void findAllCity() {
        Long merchantId = 3L;
        Result<List<AreaVo>> result = storeInfoService.findAllCity(merchantId);
        System.out.println(JSON.toJSONString(result));
    }

}