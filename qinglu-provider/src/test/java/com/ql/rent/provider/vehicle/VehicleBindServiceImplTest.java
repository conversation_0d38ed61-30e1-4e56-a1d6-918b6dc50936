package com.ql.rent.provider.vehicle;

import com.ql.Constant;
import com.ql.dto.vehicle.VehicleModelDTO;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.SignRequest;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.request.DeleteCtripSkuRequest;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.response.BaseCtripResponse;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.response.SkuResponse;
import com.ql.rent.api.aggregate.remote.ctrip.api.CtripApiClient;
import com.ql.rent.component.CtripRequestSignBuilder;
import com.ql.rent.provider.vehicle.VehicleComponent;
import com.ql.rent.dao.vehicle.VehicleBindMapper;
import com.ql.rent.dao.vehicle.VehicleModelMapper;
import com.ql.rent.dao.vehicle.VehicleSubSeryMapper;
import com.ql.rent.entity.vehicle.VehicleBind;
import com.ql.rent.entity.vehicle.VehicleBindExample;
import com.ql.rent.entity.vehicle.VehicleModel;
import com.ql.rent.entity.vehicle.VehicleSubSery;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.vehicle.VehicleBindSyncedEnums;
import com.ql.rent.enums.vehicle.VehicleModelStatusEnum;
import com.ql.rent.param.vehicle.VehicleBindQueryParam;
import com.ql.rent.service.common.IChannelService;
import com.ql.rent.service.store.IThirdIdRelationService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.common.ChannelVO;
import com.ql.rent.vo.vehicle.VehicleBindVO;
import com.ql.rent.vo.vehicle.VehicleModelVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;

class VehicleBindServiceImplTest {

  @Mock
  private VehicleBindMapper vehicleBindMapper;

  @Mock
  private VehicleModelMapper vehicleModelMapper;

  @Mock
  private VehicleSubSeryMapper vehicleSubSeryMapper;

  @Mock
  private IChannelService channelService;

  @Mock
  private IThirdIdRelationService thirdIdRelationService;

  @Mock
  private CtripApiClient ctripApiClient;

  @Mock
  private CtripRequestSignBuilder ctripRequestSignBuilder;

  @Mock
  private VehicleComponent vehicleComponent;

  @InjectMocks
  private VehicleBindServiceImpl vehicleBindService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Nested
  class ListVehicleBindTests {
    @Test
    void shouldReturnFailResultWhenParamIsNull() {
      Result<List<VehicleBindVO>> result = vehicleBindService.listVehicleBind(null);
      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailResultWhenMerchantIdIsNull() {
      VehicleBindQueryParam param = new VehicleBindQueryParam();
      Result<List<VehicleBindVO>> result = vehicleBindService.listVehicleBind(param);
      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnEmptyListWhenNoBindingsFound() {
      VehicleBindQueryParam param = new VehicleBindQueryParam();
      param.setMerchantId(1L);
      when(vehicleBindMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      Result<List<VehicleBindVO>> result = vehicleBindService.listVehicleBind(param);
      assertTrue(result.isSuccess());
      assertTrue(result.getModel().isEmpty());
    }

    @Test
    void shouldReturnBindListSuccessfully() {
      // Arrange
      VehicleBindQueryParam param = new VehicleBindQueryParam();
      param.setMerchantId(1L);
      param.setVehicleModelId(1L);
      param.setChannelId(2L);

      VehicleBind bind = new VehicleBind();
      bind.setId(1L);
      bind.setVehicleModelId(1L);
      bind.setChannelId(2L);
      bind.setMerchantId(1L);
      bind.setBindChannelVehicleId("test_id");
      bind.setBindChannelVehicleName("Test Vehicle");
      bind.setSynced(VehicleBindSyncedEnums.SYNCED.getSynced());

      when(vehicleBindMapper.selectByExample(any())).thenReturn(Collections.singletonList(bind));

      // Act
      Result<List<VehicleBindVO>> result = vehicleBindService.listVehicleBind(param);

      // Assert
      assertTrue(result.isSuccess());
      assertEquals(1, result.getModel().size());
      assertEquals("test_id", result.getModel().get(0).getBindChannelVehicleId());
      verify(vehicleBindMapper).selectByExample(any());
    }
  }

  @Nested
  class UpdateOtherVehicleBindTests {
    @Test
    void shouldReturnFailWhenParamIsInvalid() {
      // Arrange
      List<VehicleBindVO> paramList = new ArrayList<>();
      Long vehicleSubSeryId = null;
      LoginVo loginVo = new LoginVo();

      // Act
      Result<Integer> result = vehicleBindService.updateOtherVehicleBind(paramList, vehicleSubSeryId, loginVo);

      // Assert
      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
      verifyNoInteractions(vehicleModelMapper);
    }

    @Test
    void shouldUpdateBindsSuccessfully() {
      // Arrange
      List<VehicleBindVO> paramList = new ArrayList<>();
      VehicleBindVO bindVO = new VehicleBindVO();
      bindVO.setChannelId(1L);
      bindVO.setBindChannelVehicleSeryId("123");
      bindVO.setBindChannelVehicleName("Test Vehicle");
      paramList.add(bindVO);

      Long vehicleSubSeryId = 1L;
      LoginVo loginVo = new LoginVo();
      loginVo.setUserId(1L);
      loginVo.setMerchantId(1L);

      VehicleModel model = new VehicleModel();
      model.setId(1L);
      model.setMerchantId(loginVo.getMerchantId());
      model.setStatus(VehicleModelStatusEnum.ON_SELLING.getStatus());
      model.setLicenseTypeId(1L);
      model.setLicenseType("C1");

      // Mock channel service
      List<ChannelVO> channelList = new ArrayList<>();
      ChannelVO channel = new ChannelVO();
      channel.setId(1L);
      channel.setChannelName("Test Channel");
      channelList.add(channel);
      when(channelService.listAllChannel(YesOrNoEnum.NO.getValue())).thenReturn(ResultUtil.successResult(channelList));

      // Mock vehicle component
      when(vehicleComponent.transferLicenseTypePinyin(any(), any())).thenReturn("C1");

      when(vehicleModelMapper.selectByExample(any())).thenReturn(Collections.singletonList(model));
      when(vehicleBindMapper.selectByExample(any())).thenReturn(Collections.emptyList());
      when(vehicleBindMapper.batchInsert(any())).thenReturn(1);

      // Act
      Result<Integer> result = vehicleBindService.updateOtherVehicleBind(paramList, vehicleSubSeryId, loginVo);

      // Assert
      assertTrue(result.isSuccess());
      assertEquals(1, result.getModel());
      verify(vehicleModelMapper).selectByExample(any());
      verify(vehicleBindMapper).selectByExample(any());
      verify(vehicleBindMapper).batchInsert(any());
      verify(channelService).listAllChannel(any());
      verify(vehicleComponent).transferLicenseTypePinyin(any(), any());
    }
  }

  @Nested
  class CreateCtripBindTests {
    @Test
    void shouldReturnSuccessWhenCreateCtripBind() {
      // Prepare test data
      List<VehicleModelVO> vehicleModels = new ArrayList<>();
      VehicleModelVO model = new VehicleModelVO();
      model.setId(1L);
      model.setModelStatus((byte) 1);
      model.setLicenseTypeId(1L);
      model.setLicenseType("C1");
      vehicleModels.add(model);

      String ctripSubSeryId = "123";
      LoginVo loginVo = new LoginVo();
      loginVo.setUserId(1L);
      loginVo.setMerchantId(1L);

      // Mock VehicleSubSery
      VehicleSubSery vehicleSubSery = new VehicleSubSery();
      vehicleSubSery.setCtripSubSeryId(123L);
      when(vehicleSubSeryMapper.selectByExample(any())).thenReturn(Collections.singletonList(vehicleSubSery));

      // Mock VehicleComponent
      when(vehicleComponent.generateSubSeryName(any())).thenReturn("Test SubSery");
      when(vehicleComponent.transferLicenseTypePinyin(any(), any())).thenReturn("C1");

      // Mock VehicleBindMapper
      when(vehicleBindMapper.batchInsert(any())).thenReturn(1);

      // Execute test
      Result<List<VehicleModelDTO.VehicleBindDTO>> result = vehicleBindService.createCtripBind(vehicleModels,
          ctripSubSeryId, loginVo);

      // Verify results
      assertTrue(result.isSuccess());
      assertNotNull(result.getModel());
      assertFalse(result.getModel().isEmpty());

      VehicleModelDTO.VehicleBindDTO bindDTO = result.getModel().get(0);
      assertEquals(1L, bindDTO.getVehicleModelId());
      assertEquals(IChannelService.CTRIP_CHANNEL_ID, bindDTO.getChannelId());
      assertEquals(1L, bindDTO.getMerchantId());
      assertEquals("1_123_C1", bindDTO.getBindChannelVehicleId());
      assertEquals("Test SubSery", bindDTO.getBindChannelVehicleName());
      assertEquals("123", bindDTO.getBindChannelVehicleSeryId());
      assertEquals("", bindDTO.getSyncFailedReason());

      // Verify interactions
      verify(vehicleBindMapper).batchInsert(any());
      verify(vehicleComponent).generateSubSeryName(any());
      verify(vehicleComponent).transferLicenseTypePinyin(any(), any());
    }
  }

  @Nested
  class ListBySubSeryIdTests {
    @Test
    void shouldReturnFailWhenParamIsInvalid() {
      // Arrange
      Long vehicleSubSeryId = null;
      Long merchantId = 1L;

      // Act
      Result<List<VehicleBindVO>> result = vehicleBindService.listBySubSeryId(vehicleSubSeryId, merchantId);

      // Assert
      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
      verifyNoInteractions(vehicleModelMapper);
    }

    @Test
    void shouldReturnEmptyListWhenNoModelsFound() {
      // Arrange
      Long vehicleSubSeryId = 1L;
      Long merchantId = 1L;

      when(vehicleModelMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      // Act
      Result<List<VehicleBindVO>> result = vehicleBindService.listBySubSeryId(vehicleSubSeryId, merchantId);

      // Assert
      assertTrue(result.isSuccess());
      assertTrue(result.getModel().isEmpty());
      verify(vehicleModelMapper).selectByExample(any());
    }

    @Test
    void shouldReturnBindListSuccessfully() {
      // Arrange
      Long vehicleSubSeryId = 1L;
      Long merchantId = 1L;

      VehicleModel model = new VehicleModel();
      model.setId(1L);
      model.setVehicleSubSeryId(vehicleSubSeryId);
      model.setMerchantId(merchantId);
      model.setStatus(VehicleModelStatusEnum.ON_SELLING.getStatus());

      VehicleBind bind = new VehicleBind();
      bind.setId(1L);
      bind.setVehicleModelId(1L);
      bind.setChannelId(2L);
      bind.setMerchantId(merchantId);
      bind.setBindChannelVehicleId("test_id");
      bind.setBindChannelVehicleName("Test Vehicle");
      bind.setSynced(VehicleBindSyncedEnums.SYNCED.getSynced());

      when(vehicleModelMapper.selectByExample(any())).thenReturn(Collections.singletonList(model));
      when(vehicleBindMapper.selectByExample(any())).thenReturn(Collections.singletonList(bind));

      // Act
      Result<List<VehicleBindVO>> result = vehicleBindService.listBySubSeryId(vehicleSubSeryId, merchantId);

      // Assert
      assertTrue(result.isSuccess());
      assertEquals(1, result.getModel().size());
      assertEquals("test_id", result.getModel().get(0).getBindChannelVehicleId());
      verify(vehicleModelMapper).selectByExample(any());
      verify(vehicleBindMapper).selectByExample(any());
    }
  }

  @Test
  public void testUpdateCtripBind() {
    // 准备测试数据
    VehicleModelVO vehicleModel = new VehicleModelVO();
    vehicleModel.setId(1L);
    vehicleModel.setModelStatus(VehicleModelStatusEnum.ON_SELLING.getStatus());

    String ctripSubSeryId = "123";
    LoginVo loginVo = new LoginVo();
    loginVo.setUserId(1L);
    loginVo.setMerchantId(1L);

    // Mock existing bind
    VehicleBind existingBind = new VehicleBind();
    existingBind.setId(1L);
    existingBind.setVehicleModelId(1L);
    existingBind.setChannelId(IChannelService.CTRIP_CHANNEL_ID);
    existingBind.setDeleted(YesOrNoEnum.NO.getValue());
    when(vehicleBindMapper.selectByExample(any())).thenReturn(Collections.singletonList(existingBind));

    // Mock VehicleSubSery
    VehicleSubSery vehicleSubSery = new VehicleSubSery();
    vehicleSubSery.setCtripSubSeryId(123L);
    when(vehicleSubSeryMapper.selectByExample(any())).thenReturn(Collections.singletonList(vehicleSubSery));

    // Mock VehicleComponent
    when(vehicleComponent.generateSubSeryName(any())).thenReturn("Test SubSery");
    when(vehicleComponent.transferLicenseTypePinyin(any(), any())).thenReturn("C1");

    Result<VehicleModelDTO.VehicleBindDTO> result = vehicleBindService.updateCtripBind(vehicleModel, ctripSubSeryId,
        loginVo);

    assertTrue(result.isSuccess());
    assertNotNull(result.getModel());
    assertEquals(1L, result.getModel().getVehicleModelId());

    verify(vehicleBindMapper).updateByExampleSelective(any(), any());
  }
}