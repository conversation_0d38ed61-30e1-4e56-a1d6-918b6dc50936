package com.ql.rent.provider.store;

import com.ql.rent.api.aggregate.model.vo.store.ServicePolicyVO;
import com.ql.rent.api.aggregate.model.vo.store.ServicePolicyVO.*;
import com.ql.rent.common.IRedisService;
import com.ql.rent.component.PlatformBiz;
import com.ql.rent.dao.store.*;
import com.ql.rent.entity.store.*;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.service.store.IStoreInfoService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("ServicePolicyServiceImpl Tests")
class ServicePolicyServiceImplTest {

  @Mock
  private ServicePolicyMapper servicePolicyMapper;
  @Mock
  private ServicePolicyCertificatMapper servicePolicyCertificatMapper;
  @Mock
  private ServicePolicyChargeMapper servicePolicyChargeMapper;
  @Mock
  private ServicePolicyProhibitedAreaMapper servicePolicyProhibitedAreaMapper;
  @Mock
  private ServicePolicyRemarkMapper servicePolicyRemarkMapper;
  @Mock
  private ServicePolicyRentTimeMapper servicePolicyRentTimeMapper;
  @Mock
  private ServicePolicyRoadRescueMapper servicePolicyRoadRescueMapper;
  @Mock
  private ServicePolicyViolationHandleMapper servicePolicyViolationHandleMapper;
  @Mock
  private ServicePolicyStoreMapper servicePolicyStoreMapper;
  @Mock
  private IStoreInfoService storeInfoService;
  @Mock
  private PlatformBiz platformBiz;
  @Mock
  private IRedisService redisService;

  @InjectMocks
  private ServicePolicyServiceImpl servicePolicyService;

  private ServicePolicyVO validServicePolicyVO;
  private ServicePolicy existingServicePolicy;
  private final Long VALID_MERCHANT_ID = 1L;
  private final Long VALID_USER_ID = 100L;
  private final Long VALID_POLICY_ID = 1000L;

  @BeforeEach
  void setUp() {
    // Setup valid ServicePolicyVO
    validServicePolicyVO = new ServicePolicyVO();
    validServicePolicyVO.setMerchantId(VALID_MERCHANT_ID);
    validServicePolicyVO.setPolicyName("Test Policy");
    validServicePolicyVO.setRentTimePolicy(new RentTimeVO());
    validServicePolicyVO.setCertificatPolicy(new CertificatVO());
    validServicePolicyVO.setChargePolicy(new ChargeVO());
    validServicePolicyVO.setProhibitedArea(new ProhibitedAreaVO());
    validServicePolicyVO.setRoadRescue(new RoadRescueVO());
    validServicePolicyVO.setViolationHandle(new ViolationHandleVO());

    // Setup existing ServicePolicy
    existingServicePolicy = new ServicePolicy();
    existingServicePolicy.setId(VALID_POLICY_ID);
    existingServicePolicy.setPolicyName("Test Policy");
    existingServicePolicy.setMerchantId(VALID_MERCHANT_ID);
    existingServicePolicy.setDeleted(YesOrNoEnum.NO.getValue());
    existingServicePolicy.setLastVer(1);
  }

  @Nested
  @DisplayName("saveServicePolicy Tests")
  class SaveServicePolicyTests {

    @Test
    @DisplayName("Should successfully create new service policy")
    void shouldSuccessfullyCreateNewServicePolicy() {
      // Arrange
      when(servicePolicyMapper.insertSelective(any(ServicePolicy.class)))
          .thenReturn(1);

      // Act
      Result<Long> result = servicePolicyService.saveServicePolicy(validServicePolicyVO, VALID_USER_ID);

      // Assert
      assertTrue(result.isSuccess());
      verify(servicePolicyMapper).insertSelective(any(ServicePolicy.class));
    }

    @Test
    @DisplayName("Should fail when required parameters are missing")
    void shouldFailWhenRequiredParametersAreMissing() {
      // Arrange
      ServicePolicyVO invalidPolicy = new ServicePolicyVO();

      // Act
      Result<Long> result = servicePolicyService.saveServicePolicy(invalidPolicy, VALID_USER_ID);

      // Assert
      assertFalse(result.isSuccess());
      verify(servicePolicyMapper, never()).insertSelective(any(ServicePolicy.class));
    }

    @Test
    @DisplayName("Should successfully update existing service policy")
    void shouldSuccessfullyUpdateExistingServicePolicy() {
      // Arrange
      validServicePolicyVO.setId(VALID_POLICY_ID);
      when(servicePolicyMapper.selectByPrimaryKey(VALID_POLICY_ID)).thenReturn(existingServicePolicy);
      when(servicePolicyMapper.updateByPrimaryKeySelective(any(ServicePolicy.class))).thenReturn(1);

      // Act
      Result<Long> result = servicePolicyService.saveServicePolicy(validServicePolicyVO, VALID_USER_ID);

      // Assert
      assertTrue(result.isSuccess());
      assertEquals(VALID_POLICY_ID, result.getModel());
      verify(servicePolicyMapper).updateByPrimaryKeySelective(any(ServicePolicy.class));
    }
  }

  @Nested
  @DisplayName("checkPolicyNameOccupied Tests")
  class CheckPolicyNameOccupiedTests {

    @Test
    @DisplayName("Should throw exception when policy name is occupied")
    void shouldThrowExceptionWhenPolicyNameIsOccupied() {
      // Arrange
      when(servicePolicyMapper.selectByExample(any())).thenReturn(Collections.singletonList(new ServicePolicy()));

      // Act & Assert
      assertThrows(BizException.class,
          () -> servicePolicyService.checkPolicyNameOccupied("Occupied Name", null, VALID_MERCHANT_ID));
    }

    @Test
    @DisplayName("Should not throw exception when policy name is available")
    void shouldNotThrowExceptionWhenPolicyNameIsAvailable() {
      // Arrange
      when(servicePolicyMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      // Act & Assert
      assertDoesNotThrow(() -> servicePolicyService.checkPolicyNameOccupied("Available Name", null, VALID_MERCHANT_ID));
    }
  }

  @Nested
  @DisplayName("getServicePolicy Tests")
  class GetServicePolicyTests {

    @Test
    @DisplayName("Should successfully get service policy")
    void shouldSuccessfullyGetServicePolicy() {
      // Arrange
      when(servicePolicyMapper.selectByPrimaryKey(VALID_POLICY_ID)).thenReturn(existingServicePolicy);

      // Mock related policies
      ServicePolicyRentTime rentTime = new ServicePolicyRentTime();
      rentTime.setForceRerentFeeTypes("1,2,3");
      when(servicePolicyRentTimeMapper.selectByExample(any(ServicePolicyRentTimeExample.class))).thenReturn(Collections.singletonList(rentTime));

      // Act
      Result<ServicePolicyVO> result = servicePolicyService.getServicePolicy(VALID_POLICY_ID, VALID_MERCHANT_ID);

      // Assert
      assertTrue(result.isSuccess());
      assertNotNull(result.getModel());
      assertEquals(VALID_POLICY_ID, result.getModel().getId());
      assertEquals("Test Policy", result.getModel().getPolicyName());
    }

    @Test
    @DisplayName("Should fail when policy does not exist")
    void shouldFailWhenPolicyDoesNotExist() {
      // Arrange
      when(servicePolicyMapper.selectByPrimaryKey(VALID_POLICY_ID)).thenReturn(null);

      // Act
      Result<ServicePolicyVO> result = servicePolicyService.getServicePolicy(VALID_POLICY_ID, VALID_MERCHANT_ID);

      // Assert
      assertFalse(result.isSuccess());
    }
  }

  @Nested
  @DisplayName("deletePolicy Tests")
  class DeletePolicyTests {

    @Test
    @DisplayName("Should successfully delete service policy")
    void shouldSuccessfullyDeleteServicePolicy() {
      // Arrange
      when(servicePolicyMapper.selectByPrimaryKey(VALID_POLICY_ID)).thenReturn(existingServicePolicy);
      when(servicePolicyMapper.updateByPrimaryKeySelective(any())).thenReturn(1);

      // Act
      Result<Integer> result = servicePolicyService.deletePolicy(VALID_POLICY_ID, VALID_USER_ID);

      // Assert
      assertTrue(result.isSuccess());
      assertEquals(1, result.getModel());
      verify(servicePolicyMapper).updateByPrimaryKeySelective(any());
    }

    @Test
    @DisplayName("Should fail when policy does not exist")
    void shouldFailWhenPolicyDoesNotExist() {
      // Arrange
      when(servicePolicyMapper.selectByPrimaryKey(VALID_POLICY_ID)).thenReturn(null);

      // Act
      Result<Integer> result = servicePolicyService.deletePolicy(VALID_POLICY_ID, VALID_USER_ID);

      // Assert
      assertFalse(result.isSuccess());
    }
  }

  @Nested
  @DisplayName("listServicePolicy Tests")
  class ListServicePolicyTests {

    @Test
    @DisplayName("Should successfully list service policies")
    void shouldSuccessfullyListServicePolicies() {
      // Arrange
      List<ServicePolicy> policies = Arrays.asList(existingServicePolicy);
      when(servicePolicyMapper.selectByExample(any())).thenReturn(policies);

      // Act
      Result<List<ServicePolicyVO>> result = servicePolicyService.listServicePolicy(VALID_MERCHANT_ID);

      // Assert
      assertTrue(result.isSuccess());
      assertNotNull(result.getModel());
      assertFalse(result.getModel().isEmpty());
      assertEquals(1, result.getModel().size());
    }

    @Test
    @DisplayName("Should return empty list when no policies exist")
    void shouldReturnEmptyListWhenNoPoliciesExist() {
      // Arrange
      when(servicePolicyMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      // Act
      Result<List<ServicePolicyVO>> result = servicePolicyService.listServicePolicy(VALID_MERCHANT_ID);

      // Assert
      assertTrue(result.isSuccess());
      assertTrue(result.getModel().isEmpty());
    }
  }
}