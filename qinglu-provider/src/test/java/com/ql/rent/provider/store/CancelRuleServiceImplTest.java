package com.ql.rent.provider.store;

import com.ql.rent.common.IRedisService;
import com.ql.rent.component.PlatformBiz;
import com.ql.rent.dao.store.CancelRuleMapper;
import com.ql.rent.dao.store.CancelRuleTimeMapper;
import com.ql.rent.dao.store.StoreInfoMapper;
import com.ql.rent.entity.store.CancelRule;
import com.ql.rent.entity.store.CancelRuleExample;
import com.ql.rent.entity.store.CancelRuleTime;
import com.ql.rent.entity.store.CancelRuleTimeExample;
import com.ql.rent.entity.store.StoreInfo;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.store.RuleTypeEnum;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.store.CancelRuleTimeVo;
import com.ql.rent.vo.store.CancelRuleVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class CancelRuleServiceImplTest {

  @Mock
  private CancelRuleMapper cancelRuleMapper;

  @Mock
  private CancelRuleTimeMapper cancelRuleTimeMapper;

  @Mock
  private StoreInfoMapper storeInfoMapper;

  @Mock
  private IRedisService redisService;

  @Mock
  private PlatformBiz platformBiz;

  @InjectMocks
  private CancelRuleServiceImpl cancelRuleService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Nested
  class RuleSaveTests {
    @Test
    void shouldFailWhenRuleNameExists() {
      // Arrange
      CancelRuleVo param = new CancelRuleVo();
      param.setMerchantId(1L);
      param.setRuleName("Test Rule");
      param.setRuleType(RuleTypeEnum.NORMAL.getValue());

      CancelRule existingRule = new CancelRule();
      existingRule.setRuleName("Test Rule");
      existingRule.setDeleted(YesOrNoEnum.NO.getValue());

      when(cancelRuleMapper.selectByExample(any())).thenReturn(Collections.singletonList(existingRule));

      // Act
      Result<Long> result = cancelRuleService.ruleSave(param, 1L);

      // Assert
      assertFalse(result.isSuccess());
      assertEquals("规则名称已存在", result.getMessage());
      verify(cancelRuleMapper).selectByExample(any());
      verifyNoMoreInteractions(cancelRuleMapper);
    }

    @Test
    void shouldSaveNewRuleSuccessfully() {
      // Arrange
      CancelRuleVo param = new CancelRuleVo();
      param.setMerchantId(1L);
      param.setRuleName("New Rule");
      param.setRuleType(RuleTypeEnum.NORMAL.getValue());

      List<CancelRuleTimeVo> ruleTimeList = new ArrayList<>();
      CancelRuleTimeVo timeVo = new CancelRuleTimeVo();
      timeVo.setBeforeHour(24.0);
      timeVo.setTimeType((byte) 1);
      ruleTimeList.add(timeVo);
      param.setRuleTimeList(ruleTimeList);

      when(cancelRuleMapper.selectByExample(any())).thenReturn(Collections.emptyList());
      when(cancelRuleMapper.insertSelective(any())).thenReturn(1);

      // Act
      Result<Long> result = cancelRuleService.ruleSave(param, 1L);

      // Assert
      assertTrue(result.isSuccess());
      verify(cancelRuleMapper).selectByExample(any());
      verify(cancelRuleMapper).insertSelective(any());
      verify(cancelRuleTimeMapper).selectByExample(any());
    }
  }

  @Nested
  class RuleFindTests {
    @Test
    void shouldReturnFailWhenRuleNotFound() {
      // Arrange
      Long id = 1L;
      when(cancelRuleMapper.selectByPrimaryKey(id)).thenReturn(null);

      // Act
      Result<CancelRuleVo> result = cancelRuleService.ruleFind(id);

      // Assert
      assertFalse(result.isSuccess());
      assertEquals("取消规则不存在", result.getMessage());
      verify(cancelRuleMapper).selectByPrimaryKey(id);
    }

    @Test
    void shouldReturnRuleSuccessfully() {
      // Arrange
      Long id = 1L;
      CancelRule rule = new CancelRule();
      rule.setId(id);
      rule.setMerchantId(1L);
      rule.setRuleName("Test Rule");
      rule.setRuleType(RuleTypeEnum.NORMAL.getValue());

      when(cancelRuleMapper.selectByPrimaryKey(id)).thenReturn(rule);
      when(cancelRuleTimeMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      // Act
      Result<CancelRuleVo> result = cancelRuleService.ruleFind(id);

      // Assert
      assertTrue(result.isSuccess());
      assertNotNull(result.getModel());
      assertEquals(id, result.getModel().getId());
      assertEquals("Test Rule", result.getModel().getRuleName());
      verify(cancelRuleMapper).selectByPrimaryKey(id);
      verify(cancelRuleTimeMapper).selectByExample(any());
    }
  }

  @Nested
  class RuleListTests {
    @Test
    void shouldReturnEmptyListWhenNoRulesFound() {
      // Arrange
      Long merchantId = 1L;
      when(cancelRuleMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      // Act
      Result<List<CancelRuleVo>> result = cancelRuleService.ruleList(merchantId);

      // Assert
      assertTrue(result.isSuccess());
      assertTrue(result.getModel().isEmpty());
      verify(cancelRuleMapper).selectByExample(any());
    }

    @Test
    void shouldReturnRuleListSuccessfully() {
      // Arrange
      Long merchantId = 1L;
      CancelRule rule = new CancelRule();
      rule.setId(1L);
      rule.setMerchantId(merchantId);
      rule.setRuleName("Test Rule");
      rule.setRuleType(RuleTypeEnum.NORMAL.getValue());

      when(cancelRuleMapper.selectByExample(any())).thenReturn(Collections.singletonList(rule));
      when(cancelRuleTimeMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      // Act
      Result<List<CancelRuleVo>> result = cancelRuleService.ruleList(merchantId);

      // Assert
      assertTrue(result.isSuccess());
      assertEquals(1, result.getModel().size());
      assertEquals("Test Rule", result.getModel().get(0).getRuleName());
      verify(cancelRuleMapper).selectByExample(any());
      verify(cancelRuleTimeMapper).selectByExample(any());
    }
  }

  @Nested
  class RuleListByStoreIdTests {
    @Test
    void shouldReturnFailWhenStoreNotFound() {
      // Arrange
      Long storeId = 1L;
      when(storeInfoMapper.selectByPrimaryKey(storeId)).thenReturn(null);

      // Act
      Result<List<CancelRuleVo>> result = cancelRuleService.ruleListByStoreId(storeId);

      // Assert
      assertFalse(result.isSuccess());
      assertEquals("门店不存在", result.getMessage());
      verify(storeInfoMapper).selectByPrimaryKey(storeId);
    }

    @Test
    void shouldReturnRuleListSuccessfully() {
      // Arrange
      Long storeId = 1L;
      Long merchantId = 2L;

      StoreInfo storeInfo = new StoreInfo();
      storeInfo.setId(storeId);
      storeInfo.setMerchantId(merchantId);

      CancelRule rule = new CancelRule();
      rule.setId(1L);
      rule.setMerchantId(merchantId);
      rule.setRuleName("Test Rule");

      when(storeInfoMapper.selectByPrimaryKey(storeId)).thenReturn(storeInfo);
      when(cancelRuleMapper.selectByExample(any())).thenReturn(Collections.singletonList(rule));
      when(cancelRuleTimeMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      // Act
      Result<List<CancelRuleVo>> result = cancelRuleService.ruleListByStoreId(storeId);

      // Assert
      assertTrue(result.isSuccess());
      assertEquals(1, result.getModel().size());
      verify(storeInfoMapper).selectByPrimaryKey(storeId);
      verify(cancelRuleMapper).selectByExample(any());
    }
  }

  @Nested
  class RuleStatusUpdTests {
    @Test
    void shouldReturnFailWhenRuleNotFound() {
      // Arrange
      CancelRuleVo param = new CancelRuleVo();
      param.setId(1L);
      param.setStatus((byte) 1);

      when(cancelRuleMapper.selectByPrimaryKey(param.getId())).thenReturn(null);

      // Act
      Result<Boolean> result = cancelRuleService.ruleStatusUpd(param);

      // Assert
      assertFalse(result.isSuccess());
      assertEquals("规则不存在", result.getMessage());
      verify(cancelRuleMapper).selectByPrimaryKey(param.getId());
    }

    @Test
    void shouldUpdateStatusSuccessfully() {
      // Arrange
      CancelRuleVo param = new CancelRuleVo();
      param.setId(1L);
      param.setStatus((byte) 1);
      param.setOpUserId(1L);

      CancelRule existingRule = new CancelRule();
      existingRule.setId(param.getId());
      existingRule.setMerchantId(1L);

      when(cancelRuleMapper.selectByPrimaryKey(param.getId())).thenReturn(existingRule);
      when(cancelRuleMapper.updateByPrimaryKeySelective(any())).thenReturn(1);

      // Act
      Result<Boolean> result = cancelRuleService.ruleStatusUpd(param);

      // Assert
      assertTrue(result.isSuccess());
      assertTrue(result.getModel());
      verify(cancelRuleMapper).selectByPrimaryKey(param.getId());
      verify(cancelRuleMapper).updateByPrimaryKeySelective(any());
    }
  }

  @Nested
  class RuleDeleteTests {
    @Test
    void shouldDeleteRuleAndTimeRulesSuccessfully() {
      // Arrange
      Long id = 1L;
      Long userId = 1L;

      // Act
      Result<Boolean> result = cancelRuleService.ruleDelete(id, userId);

      // Assert
      assertTrue(result.isSuccess());
      assertTrue(result.getModel());

      // Verify CancelRule update
      ArgumentCaptor<CancelRule> ruleCaptor = ArgumentCaptor.forClass(CancelRule.class);
      verify(cancelRuleMapper).updateByPrimaryKeySelective(ruleCaptor.capture());
      CancelRule capturedRule = ruleCaptor.getValue();
      assertEquals(id, capturedRule.getId());
      assertEquals(YesOrNoEnum.YES.getValue(), capturedRule.getDeleted());
      assertEquals(userId, capturedRule.getOpUserId());

      // Verify CancelRuleTime update
      ArgumentCaptor<CancelRuleTime> timeCaptor = ArgumentCaptor.forClass(CancelRuleTime.class);
      ArgumentCaptor<CancelRuleTimeExample> exampleCaptor = ArgumentCaptor.forClass(CancelRuleTimeExample.class);
      verify(cancelRuleTimeMapper).updateByExampleSelective(timeCaptor.capture(), exampleCaptor.capture());

      CancelRuleTime capturedTime = timeCaptor.getValue();
      assertEquals(YesOrNoEnum.YES.getValue(), capturedTime.getDeleted());
      assertEquals(userId, capturedTime.getOpUserId());
    }
  }
}