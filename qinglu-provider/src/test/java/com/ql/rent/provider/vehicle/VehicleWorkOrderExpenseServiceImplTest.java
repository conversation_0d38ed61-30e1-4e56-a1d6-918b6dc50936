package com.ql.rent.provider.vehicle;

import com.ql.rent.dao.vehicle.VehicleWorkOrderExpenseMapper;
import com.ql.rent.dao.vehicle.VehicleWorkOrderExpensePropMapper;
import com.ql.rent.entity.vehicle.VehicleWorkOrderExpense;
import com.ql.rent.entity.vehicle.VehicleWorkOrderExpenseProp;
import com.ql.rent.entity.vehicle.VehicleWorkOrderExpensePropExample;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.param.vehicle.BatchWorkOrderExpenseParam;
import com.ql.rent.param.vehicle.WorkOrderExpenseParam;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.vehicle.VehicleWorkOrderExpenseVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class VehicleWorkOrderExpenseServiceImplTest {

  @Mock
  private VehicleWorkOrderExpenseMapper vehicleWorkOrderExpenseMapper;

  @Mock
  private VehicleWorkOrderExpensePropMapper vehicleWorkOrderExpensePropMapper;

  @InjectMocks
  private VehicleWorkOrderExpenseServiceImpl vehicleWorkOrderExpenseService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Nested
  class SaveExpenseTests {
    @Test
    void shouldReturnFailWhenParamIsNull() {
      Result<Integer> result = vehicleWorkOrderExpenseService.saveExpense(null);

      assertFalse(result.isSuccess());
      assertEquals("保存工单费用项参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenExpenseListIsEmpty() {
      BatchWorkOrderExpenseParam param = BatchWorkOrderExpenseParam.builder()
          .workOrderId(1L)
          .merchantId(1L)
          .opUserId(1L)
          .expenseList(Collections.emptyList())
          .build();

      Result<Integer> result = vehicleWorkOrderExpenseService.saveExpense(param);

      assertFalse(result.isSuccess());
      assertEquals("保存工单费用项参数错误", result.getMessage());
    }

    @Test
    void shouldThrowExceptionWhenExpensePropNotFound() {
      WorkOrderExpenseParam expenseParam = new WorkOrderExpenseParam();
      expenseParam.setExpenseItemPropId(1L);
      expenseParam.setAmount(100L);

      BatchWorkOrderExpenseParam param = BatchWorkOrderExpenseParam.builder()
          .workOrderId(1L)
          .merchantId(1L)
          .opUserId(1L)
          .expenseList(Collections.singletonList(expenseParam))
          .build();

      when(vehicleWorkOrderExpensePropMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      assertThrows(BizException.class, () -> vehicleWorkOrderExpenseService.saveExpense(param),
          "未查询到对应的费用选项");
    }

    @Test
    void shouldSaveExpenseSuccessfully() {
      WorkOrderExpenseParam expenseParam = new WorkOrderExpenseParam();
      expenseParam.setExpenseItemPropId(1L);
      expenseParam.setAmount(100L);

      BatchWorkOrderExpenseParam param = BatchWorkOrderExpenseParam.builder()
          .workOrderId(1L)
          .merchantId(1L)
          .opUserId(1L)
          .workOrderType((byte) 1)
          .expenseList(Collections.singletonList(expenseParam))
          .build();

      VehicleWorkOrderExpenseProp prop = new VehicleWorkOrderExpenseProp();
      prop.setId(1L);
      prop.setExpenseName("测试费用");

      when(vehicleWorkOrderExpensePropMapper.selectByExample(any()))
          .thenReturn(Collections.singletonList(prop));

      Result<Integer> result = vehicleWorkOrderExpenseService.saveExpense(param);

      assertTrue(result.isSuccess());
      assertEquals(Integer.valueOf(1), result.getModel());

      ArgumentCaptor<List<VehicleWorkOrderExpense>> captor = ArgumentCaptor.forClass(List.class);
      verify(vehicleWorkOrderExpenseMapper).batchInsert(captor.capture());
      List<VehicleWorkOrderExpense> saved = captor.getValue();
      assertEquals(1, saved.size());
      assertEquals(param.getWorkOrderId(), saved.get(0).getWorkOrderId());
      assertEquals(expenseParam.getExpenseItemPropId(), saved.get(0).getExpensePropId());
      assertEquals("测试费用", saved.get(0).getExpenseName());
      assertEquals(expenseParam.getAmount(), saved.get(0).getAmount());
      assertEquals(param.getMerchantId(), saved.get(0).getMerchantId());
      assertEquals(YesOrNoEnum.NO.getValue(), saved.get(0).getDeleted());
      assertEquals(param.getWorkOrderType(), saved.get(0).getWorkOrderType());
    }
  }

  @Nested
  class ListByWorkOrderIdTests {
    @Test
    void shouldReturnFailWhenParamsAreNull() {
      Result<List<VehicleWorkOrderExpenseVO>> result = vehicleWorkOrderExpenseService.listByWorkOrderId(null, null);

      assertFalse(result.isSuccess());
      assertEquals("查询工单费用项参数为空", result.getMessage());
    }

    @Test
    void shouldReturnEmptyListWhenNoExpensesFound() {
      when(vehicleWorkOrderExpenseMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      Result<List<VehicleWorkOrderExpenseVO>> result = vehicleWorkOrderExpenseService.listByWorkOrderId(1L, (byte) 1);

      assertTrue(result.isSuccess());
      assertTrue(result.getModel().isEmpty());
    }

    @Test
    void shouldReturnExpenseList() {
      VehicleWorkOrderExpense expense1 = createExpense(1L, "费用1", 100L);
      VehicleWorkOrderExpense expense2 = createExpense(2L, "费用2", 200L);

      when(vehicleWorkOrderExpenseMapper.selectByExample(any()))
          .thenReturn(Arrays.asList(expense1, expense2));

      Result<List<VehicleWorkOrderExpenseVO>> result = vehicleWorkOrderExpenseService.listByWorkOrderId(1L, (byte) 1);

      assertTrue(result.isSuccess());
      assertEquals(2, result.getModel().size());

      VehicleWorkOrderExpenseVO vo1 = result.getModel().get(0);
      assertEquals(expense1.getId(), vo1.getId());
      assertEquals(expense1.getExpensePropId(), vo1.getExpensePropId());
      assertEquals(expense1.getExpenseName(), vo1.getExpenseName());
      assertEquals(expense1.getAmount(), vo1.getAmount());
      assertEquals(expense1.getWorkOrderId(), vo1.getWorkOrderId());

      VehicleWorkOrderExpenseVO vo2 = result.getModel().get(1);
      assertEquals(expense2.getId(), vo2.getId());
      assertEquals(expense2.getExpensePropId(), vo2.getExpensePropId());
      assertEquals(expense2.getExpenseName(), vo2.getExpenseName());
      assertEquals(expense2.getAmount(), vo2.getAmount());
      assertEquals(expense2.getWorkOrderId(), vo2.getWorkOrderId());
    }
  }

  private VehicleWorkOrderExpense createExpense(Long id, String name, Long amount) {
    VehicleWorkOrderExpense expense = new VehicleWorkOrderExpense();
    expense.setId(id);
    expense.setWorkOrderId(1L);
    expense.setWorkOrderType((byte) 1);
    expense.setExpensePropId(id);
    expense.setExpenseName(name);
    expense.setAmount(amount);
    expense.setMerchantId(1L);
    expense.setDeleted(YesOrNoEnum.NO.getValue());
    expense.setCreateTime(System.currentTimeMillis());
    expense.setOpTime(System.currentTimeMillis());
    expense.setOpUserId(1L);
    return expense;
  }
}