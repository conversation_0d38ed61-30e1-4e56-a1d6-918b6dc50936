package com.ql.rent.provider.store;

import com.ql.rent.AbstractTest;
import com.ql.rent.param.store.StorePersonalAccountInfoParam;
import com.ql.rent.param.store.StorePersonalAccountInfoQueryParam;
import com.ql.rent.service.store.IStorePersonalAccountInfoService;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.store.StorePersonalAccountInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.test.annotation.Rollback;

import javax.annotation.Resource;

/**
 * 门店个人账户信息服务测试类
 * 
 * <AUTHOR>
 */
@Slf4j
public class StorePersonalAccountInfoServiceImplTest extends AbstractTest {

    @Resource
    private IStorePersonalAccountInfoService storePersonalAccountInfoService;

    private static final Long userId = 1L;
    private static final Long storeAccountInfoId = 1L;

    /**
     * 测试新增门店个人账户信息
     */
    @Test
    @Rollback
    public void testAdd() {
        log.info("=== 测试新增门店个人账户信息 ===");
        
        StorePersonalAccountInfoParam param = new StorePersonalAccountInfoParam();
        param.setStoreAccountInfoId(storeAccountInfoId);
        param.setCertificateNo("360124199001012231");
        param.setCertificateType("ID");
        param.setCardUserName("张三");
        param.setCorporateMobile("***********");
        param.setCardNo("****************123");

        LoginVo loginVo = createMockLoginVo();
        
        try {
            int result = storePersonalAccountInfoService.add(param, loginVo);
            log.info("新增门店个人账户信息结果: {}", result);
            Assert.assertTrue("新增门店个人账户信息失败", result > 0);
        } catch (Exception e) {
            log.error("新增门店个人账户信息异常", e);
            Assert.fail("新增门店个人账户信息异常: " + e.getMessage());
        }
    }

    /**
     * 测试更新门店个人账户信息
     */
    @Test
    @Rollback
    public void testUpdate() {
        log.info("=== 测试更新门店个人账户信息 ===");
        
        // 先新增一条记录
//        StorePersonalAccountInfoParam addParam = new StorePersonalAccountInfoParam();
//        addParam.setStoreAccountInfoId(storeAccountInfoId);
//        addParam.setContractNo("HT2024001");
//        addParam.setLoginNo("STPLN001");
//        addParam.setCertificateNo("****************15");
//        addParam.setCertificateType("身份证");
//        addParam.setCardUserName("张三");
//        addParam.setCorporateMobile("***********");
//        addParam.setCardNo("****************124");
//        addParam.setStatus(1);
//
        LoginVo loginVo = createMockLoginVo();
//
//        try {
//            int addResult = storePersonalAccountInfoService.add(addParam, loginVo);
//            Assert.assertTrue("新增门店个人账户信息失败", addResult > 0);
            
            // 更新记录
            StorePersonalAccountInfoParam updateParam = new StorePersonalAccountInfoParam();
            updateParam.setId((long) 7);
            updateParam.setStoreAccountInfoId(storeAccountInfoId);
            updateParam.setCertificateNo("360124199001012230");
            updateParam.setCertificateType("ID");
            updateParam.setCardUserName("李四");
            updateParam.setCorporateMobile("***********");
            updateParam.setCardNo("****************124");
            int updateResult = storePersonalAccountInfoService.update(updateParam, loginVo);
            log.info("更新门店个人账户信息结果: {}", updateResult);
            Assert.assertTrue("更新门店个人账户信息失败", updateResult > 0);
    }

    @Test
    public void testGetByStoreAccountInfoId() {
        log.info("=== 测试根据ID查询门店个人账户信息 ===");

        try {
            // 根据ID查询
            StorePersonalAccountInfoVo result = storePersonalAccountInfoService.getByStoreAccountInfoId((long) 1);
            log.info("查询门店个人账户信息结果: {}", result);
        } catch (Exception e) {
            log.error("查询门店个人账户信息异常", e);
            Assert.fail("查询门店个人账户信息异常: " + e.getMessage());
        }
    }
    /**
     * 测试根据ID查询门店个人账户信息
     */
    @Test
    @Rollback
    public void testGetById() {
        log.info("=== 测试根据ID查询门店个人账户信息 ===");
        
        // 先新增一条记录
        StorePersonalAccountInfoParam addParam = new StorePersonalAccountInfoParam();
        addParam.setStoreAccountInfoId(storeAccountInfoId);
        addParam.setCertificateNo("****************17");
        addParam.setCertificateType("身份证");
        addParam.setCardUserName("王五");
        addParam.setCorporateMobile("***********");
        addParam.setCardNo("****************125");

        LoginVo loginVo = createMockLoginVo();
        
        try {
            int addResult = storePersonalAccountInfoService.add(addParam, loginVo);
            Assert.assertTrue("新增门店个人账户信息失败", addResult > 0);
            
            // 根据ID查询
            StorePersonalAccountInfoVo result = storePersonalAccountInfoService.getById((long) addResult);
            log.info("查询门店个人账户信息结果: {}", result);
            Assert.assertNotNull("查询门店个人账户信息失败", result);
            Assert.assertEquals("门店账号ID不匹配", storeAccountInfoId, result.getStoreAccountInfoId());
            Assert.assertEquals("合同号不匹配", "HT2024003", result.getContractNo());
            Assert.assertEquals("登录号不匹配", "STPLN003", result.getLoginNo());
            Assert.assertEquals("持卡人姓名不匹配", "王五", result.getCardUserName());
        } catch (Exception e) {
            log.error("查询门店个人账户信息异常", e);
            Assert.fail("查询门店个人账户信息异常: " + e.getMessage());
        }
    }

    /**
     * 测试删除门店个人账户信息
     */
    @Test
    @Rollback
    public void testDeleteById() {
        log.info("=== 测试删除门店个人账户信息 ===");
        
        LoginVo loginVo = createMockLoginVo();
        
        try {
            // 删除记录
            int deleteResult = storePersonalAccountInfoService.deleteById((long) 1, loginVo);
            log.info("删除门店个人账户信息结果: {}", deleteResult);
            Assert.assertTrue("删除门店个人账户信息失败", deleteResult > 0);
            
            // 验证删除后查询不到
            StorePersonalAccountInfoVo result = storePersonalAccountInfoService.getById((long) 1);
            Assert.assertNull("删除后仍能查询到记录", result);
        } catch (Exception e) {
            log.error("删除门店个人账户信息异常", e);
            Assert.fail("删除门店个人账户信息异常: " + e.getMessage());
        }
    }

    /**
     * 测试分页查询门店个人账户信息
     */
    @Test
    @Rollback
    public void testPageList() {
        log.info("=== 测试分页查询门店个人账户信息 ===");
        
        // 先新增几条记录
        LoginVo loginVo = createMockLoginVo();
        
        for (int i = 1; i <= 3; i++) {
            StorePersonalAccountInfoParam addParam = new StorePersonalAccountInfoParam();
            addParam.setStoreAccountInfoId(storeAccountInfoId + i);
            addParam.setCertificateNo("****************" + (18 + i));
            addParam.setCertificateType("身份证");
            addParam.setCardUserName("测试用户" + i);
            addParam.setCorporateMobile("**********" + (7 + i));
            addParam.setCardNo("****************" + (120 + i));

            try {
                int addResult = storePersonalAccountInfoService.add(addParam, loginVo);
                Assert.assertTrue("新增门店个人账户信息失败", addResult > 0);
            } catch (Exception e) {
                log.error("新增门店个人账户信息异常", e);
                Assert.fail("新增门店个人账户信息异常: " + e.getMessage());
            }
        }
        
        // 分页查询
        StorePersonalAccountInfoQueryParam queryParam = new StorePersonalAccountInfoQueryParam();
        queryParam.setPageIndex(1);
        queryParam.setPageSize(10);
        queryParam.setStatus(1);
        
        try {
            PageListVo<StorePersonalAccountInfoVo> result = storePersonalAccountInfoService.pageList(queryParam, loginVo);
            log.info("分页查询门店个人账户信息结果: {}", result);
            Assert.assertNotNull("分页查询门店个人账户信息失败", result);
            Assert.assertTrue("分页查询结果为空", result.getCount() > 0);
            Assert.assertNotNull("分页查询列表为空", result.getList());
        } catch (Exception e) {
            log.error("分页查询门店个人账户信息异常", e);
            Assert.fail("分页查询门店个人账户信息异常: " + e.getMessage());
        }
    }

    /**
     * 创建模拟的登录信息
     */
    private LoginVo createMockLoginVo() {
        LoginVo loginVo = new LoginVo();
        loginVo.setUserId(userId);
        loginVo.setMerchantId(1L);
        loginVo.setParentId(-1L); // 设置为平台管理员
        loginVo.setIsAdmin((byte) 1); // 设置为管理员
        return loginVo;
    }
}
