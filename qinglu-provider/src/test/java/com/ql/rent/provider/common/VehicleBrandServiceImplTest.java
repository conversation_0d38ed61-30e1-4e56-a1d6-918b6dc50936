package com.ql.rent.provider.common;

import com.ql.rent.StartSpringBootApplication;
import com.ql.rent.param.vehicle.VehicleBrandParam;
import com.ql.rent.service.vehicle.IVehicleBrandService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @desc:
 * @author: zhengminjie
 * @time: 2022-09-17 14:11
 * @Version: 1.0
 */
@SpringBootTest(classes = StartSpringBootApplication.class)
@RunWith(SpringRunner.class)
@Slf4j
public class VehicleBrandServiceImplTest {

    @Resource
    private IVehicleBrandService vehicleBrandService;

    @Test
    public void testSaveVehicleBrand() {
        VehicleBrandParam vehicleBrandParam = new VehicleBrandParam();
        vehicleBrandParam.setMerchantId(1L);
        vehicleBrandParam.setStoreId(2L);
        vehicleBrandParam.setBrandName("宝马");
        vehicleBrandParam.setOpUserId(20L);
        vehicleBrandService.saveVehicleBrand(vehicleBrandParam);
    }

}
