package com.ql.rent.provider.store;

import com.alibaba.fastjson.JSON;
import com.ql.rent.entity.store.StoreAtt;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.store.StoreAttTypeEnum;
import com.ql.rent.enums.trade.OrderSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ThirdStoreServiceBase.filterStoreAttByChannel方法的单元测试
 * 使用Mockito进行测试，不依赖外部资源
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
public class ThirdStoreServiceBaseFilterStoreAttTest {

    @InjectMocks
    private ThirdStoreServiceBase thirdStoreServiceBase;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("测试filterStoreAttByChannel方法 - 门店在channelStores中")
    public void testFilterStoreAttByChannel_StoreInChannelStores() {
        // 准备测试数据
        List<Long> channelStores = Arrays.asList(1L, 2L); // 在目标渠道中的门店ID列表
        Long channelId = 2L; // 目标渠道ID
        Long storeId = 1L; // 测试门店ID，在channelStores中

        // 创建测试用的StoreAtt列表
        List<StoreAtt> storeAttList = new ArrayList<>();
        
        // 添加不同渠道和类型的图片，所有图片都属于同一个门店
        storeAttList.add(createStoreAtt(storeId, 2L, StoreAttTypeEnum.QUALIFICATION.getValue(), "/path/to/qualification1.jpg"));
        storeAttList.add(createStoreAtt(storeId, 2L, StoreAttTypeEnum.COUNTER_PHOTO.getValue(), "/path/to/counter1.jpg"));
        storeAttList.add(createStoreAtt(storeId, 1L, StoreAttTypeEnum.QUALIFICATION.getValue(), "/path/to/qualification2.jpg"));
        storeAttList.add(createStoreAtt(storeId, 1L, StoreAttTypeEnum.COUNTER_PHOTO.getValue(), "/path/to/counter2.jpg"));

        // 执行测试方法
        List<StoreAtt> result = thirdStoreServiceBase.filterStoreAttByChannel(channelStores, storeAttList, channelId, storeId);

        // 验证结果
        assertNotNull(result, "结果不应为null");
        
        // 记录测试结果
        log.info("原始门店图片列表: {}", JSON.toJSONString(storeAttList));
        log.info("过滤后的门店图片列表: {}", JSON.toJSONString(result));

        // 验证所有结果图片都属于目标渠道
        for (StoreAtt att : result) {
            assertEquals(channelId, att.getChannelId(), 
                    "当门店在channelStores中时，应只返回目标渠道的图片");
        }
        
        // 验证结果中包含所有目标渠道的图片
        List<StoreAtt> expectedImages = storeAttList.stream()
                .filter(att -> att.getChannelId().equals(channelId))
                .collect(Collectors.toList());
        
        // 验证结果数量
        assertEquals(expectedImages.size(), result.size(), "结果数量应等于目标渠道的图片数量");
        
        // 验证结果中包含所有预期的图片
        for (StoreAtt expected : expectedImages) {
            boolean found = false;
            for (StoreAtt actual : result) {
                if (actual.getStoreId().equals(expected.getStoreId()) &&
                    actual.getChannelId().equals(expected.getChannelId()) &&
                    actual.getFileType().equals(expected.getFileType()) &&
                    actual.getFilePath().equals(expected.getFilePath())) {
                    found = true;
                    break;
                }
            }
            assertTrue(found, "结果应包含所有目标渠道的图片");
        }
    }

    @Test
    @DisplayName("测试filterStoreAttByChannel方法 - 门店不在channelStores中")
    public void testFilterStoreAttByChannel_StoreNotInChannelStores() {
        // 准备测试数据
        List<Long> channelStores = Arrays.asList(1L, 2L); // 在目标渠道中的门店ID列表
        Long channelId = 2L; // 目标渠道ID
        Long storeId = 3L; // 测试门店ID，不在channelStores中

        // 创建测试用的StoreAtt列表
        List<StoreAtt> storeAttList = new ArrayList<>();
        
        // 添加不同渠道和类型的图片，所有图片都属于同一个门店
        storeAttList.add(createStoreAtt(storeId, 2L, StoreAttTypeEnum.QUALIFICATION.getValue(), "/path/to/qualification1.jpg"));
        storeAttList.add(createStoreAtt(storeId, 2L, StoreAttTypeEnum.COUNTER_PHOTO.getValue(), "/path/to/counter1.jpg"));
        storeAttList.add(createStoreAtt(storeId, 1L, StoreAttTypeEnum.QUALIFICATION.getValue(), "/path/to/qualification2.jpg"));
        storeAttList.add(createStoreAtt(storeId, 1L, StoreAttTypeEnum.COUNTER_PHOTO.getValue(), "/path/to/counter2.jpg"));

        // 执行测试方法
        List<StoreAtt> result = thirdStoreServiceBase.filterStoreAttByChannel(channelStores, storeAttList, channelId, storeId);

        // 验证结果
        assertNotNull(result, "结果不应为null");
        
        // 记录测试结果
        log.info("原始门店图片列表: {}", JSON.toJSONString(storeAttList));
        log.info("过滤后的门店图片列表: {}", JSON.toJSONString(result));

        // 验证所有结果图片都属于线下渠道
        Long offlineChannelId = OrderSourceEnum.OFFLINE.getSource().longValue();
        for (StoreAtt att : result) {
            assertEquals(offlineChannelId, att.getChannelId(), 
                    "当门店不在channelStores中时，应只返回线下渠道的图片");
        }
        
        // 验证结果中包含所有线下渠道的图片
        List<StoreAtt> expectedImages = storeAttList.stream()
                .filter(att -> att.getChannelId().equals(offlineChannelId))
                .collect(Collectors.toList());
        
        // 验证结果数量
        assertEquals(expectedImages.size(), result.size(), "结果数量应等于线下渠道的图片数量");
        
        // 验证结果中包含所有预期的图片
        for (StoreAtt expected : expectedImages) {
            boolean found = false;
            for (StoreAtt actual : result) {
                if (actual.getStoreId().equals(expected.getStoreId()) &&
                    actual.getChannelId().equals(expected.getChannelId()) &&
                    actual.getFileType().equals(expected.getFileType()) &&
                    actual.getFilePath().equals(expected.getFilePath())) {
                    found = true;
                    break;
                }
            }
            assertTrue(found, "结果应包含所有线下渠道的图片");
        }
    }

    @Test
    @DisplayName("测试filterStoreAttByChannel方法 - 空列表")
    public void testFilterStoreAttByChannel_EmptyList() {
        // 准备测试数据
        List<Long> channelStores = Arrays.asList(1L, 2L);
        Long channelId = 2L;
        Long storeId = 1L;
        List<StoreAtt> emptyList = new ArrayList<>();

        // 执行测试方法
        List<StoreAtt> result = thirdStoreServiceBase.filterStoreAttByChannel(channelStores, emptyList, channelId, storeId);

        // 验证结果
        assertNotNull(result, "结果不应为null");
        assertEquals(0, result.size(), "空列表应返回空结果");
    }

    @Test
    @DisplayName("测试filterStoreAttByChannel方法 - 多种文件类型")
    public void testFilterStoreAttByChannel_MultipleFileTypes() {
        // 准备测试数据
        List<Long> channelStores = Arrays.asList(1L, 2L);
        Long channelId = 2L;
        Long storeId = 1L; // 在channelStores中

        // 创建测试用的StoreAtt列表，包含所有文件类型
        List<StoreAtt> storeAttList = new ArrayList<>();
        
        // 添加所有类型的图片，包含目标渠道和线下渠道
        for (StoreAttTypeEnum type : StoreAttTypeEnum.values()) {
            storeAttList.add(createStoreAtt(storeId, channelId, type.getValue(), 
                    "/path/to/channel" + channelId + "/" + type.getName() + ".jpg"));
            storeAttList.add(createStoreAtt(storeId, OrderSourceEnum.OFFLINE.getSource().longValue(), type.getValue(), 
                    "/path/to/channel" + OrderSourceEnum.OFFLINE.getSource() + "/" + type.getName() + ".jpg"));
        }

        // 执行测试方法
        List<StoreAtt> result = thirdStoreServiceBase.filterStoreAttByChannel(channelStores, storeAttList, channelId, storeId);

        // 验证结果
        assertNotNull(result, "结果不应为null");
        
        // 记录测试结果
        log.info("原始门店图片列表: {}", JSON.toJSONString(storeAttList));
        log.info("过滤后的门店图片列表: {}", JSON.toJSONString(result));

        // 验证结果中包含所有类型的图片
        for (StoreAttTypeEnum type : StoreAttTypeEnum.values()) {
            boolean hasType = result.stream()
                    .anyMatch(att -> att.getFileType().equals(type.getValue()));
            assertTrue(hasType, "结果应包含" + type.getName() + "类型的图片");
        }
        
        // 验证所有图片都属于目标渠道
        for (StoreAtt att : result) {
            assertEquals(channelId, att.getChannelId(), 
                    "当门店在channelStores中时，应只返回目标渠道的图片");
        }
        
        // 验证结果数量
        int expectedCount = (int) storeAttList.stream()
                .filter(att -> att.getChannelId().equals(channelId))
                .count();
        assertEquals(expectedCount, result.size(), "结果数量应等于目标渠道的图片数量");
    }

    @Test
    @DisplayName("测试filterStoreAttByChannel方法 - 没有匹配的图片")
    public void testFilterStoreAttByChannel_NoMatchingImages() {
        // 准备测试数据
        List<Long> channelStores = Arrays.asList(1L, 2L);
        Long channelId = 3L; // 不存在的渠道ID
        Long storeId = 1L; // 在channelStores中

        // 创建测试用的StoreAtt列表
        List<StoreAtt> storeAttList = new ArrayList<>();
        
        // 添加不同渠道的图片，但不包含channelId=3的图片
        storeAttList.add(createStoreAtt(storeId, 1L, StoreAttTypeEnum.QUALIFICATION.getValue(), "/path/to/qualification1.jpg"));
        storeAttList.add(createStoreAtt(storeId, 2L, StoreAttTypeEnum.COUNTER_PHOTO.getValue(), "/path/to/counter1.jpg"));

        // 执行测试方法
        List<StoreAtt> result = thirdStoreServiceBase.filterStoreAttByChannel(channelStores, storeAttList, channelId, storeId);

        // 验证结果
        assertNotNull(result, "结果不应为null");
        assertEquals(0, result.size(), "没有匹配的图片应返回空结果");
    }

    @Test
    @DisplayName("测试filterStoreAttByChannel方法 - 特殊情况：门店资质图片")
    public void testFilterStoreAttByChannel_QualificationImages() {
        // 准备测试数据
        List<Long> channelStores = Arrays.asList(1L, 2L);
        Long channelId = 2L;
        Long storeId = 1L; // 在channelStores中

        // 创建测试用的StoreAtt列表，只包含门店资质图片
        List<StoreAtt> storeAttList = new ArrayList<>();
        
        // 添加不同渠道的门店资质图片
        storeAttList.add(createStoreAtt(storeId, 1L, StoreAttTypeEnum.QUALIFICATION.getValue(), "/path/to/qualification1.jpg"));
        storeAttList.add(createStoreAtt(storeId, 2L, StoreAttTypeEnum.QUALIFICATION.getValue(), "/path/to/qualification2.jpg"));

        // 执行测试方法
        List<StoreAtt> result = thirdStoreServiceBase.filterStoreAttByChannel(channelStores, storeAttList, channelId, storeId);

        // 验证结果
        assertNotNull(result, "结果不应为null");
        
        // 记录测试结果
        log.info("原始门店图片列表: {}", JSON.toJSONString(storeAttList));
        log.info("过滤后的门店图片列表: {}", JSON.toJSONString(result));

        // 验证所有结果图片都属于目标渠道
        for (StoreAtt att : result) {
            assertEquals(channelId, att.getChannelId(), 
                    "当门店在channelStores中时，应只返回目标渠道的图片");
            assertEquals(StoreAttTypeEnum.QUALIFICATION.getValue(), att.getFileType(),
                    "结果应只包含门店资质图片");
        }
        
        // 验证结果数量
        int expectedCount = (int) storeAttList.stream()
                .filter(att -> att.getChannelId().equals(channelId) && 
                       att.getFileType().equals(StoreAttTypeEnum.QUALIFICATION.getValue()))
                .count();
        assertEquals(expectedCount, result.size(), "结果数量应等于目标渠道的门店资质图片数量");
    }

    /**
     * 创建StoreAtt测试对象
     */
    private StoreAtt createStoreAtt(Long storeId, Long channelId, Byte fileType, String filePath) {
        StoreAtt storeAtt = new StoreAtt();
        storeAtt.setId(System.currentTimeMillis()); // 使用时间戳作为ID
        storeAtt.setStoreId(storeId);
        storeAtt.setChannelId(channelId);
        storeAtt.setFileType(fileType);
        storeAtt.setFilePath(filePath);
        storeAtt.setDeleted(YesOrNoEnum.NO.getValue());
        storeAtt.setCreateTime(System.currentTimeMillis());
        storeAtt.setOpTime(System.currentTimeMillis());
        storeAtt.setOpUserId(1L);
        return storeAtt;
    }
}
