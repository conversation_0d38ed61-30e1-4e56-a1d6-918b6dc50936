package com.ql.rent.provider.vehicle;

import com.ql.rent.dao.vehicle.VehicleBrandMapper;
import com.ql.rent.dao.vehicle.VehicleSeryMapper;
import com.ql.rent.entity.vehicle.VehicleBrand;
import com.ql.rent.entity.vehicle.VehicleSery;
import com.ql.rent.entity.vehicle.VehicleSeryExample;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.param.vehicle.VehicleSeryParam;
import com.ql.rent.param.vehicle.VehicleSeryQueryParam;
import com.ql.rent.service.vehicle.IVehicleModelService;
import com.ql.rent.service.vehicle.IVehicleSubSeryService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.common.VehicleSeryVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class VehicleSeryServiceImplTest {

  @Mock
  private VehicleSeryMapper vehicleSeryMapper;

  @Mock
  private VehicleBrandMapper vehicleBrandMapper;

  @Mock
  private IVehicleSubSeryService vehicleSubSeryService;

  @Mock
  private IVehicleModelService vehicleModelService;

  @InjectMocks
  private VehicleSeryServiceImpl vehicleSeryService;

  private LoginVo loginVo;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    loginVo = new LoginVo();
    loginVo.setUserId(1L);
    loginVo.setMerchantId(1L);
  }

  @Nested
  class SaveVehicleSeryTests {
    @Test
    void shouldReturnFailWhenParamIsNull() {
      Result<Integer> result = vehicleSeryService.saveVehicleSery(null);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenRequiredFieldsAreNull() {
      VehicleSeryParam param = new VehicleSeryParam();

      Result<Integer> result = vehicleSeryService.saveVehicleSery(param);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenSeryNameExists() {
      VehicleSeryParam param = createValidParam();
      when(vehicleSeryMapper.countByExample(any())).thenReturn(1L);

      Result<Integer> result = vehicleSeryService.saveVehicleSery(param);

      assertFalse(result.isSuccess());
      assertEquals("数据重复", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenInsertFails() {
      VehicleSeryParam param = createValidParam();
      when(vehicleSeryMapper.countByExample(any())).thenReturn(0L);
      when(vehicleSeryMapper.insertSelective(any())).thenReturn(0);

      Result<Integer> result = vehicleSeryService.saveVehicleSery(param);

      assertFalse(result.isSuccess());
      assertEquals("保存失败", result.getMessage());
    }

    @Test
    void shouldSaveSuccessfully() {
      VehicleSeryParam param = createValidParam();
      when(vehicleSeryMapper.countByExample(any())).thenReturn(0L);
      when(vehicleSeryMapper.insertSelective(any())).thenReturn(1);

      Result<Integer> result = vehicleSeryService.saveVehicleSery(param);

      assertTrue(result.isSuccess());
      assertEquals(Integer.valueOf(1), result.getModel());

      ArgumentCaptor<VehicleSery> captor = ArgumentCaptor.forClass(VehicleSery.class);
      verify(vehicleSeryMapper).insertSelective(captor.capture());
      VehicleSery saved = captor.getValue();
      assertEquals(param.getSeryName(), saved.getSeryName());
      assertEquals(param.getBrandId(), saved.getBrandId());
      assertEquals(YesOrNoEnum.NO.getValue(), saved.getPreset());
    }
  }

  @Nested
  class DeleteVehicleSeryTests {
    @Test
    void shouldReturnFailWhenIdIsNull() {
      Result<Integer> result = vehicleSeryService.deleteVehicleSery(null, loginVo);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenLoginVoIsNull() {
      Result<Integer> result = vehicleSeryService.deleteVehicleSery(1L, null);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenSeryNotFound() {
      when(vehicleSeryMapper.selectByPrimaryKey(1L)).thenReturn(null);

      Result<Integer> result = vehicleSeryService.deleteVehicleSery(1L, loginVo);

      assertFalse(result.isSuccess());
      assertEquals("车系数据不存在，请刷新页面", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenSeryIsPreset() {
      VehicleSery sery = createVehicleSery(1L, "测试车系", true);
      when(vehicleSeryMapper.selectByPrimaryKey(1L)).thenReturn(sery);

      Result<Integer> result = vehicleSeryService.deleteVehicleSery(1L, loginVo);

      assertFalse(result.isSuccess());
      assertEquals("无法删除预设数据", result.getMessage());
    }

    @Test
    void shouldThrowExceptionWhenDeleteModelFails() {
      VehicleSery sery = createVehicleSery(1L, "测试车系", false);
      when(vehicleSeryMapper.selectByPrimaryKey(1L)).thenReturn(sery);
      when(vehicleModelService.deleteVehicleModel(any(), any(), any(), any(), any()))
          .thenReturn(ResultUtil.failResult("删除失败"));

      assertThrows(BizException.class, () -> vehicleSeryService.deleteVehicleSery(1L, loginVo),
          "删除车型数据失败");
    }

    @Test
    void shouldThrowExceptionWhenDeleteSubSeryFails() {
      VehicleSery sery = createVehicleSery(1L, "测试车系", false);
      when(vehicleSeryMapper.selectByPrimaryKey(1L)).thenReturn(sery);
      when(vehicleModelService.deleteVehicleModel(any(), any(), any(), any(), any()))
          .thenReturn(ResultUtil.successResult(1));
      when(vehicleSubSeryService.deleteBySeryIds(any(), any()))
          .thenReturn(ResultUtil.failResult("删除失败"));

      assertThrows(BizException.class, () -> vehicleSeryService.deleteVehicleSery(1L, loginVo),
          "删除子车系数据失败");
    }

    @Test
    void shouldThrowExceptionWhenUpdateFails() {
      VehicleSery sery = createVehicleSery(1L, "测试车系", false);
      when(vehicleSeryMapper.selectByPrimaryKey(1L)).thenReturn(sery);
      when(vehicleModelService.deleteVehicleModel(any(), any(), any(), any(), any()))
          .thenReturn(ResultUtil.successResult(1));
      when(vehicleSubSeryService.deleteBySeryIds(any(), any()))
          .thenReturn(ResultUtil.successResult(1));
      when(vehicleSeryMapper.updateByExampleSelective(any(), any())).thenReturn(0);

      assertThrows(BizException.class, () -> vehicleSeryService.deleteVehicleSery(1L, loginVo),
          "删除车系数据失败");
    }

    @Test
    void shouldDeleteSuccessfully() {
      VehicleSery sery = createVehicleSery(1L, "测试车系", false);
      when(vehicleSeryMapper.selectByPrimaryKey(1L)).thenReturn(sery);
      when(vehicleModelService.deleteVehicleModel(any(), any(), any(), any(), any()))
          .thenReturn(ResultUtil.successResult(1));
      when(vehicleSubSeryService.deleteBySeryIds(any(), any()))
          .thenReturn(ResultUtil.successResult(1));
      when(vehicleSeryMapper.updateByExampleSelective(any(), any())).thenReturn(1);

      Result<Integer> result = vehicleSeryService.deleteVehicleSery(1L, loginVo);

      assertTrue(result.isSuccess());
      assertEquals(Integer.valueOf(1), result.getModel());
    }
  }

  @Nested
  class ListVehicleSeryPageTests {
    @Test
    void shouldReturnFailWhenParamIsNull() {
      Result<PageListVo<VehicleSeryVO>> result = vehicleSeryService.listVehicleSeryPage(null);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnEmptyPageWhenNoRecordsFound() {
      VehicleSeryQueryParam param = new VehicleSeryQueryParam();
      param.setMerchantId(1L);
      when(vehicleSeryMapper.countByExample(any())).thenReturn(0L);

      Result<PageListVo<VehicleSeryVO>> result = vehicleSeryService.listVehicleSeryPage(param);

      assertTrue(result.isSuccess());
      assertEquals(0L, result.getModel().getCount());
      assertTrue(result.getModel().getList().isEmpty());
    }

    @Test
    void shouldReturnPagedResults() {
      VehicleSeryQueryParam param = new VehicleSeryQueryParam();
      param.setMerchantId(1L);
      VehicleSery sery = createVehicleSery(1L, "测试车系", false);
      when(vehicleSeryMapper.countByExample(any())).thenReturn(1L);
      when(vehicleSeryMapper.selectByExample(any())).thenReturn(Collections.singletonList(sery));

      Result<PageListVo<VehicleSeryVO>> result = vehicleSeryService.listVehicleSeryPage(param);

      assertTrue(result.isSuccess());
      assertEquals(1L, result.getModel().getCount());
      assertEquals(1, result.getModel().getList().size());
      assertEquals("测试车系", result.getModel().getList().get(0).getSeryName());
    }
  }

  @Nested
  class GetVehicleSeryByIdTests {
    @Test
    void shouldReturnFailWhenIdIsNull() {
      Result<VehicleSeryVO> result = vehicleSeryService.getVehicleSeryById(null);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenSeryNotFound() {
      when(vehicleSeryMapper.selectByPrimaryKey(1L)).thenReturn(null);

      Result<VehicleSeryVO> result = vehicleSeryService.getVehicleSeryById(1L);

      assertFalse(result.isSuccess());
      assertEquals("未查询到对应的车系数据", result.getMessage());
    }

    @Test
    void shouldReturnFailWhenBrandNotFound() {
      VehicleSery sery = createVehicleSery(1L, "测试车系", false);
      when(vehicleSeryMapper.selectByPrimaryKey(1L)).thenReturn(sery);
      when(vehicleBrandMapper.selectByPrimaryKey(any())).thenReturn(null);

      Result<VehicleSeryVO> result = vehicleSeryService.getVehicleSeryById(1L);

      assertFalse(result.isSuccess());
      assertEquals("未查询到对应的品牌信息", result.getMessage());
    }

    @Test
    void shouldReturnSeryWithBrandInfo() {
      VehicleSery sery = createVehicleSery(1L, "测试车系", false);
      VehicleBrand brand = createVehicleBrand(1L, "测试品牌");
      when(vehicleSeryMapper.selectByPrimaryKey(1L)).thenReturn(sery);
      when(vehicleBrandMapper.selectByPrimaryKey(any())).thenReturn(brand);

      Result<VehicleSeryVO> result = vehicleSeryService.getVehicleSeryById(1L);

      assertTrue(result.isSuccess());
      assertEquals("测试车系", result.getModel().getSeryName());
      assertEquals("测试品牌", result.getModel().getBrandName());
    }
  }

  @Nested
  class ListVehicleSeryTests {
    @Test
    void shouldReturnFailWhenQueryIsNull() {
      Result<List<VehicleSeryVO>> result = vehicleSeryService.listVehicleSery(null);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnEmptyListWhenNoRecordsFound() {
      VehicleSeryQueryParam param = new VehicleSeryQueryParam();
      when(vehicleSeryMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      Result<List<VehicleSeryVO>> result = vehicleSeryService.listVehicleSery(param);

      assertTrue(result.isSuccess());
      assertTrue(result.getModel().isEmpty());
    }

    @Test
    void shouldReturnSeryListWithBrandInfo() {
      VehicleSeryQueryParam param = new VehicleSeryQueryParam();
      List<VehicleSery> seryList = Arrays.asList(
          createVehicleSery(1L, "车系1", false),
          createVehicleSery(2L, "车系2", false));
      List<VehicleBrand> brandList = Arrays.asList(
          createVehicleBrand(1L, "品牌1"),
          createVehicleBrand(2L, "品牌2"));
      when(vehicleSeryMapper.selectByExample(any())).thenReturn(seryList);
      when(vehicleBrandMapper.selectByExample(any())).thenReturn(brandList);

      Result<List<VehicleSeryVO>> result = vehicleSeryService.listVehicleSery(param);

      assertTrue(result.isSuccess());
      assertEquals(2, result.getModel().size());
      assertEquals("车系1", result.getModel().get(0).getSeryName());
      assertEquals("品牌1", result.getModel().get(0).getBrandName());
      assertEquals("车系2", result.getModel().get(1).getSeryName());
      assertEquals("品牌2", result.getModel().get(1).getBrandName());
    }
  }

  private VehicleSeryParam createValidParam() {
    VehicleSeryParam param = new VehicleSeryParam();
    param.setSeryName("测试车系");
    param.setBrandId(1L);
    param.setMerchantId(1L);
    param.setOpUserId(1L);
    return param;
  }

  private VehicleSery createVehicleSery(Long id, String seryName, boolean preset) {
    VehicleSery sery = new VehicleSery();
    sery.setId(id);
    sery.setSeryName(seryName);
    sery.setBrandId(id);
    sery.setMerchantId(1L);
    sery.setPreset(preset ? YesOrNoEnum.YES.getValue() : YesOrNoEnum.NO.getValue());
    sery.setDeleted(YesOrNoEnum.NO.getValue());
    sery.setCreateTime(System.currentTimeMillis());
    sery.setOpTime(System.currentTimeMillis());
    sery.setOpUserId(1L);
    return sery;
  }

  private VehicleBrand createVehicleBrand(Long id, String brandName) {
    VehicleBrand brand = new VehicleBrand();
    brand.setId(id);
    brand.setBrandName(brandName);
    brand.setMerchantId(1L);
    brand.setDeleted(YesOrNoEnum.NO.getValue());
    return brand;
  }
}