package com.ql.rent.provider.trade;

import com.alibaba.fastjson.JSON;
import com.ql.rent.AbstractTest;
import com.ql.rent.param.trade.VehicleIllegalOrderQueryParam;
import com.ql.rent.service.trade.IVehicleIllegalOrderService;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.trade.VehicleIllegalOrderVO;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class VehicleIllegalOrderServiceImplTest extends AbstractTest {

    @Resource
    private IVehicleIllegalOrderService vehicleIllegalOrderService;

    @Test
    public void testSaveOrUpdate() {
//        VehicleIllegalProofVO proof = new VehicleIllegalProofVO();
//        proof.setProofUrl("illegalUrl");
//
//        VehicleIllegalOrderParam illegalOrder =
//            VehicleIllegalOrderParam.builder().orderId(1L)
//                .illegalTime(new Date())
//                .illegalCityId(121L)
//                .illegalAddr("断桥").fraction(2).penaltyAmount(20000L)
//                .deductionType(PlatformOrUnderlineEnum.UNDERLINE.getType())
//                .illegalAction("违规停车").contractDamageAmount(20000L).build();
//        illegalOrder.setProofList(Collections.singletonList(proof));
//        Result<Integer> result = vehicleIllegalOrderService.saveOrUpdateForOpen(illegalOrder, 20L);
//        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testListIllegalOrder() {
//        VehicleIllegalOrderQueryParam queryParam = new VehicleIllegalOrderQueryParam();
//        queryParam.setOrderId(1L);
//        Result<List<VehicleIllegalOrderVO>> result = vehicleIllegalOrderService.listIllegalOrder(queryParam);
//
//        System.out.println(JSON.toJSONString(result));
    }
}