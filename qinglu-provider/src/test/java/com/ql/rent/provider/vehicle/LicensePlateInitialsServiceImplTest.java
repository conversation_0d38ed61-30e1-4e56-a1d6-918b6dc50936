package com.ql.rent.provider.vehicle;

import com.ql.rent.common.IRedisService;
import com.ql.rent.dao.vehicle.LicensePlateInitialsMapper;
import com.ql.rent.entity.common.LicensePlateInitials;
import com.ql.rent.entity.common.LicensePlateInitialsExample;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.common.LicensePlateInitialsVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.ql.rent.constant.RedisConstant.CommonRedisKey.ALL_LICENSE_PLATE_INITIALS;
import static com.ql.rent.constant.RedisConstant.RedisExpireTime.HOUR_2;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class LicensePlateInitialsServiceImplTest {

  @Mock
  private LicensePlateInitialsMapper licensePlateInitialsMapper;

  @Mock
  private IRedisService redisService;

  @InjectMocks
  private LicensePlateInitialsServiceImpl licensePlateInitialsService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Nested
  class ListAllLicensePlateInitialsTests {
    @Test
    void shouldReturnFromCacheWhenExists() {
      List<LicensePlateInitialsVO> cachedList = Collections.singletonList(createInitialsVO(1L, "A"));
      when(redisService.get(ALL_LICENSE_PLATE_INITIALS)).thenReturn(cachedList);

      Result<List<LicensePlateInitialsVO>> result = licensePlateInitialsService.listAllLicensePlateInitials();

      assertTrue(result.isSuccess());
      assertEquals(cachedList, result.getModel());
      verify(licensePlateInitialsMapper, never()).selectByExample(any());
    }

    @Test
    void shouldQueryFromDbAndCacheWhenNotInCache() {
      when(redisService.get(ALL_LICENSE_PLATE_INITIALS)).thenReturn(null);
      LicensePlateInitials initials = createInitials(1L, "A");
      when(licensePlateInitialsMapper.selectByExample(any())).thenReturn(Collections.singletonList(initials));

      Result<List<LicensePlateInitialsVO>> result = licensePlateInitialsService.listAllLicensePlateInitials();

      assertTrue(result.isSuccess());
      assertEquals(1, result.getModel().size());
      assertEquals("A", result.getModel().get(0).getInitials());
      verify(redisService).set(eq(ALL_LICENSE_PLATE_INITIALS), any(), eq(HOUR_2));
    }

    @Test
    void shouldHandleEmptyDbResult() {
      when(redisService.get(ALL_LICENSE_PLATE_INITIALS)).thenReturn(null);
      when(licensePlateInitialsMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      Result<List<LicensePlateInitialsVO>> result = licensePlateInitialsService.listAllLicensePlateInitials();

      assertTrue(result.isSuccess());
      assertTrue(result.getModel().isEmpty());
    }

    @Test
    void shouldHandleMultipleInitials() {
      when(redisService.get(ALL_LICENSE_PLATE_INITIALS)).thenReturn(null);
      List<LicensePlateInitials> dbList = Arrays.asList(
          createInitials(1L, "A"),
          createInitials(2L, "B"),
          createInitials(3L, "C"));
      when(licensePlateInitialsMapper.selectByExample(any())).thenReturn(dbList);

      Result<List<LicensePlateInitialsVO>> result = licensePlateInitialsService.listAllLicensePlateInitials();

      assertTrue(result.isSuccess());
      assertEquals(3, result.getModel().size());
      assertEquals("A", result.getModel().get(0).getInitials());
      assertEquals("B", result.getModel().get(1).getInitials());
      assertEquals("C", result.getModel().get(2).getInitials());
    }
  }

  @Nested
  class GetByIdTests {
    @Test
    void shouldReturnFailWhenIdIsNull() {
      Result<LicensePlateInitialsVO> result = licensePlateInitialsService.getById(null);

      assertFalse(result.isSuccess());
      assertEquals("参数错误", result.getMessage());
    }

    @Test
    void shouldReturnFromCacheWhenExists() {
      List<LicensePlateInitialsVO> cachedList = Arrays.asList(
          createInitialsVO(1L, "A"),
          createInitialsVO(2L, "B"));
      when(redisService.get(ALL_LICENSE_PLATE_INITIALS)).thenReturn(cachedList);

      Result<LicensePlateInitialsVO> result = licensePlateInitialsService.getById(1L);

      assertTrue(result.isSuccess());
      assertEquals("A", result.getModel().getInitials());
      verify(licensePlateInitialsMapper, never()).selectByPrimaryKey(any());
    }

    @Test
    void shouldClearCacheAndQueryDbWhenNotFoundInCache() {
      List<LicensePlateInitialsVO> cachedList = Collections.singletonList(createInitialsVO(2L, "B"));
      when(redisService.get(ALL_LICENSE_PLATE_INITIALS)).thenReturn(cachedList);

      LicensePlateInitials initials = createInitials(1L, "A");
      when(licensePlateInitialsMapper.selectByPrimaryKey(1L)).thenReturn(initials);

      Result<LicensePlateInitialsVO> result = licensePlateInitialsService.getById(1L);

      assertTrue(result.isSuccess());
      assertEquals("A", result.getModel().getInitials());
      verify(redisService).remove(ALL_LICENSE_PLATE_INITIALS);
    }

    @Test
    void shouldReturnSuccessWithNullWhenNotFoundInDb() {
      when(redisService.get(ALL_LICENSE_PLATE_INITIALS)).thenReturn(null);
      when(licensePlateInitialsMapper.selectByPrimaryKey(1L)).thenReturn(null);

      Result<LicensePlateInitialsVO> result = licensePlateInitialsService.getById(1L);

      assertTrue(result.isSuccess());
      assertNull(result.getModel());
    }

    @Test
    void shouldQueryDbDirectlyWhenCacheIsNull() {
      when(redisService.get(ALL_LICENSE_PLATE_INITIALS)).thenReturn(null);
      LicensePlateInitials initials = createInitials(1L, "A");
      when(licensePlateInitialsMapper.selectByPrimaryKey(1L)).thenReturn(initials);

      Result<LicensePlateInitialsVO> result = licensePlateInitialsService.getById(1L);

      assertTrue(result.isSuccess());
      assertEquals("A", result.getModel().getInitials());
    }
  }

  private LicensePlateInitials createInitials(Long id, String initials) {
    LicensePlateInitials entity = new LicensePlateInitials();
    entity.setId(id);
    entity.setInitials(initials);
    return entity;
  }

  private LicensePlateInitialsVO createInitialsVO(Long id, String initials) {
    LicensePlateInitialsVO vo = new LicensePlateInitialsVO();
    vo.setId(id);
    vo.setInitials(initials);
    return vo;
  }
}