package com.ql.rent.provider.fundauth;


import com.alibaba.fastjson.JSON;
import com.ql.dto.fundauth.FundAuthUnfreezeResult;
import com.ql.rent.AbstractTest;
import com.ql.rent.param.fundauth.FundAuthUnfreezeParam;
import com.ql.rent.param.fundauth.PayNotifyForFreezeParam;
import com.ql.rent.service.fundauth.IThirdFundAuthService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.utils.UuidUtil;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

public class FundAuthServiceTest extends AbstractTest {
    @Resource
    private IThirdFundAuthService thirdFundAuthService;

    @Test
    public void fundAuthUnfreeze() {
        FundAuthUnfreezeParam param = new FundAuthUnfreezeParam();
        param.setThirdFundAuthSource((byte) 1);
        param.setRequestNo(UuidUtil.getUUID());
        param.setAmount(400L);
        param.setRemark("解冻测试");
        param.setBizComplete(true);
        param.setOrderId(470570L);

        Result<FundAuthUnfreezeResult> result = thirdFundAuthService.fundAuthUnfreeze(param);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void payNotifyForFreeze() {
        String context = "{\"payNo\":\"67b86511a44b45d1ae5ac9632eeea243\",\"payOpTime\":1731489994597,\"payStatus\":2,\"payTime\":1731489994597,\"thirdPaySource\":1,\"thirdSouceNo\":\"2024111322001430591401853239\",\"totalAmount\":100}";
        PayNotifyForFreezeParam param = JSON.parseObject(context, PayNotifyForFreezeParam.class);
        Result<Boolean> result = thirdFundAuthService.payNotifyForFreeze(param);
        System.out.println(JSON.toJSONString(result));
    }
}
