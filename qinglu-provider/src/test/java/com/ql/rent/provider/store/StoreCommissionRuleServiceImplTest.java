package com.ql.rent.provider.store;

import com.alibaba.fastjson.JSON;
import com.ql.rent.AbstractTest;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.param.store.StoreCommissionRuleParam;
import com.ql.rent.param.store.StoreCommissionRuleQueryParam;
import com.ql.rent.service.store.IStoreCommissionRuleService;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.store.StoreCommissionRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.test.annotation.Rollback;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 门店佣金规则服务测试类
 * 
 * <AUTHOR>
 */
@Slf4j
public class StoreCommissionRuleServiceImplTest extends AbstractTest {

    @Resource
    private IStoreCommissionRuleService storeCommissionRuleService;

    @Test
    @Rollback
    public void testAdd() {
        log.info("=== 测试新增门店佣金规则 ===");

        StoreCommissionRuleParam info = new StoreCommissionRuleParam();
        info.setRuleName("test");
        info.setMerchantId(109L);
        info.setStoreIds("7435,7437");
        info.setCommissionRate(new BigDecimal("1000"));
        info.setOperatorId(1L);
        info.setCreateTime(System.currentTimeMillis());
        info.setUpdateTime(System.currentTimeMillis());
        
        int result = storeCommissionRuleService.add(info,1L);
        log.info("新增结果: {}", result);
        
        assert result > 0 : "新增失败";
    }

    @Test
    @Rollback
    public void testUpdate() {
        log.info("=== 测试更新门店佣金规则 ===");
//        StoreCommissionRuleQueryParam param = new StoreCommissionRuleQueryParam();
//        PageListVo<StoreCommissionRuleVo> pageListVo = storeCommissionRuleService.pageList(param);

        // 更新数据
        StoreCommissionRuleParam updateInfo = new StoreCommissionRuleParam();
        updateInfo.setId(37L);
        updateInfo.setCommissionRate(new BigDecimal("0.08"));
        updateInfo.setStoreIds("7435,7437");
        updateInfo.setUpdateTime(System.currentTimeMillis());
        
        int updateResult = storeCommissionRuleService.update(updateInfo,1L);
        log.info("更新结果: {}", updateResult);
        
        assert updateResult > 0 : "更新失败";
    }

    @Test
    public void testGetById() {
        log.info("=== 测试根据ID查询门店佣金规则 ===");

        StoreCommissionRuleQueryParam param = new StoreCommissionRuleQueryParam();
        PageListVo<StoreCommissionRuleVo> pageListVo = storeCommissionRuleService.pageList(param);
        Long id = pageListVo.getList().get(0).getId();
        StoreCommissionRuleVo result = storeCommissionRuleService.getById(id);
        
        if (result != null) {
            log.info("查询结果: {}", JSON.toJSONString(result));
            assert result.getId().equals(id) : "ID不匹配";
        } else {
            log.info("未找到ID为{}的记录", id);
        }
    }

    @Test
    public void testDeleteById() {
        Long userId = 1L;
        Long id = 30L;
        int result = storeCommissionRuleService.deleteById(id,userId);
        log.info("=== 测试根据ID删除门店佣金规则 ===");
    }

    @Test
    public void testAllByMerchantIds() {
        Map<Long, BigDecimal> map =  storeCommissionRuleService.getAllByMerchantId(35L, 37L);
        log.info("=== 测试根据商家ID查询门店佣金规则 ===");
    }

    @Test
    public void testPageList() {
        log.info("=== 测试分页查询门店佣金规则 ===");
        
        StoreCommissionRuleQueryParam param = new StoreCommissionRuleQueryParam();
        param.setPageIndex(1);
        param.setPageSize(30);
        param.setMerchantId(109L);
        param.setStoreId(74358L);
        
        PageListVo<StoreCommissionRuleVo> result = storeCommissionRuleService.pageList(param);
        
        log.info("分页查询结果: {}", JSON.toJSONString(result));
        assert result != null : "分页查询失败";
        assert result.getCount() >= 0 : "总数不能为负数";
    }

    @Test
    public void testPageListWithEmptyResult() {
        log.info("=== 测试分页查询（无结果） ===");
        
        StoreCommissionRuleQueryParam param = new StoreCommissionRuleQueryParam();
        param.setPageIndex(1);
        param.setPageSize(10);
        param.setMerchantId(999L); // 不存在的商家ID
        
        PageListVo<StoreCommissionRuleVo> result = storeCommissionRuleService.pageList(param);
        
        log.info("分页查询结果: {}", JSON.toJSONString(result));
        assert result != null : "分页查询失败";
        assert result.getCount() == 0 : "应该没有结果";
        assert result.getList().isEmpty() : "列表应该为空";
    }

    @Test
    @Rollback
    public void testCommissionRateValidation() {
        log.info("=== 测试佣金率验证 ===");
        
        // 测试正常佣金率
        StoreCommissionRuleParam normalInfo = new StoreCommissionRuleParam();
        normalInfo.setMerchantId(1L);
        normalInfo.setStoreIds("15,16");
        normalInfo.setCommissionRate(new BigDecimal("0.05"));
        normalInfo.setOperatorId(1L);
        normalInfo.setCreateTime(System.currentTimeMillis());
        normalInfo.setUpdateTime(System.currentTimeMillis());
        
        int normalResult = storeCommissionRuleService.add(normalInfo,1L);
        log.info("正常佣金率新增结果: {}", normalResult);
        assert normalResult > 0 : "正常佣金率新增失败";
        
        // 测试零佣金率
        StoreCommissionRuleParam zeroInfo = new StoreCommissionRuleParam();
        zeroInfo.setMerchantId(1L);
        zeroInfo.setStoreIds("17,18");
        zeroInfo.setCommissionRate(BigDecimal.ZERO);
        zeroInfo.setOperatorId(1L);
        zeroInfo.setCreateTime(System.currentTimeMillis());
        zeroInfo.setUpdateTime(System.currentTimeMillis());
        
        int zeroResult = storeCommissionRuleService.add(zeroInfo,1L);
        log.info("零佣金率新增结果: {}", zeroResult);
        assert zeroResult > 0 : "零佣金率新增失败";
        
        // 测试高佣金率
        StoreCommissionRuleParam highInfo = new StoreCommissionRuleParam();
        highInfo.setMerchantId(1L);
        highInfo.setStoreIds("19,20");
        highInfo.setCommissionRate(new BigDecimal("0.50"));
        highInfo.setOperatorId(1L);
        highInfo.setCreateTime(System.currentTimeMillis());
        highInfo.setUpdateTime(System.currentTimeMillis());
        
        int highResult = storeCommissionRuleService.add(highInfo,1L);
        log.info("高佣金率新增结果: {}", highResult);
        assert highResult > 0 : "高佣金率新增失败";
    }

    @Test
    @Rollback
    public void testStoreIdsFormat() {
        log.info("=== 测试门店ID格式 ===");
        
        // 测试单个门店ID
        StoreCommissionRuleParam singleInfo = new StoreCommissionRuleParam();
        singleInfo.setMerchantId(1L);
        singleInfo.setStoreIds("21");
        singleInfo.setCommissionRate(new BigDecimal("0.03"));
        singleInfo.setOperatorId(1L);
        singleInfo.setCreateTime(System.currentTimeMillis());
        singleInfo.setUpdateTime(System.currentTimeMillis());
        
        int singleResult = storeCommissionRuleService.add(singleInfo,1L);
        log.info("单个门店ID新增结果: {}", singleResult);
        assert singleResult > 0 : "单个门店ID新增失败";
        
        // 测试多个门店ID
        StoreCommissionRuleParam multipleInfo = new StoreCommissionRuleParam();
        multipleInfo.setMerchantId(1L);
        multipleInfo.setStoreIds("22,23,24,25,26");
        multipleInfo.setCommissionRate(new BigDecimal("0.04"));
        multipleInfo.setOperatorId(1L);
        multipleInfo.setCreateTime(System.currentTimeMillis());
        multipleInfo.setUpdateTime(System.currentTimeMillis());
        
        int multipleResult = storeCommissionRuleService.add(multipleInfo,1L);
        log.info("多个门店ID新增结果: {}", multipleResult);
        assert multipleResult > 0 : "多个门店ID新增失败";
        
        // 测试空门店ID
        StoreCommissionRuleParam emptyInfo = new StoreCommissionRuleParam();
        emptyInfo.setMerchantId(1L);
        emptyInfo.setStoreIds("");
        emptyInfo.setCommissionRate(new BigDecimal("0.02"));
        emptyInfo.setOperatorId(1L);
        emptyInfo.setCreateTime(System.currentTimeMillis());
        emptyInfo.setUpdateTime(System.currentTimeMillis());
        
        int emptyResult = storeCommissionRuleService.add(emptyInfo,1L);
        log.info("空门店ID新增结果: {}", emptyResult);
        assert emptyResult > 0 : "空门店ID新增失败";
    }

    @Test
    public void delete(){
        Long userId = 1L;
        Long id = 35L;
        storeCommissionRuleService.deleteById(id,userId);
    }
} 