package com.ql.rent.provider.trade;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ql.rent.AbstractTest;
import com.ql.rent.param.trade.VehicleIllegalRecordQuery;
import com.ql.rent.param.trade.VehicleIllegalSearchParam;
import com.ql.rent.param.trade.VehicleIllegalSettingParam;
import com.ql.rent.service.trade.IVehicleIllegalContractService;
import com.ql.rent.service.trade.IVehicleIllegalSearchService;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.trade.VehicleIllegalRecordDetailVO;
import com.ql.rent.vo.trade.VehicleIllegalRecordVO;
import com.ql.rent.vo.trade.VehicleIllegalSearchVO;
import com.ql.rent.vo.trade.VehicleIllegalSettingVO;
import org.junit.Test;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class VehicleIllegalSearchServiceImplTest extends AbstractTest {
    @Resource
    private IVehicleIllegalSearchService vehicleIllegalSearchService;
    @Resource
    private IVehicleIllegalContractService vehicleIllegalContractService;

    @Test
    public void testListIllegalSearchAndSave() {
        List<VehicleIllegalSearchParam> vehicleIllegalSearchParamList = new ArrayList<>();
        VehicleIllegalSearchParam vehicleIllegalSearchParam = new VehicleIllegalSearchParam();
        vehicleIllegalSearchParam.setVehicleId(6606L);
        vehicleIllegalSearchParam.setLicenseNo("湘AD10111");
        vehicleIllegalSearchParamList.add(vehicleIllegalSearchParam);
        Result<VehicleIllegalSearchVO> vehicleIllegalSearchVOResult = vehicleIllegalSearchService.listIllegalSearchAndSave(vehicleIllegalSearchParamList, 3L, 1L);
        System.out.println(JSON.toJSONString(vehicleIllegalSearchVOResult));
    }

    @Test
    public void testSaveAutoSearchIllegalSetting() {
        VehicleIllegalSettingParam param = new VehicleIllegalSettingParam();
        param.setSearchInterval(1);
        param.setMerchantId(55L);
        param.setVehicleIds(Lists.newArrayList(5029L, 6605L));
        Result<Boolean> booleanResult = vehicleIllegalSearchService.saveAutoSearchIllegalSetting(param, 1L);
        System.out.println(JSON.toJSONString(booleanResult));
    }

    @Test
    public void testGetVehicleIllegalSetting() {
        Result<VehicleIllegalSettingVO> vehicleIllegalSetting = vehicleIllegalSearchService.getVehicleIllegalSetting(55L);
        System.out.println(JSON.toJSONString(vehicleIllegalSetting));
    }

    @Test
    public void testListBaseIllegalRecord() {
        VehicleIllegalRecordQuery vehicleIllegalRecordQuery = new VehicleIllegalRecordQuery();
        vehicleIllegalRecordQuery.setMerchantId(55L);
        Result<PageListVo<VehicleIllegalRecordVO>> pageListVoResult = vehicleIllegalSearchService.listBaseIllegalRecord(vehicleIllegalRecordQuery);
        System.out.println(JSON.toJSONString(pageListVoResult));
    }

    @Test
    public void testListIllegalRecord() {
        VehicleIllegalRecordQuery vehicleIllegalRecordQuery = new VehicleIllegalRecordQuery();
        vehicleIllegalRecordQuery.setMerchantId(55L);
        Result<PageListVo<VehicleIllegalRecordDetailVO>> pageListVoResult = vehicleIllegalSearchService.listIllegalRecord(vehicleIllegalRecordQuery);
        System.out.println(JSON.toJSONString(pageListVoResult));
    }

    @Test
    public void testListIllegalSearchAndSaveByJob() {
        vehicleIllegalSearchService.listIllegalSearchAndSaveByJob();
    }
    @Test
    public void testGetProgress() {
        vehicleIllegalSearchService.getProgress(45L);
    }
    @Test
    public void testSubmitContract() throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
        Date beginTime = sdf.parse("2024-04-30T10:51:18.077+0800");
        Date  endTime = sdf.parse("2025-04-30T10:51:18.077+0800");
        vehicleIllegalContractService.asyncSubmitContract(466453L, beginTime, endTime);
    }
}
