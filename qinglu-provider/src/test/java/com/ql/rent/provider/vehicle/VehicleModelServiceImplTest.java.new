package com.ql.rent.provider.vehicle;

import com.ql.rent.dao.vehicle.VehicleModelMapper;
import com.ql.rent.entity.vehicle.VehicleModel;
import com.ql.rent.entity.vehicle.VehicleModelExample;
import com.ql.rent.enums.vehicle.VehicleModelStatusEnum;
import com.ql.rent.param.vehicle.VehicleModelInnerQuery;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.vehicle.BaseVehicleModelVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class VehicleModelServiceImplTest {

    @Mock
    private VehicleModelMapper vehicleModelMapper;

    @InjectMocks
    private VehicleModelServiceImpl vehicleModelService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Nested
    class ListBaseVehicleModelTests {

        @Test
        void shouldFailWhenQueryIsNull() {
            Result<List<BaseVehicleModelVO>> result = vehicleModelService.listBaseVehicleModel(null);
            assertFalse(result.isSuccess());
            assertEquals("参数错误", result.getMessage());
        }

        @Test
        void shouldFailWhenAllParamsAreNull() {
            VehicleModelInnerQuery query = new VehicleModelInnerQuery();
            Result<List<BaseVehicleModelVO>> result = vehicleModelService.listBaseVehicleModel(query);
            assertFalse(result.isSuccess());
            assertEquals("参数错误", result.getMessage());
        }

        @Test
        void shouldReturnEmptyListWhenNoModelsFound() {
            VehicleModelInnerQuery query = new VehicleModelInnerQuery();
            query.setMerchantId(1L);
            when(vehicleModelMapper.selectByExample(any(VehicleModelExample.class))).thenReturn(Collections.emptyList());

            Result<List<BaseVehicleModelVO>> result = vehicleModelService.listBaseVehicleModel(query);
            assertTrue(result.isSuccess());
            assertTrue(result.getModel().isEmpty());
        }

        @Test
        void shouldSuccessfullyListBaseVehicleModels() {
            // Prepare test data
            VehicleModelInnerQuery query = new VehicleModelInnerQuery();
            query.setMerchantId(1L);
            query.setStoreId(1L);

            List<VehicleModel> mockModels = new ArrayList<>();
            VehicleModel model = new VehicleModel();
            model.setId(1L);
            model.setVehicleSeryId(1L);
            model.setVehicleSeryName("Test Series");
            model.setStatus(VehicleModelStatusEnum.ON_SELLING.getStatus());
            mockModels.add(model);

            when(vehicleModelMapper.selectByExample(any(VehicleModelExample.class))).thenReturn(mockModels);

            Result<List<BaseVehicleModelVO>> result = vehicleModelService.listBaseVehicleModel(query);
            assertTrue(result.isSuccess());
            assertFalse(result.getModel().isEmpty());
            assertEquals(1, result.getModel().size());
            assertEquals(1L, result.getModel().get(0).getId());
            assertEquals("Test Series", result.getModel().get(0).getVehicleSeryName());
        }

        @Test
        void shouldFilterDeletedModels() {
            VehicleModelInnerQuery query = new VehicleModelInnerQuery();
            query.setMerchantId(1L);
            query.setStoreId(1L);

            List<VehicleModel> mockModels = new ArrayList<>();
            VehicleModel model = new VehicleModel();
            model.setId(1L);
            model.setStatus(VehicleModelStatusEnum.DELETED.getStatus());
            mockModels.add(model);

            when(vehicleModelMapper.selectByExample(any(VehicleModelExample.class))).thenReturn(mockModels);

            Result<List<BaseVehicleModelVO>> result = vehicleModelService.listBaseVehicleModel(query);
            assertTrue(result.isSuccess());
            assertTrue(result.getModel().isEmpty());
        }

        @Test
        void shouldSuccessfullyQueryWithMultipleParams() {
            // Prepare test data
            VehicleModelInnerQuery query = new VehicleModelInnerQuery();
            query.setMerchantId(1L);
            query.setStoreIdList(Arrays.asList(1L, 2L));
            query.setVehicleSeryIdList(Arrays.asList(100L, 200L));
            query.setVehicleModelGroup(1000L);

            List<VehicleModel> mockModels = new ArrayList<>();
            VehicleModel model1 = new VehicleModel();
            model1.setId(1L);
            model1.setVehicleSeryId(100L);
            model1.setVehicleSeryName("Series 1");
            model1.setStatus(VehicleModelStatusEnum.ON_SELLING.getStatus());
            model1.setVehicleModelGroupId(1000L);

            VehicleModel model2 = new VehicleModel();
            model2.setId(2L);
            model2.setVehicleSeryId(200L);
            model2.setVehicleSeryName("Series 2");
            model2.setStatus(VehicleModelStatusEnum.ON_SELLING.getStatus());
            model2.setVehicleModelGroupId(1000L);

            mockModels.add(model1);
            mockModels.add(model2);

            when(vehicleModelMapper.selectByExample(any(VehicleModelExample.class))).thenReturn(mockModels);

            Result<List<BaseVehicleModelVO>> result = vehicleModelService.listBaseVehicleModel(query);
            assertTrue(result.isSuccess());
            assertFalse(result.getModel().isEmpty());
            assertEquals(2, result.getModel().size());
            
            // Verify first model
            BaseVehicleModelVO firstModel = result.getModel().get(0);
            assertEquals(1L, firstModel.getId());
            assertEquals("Series 1", firstModel.getVehicleSeryName());
            assertEquals(100L, firstModel.getVehicleSerId());

            // Verify second model
            BaseVehicleModelVO secondModel = result.getModel().get(1);
            assertEquals(2L, secondModel.getId());
            assertEquals("Series 2", secondModel.getVehicleSeryName());
            assertEquals(200L, secondModel.getVehicleSerId());
        }
    }
} 