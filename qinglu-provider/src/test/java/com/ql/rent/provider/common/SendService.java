package com.ql.rent.provider.common;

import com.alibaba.fastjson.JSON;
import com.ql.enums.SmsTemplateEnum;
import com.ql.rent.AbstractTest;
import com.ql.rent.common.ISendService;
import com.ql.rent.component.sms.SmsFactory;
import com.ql.rent.constant.RedisConstant;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SendService extends AbstractTest {
    @Resource
    private ISendService sendService;
    @Resource
    private SmsFactory smsFactory;

    @Test
    public void sendSmsCode() {
        sendService.sendSmsCode("13627003157", SmsTemplateEnum.USER_LOGIN, RedisConstant.CodeRedisKey.SEND_SMS_LOGIN);
    }

    @Test
    public void batchSendSms() {
        List<String> mobileList = List.of("13537737892", "13627003157");
        List<Map<String, Object>> paramList = new ArrayList<>(mobileList.size());
        for (int index = 0; index < mobileList.size(); index++) {
            Map<String, Object> oneParamMap = new HashMap<>(3);
            oneParamMap.put("pname", "携程");
            oneParamMap.put("cname", "啦啦啦");
            oneParamMap.put("tel", "1234567890");
            paramList.add(oneParamMap);
        }
        smsFactory.getAliSmsService().batchSendSms(mobileList, JSON.toJSONString(paramList),
                SmsTemplateEnum.ORDER_CREATE);
    }
}
