package com.ql.rent.provider.vehicle;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.ql.rent.dao.vehicle.TiexingVehicleMapper;
import com.ql.rent.dao.vehicle.VehicleBrandMapper;
import com.ql.rent.dao.vehicle.VehicleSubSeryMapper;
import com.ql.rent.entity.vehicle.TiexingVehicle;
import com.ql.rent.entity.vehicle.TiexingVehicleExample;
import com.ql.rent.entity.vehicle.VehicleBrand;
import com.ql.rent.entity.vehicle.VehicleSubSery;
import com.ql.rent.vo.vehicle.ThirdVehicleModelSelectVO;
import com.ql.rent.vo.vehicle.ThirdVehicleModelVO;

/**
 * TiexingVehicleServiceImpl单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class TiexingVehicleServiceImplTest {

    @Mock
    private TiexingVehicleMapper tiexingVehicleMapper;
    
    @Mock
    private VehicleSubSeryMapper vehicleSubSeryMapper;
    
    @Mock
    private VehicleBrandMapper vehicleBrandMapper;
    
    @InjectMocks
    private TiexingVehicleServiceImpl tiexingVehicleService;
    
    private VehicleSubSery mockVehicleSubSery;
    private VehicleBrand mockVehicleBrand;
    private TiexingVehicle mockTiexingVehicle;
    
    @BeforeEach
    void setUp() {
        // 模拟子车系数据
        mockVehicleSubSery = new VehicleSubSery();
        mockVehicleSubSery.setId(2687L);
        mockVehicleSubSery.setBrandId(281L);
        mockVehicleSubSery.setName("G3i 520G+");
        mockVehicleSubSery.setDoors("5门");
        mockVehicleSubSery.setPassengers("5座");
        mockVehicleSubSery.setTransmission("自动");
        mockVehicleSubSery.setYears("2022款");
        mockVehicleSubSery.setFuelForm("电动");
        mockVehicleSubSery.setDisplacement("");
        mockVehicleSubSery.setDeleted((byte)0);
        
        // 模拟品牌数据
        mockVehicleBrand = new VehicleBrand();
        mockVehicleBrand.setId(281L);
        mockVehicleBrand.setBrandName("小鹏");
        mockVehicleBrand.setDeleted((byte)0);
        
        // 模拟铁行车型数据
        mockTiexingVehicle = new TiexingVehicle();
        mockTiexingVehicle.setVehicleCode("Hello1598044683329576962");
        mockTiexingVehicle.setVehicleImg("https://img1.bitautoimg.com/autoalbum/files/20221125/576/202211253180936995557600204_6.png");
        mockTiexingVehicle.setVehicleName("G9");
        mockTiexingVehicle.setBrandName("小鹏");
        mockTiexingVehicle.setCategory("SUV");
        mockTiexingVehicle.setConfiguration("通用年款");
        mockTiexingVehicle.setDoorCount("5");
        mockTiexingVehicle.setEmission("");
        mockTiexingVehicle.setFuelType("4");
        mockTiexingVehicle.setPassengerNum("5");
        mockTiexingVehicle.setTransmissionType("自排");
        mockTiexingVehicle.setYear("0");
        mockTiexingVehicle.setStatus((byte)1);
    }
    
    @Test
    void testSearchVehicle_Success() {
        // 设置mock返回值
        when(vehicleSubSeryMapper.selectByPrimaryKey(2687L)).thenReturn(mockVehicleSubSery);
        when(vehicleBrandMapper.selectByPrimaryKey(281L)).thenReturn(mockVehicleBrand);
        when(tiexingVehicleMapper.selectByExample(any(TiexingVehicleExample.class)))
            .thenReturn(Collections.singletonList(mockTiexingVehicle));
            
        // 执行测试
        ThirdVehicleModelSelectVO result = tiexingVehicleService.searchVehicle(2687L);
        
        // 验证结果
        assertNotNull(result);
        List<ThirdVehicleModelVO> modelList = result.getVehicleModelMatchList();
        assertNotNull(modelList);
        assertEquals(1, modelList.size());
        
        ThirdVehicleModelVO model = modelList.get(0);
        assertEquals("Hello1598044683329576962", model.getId());
        assertEquals("小鹏 G9 5座 自动", model.getName());
        
        // 验证精确匹配结果
        List<ThirdVehicleModelVO> matchedList = result.getMatchedList();
        assertNotNull(matchedList);
        assertTrue(matchedList.size() > 0);
        
        // 验证匹配项的属性转换
        ThirdVehicleModelVO matchedModel = matchedList.get(0);
        assertEquals("Hello1598044683329576962", matchedModel.getId());
        assertEquals("小鹏 G9 5座 自动", matchedModel.getName());
    }
    
    @Test
    void testSearchVehicle_NoSubSery() {
        // 模拟子车系不存在
        when(vehicleSubSeryMapper.selectByPrimaryKey(2687L)).thenReturn(null);
        
        // 执行测试
        ThirdVehicleModelSelectVO result = tiexingVehicleService.searchVehicle(2687L);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.getVehicleModelMatchList().isEmpty());
        assertTrue(result.getMatchedList().isEmpty());
    }
    
    @Test
    void testSearchVehicle_NoBrand() {
        // 模拟子车系存在但品牌不存在
        when(vehicleSubSeryMapper.selectByPrimaryKey(2687L)).thenReturn(mockVehicleSubSery);
        when(vehicleBrandMapper.selectByPrimaryKey(281L)).thenReturn(null);
        
        // 执行测试
        ThirdVehicleModelSelectVO result = tiexingVehicleService.searchVehicle(2687L);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.getVehicleModelMatchList().isEmpty());
        assertTrue(result.getMatchedList().isEmpty());
    }
    
    @Test
    void testSearchVehicle_NoVehicles() {
        // 设置mock返回值
        when(vehicleSubSeryMapper.selectByPrimaryKey(2687L)).thenReturn(mockVehicleSubSery);
        when(vehicleBrandMapper.selectByPrimaryKey(281L)).thenReturn(mockVehicleBrand);
        when(tiexingVehicleMapper.selectByExample(any(TiexingVehicleExample.class)))
            .thenReturn(Collections.emptyList());
            
        // 执行测试
        ThirdVehicleModelSelectVO result = tiexingVehicleService.searchVehicle(2687L);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.getVehicleModelMatchList().isEmpty());
        assertTrue(result.getMatchedList().isEmpty());
    }
} 