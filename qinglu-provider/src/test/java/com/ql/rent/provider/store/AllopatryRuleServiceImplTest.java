package com.ql.rent.provider.store;

import com.ql.rent.api.aggregate.model.dto.AllopatryRuleDTO;
import com.ql.rent.api.aggregate.model.dto.StoreCircleIdDTO;
import com.ql.rent.api.aggregate.model.dto.StorePairDTO;
import com.ql.rent.common.IRedisService;
import com.ql.rent.component.PlatformBiz;
import com.ql.rent.converter.AllopatryRuleConverter;
import com.ql.rent.dao.store.AllopatryRuleMapper;
import com.ql.rent.dao.store.StoreDistanceMapper;
import com.ql.rent.entity.store.AllopatryRule;
import com.ql.rent.entity.store.AllopatryRuleExample;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.event.EventPublisher;
import com.ql.rent.param.store.AllopatryRulePageQuery;
import com.ql.rent.service.common.IAreaService;
import com.ql.rent.service.price.IRentMainService;
import com.ql.rent.service.store.IStoreInfoService;
import com.ql.rent.service.vehicle.IVehicleModelService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.store.AllopatryAddVO;
import com.ql.rent.vo.store.AllopatryRuleVO;
import com.ql.rent.vo.store.AllopatryUpdateVO;
import com.ql.rent.param.vehicle.VehicleModelInnerQuery;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.store.StoreSimpleVo;
import com.ql.rent.vo.vehicle.BaseVehicleModelVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Executor;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class AllopatryRuleServiceImplTest {

  @Mock
  private AllopatryRuleMapper allopatryRuleMapper;

  @Mock
  private IVehicleModelService vehicleModelService;

  @Mock
  private IStoreInfoService storeInfoService;

  @Mock
  private IAreaService areaService;

  @Mock
  private IRedisService redisService;

  @Mock
  private IRentMainService rentMainService;

  @Mock
  private EventPublisher eventPublisher;

  @Mock
  private StoreDistanceMapper storeDistanceMapper;

  @Mock
  private Executor asyncPromiseExecutor;

  @Mock
  private PlatformBiz platformBiz;

  @InjectMocks
  private AllopatryRuleServiceImpl allopatryRuleService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Nested
  class PageTests {
    @Test
    void shouldReturnEmptyPageWhenNoRulesFound() {
      // Arrange
      AllopatryRulePageQuery pageQuery = new AllopatryRulePageQuery();
      LoginVo loginVo = new LoginVo();
      loginVo.setMerchantId(1L);

      when(allopatryRuleMapper.countByExample(any())).thenReturn(0L);

      // Act
      PageListVo<AllopatryRuleVO> result = allopatryRuleService.page(pageQuery, loginVo);

      // Assert
      assertNotNull(result);
      assertTrue(result.getList().isEmpty());
      assertEquals(0, result.getCount());

      verify(allopatryRuleMapper).countByExample(any());
      verifyNoMoreInteractions(allopatryRuleMapper);
    }

    @Test
    void shouldReturnPagedRulesSuccessfully() {
      // Arrange
      AllopatryRulePageQuery pageQuery = new AllopatryRulePageQuery();
      pageQuery.setPageIndex(1);
      pageQuery.setPageSize(10);
      pageQuery.setVehicleModelId(1L);
      pageQuery.setChannelId(2L);
      pageQuery.setPickUpId(3L);
      pageQuery.setReturnId(4L);
      pageQuery.setRuleType(1);
      pageQuery.setIsDeleted(YesOrNoEnum.NO.getValue());

      List<AllopatryRule> rules = new ArrayList<>();
      AllopatryRule rule1 = new AllopatryRule();
      rule1.setId(1L);
      rule1.setChannelSetting("[1,2,3]");
      rule1.setModelSetting("[4,5,6]");
      rule1.setPickUpId(3L);
      rule1.setReturnId(4L);
      rule1.setEnabled(YesOrNoEnum.YES.getValue());
      rule1.setDeleted(YesOrNoEnum.NO.getValue());
      rules.add(rule1);

      AllopatryRule rule2 = new AllopatryRule();
      rule2.setId(2L);
      rule2.setChannelSetting("[2,3,4]");
      rule2.setModelSetting("[5,6,7]");
      rule2.setPickUpId(5L);
      rule2.setReturnId(6L);
      rule2.setEnabled(YesOrNoEnum.NO.getValue());
      rule2.setDeleted(YesOrNoEnum.NO.getValue());
      rules.add(rule2);

      when(allopatryRuleMapper.selectByExample(any())).thenReturn(rules);
      when(allopatryRuleMapper.countByExample(any())).thenReturn(2L);

      // Mock vehicle model service response
      BaseVehicleModelVO modelVO = new BaseVehicleModelVO();
      modelVO.setId(1L);
      modelVO.setModelSimpleName("Test Model");

      // Mock store rvice response
      StoreSimpleVo pickUpStore = new StoreSimpleVo();
      pickUpStore.setStoreId(3L);
      pickUpStore.setStoreName("Pick Up Store");

      StoreSimpleVo returnStore = new StoreSimpleVo();
      returnStore.setStoreId(4L);
      returnStore.setStoreName("Return Store");

      when(storeInfoService.storeSampleForUnion(anyList()))
          .thenReturn(ResultUtil.successResult(Arrays.asList(pickUpStore, returnStore)));

      LoginVo loginVo = new LoginVo();
      loginVo.setMerchantId(1L);

      // Act
      PageListVo<AllopatryRuleVO> result = allopatryRuleService.page(pageQuery, loginVo);

      // Assert
      assertNotNull(result);
      assertEquals(2, result.getCount());

      List<AllopatryRuleVO> vos = result.getList();
      assertNotNull(vos);
      assertEquals(2, vos.size());

      // Verify first rule
      AllopatryRuleVO firstRule = vos.get(0);
      assertEquals(1L, firstRule.getId().longValue());
      assertEquals(3L, firstRule.getPickUpId().longValue());
      assertEquals(4L, firstRule.getReturnId().longValue());
      assertEquals("Pick Up Store", firstRule.getPickUpName());
      assertEquals("Return Store", firstRule.getReturnName());
      assertEquals(YesOrNoEnum.YES.getValue(), firstRule.getEnabled());
      assertEquals(YesOrNoEnum.NO.getValue(), firstRule.getDeleted());

      // Verify the service calls
      ArgumentCaptor<AllopatryRuleExample> exampleCaptor = ArgumentCaptor.forClass(AllopatryRuleExample.class);
      verify(allopatryRuleMapper).selectByExample(exampleCaptor.capture());
      verify(allopatryRuleMapper).countByExample(any());
      verify(vehicleModelService).listBaseVehicleModel(any());
      verify(storeInfoService).storeSampleForUnion(anyList());
    }
  }

  @Nested
  class DetailTests {
    @Test
    void shouldReturnNullWhenRuleNotFound() {
      // Arrange
      Long id = 1L;
      LoginVo loginVo = new LoginVo();
      loginVo.setMerchantId(1L);

      when(allopatryRuleMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      // Act
      AllopatryRuleVO result = allopatryRuleService.detail(id, loginVo);

      // Assert
      assertNull(result);
      verify(allopatryRuleMapper).selectByExample(any());
    }

    @Test
    void shouldReturnRuleDetailSuccessfully() {
      // Arrange
      Long id = 1L;
      LoginVo loginVo = new LoginVo();
      loginVo.setMerchantId(1L);

      AllopatryRule rule = createAllopatryRule(id, loginVo.getMerchantId(), (byte) 1);
      rule.setChannelSetting("[1,2]");
      rule.setModelSetting("[3,4]");
      rule.setEnabled((byte) 1);
      rule.setDeleted(YesOrNoEnum.NO.getValue());
      rule.setPickUpId(100L);
      rule.setReturnId(200L);
      rule.setPermanentTag((byte) 1);
      rule.setEffectiveStartTime(System.currentTimeMillis());
      rule.setEffectiveEndTime(System.currentTimeMillis() + 86400000); // 24 hours later
      rule.setDuration(30);
      rule.setFixedPrice(1000);
      rule.setPriceKm(2);

      when(allopatryRuleMapper.selectByExample(any())).thenReturn(Collections.singletonList(rule));

      // Act
      AllopatryRuleVO result = allopatryRuleService.detail(id, loginVo);

      // Assert
      assertNotNull(result);
      assertEquals(id, result.getId());
      assertEquals(rule.getRuleType(), result.getRuleType());
      assertEquals(rule.getPickUpId(), result.getPickUpId());
      assertEquals(rule.getReturnId(), result.getReturnId());
      assertEquals(rule.getPermanentTag(), result.getPermanentTag());
      assertEquals(rule.getEnabled(), result.getEnabled());
      assertEquals(rule.getEffectiveStartTime(), result.getEffectiveStartTime());
      assertEquals(rule.getEffectiveEndTime(), result.getEffectiveEndTime());
      assertEquals(rule.getDuration().intValue(), result.getDuration().intValue());
      assertEquals(rule.getFixedPrice(), result.getFixedPrice());
      assertEquals(rule.getPriceKm(), result.getPriceKm());
      assertEquals(Integer.valueOf(rule.getDeleted()), result.getDeleted());

      // Verify JSON converted fields
      assertNotNull(result.getChannelId());
      assertEquals(2, result.getChannelId().size());
      assertTrue(result.getChannelId().contains(1L));
      assertTrue(result.getChannelId().contains(2L));

      assertNotNull(result.getVehicleModelId());
      assertEquals(2, result.getVehicleModelId().size());
      assertTrue(result.getVehicleModelId().contains(3L));
      assertTrue(result.getVehicleModelId().contains(4L));
    }
  }

  @Nested
  class DeleteTests {
    @Test
    void shouldThrowExceptionWhenRuleNotFound() {
      // Arrange
      Long id = 1L;
      LoginVo loginVo = new LoginVo();
      loginVo.setMerchantId(1L);

      when(allopatryRuleMapper.selectByPrimaryKey(id)).thenReturn(null);

      // Act & Assert
      assertThrows(BizException.class, () -> allopatryRuleService.delete(id, loginVo));
      verify(allopatryRuleMapper).selectByPrimaryKey(id);
    }

    @Test
    void shouldDeleteRuleSuccessfully() {
      // Arrange
      Long id = 1L;
      LoginVo loginVo = new LoginVo();
      loginVo.setMerchantId(1L);

      AllopatryRule rule = createAllopatryRule(id, loginVo.getMerchantId(), (byte) 1);
      when(allopatryRuleMapper.selectByPrimaryKey(id)).thenReturn(rule);

      // Act
      allopatryRuleService.delete(id, loginVo);

      // Assert
      ArgumentCaptor<AllopatryRule> ruleCaptor = ArgumentCaptor.forClass(AllopatryRule.class);
      verify(allopatryRuleMapper).updateByPrimaryKey(ruleCaptor.capture());

      AllopatryRule capturedRule = ruleCaptor.getValue();
      assertEquals(YesOrNoEnum.YES.getValue(), capturedRule.getDeleted());
    }
  }

  @Nested
  class GetAllopatryRuleTests {
    @Test
    void shouldReturnNullWhenNoRulesFound() {
      // Arrange
      Long merchantId = 1L;
      Long channelId = 1L;
      Long pickUpStoreId = 1L;
      Long returnStoreId = 2L;
      Long vehicleModelId = 1L;
      Date returnTime = new Date();

      when(redisService.get(any())).thenReturn(null);
      when(allopatryRuleMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      // Act
      AllopatryRuleDTO result = allopatryRuleService.getAllopatryRule(merchantId, channelId,
          pickUpStoreId, returnStoreId, vehicleModelId, returnTime);

      // Assert
      assertNull(result);
    }

    @Test
    void shouldReturnRuleSuccessfully() {
      // Arrange
      Long merchantId = 1L;
      Long channelId = 1L;
      Long pickUpStoreId = 1L;
      Long returnStoreId = 2L;
      Long vehicleModelId = 1L;
      Date returnTime = new Date();

      AllopatryRule rule = createAllopatryRule(1L, merchantId, (byte) 1);
      rule.setModelSetting("[1]");
      rule.setChannelSetting("[1]");
      rule.setPermanentTag((byte) 1);
      rule.setEnabled((byte) 1);

      when(redisService.get(any())).thenReturn(Collections.singletonList(rule));

      // Act
      AllopatryRuleDTO result = allopatryRuleService.getAllopatryRule(merchantId, channelId,
          pickUpStoreId, returnStoreId, vehicleModelId, returnTime);

      // Assert
      assertNotNull(result);
      assertEquals(rule.getId(), result.getId());
    }
  }

  private AllopatryRule createAllopatryRule(Long id, Long merchantId, Byte ruleType) {
    AllopatryRule rule = new AllopatryRule();
    rule.setId(id);
    rule.setMerchantId(merchantId);
    rule.setRuleType(ruleType);
    rule.setDeleted(YesOrNoEnum.NO.getValue());
    rule.setEnabled((byte) 1);
    rule.setCreateTime(System.currentTimeMillis());
    rule.setOpTime(System.currentTimeMillis());
    return rule;
  }
}