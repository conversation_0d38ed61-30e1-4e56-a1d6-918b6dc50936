package com.ql.rent.provider.vehicle;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.ql.rent.dao.vehicle.DidiVehicleMapper;
import com.ql.rent.dao.vehicle.VehicleBrandMapper;
import com.ql.rent.dao.vehicle.VehicleSubSeryMapper;
import com.ql.rent.entity.vehicle.DidiVehicle;
import com.ql.rent.entity.vehicle.DidiVehicleExample;
import com.ql.rent.entity.vehicle.VehicleBrand;
import com.ql.rent.entity.vehicle.VehicleSubSery;
import com.ql.rent.vo.vehicle.ThirdVehicleModelSelectVO;
import com.ql.rent.vo.vehicle.ThirdVehicleModelVO;

/**
 * DidiVehicleServiceImpl单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class DidiVehicleServiceImplTest {

    @Mock
    private DidiVehicleMapper didiVehicleMapper;
    
    @Mock
    private VehicleSubSeryMapper vehicleSubSeryMapper;
    
    @Mock
    private VehicleBrandMapper vehicleBrandMapper;
    
    @InjectMocks
    private DidiVehicleServiceImpl didiVehicleService;
    
    private VehicleSubSery mockVehicleSubSery;
    private VehicleBrand mockVehicleBrand;
    private DidiVehicle mockDidiVehicle;
    
    @BeforeEach
    void setUp() {
        // 模拟子车系数据
        mockVehicleSubSery = new VehicleSubSery();
        mockVehicleSubSery.setId(2687L);
        mockVehicleSubSery.setBrandId(281L);
        mockVehicleSubSery.setName("Giulia");
        mockVehicleSubSery.setDoors("4门");
        mockVehicleSubSery.setPassengers("5座");
        mockVehicleSubSery.setTransmission("自动");
        mockVehicleSubSery.setYears("2017款");
        mockVehicleSubSery.setFuelForm("汽油");
        mockVehicleSubSery.setDisplacement("2.0T");
        mockVehicleSubSery.setDeleted((byte)0);
        
        // 模拟品牌数据
        mockVehicleBrand = new VehicleBrand();
        mockVehicleBrand.setId(281L);
        mockVehicleBrand.setBrandName("阿尔法·罗密欧");
        mockVehicleBrand.setDeleted((byte)0);
        
        // 模拟滴滴车型数据
        mockDidiVehicle = new DidiVehicle();
        mockDidiVehicle.setModelId("M_F59DB5FD");
        mockDidiVehicle.setModelName("阿尔法罗密欧Giulia-2.0T-A/MT(280HP)豪华版");
        mockDidiVehicle.setNickname("Giulia2017款2.0T阿尔法罗密欧豪华版");
        mockDidiVehicle.setBrandId("B_266A5601");
        mockDidiVehicle.setBrandName("阿尔法·罗密欧");
        mockDidiVehicle.setSeriesId("S_5A44A61C");
        mockDidiVehicle.setSeriesName("阿尔法·罗密欧Giulia朱丽叶");
        mockDidiVehicle.setStyleYear("2017");
        mockDidiVehicle.setGuidePrice(40L);
        mockDidiVehicle.setGroupCode("舒适型");
        mockDidiVehicle.setFuelType(1); // 汽油
        mockDidiVehicle.setTransmissionType(1); // 自动
        mockDidiVehicle.setDisplacement("2.0T");
        mockDidiVehicle.setPassengerNo(5);
        mockDidiVehicle.setLuggageNo(0);
        mockDidiVehicle.setDoorNo(4);
        mockDidiVehicle.setStatus((byte)1);
    }
    
    @Test
    void testSearchVehicle_Success() {
        // 设置mock返回值
        when(vehicleSubSeryMapper.selectByPrimaryKey(2687L)).thenReturn(mockVehicleSubSery);
        when(vehicleBrandMapper.selectByPrimaryKey(281L)).thenReturn(mockVehicleBrand);
        when(didiVehicleMapper.selectByExample(any(DidiVehicleExample.class)))
            .thenReturn(Collections.singletonList(mockDidiVehicle));
            
        // 执行测试
        ThirdVehicleModelSelectVO result = didiVehicleService.searchVehicle(2687L);
        
        // 验证结果
        assertNotNull(result);
        List<ThirdVehicleModelVO> modelList = result.getVehicleModelMatchList();
        assertNotNull(modelList);
        assertEquals(1, modelList.size());
        
        ThirdVehicleModelVO model = modelList.get(0);
        assertEquals("M_F59DB5FD", model.getId());
        assertEquals("阿尔法·罗密欧 阿尔法罗密欧Giulia-2.0T-A/MT(280HP)豪华版 2017款 5座 自动", model.getName());
        
        // 验证精确匹配结果
        List<ThirdVehicleModelVO> matchedList = result.getMatchedList();
        assertNotNull(matchedList);
        assertTrue(matchedList.size() > 0);
        
        // 验证匹配项的属性转换
        ThirdVehicleModelVO matchedModel = matchedList.get(0);
        assertEquals("M_F59DB5FD", matchedModel.getId());
        assertEquals("阿尔法·罗密欧 阿尔法罗密欧Giulia-2.0T-A/MT(280HP)豪华版 2017款 5座 自动", matchedModel.getName());
    }
    
    @Test
    void testSearchVehicle_NoSubSery() {
        // 模拟子车系不存在
        when(vehicleSubSeryMapper.selectByPrimaryKey(2687L)).thenReturn(null);
        
        // 执行测试
        ThirdVehicleModelSelectVO result = didiVehicleService.searchVehicle(2687L);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.getVehicleModelMatchList().isEmpty());
        assertTrue(result.getMatchedList().isEmpty());
    }
    
    @Test
    void testSearchVehicle_NoBrand() {
        // 模拟子车系存在但品牌不存在
        when(vehicleSubSeryMapper.selectByPrimaryKey(2687L)).thenReturn(mockVehicleSubSery);
        when(vehicleBrandMapper.selectByPrimaryKey(281L)).thenReturn(null);
        
        // 执行测试
        ThirdVehicleModelSelectVO result = didiVehicleService.searchVehicle(2687L);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.getVehicleModelMatchList().isEmpty());
        assertTrue(result.getMatchedList().isEmpty());
    }
    
    @Test
    void testSearchVehicle_NoVehicles() {
        // 设置mock返回值
        when(vehicleSubSeryMapper.selectByPrimaryKey(2687L)).thenReturn(mockVehicleSubSery);
        when(vehicleBrandMapper.selectByPrimaryKey(281L)).thenReturn(mockVehicleBrand);
        when(didiVehicleMapper.selectByExample(any(DidiVehicleExample.class)))
            .thenReturn(Collections.emptyList());
            
        // 执行测试
        ThirdVehicleModelSelectVO result = didiVehicleService.searchVehicle(2687L);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.getVehicleModelMatchList().isEmpty());
        assertTrue(result.getMatchedList().isEmpty());
    }
    
    @Test
    void testConvertFuelType() {
        // 通过反射测试私有方法的逻辑，这里我们通过公共方法间接测试
        // 设置不同燃油类型的车型数据
        DidiVehicle electricVehicle = new DidiVehicle();
        electricVehicle.setModelId("ELECTRIC_001");
        electricVehicle.setModelName("电动车型");
        electricVehicle.setBrandName("阿尔法·罗密欧");
        electricVehicle.setStyleYear("2023");
        electricVehicle.setFuelType(4); // 电动
        electricVehicle.setTransmissionType(1); // 自动
        electricVehicle.setPassengerNo(5);
        electricVehicle.setStatus((byte)1);
        
        when(vehicleSubSeryMapper.selectByPrimaryKey(2687L)).thenReturn(mockVehicleSubSery);
        when(vehicleBrandMapper.selectByPrimaryKey(281L)).thenReturn(mockVehicleBrand);
        when(didiVehicleMapper.selectByExample(any(DidiVehicleExample.class)))
            .thenReturn(Collections.singletonList(electricVehicle));
            
        ThirdVehicleModelSelectVO result = didiVehicleService.searchVehicle(2687L);
        
        assertNotNull(result);
        List<ThirdVehicleModelVO> modelList = result.getVehicleModelMatchList();
        assertEquals(1, modelList.size());
        
        ThirdVehicleModelVO model = modelList.get(0);
        assertEquals("ELECTRIC_001", model.getId());
        assertTrue(model.getName().contains("电动车型"));
    }
} 