package com.ql.rent.provider.common;

import com.ql.rent.param.vehicle.VehicleSeryParam;
import com.ql.rent.service.vehicle.IVehicleSeryService;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class VehicleSeryServiceImplTest {

    @Resource
    private IVehicleSeryService vehicleSeryService;

    @Test
    void saveVehicleSery() {
        VehicleSeryParam vehicleSeryParam = new VehicleSeryParam();
        vehicleSeryParam.setSeryName("A6");
        vehicleSeryParam.setBrandId(5L);
        vehicleSeryParam.setMerchantId(10L);
//        vehicleSeryParam.setStoreId(1000L);
        vehicleSeryParam.setOpUserId(1002L);
        vehicleSeryService.saveVehicleSery(vehicleSeryParam);
    }

    @Test
    void deleteVehicleSery() {
    }

    @Test
    void deleteByBrandId() {
    }

    @Test
    void listVehicleSeryPage() {
    }

    @Test
    void listVehicleSery() {
    }

    @Test
    void getVehicleSeryById() {
    }
}