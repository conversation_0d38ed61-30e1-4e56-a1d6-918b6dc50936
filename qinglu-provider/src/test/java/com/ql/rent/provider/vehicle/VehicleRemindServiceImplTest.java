package com.ql.rent.provider.vehicle;

import com.ql.Constant;
import com.ql.enums.VehicleInfoEnums;
import com.ql.rent.constant.QyWechatConstant;
import com.ql.rent.dao.vehicle.VehicleInfoInsuranceMapper;
import com.ql.rent.dao.vehicle.VehicleInfoMapper;
import com.ql.rent.dao.vehicle.ex.VehicleInfoMapperEx;
import com.ql.rent.entity.vehicle.VehicleInfo;
import com.ql.rent.entity.vehicle.VehicleInfoExample;
import com.ql.rent.entity.vehicle.VehicleInfoInsurance;
import com.ql.rent.entity.vehicle.VehicleInfoInsuranceExample;
import com.ql.rent.entity.vehicle.ex.AuditFailedVehicle;
import com.ql.rent.enums.WxMpTemplateEnum;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.merchant.PushTypeEnum;
import com.ql.rent.param.BaseQuery;
import com.ql.rent.provider.merchant.OpenMerchantComponent;
import com.ql.rent.service.common.IApiConnService;
import com.ql.rent.service.common.IPushMsgService;
import com.ql.rent.service.merchant.MerchantInfoService;
import com.ql.rent.service.third.IEnterpriseWechatService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.common.ApiConnVo;
import com.ql.rent.vo.common.PushVO;
import com.ql.rent.vo.login.WxMsgVo;
import com.ql.rent.vo.merchant.MerchantInfoVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.contains;
import static org.mockito.Mockito.*;

class VehicleRemindServiceImplTest {

  @Mock
  private VehicleInfoMapperEx vehicleInfoMapperEx;
  @Mock
  private VehicleInfoMapper vehicleInfoMapper;
  @Mock
  private VehicleInfoInsuranceMapper vehicleInfoInsuranceMapper;
  @Mock
  private IPushMsgService pushMsgService;
  @Mock
  private IEnterpriseWechatService enterpriseWechatService;
  @Mock
  private IApiConnService apiConnService;
  @Mock
  private OpenMerchantComponent openMerchantComponent;
  @Mock
  private MerchantInfoService merchantInfoService;

  @InjectMocks
  private VehicleRemindServiceImpl vehicleRemindService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Nested
  class RemindExpiringMaintenanceTests {
    @Test
    void shouldNotPushWhenNoVehiclesFound() {
      when(vehicleInfoMapperEx.listExpiringMaintenanceVehicle(any())).thenReturn(Collections.emptyList());

      vehicleRemindService.remindExpiringMaintenance();

      verify(pushMsgService, never()).push(any());
    }

    @Test
    void shouldPushNotificationForExpiringMaintenance() {
      VehicleInfo vehicle = new VehicleInfo();
      vehicle.setId(1L);
      vehicle.setMerchantId(1L);
      vehicle.setStoreId(1L);
      vehicle.setLicense("粤B12345");
      vehicle.setNextMaintenanceMileage(10000);

      when(vehicleInfoMapperEx.listExpiringMaintenanceVehicle(any()))
          .thenReturn(Collections.singletonList(vehicle))
          .thenReturn(Collections.emptyList());

      vehicleRemindService.remindExpiringMaintenance();

      ArgumentCaptor<PushVO> pushCaptor = ArgumentCaptor.forClass(PushVO.class);
      verify(pushMsgService).push(pushCaptor.capture());

      PushVO pushVO = pushCaptor.getValue();
      Map<String, WxMsgVo.Template> mpPushObj = pushVO.getMpPushObj();
      assert mpPushObj != null;
      assert mpPushObj.get("first").getValue().contains("粤B12345");
      assert mpPushObj.get("car_number5").getValue().equals("粤B12345");
      assert mpPushObj.get("character_string14").getValue().equals("10000");
    }
  }

  @Nested
  class RemindExpiringInsuranceTests {
    @Test
    void shouldNotPushWhenNoVehiclesFound() {
      when(vehicleInfoMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      vehicleRemindService.remindExpiringInsurance();

      verify(pushMsgService, never()).push(any());
    }

    @Test
    void shouldPushNotificationForExpiringInsurance() {
      VehicleInfo vehicle = new VehicleInfo();
      vehicle.setId(1L);
      vehicle.setMerchantId(1L);
      vehicle.setStoreId(1L);
      vehicle.setLicense("粤B12345");
      vehicle.setStoreName("测试门店");

      VehicleInfoInsurance insurance = new VehicleInfoInsurance();
      insurance.setVehicleId(1L);
      insurance.setInsuranceType((byte) 1);
      insurance.setInsuranceEndDate(LocalDate.now().plusDays(5).toString());

      when(vehicleInfoMapper.selectByExample(any()))
          .thenReturn(Collections.singletonList(vehicle))
          .thenReturn(Collections.emptyList());
      when(vehicleInfoInsuranceMapper.selectByExample(any()))
          .thenReturn(Collections.singletonList(insurance));

      vehicleRemindService.remindExpiringInsurance();

      ArgumentCaptor<PushVO> pushCaptor = ArgumentCaptor.forClass(PushVO.class);
      verify(pushMsgService).push(pushCaptor.capture());

      PushVO pushVO = pushCaptor.getValue();
      Map<String, WxMsgVo.Template> mpPushObj = pushVO.getMpPushObj();
      assert mpPushObj != null;
      assert mpPushObj.get("keyword1").getValue().equals("测试门店");
      assert mpPushObj.get("keyword2").getValue().contains("粤B12345");
      assert mpPushObj.get("keyword3").getValue().equals(insurance.getInsuranceEndDate());
    }
  }

  @Nested
  class RemindVehicleAuditTests {
    @Test
    void shouldNotPushWhenNoMerchantFound() {
      when(apiConnService.getAllMerchantInfoByStatus())
          .thenReturn(ResultUtil.successResult(Collections.emptyList()));

      vehicleRemindService.remindVehicleAudit();

      verify(enterpriseWechatService, never()).sendGroupMsgV2(any(), any(), any());
    }

    @Test
    void shouldPushNotificationForAuditFailedVehicles() {
      ApiConnVo apiConnVo = new ApiConnVo();
      apiConnVo.setMerchantId(1L);

      MerchantInfoVo merchantInfoVo = new MerchantInfoVo();
      merchantInfoVo.setNameShort("测试商家");

      AuditFailedVehicle vehicle = new AuditFailedVehicle();
      vehicle.setLicense("粤B12345");

      when(apiConnService.getAllMerchantInfoByStatus())
          .thenReturn(ResultUtil.successResult(Collections.singletonList(apiConnVo)));
      when(openMerchantComponent.isOpenMerchant(1L)).thenReturn(false);
      when(vehicleInfoMapperEx.listAuditFailedVehicle(eq(1L), eq(Constant.ChannelId.CTRIP),
          eq(VehicleInfoEnums.VehicleChannelAuditStatusEnum.AUDIT_FAILED.getStatus())))
          .thenReturn(Collections.singletonList(vehicle));
      when(merchantInfoService.findById(1L))
          .thenReturn(ResultUtil.successResult(merchantInfoVo));

      vehicleRemindService.remindVehicleAudit();

      verify(enterpriseWechatService).sendGroupMsgV2(
          eq(QyWechatConstant.VEHICLE_AUDIT_ROBOT_KEY),
          contains("粤B12345"),
          eq(Collections.emptyList()));
    }

    @Test
    void shouldSkipOpenMerchant() {
      ApiConnVo apiConnVo = new ApiConnVo();
      apiConnVo.setMerchantId(1L);

      when(apiConnService.getAllMerchantInfoByStatus())
          .thenReturn(ResultUtil.successResult(Collections.singletonList(apiConnVo)));
      when(openMerchantComponent.isOpenMerchant(1L)).thenReturn(true);

      vehicleRemindService.remindVehicleAudit();

      verify(vehicleInfoMapperEx, never()).listAuditFailedVehicle(any(), any(), any());
      verify(enterpriseWechatService, never()).sendGroupMsgV2(any(), any(), any());
    }
  }
}