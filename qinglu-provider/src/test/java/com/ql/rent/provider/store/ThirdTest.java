package com.ql.rent.provider.store;

import com.alibaba.fastjson.JSON;
import com.ql.rent.api.aggregate.model.dto.PointDTO;
import com.ql.rent.api.aggregate.model.request.StoreSearchReq;
import com.ql.rent.api.aggregate.model.request.StoreVehicleModelPriceReq;
import com.ql.rent.api.aggregate.model.request.VehicleModelPriceDetailReq;
import com.ql.rent.api.aggregate.model.response.StoreSearchResp;
import com.ql.rent.api.aggregate.model.response.StoreVehicleModelPriceResp;
import com.ql.rent.api.aggregate.model.response.VehicleModelPriceDetailResp;
import com.ql.rent.api.aggregate.service.VehicleAggregateService;
import com.ql.rent.service.store.IThirdStoreService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
class ThirdTest {

    @Resource
    private VehicleAggregateService vehicleAggregateService;
    @Resource
    private IThirdStoreService thirdStoreService;

    @Test
    public void storeSearch() {
        StoreSearchReq req = new StoreSearchReq();
        String json = "";
        json = "{\"channelId\":4,\"pickUpCityId\":107,\"pickUpDeliveryServiceType\":1,\"pickUpPoint\":{\"latitude\":31.19605,\"longitude\":121.339775},\"pickUpStoreId\":2,\"returnCityId\":107,\"returnDeliveryServiceType\":2,\"returnPoint\":{\"latitude\":31.19605,\"longitude\":121.339775},\"returnStoreId\":2}";
        json = "{\"channelId\":3,\"pickUpPoint\":{\"longitude\":116.488731,\"latitude\":40.002813},\"pickUpCityId\":35,\"returnPoint\":{\"longitude\":116.488731,\"latitude\":40.002813},\"returnCityId\":268}";
        json = "{\"channelId\":4,\"pickUpStoreId\":2,\"pickUpPoint\":{\"longitude\":121.339775,\"latitude\":31.19605},\"pickUpDeliveryServiceType\":2,\"pickUpCityId\":107,\"returnStoreId\":2,\"returnPoint\":{\"longitude\":121.339775,\"latitude\":31.19605},\"returnCityId\":107,\"returnDeliveryServiceType\":2}";
        json = "{\"channelId\":4,\"pickUpDate\":\"2023-03-09 10:00\",\"pickUpDeliveryServiceType\":2,\"returnDate\":\"2023-03-10 10:00\",\"pickUpPoint\":{\"longitude\":121.339775,\"latitude\":31.19605},\"pickUpCityId\":107,\"returnPoint\":{\"longitude\":121.339775,\"latitude\":31.19605},\"returnCityId\":107}";

        json = "{\"channelId\":4,\"pickUpDate\":\"2023-03-09 10:00\",\"returnDate\":\"2023-03-10 10:00\",\"pickUpPoint\":{\"longitude\":121.339775,\"latitude\":31.19605},\"pickUpCityId\":107,\"returnPoint\":{\"longitude\":121.339775,\"latitude\":31.19605},\"returnCityId\":107}";


        json = "{\"channelId\":4,\"pickUpCityId\":107,\"pickUpDate\":\"2023-03-09 09:00\",\"pickUpPoint\":{\"latitude\":31.257733018663195,\"longitude\":121.62858154296875},\"returnCityId\":107,\"returnDate\":\"2023-03-11 09:00\",\"returnPoint\":{\"latitude\":31.257733018663195,\"longitude\":121.62858154296875}}";

        json = "{\"channelId\":4,\"pickUpPoint\":{\"longitude\":121.339775,\"latitude\":31.19605},\"pickUpCityId\":107,\"pickUpDate\":\"2023-04-01 08:00\",\"returnPoint\":{\"longitude\":121.339775,\"latitude\":31.19605},\"returnCityId\":107,\"returnDate\":\"2023-04-15 18:00\"}";

        json = " {\"channelId\":3,\"pickUpPoint\":{\"longitude\":121.328616,\"latitude\":31.194314},\"pickUpCityId\":107,\"pickUpDate\":\"2023-03-24 10:30\",\"returnPoint\":{\"longitude\":121.808682,\"latitude\":31.142267},\"returnCityId\":107,\"returnDate\":\"2023-03-24 15:00\"}";

        req = JSON.parseObject(json, StoreSearchReq.class);
        Long merchantId = 1L;
        Long channelId = 2L;

        StoreSearchResp resp = thirdStoreService.storeSearch(merchantId, req);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void storeVehicleModelPriceSearch() {
        StoreVehicleModelPriceReq req = new StoreVehicleModelPriceReq();
        Long merchantId = 1L;
        String json = "{\"channelId\":4,\"pickUpDate\":\"2023-02-14 19:00\",\"pickUpPoint\":{\"latitude\":31.19605,\"longitude\":121.339775},\"returnDate\":\"2023-02-15 19:00\",\"returnPoint\":{\"latitude\":31.19605,\"longitude\":121.339775},\"storePairList\":[{\"id\":1,\"pickUpStore\":{\"cityCode\":\"null\",\"id\":2,\"phones\":[\"18721017575\"],\"serviceCircleIdList\":[-1,3]},\"returnStore\":{\"cityCode\":\"null\",\"id\":2,\"phones\":[\"18721017575\"],\"serviceCircleIdList\":[-1,3]}}],\"useServiceCircle\":1}\n";
        json = "{\"channelId\":2,\"storePairList\":[{\"id\":1,\"pickUpStore\":{\"id\":4,\"cityCode\":\"107\",\"serviceCircleIdList\":[18]},\"returnStore\":{\"id\":4,\"cityCode\":\"107\",\"serviceCircleIdList\":[18]}}],\"pickUpDate\":\"2023-02-15 18:30\",\"returnDate\":\"2023-02-17 18:30\",\"pickUpPoint\":{\"longitude\":31.20291173276458,\"latitude\":121.26931192522845},\"returnPoint\":{\"longitude\":31.20291173276458,\"latitude\":121.26931192522845},\"useServiceCircle\":1}";
        json = "{\"channelId\":2,\"pickUpDate\":\"2023-02-24 21:00\",\"pickUpPoint\":{\"latitude\":121.347566,\"longitude\":31.194807},\"returnDate\":\"2023-02-25 21:00\",\"returnPoint\":{\"latitude\":121.347566,\"longitude\":31.194807},\"storePairList\":[{\"id\":1,\"pickUpStore\":{\"cityCode\":\"107\",\"id\":2,\"serviceCircleIdList\":[4]},\"returnStore\":{\"cityCode\":\"107\",\"id\":2,\"serviceCircleIdList\":[4]}}],\"useServiceCircle\":1}";
        json = "{\"channelId\":4,\"storePairList\":[{\"id\":1,\"pickUpStore\":{\"id\":3,\"cityCode\":\"null\",\"phones\":[\"18616267759\"],\"serviceCircleIdList\":[-1,6,9,11]},\"returnStore\":{\"id\":3,\"cityCode\":\"null\",\"phones\":[\"18616267759\"],\"serviceCircleIdList\":[-1,6,9,11]}},{\"id\":2,\"pickUpStore\":{\"id\":4,\"cityCode\":\"null\",\"phones\":[\"18616267759\"],\"serviceCircleIdList\":[-1,14,18,20]},\"returnStore\":{\"id\":4,\"cityCode\":\"null\",\"phones\":[\"18616267759\"],\"serviceCircleIdList\":[-1,14,18,20]}}],\"pickUpDate\":\"2023-02-24 10:15\",\"returnDate\":\"2023-02-25 10:15\",\"pickUpPoint\":{\"longitude\":121.320081,\"latitude\":31.193964},\"returnPoint\":{\"longitude\":121.320081,\"latitude\":31.193964},\"useServiceCircle\":1}";

        json = " {\"channelId\":4,\"storePairList\":[{\"id\":1,\"pickUpStore\":{\"id\":3,\"cityCode\":\"null\",\"phones\":[\"18616267759\"],\"serviceCircleIdList\":[-1,12]},\"returnStore\":{\"id\":3,\"cityCode\":\"null\",\"phones\":[\"18616267759\"],\"serviceCircleIdList\":[-1,12]}},{\"id\":2,\"pickUpStore\":{\"id\":3,\"cityCode\":\"null\",\"phones\":[\"18616267759\"],\"serviceCircleIdList\":[-1,12]},\"returnStore\":{\"id\":4,\"cityCode\":\"null\",\"phones\":[\"18616267759\"],\"serviceCircleIdList\":[-1,18]}},{\"id\":3,\"pickUpStore\":{\"id\":4,\"cityCode\":\"null\",\"phones\":[\"18616267759\"],\"serviceCircleIdList\":[-1,18]},\"returnStore\":{\"id\":3,\"cityCode\":\"null\",\"phones\":[\"18616267759\"],\"serviceCircleIdList\":[-1,12]}},{\"id\":4,\"pickUpStore\":{\"id\":4,\"cityCode\":\"null\",\"phones\":[\"18616267759\"],\"serviceCircleIdList\":[-1,18]},\"returnStore\":{\"id\":4,\"cityCode\":\"null\",\"phones\":[\"18616267759\"],\"serviceCircleIdList\":[-1,18]}}],\"pickUpDate\":\"2023-03-14 19:15\",\"returnDate\":\"2023-03-16 19:15\",\"pickUpPoint\":{\"longitude\":121.556332,\"latitude\":31.294821},\"returnPoint\":{\"longitude\":121.555902,\"latitude\":31.295304},\"useServiceCircle\":1}";


        json = "{\"channelId\":2,\"storePairList\":[{\"id\":1,\"pickUpStore\":{\"id\":2,\"cityCode\":\"107\",\"serviceCircleIdList\":[4]},\"returnStore\":{\"id\":2,\"cityCode\":\"107\",\"serviceCircleIdList\":[3]}}],\"pickUpDate\":\"2023-04-01 15:00\",\"returnDate\":\"2023-04-02 15:00\",\"pickUpPoint\":{\"longitude\":31.196295,\"latitude\":121.337425},\"returnPoint\":{\"longitude\":31.196295,\"latitude\":121.337425},\"useServiceCircle\":1}";

        json = "{\"channelId\":2,\"storePairList\":[{\"id\":1,\"pickUpStore\":{\"id\":2,\"cityCode\":\"107\",\"serviceCircleIdList\":[3]},\"returnStore\":{\"id\":2,\"cityCode\":\"107\",\"serviceCircleIdList\":[4]}}],\"pickUpDate\":\"2023-03-27 15:00\",\"returnDate\":\"2023-03-29 14:00\",\"pickUpPoint\":{\"longitude\":121.347566,\"latitude\":31.194807},\"returnPoint\":{\"longitude\":121.347566,\"latitude\":31.194807},\"useServiceCircle\":1}";

        json = "{\"channelId\":2,\"storePairList\":[{\"id\":1,\"pickUpStore\":{\"id\":2,\"cityCode\":\"107\",\"serviceCircleIdList\":[4]},\"returnStore\":{\"id\":2,\"cityCode\":\"107\",\"serviceCircleIdList\":[4]}}],\"pickUpDate\":\"2023-03-30 21:00\",\"returnDate\":\"2023-04-02 21:00\",\"pickUpPoint\":{\"longitude\":121.347566,\"latitude\":31.194807},\"returnPoint\":{\"longitude\":121.347566,\"latitude\":31.194807},\"useServiceCircle\":1}";

        json = "{\"channelId\":3,\"storePairList\":[{\"id\":1,\"pickUpStore\":{\"id\":2,\"cityCode\":\"null\",\"phones\":[\"18156577260\"],\"serviceCircleIdList\":[-1,4]},\"returnStore\":{\"id\":2,\"cityCode\":\"null\",\"phones\":[\"18156577260\"],\"serviceCircleIdList\":[-1,4]}},{\"id\":2,\"pickUpStore\":{\"id\":12,\"cityCode\":\"null\",\"phones\":[\"18156577260\"],\"serviceCircleIdList\":[-1]},\"returnStore\":{\"id\":12,\"cityCode\":\"null\",\"phones\":[\"18156577260\"],\"serviceCircleIdList\":[-1]}}],\"pickUpDate\":\"2023-04-27 15:00:00\",\"returnDate\":\"2023-04-28 18:00:00\",\"pickUpPoint\":{\"longitude\":121.328616,\"latitude\":31.194314},\"returnPoint\":{\"longitude\":121.328616,\"latitude\":31.194314},\"useServiceCircle\":1}";

        json = "{\"channelId\":2,\"storePairList\":[{\"id\":1,\"pickUpStore\":{\"id\":2,\"cityCode\":\"107\",\"serviceCircleIdList\":[4]},\"returnStore\":{\"id\":2,\"cityCode\":\"107\",\"serviceCircleIdList\":[3]}}],\"pickUpDate\":\"2023-06-11 15:00\",\"returnDate\":\"2023-06-13 15:00\",\"pickUpPoint\":{\"longitude\":31.196295,\"latitude\":121.337425},\"returnPoint\":{\"longitude\":31.196295,\"latitude\":121.337425},\"useServiceCircle\":1}";

        json = "{\"channelId\":10,\"storePairList\":[{\"id\":1,\"pickUpStore\":{\"id\":2,\"cityCode\":\"null\",\"phones\":[\"18156577260\"],\"serviceCircleIdList\":[-1,4]},\"returnStore\":{\"id\":2,\"cityCode\":\"null\",\"phones\":[\"18156577260\"],\"serviceCircleIdList\":[-1,4]}}],\"pickUpDate\":\"2023-06-22 17:30:00\",\"returnDate\":\"2023-06-24 17:30:00\",\"pickUpPoint\":{\"longitude\":121.402795,\"latitude\":31.249696},\"returnPoint\":{\"longitude\":121.402795,\"latitude\":31.249696},\"useServiceCircle\":1, \"debugInfo\":\"test\"}";

        json = "{\"channelId\":2,\"originReq\":{\"requestId\":\"89a5d7b1-1fe3-4dbb-8e32-0158b2459821\",\"pickupDate\":\"2025-04-02 11:30\",\"returnDate\":\"2025-04-03 15:30\",\"storeList\":[{\"pickupCityCode\":\"264\",\"returnCityCode\":\"264\",\"pickupStoreCode\":\"74399\",\"returnStoreCode\":\"74399\",\"pickUpStoreServiceCircleIdList\":[23665],\"returnStoreServiceCircleIdList\":[23665]}],\"pickupOndoorAddr\":{\"address\":\"凤凰国际机场T1航站楼-三亚凤凰机场店\",\"longitude\":109.413683,\"latitude\":18.305902},\"pickoffOndoorAddr\":{\"address\":\"凤凰国际机场T1航站楼-三亚凤凰机场店\",\"longitude\":109.413683,\"latitude\":18.305902},\"pickUpLatitude\":18.305902,\"pickUpLongitude\":109.413683,\"pickOffLatitude\":18.305902,\"pickOffLongitude\":109.413683,\"priceChannel\":10},\"pickUpDate\":\"2025-04-02 11:30\",\"pickUpPoint\":{\"latitude\":18.305902,\"longitude\":109.413683},\"returnDate\":\"2025-04-03 15:30\",\"returnPoint\":{\"latitude\":18.305902,\"longitude\":109.413683},\"storePairList\":[{\"id\":1,\"pickUpStore\":{\"allDay\":0,\"cityCode\":\"264\",\"id\":355,\"serviceCircleIdList\":[890]},\"returnStore\":{\"allDay\":0,\"cityCode\":\"264\",\"id\":355,\"serviceCircleIdList\":[890]}}],\"useServiceCircle\":1}";
        req = JSON.parseObject(json, StoreVehicleModelPriceReq.class);
        StoreVehicleModelPriceResp
            resp = vehicleAggregateService.storeVehicleModelPriceSearch(45L, req);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void vehicleModelPriceDetail() {
        VehicleModelPriceDetailReq req = new VehicleModelPriceDetailReq();
        Long merchantId = 1L;
        Long channelId = 2L;
        Long vehicleModelId = 10L;
        String json = "{\"channelId\":1,\"storePair\":{\"id\":1,\"pickUpStore\":{\"id\":2,\"cityCode\":\"null\",\"phones\":[\"18721017575\"],\"serviceCircleIdList\":[-1,4]},\"returnStore\":{\"id\":2,\"cityCode\":\"null\",\"phones\":[\"18721017575\"],\"serviceCircleIdList\":[-1,4]}},\"pickUpDate\":\"2023-06-22 17:00\",\"returnDate\":\"2023-06-24 17:00\",\"useServiceCircle\":1}";
        req = JSON.parseObject(json, VehicleModelPriceDetailReq.class);
        VehicleModelPriceDetailResp
            resp = vehicleAggregateService.vehicleModelPriceDetail(merchantId, vehicleModelId, req);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void caseDistance() {
        //"pickUpPoint":{"longitude":31.194807,"latitude":121.347566},"returnPoint":{"longitude":31.151152,"latitude":121.808685}
        PointDTO pickUpGis = new PointDTO();
        pickUpGis.setLongitude(121.20871256510416);
        pickUpGis.setLatitude(31.23108425564236);
        PointDTO returnGis = new PointDTO();
        returnGis.setLongitude(121.323141);
        returnGis.setLatitude(31.296952);

        pickUpGis.setLongitude(126.49398);
        pickUpGis.setLatitude(43.818415);
        returnGis.setLongitude(120.153576);
        returnGis.setLatitude(30.287459);

        thirdStoreService.caseDistance(pickUpGis, returnGis);
    }

    @Test
    public void vehicleModelDailyPriceV2() throws Exception{

        String json = "{\"channelId\":2,\"storePair\":{\"id\":1,\"pickUpStore\":{\"id\":3,\"cityCode\":\"null\",\"phones\":[\"18721017575\"],\"serviceCircleIdList\":[-1,4]},\"returnStore\":{\"id\":2,\"cityCode\":\"null\",\"phones\":[\"18721017575\"],\"serviceCircleIdList\":[-1,4]}},\"pickUpDate\":\"2023-06-22 17:00\",\"returnDate\":\"2023-06-24 18:00\",\"useServiceCircle\":1}";
        VehicleModelPriceDetailReq req = JSON.parseObject(json, VehicleModelPriceDetailReq.class);

        Long merchantId = 2L;
        Long vehicleModelId = 14L;
        vehicleAggregateService.vehicleModelHourlyPrice(merchantId, vehicleModelId, req, null);
    }
}