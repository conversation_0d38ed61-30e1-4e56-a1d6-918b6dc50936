package com.ql.rent.provider.trade;

import com.ql.rent.AbstractTest;
import com.ql.rent.dto.trade.TransferReportDTO;
import com.ql.rent.dto.trade.TransferReportSummaryDTO;
import com.ql.rent.service.trade.ITransferContractStatusService;
import com.ql.rent.share.utils.JsonUtil;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;

/**
 * 转移上报统计服务测试
 */
public class TransferReportServiceImplTest extends AbstractTest {

    @Resource
    private ITransferContractStatusService transferContractStatusService;

    @Test
    public void testGetTransferReportSummary() {
        // 测试统计昨天的数据
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 6);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date startTime = calendar.getTime();
        
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        Date endTime = calendar.getTime();
        
        TransferReportSummaryDTO result = transferContractStatusService.getTransferReportSummary(startTime, endTime);
        System.out.println("统计结果：" + JsonUtil.beanToJson(result));
    }

    @Test
    public void testExecuteTransferReportTask() {
        transferContractStatusService.executeTransferReportTask();
    }

    @Test
    public void executeViolationMonthlyBillTask() {
        transferContractStatusService.executeViolationMonthlyBillTask();
    }
    
    @Test
    public void testGetTransferReportSummaryWithPackageName() {
        // 测试套餐名称获取功能
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -7); // 测试最近7天的数据
        calendar.set(Calendar.HOUR_OF_DAY, 6);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date startTime = calendar.getTime();
        
        calendar.add(Calendar.DAY_OF_MONTH, 7);
        Date endTime = calendar.getTime();
        
        TransferReportSummaryDTO result = transferContractStatusService.getTransferReportSummary(startTime, endTime);
        System.out.println("统计结果：" + result);
        
        if (result != null && result.getReportList() != null) {
            for (TransferReportDTO report : result.getReportList()) {
                System.out.println("订单ID: " + report.getOrderId() + 
//                                 ", 套餐名称: " + report.getPackageName() +
                                 ", 商家名称: " + report.getMerchantName() + 
                                 ", postMsg: " + report.getPostMsg());
            }
        }
    }
}