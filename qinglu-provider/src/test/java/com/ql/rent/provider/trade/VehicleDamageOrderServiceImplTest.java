package com.ql.rent.provider.trade;

import com.alibaba.fastjson.JSON;
import com.ql.rent.AbstractTest;
import com.ql.rent.dao.trade.OrderInfoMapper;
import com.ql.rent.entity.trade.OrderInfo;
import com.ql.rent.enums.trade.OrderDeductionTypeEnum;
import com.ql.rent.enums.trade.OrderSourceEnum;
import com.ql.rent.param.trade.VehicleDamageOrderQueryParam;
import com.ql.rent.service.trade.IVehicleDamageOrderService;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.trade.VehicleDamageOrderVO;
import com.ql.rent.vo.trade.VehicleDamageProofVO;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.Date;

import static org.mockito.ArgumentMatchers.anyLong;

public class VehicleDamageOrderServiceImplTest extends AbstractTest {

    @Autowired
    private VehicleDamageOrderServiceImpl vehicleDamageOrderServiceImpl;
    @Autowired
    private IVehicleDamageOrderService vehicleDamageOrderService;

    @Test
    public void testSaveOrUpdate() {
        OrderInfoMapper orderInfoMapper = Mockito.mock(OrderInfoMapper.class);
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setVehicleId(30L);
        orderInfo.setOrderSource(OrderSourceEnum.OFFLINE.getSource());
        Mockito.when(orderInfoMapper.selectByPrimaryKey(anyLong())).thenReturn(orderInfo);
        //vehicleDamageOrderServiceImpl.setOrderInfoMapper(orderInfoMapper);

        Long opUserId = 100L;
        Long orderId = 100L;
        VehicleDamageOrderVO param = new VehicleDamageOrderVO();
        param.setOrderId(orderId);
        param.setDamageTime(new Date());
        param.setRepairFee(200L);
        param.setDepreciationFee(300L);
        param.setOutageFee(20000L);
        param.setOtherFee(2000000L);
        param.setDeductionType(OrderDeductionTypeEnum.UNDERLINE.getType());

        VehicleDamageProofVO proof = new VehicleDamageProofVO();
        proof.setProofUrl("10000L");
        param.setDamageProofList(Collections.singletonList(proof));
        vehicleDamageOrderServiceImpl.saveOrUpdate(param, opUserId);
    }

    @Test
    public void testSaveOrUpdate2() {
        OrderInfoMapper orderInfoMapper = Mockito.mock(OrderInfoMapper.class);
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setVehicleId(30L);
        orderInfo.setOrderSource(OrderSourceEnum.OFFLINE.getSource());
        Mockito.when(orderInfoMapper.selectByPrimaryKey(anyLong())).thenReturn(orderInfo);
        //vehicleDamageOrderServiceImpl.setOrderInfoMapper(orderInfoMapper);

        Long opUserId = 100L;
        Long orderId = 100L;
        VehicleDamageOrderVO param = new VehicleDamageOrderVO();
        param.setId(6L);
        param.setOrderId(orderId);
        param.setDamageTime(new Date());
        param.setRepairFee(200L);
        param.setDepreciationFee(300L);
        param.setOutageFee(20000L);
        param.setOtherFee(2000000L);
        param.setDeductionType(OrderDeductionTypeEnum.UNDERLINE.getType());

        VehicleDamageProofVO proof = new VehicleDamageProofVO();
        proof.setProofUrl("10000L");
        param.setDamageProofList(Collections.singletonList(proof));
        vehicleDamageOrderServiceImpl.saveOrUpdate(param, opUserId);
    }

    @Test
    public void testListVehicleDamageOrder() {
        VehicleDamageOrderQueryParam queryParam = new VehicleDamageOrderQueryParam();
        queryParam.setOrderId(100L);
        System.out.println(JSON.toJSONString(vehicleDamageOrderServiceImpl.listVehicleDamageOrderPage((queryParam))));
    }

    @Test
    public void addThird() {
        VehicleDamageOrderVO vehicleDamageOrderVO = new VehicleDamageOrderVO();
        vehicleDamageOrderVO.setOrderId(85L);
        vehicleDamageOrderVO.setDamageTime(new Date());
//        vehicleDamageOrderVO.setRepairFee(20L);
        vehicleDamageOrderVO.setOutageFee(1L);
//        vehicleDamageOrderVO.setOtherFee(100L);
        vehicleDamageOrderVO.setDeductionType((byte)1);
        VehicleDamageProofVO vehicleDamageProofVO = new VehicleDamageProofVO();
        vehicleDamageProofVO.setProofUrl("aaaaa.jpg");
        vehicleDamageOrderVO.setDamageProofList(Collections.singletonList(vehicleDamageProofVO));

        vehicleDamageOrderServiceImpl.saveOrUpdate(vehicleDamageOrderVO, 0L);
    }

    @Test
    public void doTestDelete() {
        LoginVo loginVo = new LoginVo();
        loginVo.setUserId(1L);
        vehicleDamageOrderServiceImpl.cancel(40L, loginVo);
    }
}