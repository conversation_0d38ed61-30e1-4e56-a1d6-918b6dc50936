package com.ql.rent.provider.trade;

import com.alibaba.fastjson.JSON;
import com.ql.rent.AbstractTest;
import com.ql.rent.service.common.IOcrService;
import com.ql.rent.service.trade.IOrderMemberService;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.trade.CertificatesVo;
import com.ql.rent.vo.trade.UserCertificateVo;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @data 2022/10/30 23:17
 * @description
 */
public class OrderMemberServiceImplTest extends AbstractTest {

    @Resource
    private IOcrService ocrService;
    @Resource
    private IOrderMemberService orderMemberService;

    @Test
    public  void testOcrCertificate() {
        //身份证正页
//        Result<CertificatesVo> result1 = orderMemberService.ocrCertificate("https://tse3-mm.cn.bing.net/th/id/OIP-C.hgXZvPHWHvrqejsMKD0oHQHaEK?w=302&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", (byte) 1);
//        System.out.println(JSON.toJSONString(result1));
        //身份证反页
//        Result<CertificatesVo> result2 = orderMemberService.ocrCertificate("https://tse3-mm.cn.bing.net/th/id/OIP-C.YUX6ukRya0DkmpSlc1w9GwHaEK?w=319&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", (byte) 2);
//        System.out.println(JSON.toJSONString(result2));
        //驾驶证正页
        Result<? extends CertificatesVo> result3 = ocrService.ocrCertificate("https://file.ls.jianxiao.ltd/qinglu/853892a7-b985-4b84-8ddd-cf2ccd56cb4e.jpeg", (byte) 3);
        System.out.println(JSON.toJSONString(result3));
        //行驶证正页
//        Result<CertificatesVo> result4 = orderMemberService.ocrCertificate("https://gss0.baidu.com/9vo3dSag_xI4khGko9WTAnF6hhy/zhidao/wh%3D600%2C800/sign=bc2553592da446237e9fad64a8125e36/f636afc379310a55621ef7d7ba4543a9822610de.jpg", (byte) 4);
//        System.out.println(JSON.toJSONString(result4));
        //行驶证副页
//        Result<CertificatesVo> result5 = orderMemberService.ocrCertificate("https://pic2.58cdn.com.cn/paipic/n_v2f5c94b94e33042729a23a4755c92e3f2.jpg", (byte) 5);
//        System.out.println(JSON.toJSONString(result5));
    }

    @Test
    public  void testSaveUserCertificate() {
        UserCertificateVo vo = new UserCertificateVo();
        Result<Boolean> result = orderMemberService.saveUserCertificate(vo, 8l);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public  void testUpdateUserCertificate() {
        UserCertificateVo vo = new UserCertificateVo();
        Result<Boolean> result = orderMemberService.updateUserCertificate(vo, 118972l);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public  void testGetUserCertificate() {
        Result<UserCertificateVo> result = orderMemberService.getUserCertificate(118972l);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public  void testGetUserInfoBySubSourceInnerOrderIds() {
        Result result = orderMemberService.getUserInfoBySubSourceInnerOrderIds(Arrays.asList("8"), 2L);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public  void testGetUserInfoBySourceInnerOrderIds() {
        Result result = orderMemberService.getUserInfoBySourceInnerOrderIds(Arrays.asList("8"), 2L);
        System.out.println(JSON.toJSONString(result));
    }

}
