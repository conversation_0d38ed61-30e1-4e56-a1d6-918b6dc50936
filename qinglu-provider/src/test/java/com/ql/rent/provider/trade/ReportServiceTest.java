package com.ql.rent.provider.trade;

import com.alibaba.fastjson.JSON;
import com.ql.rent.AbstractTest;
import com.ql.rent.param.trade.DailyOverviewQuery;
import com.ql.rent.param.trade.ReportDataQuery;
import com.ql.rent.param.trade.SaveReportParam;
import com.ql.rent.param.trade.excel.ReportExcelParam;
import com.ql.rent.service.trade.IReportNewService;
import com.ql.rent.service.trade.IReportService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.vo.trade.*;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Test;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-01-16  15:00
 * Description
 */
public class ReportServiceTest extends AbstractTest {

    @Resource
    private IReportService reportService;

    @Resource
    private IReportNewService reportNewService;

    @Test
    public void reportQuota() {
        reportService.reportQuota(null);
//        reportService.reportQuota("20230225");
//        reportService.reportQuota("20230226");
//        reportService.reportQuota("20230227");
//        reportService.reportQuota("20230228");
//        reportService.reportQuota("20230301");
//        reportService.reportQuota("20230302");
//        reportService.reportQuota("20230303");
//        reportService.reportQuota("20230304");
//        reportService.reportQuota("20230305");
//        reportService.reportQuota("20230306");
//        reportService.reportQuota("20230307");
//        reportService.reportQuota("20230308");
//        reportService.reportQuota("20230309");
    }

    @Test
    public void getReportExcel() {
        ReportExcelParam param = new ReportExcelParam();
        param.setMerchantId(1L);
        param.setStartYmd(20230326);
        param.setEndYmd(20230401);
        Result<List<OrderReportExcelVO>> result = reportService.getReportExcel(param);
        System.out.println(JSON.toJSONString(result));

    }

    @Test
    public void getDailyReportExcel() {
        DailyOverviewQuery param = new DailyOverviewQuery();
        param.setMerchantId(1L);
        List<Long> storeIdList = new ArrayList<>();
        storeIdList.add(2L);
        param.setStoreIdList(storeIdList);
        List<Long> vehicleModelIds = new ArrayList<>();
        vehicleModelIds.add(3738L);
        vehicleModelIds.add(1643L);
        param.setVehicleModelIds(vehicleModelIds);
        param.setYmd(DateUtils.addDays(new Date(),-3));
        param.setPageIndex(1);
        param.setPageSize(9999);
        System.out.println(JSON.toJSONString(param));
        Result<List<DailyOverviewExcelVO>> result = reportService.getDailyOverviewExcel(param);
        System.out.println(JSON.toJSONString(result));

    }

    @Test
    public void getDailyOverview(){
        try {
            DailyOverviewQuery param = new DailyOverviewQuery();
            param.setMerchantId(1L);
            List<Long> storeIdList = new ArrayList<>();
            param.setStoreIdList(storeIdList);
            List<Long> vehicleModelIds = new ArrayList<>();
            vehicleModelIds.add(3738L);
            param.setVehicleModelIds(vehicleModelIds);
            param.setYmd(new Date());
            param.setPageIndex(1);
            param.setPageSize(10);
            System.out.println(JSON.toJSONString(param));
            Result<DailyOverviewVO> result = reportService.getDailyOverview(param);
            System.out.println(JSON.toJSONString(result));
        }catch (Exception e){
            System.out.println(e);
        }

    }


    @Test
    public void orderChangeRecount() {
        reportService.orderChangeRecount(1L, 2L, 1L, new Date(1689697165000L));

    }

    @Test
    public void saveReportOrderTest() {
        try {
//            Date date = DateUtils.addDays(new Date(), -1);
            Date date = new Date();
            SaveReportParam saveReportParam = new SaveReportParam();
            saveReportParam.setMerchantId(43L);
            saveReportParam.setYmd(DateUtil.getFormatDateStr(date, DateUtil.yyyyMMdd1));
            reportNewService.saveReportOrder(saveReportParam);
            System.out.println("saveReportOrder success");
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    @Test
    public void saveReportRentRateTest() {
        try {
            Date date = DateUtils.addDays(new Date(), 1);
//            Date date = new Date();
            SaveReportParam saveReportParam = new SaveReportParam();
            saveReportParam.setMerchantId(1L);
            saveReportParam.setYmd(DateUtil.getFormatDateStr(date, DateUtil.yyyyMMdd1));
            reportNewService.saveReportRentRate(saveReportParam);
            System.out.println("saveReportRentRate success");
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    @Test
    public void saveReportRentStockTimeTest() {
        try {
            Date date = DateUtils.addDays(new Date(), -1);
//            Date date = new Date();
            SaveReportParam saveReportParam = new SaveReportParam();
            saveReportParam.setMerchantId(29L);
            saveReportParam.setYmd(DateUtil.getFormatDateStr(date, DateUtil.yyyyMMdd1));
            reportNewService.saveReportRentStockTime(saveReportParam);
            System.out.println("saveReportRentStockTime success");
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    @Test
    public void saveReportInsuranceTest() {
        for (int i = 14; i < 15; i++) {
//            Date date = DateUtils.addDays(new Date(), -i);
            Date date = new Date();
            try {
                SaveReportParam saveReportParam = new SaveReportParam();
                saveReportParam.setYmd(DateUtil.getFormatDateStr(date, DateUtil.yyyyMMdd1));
                saveReportParam.setMerchantId(43L);
                saveReportParam.setStoreId(148L);
                reportNewService.saveReportInsurance(saveReportParam);
                System.out.println("saveReportInsurance success");
            }catch (Exception e) {
                System.out.println("date=" + date);
                System.out.println(e);
            }
        }
    }

    @Test
    public void getReportDataTest() {
        try {

            ReportDataQuery reportDataQuery = new ReportDataQuery();
            reportDataQuery.setMerchantId(1L);
//            reportDataQuery.setStartYmd(20240606);
//            reportDataQuery.setEndYmd(20240613);
//            reportDataQuery.setReferenceEndYmd(20240606);
//            reportDataQuery.setReferenceStartYmd(20240530);
            reportDataQuery.setStartYmd(20240702);
            reportDataQuery.setEndYmd(20240709);
            reportDataQuery.setReferenceEndYmd(20240702);
            reportDataQuery.setReferenceStartYmd(20240625);
            reportDataQuery.setPageSize(1000);
            reportDataQuery.setQuotaType((byte) 6);
//            List<Long> storeIdList = new ArrayList<>();
//            storeIdList.add(147L);
//            storeIdList.add(148L);
//            reportDataQuery.setStoreIdList(storeIdList);
//            List<Long> vehicleModelIds = new ArrayList<>();
//            vehicleModelIds.add(2173L);
//            reportDataQuery.setVehicleModelIds(vehicleModelIds);
//            List<Long> cityIdList = new ArrayList<>();
//            cityIdList.add(264L);
//            reportDataQuery.setCityIdList(cityIdList);
            Result<ReportDataAllVO> result = reportNewService.getReportData(reportDataQuery);
            System.out.println(JSON.toJSONString(result));
        } catch (Exception e){
            System.out.println(e);
        }
    }

    @Test
    public void getReportDataExcelTest() {
        try {
            ReportDataQuery reportDataQuery = new ReportDataQuery();
            reportDataQuery.setMerchantId(1L);
            reportDataQuery.setStartYmd(20240702);
            reportDataQuery.setEndYmd(20240709);
            Result<List<ReportDataExcelVO>> result = reportNewService.getReportDataExcel(reportDataQuery);
            System.out.println(JSON.toJSONString(result));
        } catch (Exception e){
            System.out.println(e);
        }
    }

}
