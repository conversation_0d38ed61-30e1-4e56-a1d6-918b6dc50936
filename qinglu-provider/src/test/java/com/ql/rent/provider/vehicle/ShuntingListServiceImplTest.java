package com.ql.rent.provider.vehicle;

import com.alibaba.fastjson.JSON;
import com.ql.rent.service.vehicle.IShuntingListService;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.vehicle.ShuntingListVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@SpringBootTest
@RunWith(SpringRunner.class)
public class ShuntingListServiceImplTest {

    @Resource
    private IShuntingListService shuntingListService;

    @Test
    public void getById() {
        Result<ShuntingListVO> byId = shuntingListService.getById(1L);
        System.out.println(JSON.toJSONString(byId));
    }
}