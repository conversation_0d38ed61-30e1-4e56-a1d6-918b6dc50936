package com.ql.rent.provider.trade;

import com.ql.rent.component.RemoteViolationService;
import com.ql.rent.param.trade.TransferVehicleBindQuery;
import com.ql.rent.param.trade.MallServiceOrderParam;
import com.ql.rent.remote.vehicle.vo.request.*;
import com.ql.rent.remote.vehicle.vo.response.*;
import com.ql.rent.service.merchant.TrafficManagementBindService;
import com.ql.rent.service.trade.IMallServiceOrderInfoService;
import com.ql.rent.service.trade.IVehicleIllegalTransferDetailService;
import com.ql.rent.service.vehicle.IVehicleInfoService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.merchant.TrafficManagementBindDTO;
import com.ql.rent.vo.trade.MallServiceOrderInfoDTO;
import com.ql.rent.vo.trade.VehicleIllegalTransferDetailVo;
import com.ql.rent.vo.vehicle.VehicleInfoVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ThirdIllegalTransferServiceImpl 单元测试
 */
@ExtendWith(MockitoExtension.class)
class ThirdIllegalTransferServiceImplTest {

    @Mock
    private IVehicleIllegalTransferDetailService vehicleIllegalTransferDetailService;

    @Mock
    private IMallServiceOrderInfoService mallServiceOrderInfoService;

    @Mock
    private RemoteViolationService remoteViolationService;

    @Mock
    private IVehicleInfoService vehicleInfoService;

    @Mock
    private TrafficManagementBindService trafficManagementBindService;

    @InjectMocks
    private ThirdIllegalTransferServiceImpl thirdIllegalTransferService;

    private TransferVehicleBindQuery mockQuery;
    private SubmitContractReq mockSubmitContractReq;
    private VehicleInfoVO mockVehicleInfo;
    private TrafficManagementBindDTO mockBindDTO;

    @BeforeEach
    void setUp() {
        // 设置模拟查询参数
        mockQuery = new TransferVehicleBindQuery();
        mockQuery.setMerchantId(100L);
        mockQuery.setVehicleId(1L);

        // 设置模拟合同提交请求
        mockSubmitContractReq = new SubmitContractReq();
        mockSubmitContractReq.setCarNumber("京A12345");
        mockSubmitContractReq.setDriverId("123456789012345678");
        mockSubmitContractReq.setContractNo("CONTRACT001");
        mockSubmitContractReq.setRentType(1);
        mockSubmitContractReq.setBeginTime(new Date());
        mockSubmitContractReq.setEndTime(new Date(System.currentTimeMillis() + 86400000L));
        mockSubmitContractReq.setCarType("1");

        // 设置模拟车辆信息
        mockVehicleInfo = new VehicleInfoVO();
        mockVehicleInfo.setId(1L);
        mockVehicleInfo.setLicense("京A12345");

        // 设置模拟交管绑定信息
        mockBindDTO = new TrafficManagementBindDTO();
        mockBindDTO.setUsername("test123");
    }

    @Test
    void testListTransferVehicleBind_Success() {
        // 准备测试数据
        List<VehicleIllegalTransferDetailVo> mockDetailList = new ArrayList<>();
        VehicleIllegalTransferDetailVo detail = new VehicleIllegalTransferDetailVo();
        detail.setId(1L);
        detail.setOrderId(1001L);
        detail.setVehicleId(1L);
        mockDetailList.add(detail);

        // Mock 依赖服务
        when(vehicleIllegalTransferDetailService.listTransferVehicleBind(mockQuery))
                .thenReturn(ResultUtil.successResult(mockDetailList));

        // 执行测试
        Result<List<VehicleIllegalTransferDetailVo>> result = 
                thirdIllegalTransferService.listTransferVehicleBind(mockQuery);

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getModel());
        assertEquals(1, result.getModel().size());
        assertEquals(1L, result.getModel().get(0).getId());

        // 验证调用
        verify(vehicleIllegalTransferDetailService).listTransferVehicleBind(mockQuery);
    }

    // 已删除 serviceOrderList 方法的测试，因为该方法已经移除

    @Test
    void testSubmitContract_Success() {
        // 准备测试数据
        Long merchantId = 100L;
        
        List<VehicleIllegalTransferDetailVo> mockBindList = new ArrayList<>();
        VehicleIllegalTransferDetailVo bindDetail = new VehicleIllegalTransferDetailVo();
        bindDetail.setOrderId(1001L);
        mockBindList.add(bindDetail);

        SubmitContractResponse mockResponse = new SubmitContractResponse();
        mockResponse.setOrderId("CONTRACT001");
        mockResponse.setState(1);
        
        // 创建有效订单返回对象
        MallServiceOrderInfoDTO validOrder = new MallServiceOrderInfoDTO();
        validOrder.setId(1001L);
        validOrder.setOrderStatus((byte) 1); // 已全额支付

        // Mock 依赖服务
        when(vehicleInfoService.vehicleInfoByLicense(merchantId, "京A12345"))
                .thenReturn(ResultUtil.successResult(mockVehicleInfo));
        when(vehicleIllegalTransferDetailService.listTransferVehicleBind(any(TransferVehicleBindQuery.class)))
                .thenReturn(ResultUtil.successResult(mockBindList));
        // 返回有效订单列表
        when(mallServiceOrderInfoService.serviceOrderList(eq(merchantId), any(MallServiceOrderParam.class)))
                .thenReturn(Arrays.asList(validOrder));
        when(trafficManagementBindService.selectIllegalTransferAccount(merchantId, 1L))
                .thenReturn(mockBindDTO);
        when(remoteViolationService.submitContract(any(SubmitContractReq.class)))
                .thenReturn(ResultUtil.successResult(mockResponse));

        // 执行测试
        Result<SubmitContractResponse> result = 
                thirdIllegalTransferService.submitContract(mockSubmitContractReq, merchantId);

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getModel());
        assertEquals("CONTRACT001", result.getModel().getOrderId());

        // 验证调用
        verify(vehicleInfoService).vehicleInfoByLicense(merchantId, "京A12345");
        verify(mallServiceOrderInfoService).serviceOrderList(eq(merchantId), any(MallServiceOrderParam.class));
        verify(remoteViolationService).submitContract(any(SubmitContractReq.class));
    }

    @Test
    void testSubmitContract_NullMerchantId() {
        // 执行测试
        Result<SubmitContractResponse> result = 
                thirdIllegalTransferService.submitContract(mockSubmitContractReq, null);

        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals("商户ID不能为空", result.getMessage());
    }

    @Test
    void testSubmitContract_InvalidParameters() {
        // 测试无效参数
        SubmitContractReq invalidReq = new SubmitContractReq();
        // 缺少必要字段

        Result<SubmitContractResponse> result = 
                thirdIllegalTransferService.submitContract(invalidReq, 100L);

        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals("参数错误", result.getMessage());
    }

    @Test
    void testSubmitContract_VehicleNotFound() {
        // 准备测试数据
        Long merchantId = 100L;

        // Mock 依赖服务 - 车辆不存在
        when(vehicleInfoService.vehicleInfoByLicense(merchantId, "京A12345"))
                .thenReturn(ResultUtil.failResult("车辆不存在"));

        // 执行测试
        Result<SubmitContractResponse> result = 
                thirdIllegalTransferService.submitContract(mockSubmitContractReq, merchantId);

        // 验证结果
        assertFalse(result.isSuccess());
        assertTrue(result.getMessage().contains("车辆不存在"));
    }

    @Test
    void testSubmitContract_NoBindingRecord() {
        // 准备测试数据
        Long merchantId = 100L;

        // Mock 依赖服务 - 无绑定记录
        when(vehicleInfoService.vehicleInfoByLicense(merchantId, "京A12345"))
                .thenReturn(ResultUtil.successResult(mockVehicleInfo));
        when(vehicleIllegalTransferDetailService.listTransferVehicleBind(any(TransferVehicleBindQuery.class)))
                .thenReturn(ResultUtil.successResult(Collections.emptyList()));

        // 执行测试
        Result<SubmitContractResponse> result = 
                thirdIllegalTransferService.submitContract(mockSubmitContractReq, merchantId);

        // 验证结果
        assertFalse(result.isSuccess());
        assertTrue(result.getMessage().contains("未绑定违章转移订单"));
    }

    @Test
    void testSubmitContract_NoValidOrder() {
        // 准备测试数据
        Long merchantId = 100L;

        List<VehicleIllegalTransferDetailVo> mockBindList = new ArrayList<>();
        VehicleIllegalTransferDetailVo bindDetail = new VehicleIllegalTransferDetailVo();
        bindDetail.setOrderId(1001L);
        mockBindList.add(bindDetail);

        // Mock 依赖服务 - 无有效订单
        when(vehicleInfoService.vehicleInfoByLicense(merchantId, "京A12345"))
                .thenReturn(ResultUtil.successResult(mockVehicleInfo));
        when(vehicleIllegalTransferDetailService.listTransferVehicleBind(any(TransferVehicleBindQuery.class)))
                .thenReturn(ResultUtil.successResult(mockBindList));
        // 返回空列表，表示没有有效订单
        when(mallServiceOrderInfoService.serviceOrderList(eq(merchantId), any(MallServiceOrderParam.class)))
                .thenReturn(Collections.emptyList());

        // 执行测试
        Result<SubmitContractResponse> result = 
                thirdIllegalTransferService.submitContract(mockSubmitContractReq, merchantId);

        // 验证结果
        assertFalse(result.isSuccess());
        assertTrue(result.getMessage().contains("未绑定可用的违章转移订单"));
    }

    @Test
    void testSubmitContract_NoAccountBinding() {
        // 准备测试数据
        Long merchantId = 100L;
        mockSubmitContractReq.setAccount(null); // 不提供account参数

        List<VehicleIllegalTransferDetailVo> mockBindList = new ArrayList<>();
        VehicleIllegalTransferDetailVo bindDetail = new VehicleIllegalTransferDetailVo();
        bindDetail.setOrderId(1001L);
        mockBindList.add(bindDetail);
        
        // 创建有效订单返回对象
        MallServiceOrderInfoDTO validOrder = new MallServiceOrderInfoDTO();
        validOrder.setId(1001L);
        validOrder.setOrderStatus((byte) 1); // 已全额支付

        // Mock 依赖服务 - 无122账号绑定
        when(vehicleInfoService.vehicleInfoByLicense(merchantId, "京A12345"))
                .thenReturn(ResultUtil.successResult(mockVehicleInfo));
        when(vehicleIllegalTransferDetailService.listTransferVehicleBind(any(TransferVehicleBindQuery.class)))
                .thenReturn(ResultUtil.successResult(mockBindList));
        when(mallServiceOrderInfoService.serviceOrderList(eq(merchantId), any(MallServiceOrderParam.class)))
                .thenReturn(Arrays.asList(validOrder));
        when(trafficManagementBindService.selectIllegalTransferAccount(merchantId, 1L))
                .thenReturn(null);

        // 执行测试
        Result<SubmitContractResponse> result = 
                thirdIllegalTransferService.submitContract(mockSubmitContractReq, merchantId);

        // 验证结果
        assertFalse(result.isSuccess());
        assertTrue(result.getMessage().contains("未绑定122账号"));
    }

    @Test
    void testCancelContract() {
        // 准备测试数据
        CancelContractReq cancelReq = new CancelContractReq();
        cancelReq.setOrderId("CONTRACT001");

        // Mock 依赖服务
        when(remoteViolationService.cancelContract(cancelReq))
                .thenReturn(ResultUtil.successResult(true));

        // 执行测试
        Result<Boolean> result = thirdIllegalTransferService.cancelContract(cancelReq);

        // 验证结果
        assertTrue(result.isSuccess());
        assertTrue(result.getModel());

        // 验证调用
        verify(remoteViolationService).cancelContract(cancelReq);
    }

    @Test
    void testContractDetail() {
        // 准备测试数据
        ContractDetailReq detailReq = new ContractDetailReq();
        detailReq.setOrderId("CONTRACT001");

        ContractDetailResponse mockResponse = new ContractDetailResponse();
        mockResponse.setOrderId("CONTRACT001");

        // Mock 依赖服务
        when(remoteViolationService.contractDetail(detailReq))
                .thenReturn(ResultUtil.successResult(mockResponse));

        // 执行测试
        Result<ContractDetailResponse> result = thirdIllegalTransferService.contractDetail(detailReq);

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getModel());
        assertEquals("CONTRACT001", result.getModel().getOrderId());

        // 验证调用
        verify(remoteViolationService).contractDetail(detailReq);
    }

    @Test
    void testAbortContract() {
        // 准备测试数据
        AbortContractReq abortReq = new AbortContractReq();
        abortReq.setOrderId("CONTRACT001");

        // Mock 依赖服务
        when(remoteViolationService.abortContract(abortReq))
                .thenReturn(ResultUtil.successResult(true));

        // 执行测试
        Result<Boolean> result = thirdIllegalTransferService.abortContract(abortReq);

        // 验证结果
        assertTrue(result.isSuccess());
        assertTrue(result.getModel());

        // 验证调用
        verify(remoteViolationService).abortContract(abortReq);
    }

    @Test
    void testContractList() {
        // 准备测试数据
        ContractListReq listReq = new ContractListReq();
        
        List<ContractListResponse> mockResponseList = new ArrayList<>();
        ContractListResponse response = new ContractListResponse();
        response.setOrderId("CONTRACT001");
        mockResponseList.add(response);

        // Mock 依赖服务
        when(remoteViolationService.contractList(listReq))
                .thenReturn(ResultUtil.successResult(mockResponseList));

        // 执行测试
        Result<List<ContractListResponse>> result = thirdIllegalTransferService.contractList(listReq);

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getModel());
        assertEquals(1, result.getModel().size());

        // 验证调用
        verify(remoteViolationService).contractList(listReq);
    }

    @Test
    void testTransferContract_Success() {
        // 准备测试数据
        String contractId = "CONTRACT001";
        String contractFile = "base64encodedfile";

        // Mock 依赖服务
        when(remoteViolationService.transferContract(any(TransferContractReq.class)))
                .thenReturn(ResultUtil.successResult(true));

        // 执行测试
        Result<Boolean> result = thirdIllegalTransferService.transferContract(contractId, contractFile);

        // 验证结果
        assertTrue(result.isSuccess());
        assertTrue(result.getModel());

        // 验证调用
        verify(remoteViolationService).transferContract(any(TransferContractReq.class));
    }

    @Test
    void testTransferContract_NullParameters() {
        // 测试空参数
        Result<Boolean> result1 = thirdIllegalTransferService.transferContract(null, "file");
        assertFalse(result1.isSuccess());
        assertEquals("参数错误", result1.getMessage());

        Result<Boolean> result2 = thirdIllegalTransferService.transferContract("CONTRACT001", null);
        assertFalse(result2.isSuccess());
        assertEquals("参数错误", result2.getMessage());
    }
} 