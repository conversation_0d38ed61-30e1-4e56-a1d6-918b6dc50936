package com.ql.rent.provider.vehicle;

import com.google.common.collect.Lists;
import com.ql.rent.converter.vehicle.VehicleSelfSettingConverter;
import com.ql.rent.dao.vehicle.VehicleSelfSettingMapper;
import com.ql.rent.entity.vehicle.VehicleSelfSetting;
import com.ql.rent.entity.vehicle.VehicleSelfSettingExample;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.vo.vehicle.VehicleSelfSettingDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class VehicleSelfSettingServiceImplTest {

  @Mock
  private VehicleSelfSettingMapper selfSettingMapper;

  @InjectMocks
  private VehicleSelfSettingServiceImpl vehicleSelfSettingService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Nested
  class SelfSettingTests {
    @Test
    void shouldReturnEmptyListWhenNoSettingsFound() {
      // Arrange
      Long merchantId = 1L;
      List<Long> storeIds = Arrays.asList(1L, 2L);
      Long vehicleId = 1L;

      when(selfSettingMapper.selectByExampleWithBLOBs(any())).thenReturn(Collections.emptyList());

      // Act
      List<VehicleSelfSettingDTO> result = vehicleSelfSettingService.selfSetting(merchantId, storeIds, vehicleId);

      // Assert
      assertTrue(result.isEmpty());
      verify(selfSettingMapper).selectByExampleWithBLOBs(any());
    }

    @Test
    void shouldReturnSortedSettingsWhenSettingsExist() {
      // Arrange
      Long merchantId = 1L;
      List<Long> storeIds = Arrays.asList(1L, 2L);
      Long vehicleId = 1L;

      VehicleSelfSetting setting1 = new VehicleSelfSetting();
      setting1.setId(1L);
      setting1.setMerchantId(merchantId);
      setting1.setDeleted(YesOrNoEnum.NO.getValue());
      setting1.setVehicleId("1");

      VehicleSelfSetting setting2 = new VehicleSelfSetting();
      setting2.setId(2L);
      setting2.setMerchantId(merchantId);
      setting2.setDeleted(YesOrNoEnum.NO.getValue());
      setting2.setVehicleId("2");

      when(selfSettingMapper.selectByExampleWithBLOBs(any()))
          .thenReturn(Arrays.asList(setting1, setting2));

      // Act
      List<VehicleSelfSettingDTO> result = vehicleSelfSettingService.selfSetting(merchantId, storeIds, vehicleId);

      // Assert
      assertFalse(result.isEmpty());
      assertEquals(2, result.size());

      // Verify query criteria
      ArgumentCaptor<VehicleSelfSettingExample> exampleCaptor = ArgumentCaptor
          .forClass(VehicleSelfSettingExample.class);
      verify(selfSettingMapper).selectByExampleWithBLOBs(exampleCaptor.capture());
      VehicleSelfSettingExample capturedExample = exampleCaptor.getValue();
      VehicleSelfSettingExample.Criteria criteria = capturedExample.getOredCriteria().get(0);

      // Verify the criteria was set correctly
      assertTrue(criteria.getAllCriteria().stream()
          .anyMatch(c -> c.getCondition().contains("merchant_id") && c.getValue().equals(merchantId)));
      assertTrue(criteria.getAllCriteria().stream()
          .anyMatch(c -> c.getCondition().contains("deleted") && c.getValue().equals(YesOrNoEnum.NO.getValue())));
    }
  }
}