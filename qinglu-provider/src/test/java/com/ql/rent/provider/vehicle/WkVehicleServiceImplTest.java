package com.ql.rent.provider.vehicle;

import com.ql.rent.dao.vehicle.VehicleBrandMapper;
import com.ql.rent.dao.vehicle.VehicleSeryMapper;
import com.ql.rent.dao.vehicle.VehicleSubSeryMapper;
import com.ql.rent.dao.vehicle.WkCarTypeInfoMapper;
import com.ql.rent.entity.vehicle.VehicleBrand;
import com.ql.rent.entity.vehicle.VehicleSubSery;
import com.ql.rent.entity.vehicle.WkCarTypeInfo;
import com.ql.rent.entity.vehicle.WkCarTypeInfoExample;
import com.ql.rent.vo.vehicle.ThirdVehicleModelSelectVO;
import com.ql.rent.vo.vehicle.ThirdVehicleModelVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class WkVehicleServiceImplTest {

  @Mock
  private VehicleSubSeryMapper vehicleSubSeryMapper;

  @Mock
  private VehicleBrandMapper vehicleBrandMapper;

  @Mock
  private VehicleSeryMapper vehicleSeryMapper;

  @Mock
  private WkCarTypeInfoMapper wkCarTypeInfoMapper;

  @InjectMocks
  private WkVehicleServiceImpl wkVehicleService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Nested
  class SearchVehicleTests {
    @Test
    void shouldReturnEmptyListWhenNoMatchingVehiclesFound() {
      // Arrange
      Long subSeryId = 1L;
      Long merchantId = 1L;

      VehicleSubSery subSery = new VehicleSubSery();
      subSery.setBrandId(1L);
      subSery.setDisplacement("2.0L");
      subSery.setTransmission("6");
      subSery.setDoors("4门");
      subSery.setPassengers("5座");
      subSery.setYears("2023");

      VehicleBrand brand = new VehicleBrand();
      brand.setBrandName("TestBrand");

      when(vehicleSubSeryMapper.selectByPrimaryKey(subSeryId)).thenReturn(subSery);
      when(vehicleBrandMapper.selectByPrimaryKey(subSery.getBrandId())).thenReturn(brand);
      when(wkCarTypeInfoMapper.selectByExample(any())).thenReturn(Collections.emptyList());

      // Act
      ThirdVehicleModelSelectVO result = wkVehicleService.searchVehicle(subSeryId);

      // Assert
      assertNotNull(result);
      assertTrue(result.getVehicleModelMatchList().isEmpty());
      assertTrue(result.getMatchedList().isEmpty());

      // Verify query criteria
      ArgumentCaptor<WkCarTypeInfoExample> exampleCaptor = ArgumentCaptor.forClass(WkCarTypeInfoExample.class);
      verify(wkCarTypeInfoMapper).selectByExample(exampleCaptor.capture());
      WkCarTypeInfoExample capturedExample = exampleCaptor.getValue();
      WkCarTypeInfoExample.Criteria criteria = capturedExample.getOredCriteria().get(0);
      assertTrue(criteria.getAllCriteria().stream()
          .anyMatch(c -> c.getCondition().contains("brand_name") && c.getValue().equals("TestBrand")));
    }

    @Test
    void shouldReturnMatchedVehiclesWithPreciseMatching() {
      // Arrange
      Long subSeryId = 1L;
      Long merchantId = 1L;

      VehicleSubSery subSery = new VehicleSubSery();
      subSery.setBrandId(1L);
      subSery.setDisplacement("2.0L");
      subSery.setTransmission("6");
      subSery.setDoors("4门");
      subSery.setPassengers("5座");
      subSery.setYears("2023");

      VehicleBrand brand = new VehicleBrand();
      brand.setBrandName("TestBrand");

      WkCarTypeInfo carType1 = new WkCarTypeInfo();
      carType1.setCarTypeId("CT001");
      carType1.setBrandName("TestBrand");
      carType1.setName("Model1");
      carType1.setDisplacement("2.0");
      carType1.setDisplacementName("L");
      carType1.setTransmissionName("6档");
      carType1.setDoorNum(4);
      carType1.setCapacity(5);
      carType1.setYear("2023");

      WkCarTypeInfo carType2 = new WkCarTypeInfo();
      carType2.setCarTypeId("CT002");
      carType2.setBrandName("TestBrand");
      carType2.setName("Model2");
      carType2.setDisplacement("2.5");
      carType2.setDisplacementName("L");
      carType2.setTransmissionName("6档");
      carType2.setDoorNum(4);
      carType2.setCapacity(5);
      carType2.setYear("2023");

      when(vehicleSubSeryMapper.selectByPrimaryKey(subSeryId)).thenReturn(subSery);
      when(vehicleBrandMapper.selectByPrimaryKey(subSery.getBrandId())).thenReturn(brand);
      when(wkCarTypeInfoMapper.selectByExample(any())).thenReturn(Arrays.asList(carType1, carType2));

      // Act
      ThirdVehicleModelSelectVO result = wkVehicleService.searchVehicle(subSeryId);

      // Assert
      assertNotNull(result);
      assertEquals(2, result.getVehicleModelMatchList().size());

      // Verify the first matched vehicle
      ThirdVehicleModelVO firstMatch = result.getVehicleModelMatchList().get(0);
      assertEquals("CT001", firstMatch.getId());
      assertEquals("TestBrand Model1 5座 6档", firstMatch.getName());

      // Verify the second matched vehicle
      ThirdVehicleModelVO secondMatch = result.getVehicleModelMatchList().get(1);
      assertEquals("CT002", secondMatch.getId());
      assertEquals("TestBrand Model2 5座 6档", secondMatch.getName());
    }

    @Test
    void shouldHandlePureElectricVehicles() {
      // Arrange
      Long subSeryId = 1L;
      Long merchantId = 1L;

      VehicleSubSery subSery = new VehicleSubSery();
      subSery.setBrandId(1L);
      subSery.setDisplacement("纯电动");
      subSery.setTransmission("6");
      subSery.setDoors("4门");
      subSery.setPassengers("5座");
      subSery.setYears("2023");

      VehicleBrand brand = new VehicleBrand();
      brand.setBrandName("TestBrand");

      WkCarTypeInfo carType = new WkCarTypeInfo();
      carType.setCarTypeId("CT001");
      carType.setBrandName("TestBrand");
      carType.setName("Model1");
      carType.setDisplacement("0.0");
      carType.setTransmissionName("6档");
      carType.setDoorNum(4);
      carType.setCapacity(5);
      carType.setYear("2023");

      when(vehicleSubSeryMapper.selectByPrimaryKey(subSeryId)).thenReturn(subSery);
      when(vehicleBrandMapper.selectByPrimaryKey(subSery.getBrandId())).thenReturn(brand);
      when(wkCarTypeInfoMapper.selectByExample(any())).thenReturn(Collections.singletonList(carType));

      // Act
      ThirdVehicleModelSelectVO result = wkVehicleService.searchVehicle(subSeryId);

      // Assert
      assertNotNull(result);
      assertEquals(1, result.getVehicleModelMatchList().size());

      ThirdVehicleModelVO match = result.getVehicleModelMatchList().get(0);
      assertEquals("CT001", match.getId());
      assertEquals("TestBrand Model1 5座 6档", match.getName());
    }

    @Test
    void shouldHandleTurbochargedVehicles() {
      // Arrange
      Long subSeryId = 1L;
      Long merchantId = 1L;

      VehicleSubSery subSery = new VehicleSubSery();
      subSery.setBrandId(1L);
      subSery.setDisplacement("2.0T");
      subSery.setTransmission("6");
      subSery.setDoors("4门");
      subSery.setPassengers("5座");
      subSery.setYears("2023");

      VehicleBrand brand = new VehicleBrand();
      brand.setBrandName("TestBrand");

      WkCarTypeInfo carType = new WkCarTypeInfo();
      carType.setCarTypeId("CT001");
      carType.setBrandName("TestBrand");
      carType.setName("Model1");
      carType.setDisplacement("2.0");
      carType.setDisplacementName("T");
      carType.setTransmissionName("6档");
      carType.setDoorNum(4);
      carType.setCapacity(5);
      carType.setYear("2023");

      when(vehicleSubSeryMapper.selectByPrimaryKey(subSeryId)).thenReturn(subSery);
      when(vehicleBrandMapper.selectByPrimaryKey(subSery.getBrandId())).thenReturn(brand);
      when(wkCarTypeInfoMapper.selectByExample(any())).thenReturn(Collections.singletonList(carType));

      // Act
      ThirdVehicleModelSelectVO result = wkVehicleService.searchVehicle(subSeryId);

      // Assert
      assertNotNull(result);
      assertEquals(1, result.getVehicleModelMatchList().size());

      ThirdVehicleModelVO match = result.getVehicleModelMatchList().get(0);
      assertEquals("CT001", match.getId());
      assertEquals("TestBrand Model1 5座 6档", match.getName());
    }
  }
}