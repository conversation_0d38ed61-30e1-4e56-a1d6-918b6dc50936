package com.ql.rent.schedule.task;

import com.ql.rent.AbstractTest;
import org.junit.Test;
import org.quartz.JobExecutionException;

import javax.annotation.Resource;

public class VehicleOrderTaskTest extends AbstractTest {


    @Resource
    VehicleOrderTask task;


    @Test
    public void execute() {
        try {
            task.execute(null);
        } catch (JobExecutionException e) {
            throw new RuntimeException(e);
        }

    }

}