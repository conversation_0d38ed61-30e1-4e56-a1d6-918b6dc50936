package com.ql.rent.price;

import com.alibaba.fastjson.JSON;
import com.ql.dto.ApiResultResp;
import com.ql.dto.open.request.price.CheckPriceRequest;
import com.ql.dto.open.request.price.OpenCtripStandardFeeRequest;
import com.ql.dto.open.response.OpenCtripStandardFeeResponse;
import com.ql.dto.price.*;
import com.ql.dto.ResultResp;
import com.ql.dto.open.request.HolidayRequest;
import com.ql.dto.open.request.trade.RentBookingPolicySyncRequest;
import com.ql.dto.open.response.HolidaySaasRes;
import com.ql.dto.open.request.price.OpenProductPriceCalendarRequest;
import com.ql.enums.FeeTypeEnum;
import com.ql.rent.api.aggregate.model.remote.ctrip.dto.CtripSkuPriceInfoDTO;
import com.ql.rent.api.aggregate.model.remote.ctrip.dto.SkuPriceInfoDTO;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.request.GetSkuPriceInfoRequest;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.request.PushCarRentBookingPolicyDetailRequest;
import com.ql.enums.open.ModificationBusiTypeEnum;
import com.ql.rent.bizdata.param.PushDataParam;
import com.ql.rent.bizdata.service.ISkuPricePushDateService;
import com.ql.rent.client.IPriceOpenService;
import com.ql.rent.component.PlatformBiz;
import com.ql.rent.enums.price.CtripHolidayTypeEnum;
import com.ql.rent.param.price.DayPriceListQuery;
import com.ql.rent.param.price.LimitPriceV2Param;
import com.ql.rent.param.price.StandardFeeParam;
import com.ql.rent.param.sync.CtripSkuPriceTransactionParam;
import com.ql.rent.rpc.PriceInfoRpc;
import com.ql.rent.service.price.ICtripPriceService;
import com.ql.rent.service.price.IRentMainService;
import com.ql.dto.open.request.price.OpenPriceInfoFullSyncRequest;
import com.ql.rent.service.price.IThirdPriceService;
import com.ql.rent.service.store.ICtripStoreService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
class PriceTest {
    @Resource
    private IRentMainService rentMainService;

    @Resource
    private IPriceOpenService priceOpenService;
    @Resource
    private IThirdPriceService thirdPriceService;

    @Resource
    private PlatformBiz platformBiz;

    @Resource
    private ICtripStoreService ctripStoreService;
    @Resource
    private ISkuPricePushDateService skuPricePushDateService;
    @Resource
    private PriceInfoRpc priceInfoRpc;
    @Resource
    private ICtripPriceService ctripPriceService;

    @Test
    public void getSkuPriceInfo() throws Exception {
        Long merchantId = 44L;
        GetSkuPriceInfoRequest skuPriceInfoRequest = new GetSkuPriceInfoRequest();
        List<SkuPriceInfoDTO> skuList = new ArrayList<>();
        skuPriceInfoRequest.setVendorId("67709");
        skuPriceInfoRequest.setSkuList(skuList);
        SkuPriceInfoDTO skuPriceInfoDTO = new SkuPriceInfoDTO();
        skuList.add(skuPriceInfoDTO);
        skuPriceInfoDTO.setVendorId("67709");
        skuPriceInfoDTO.setStoreCode("353");
        skuPriceInfoDTO.setVvc("1380_20797_pupai");
        //skuPriceInfoDTO.setSkuId(110346377L);
        Long channelId = 2L;
        CtripSkuPriceInfoDTO test = ctripPriceService.getSkuPriceInfo(merchantId, skuPriceInfoRequest, channelId);
        System.out.println(JSON.toJSONString(test));
    }

    @Test
    public void priceInfoRpc() throws Exception {
        FeiZhuPriceParam param = new FeiZhuPriceParam();
        param.setChannel(3L);
        param.setStoreIds(Arrays.asList(487L));
        param.setVehicleModelId(12682L);
        ApiResultResp<List<InsuranceAddedInfo>> test = priceInfoRpc.getInsuranceAddedService(param);
        //ApiResultResp<List<AddedInfo>> test = priceInfoRpc.getAddedServiceInfo(param);
        System.out.println(JSON.toJSONString(test));
    }

    @Test
    public void doInitPriceDate() throws Exception {
        String str = "{\"channelId\":4,\"merchantId\":6,\"priceDateList\":[{\"illegalDeposit\":200000,\"mileage\":0,\"mileageLimit\":0,\"mileageRent\":0,\"priceList\":[{\"price\":11000,\"priceName\":\"基础价格(平日)\",\"priceType\":\"BASE_PRICE_WEEKDAY\"},{\"price\":12000,\"priceName\":\"基础价格(周末)\",\"priceType\":\"BASE_PRICE_WEEKEND\"}],\"rentDeposit\":200000,\"servicePriceList\":[{\"price\":6000,\"priceName\":\"无忧尊享服务费\",\"priceType\":\"2011\"},{\"price\":5000,\"priceName\":\"优享服务费\",\"priceType\":\"2001\"},{\"price\":4000,\"priceName\":\"基本保障服务费\",\"priceType\":\"1002\"}],\"status\":1,\"storeId\":7392,\"vehicleModelId\":13174},{\"illegalDeposit\":200000,\"mileage\":0,\"mileageLimit\":0,\"mileageRent\":0,\"priceList\":[{\"price\":12000,\"priceName\":\"基础价格(平日)\",\"priceType\":\"BASE_PRICE_WEEKDAY\"},{\"price\":13000,\"priceName\":\"基础价格(周末)\",\"priceType\":\"BASE_PRICE_WEEKEND\"}],\"rentDeposit\":200800,\"servicePriceList\":[{\"price\":6500,\"priceName\":\"无忧尊享服务费\",\"priceType\":\"2011\"},{\"price\":4500,\"priceName\":\"优享服务费\",\"priceType\":\"2001\"},{\"price\":3300,\"priceName\":\"基本保障服务费\",\"priceType\":\"1002\"}],\"status\":1,\"storeId\":7392,\"vehicleModelId\":13183}]}";
        PriceDatePull priceDatePull = JSON.parseObject(str, PriceDatePull.class);

        // Act & Assert
        thirdPriceService.doInitPriceDate(priceDatePull);
    }


    @Test
    public void pushSkuPrice() throws Exception {
        String str = "{\"merchantId\":45,\"storeId\":3355,\"thirdType\":2}";
        PushDataParam request  = JSON.parseObject(str, PushDataParam.class);

        CtripSkuPriceTransactionParam param = new CtripSkuPriceTransactionParam();
        param.setMerchantId(45L);
        param.setStoreId(354L);
        param.setVehicleModelId(1429L);
        param.setChannelId(Arrays.asList(2L));

        request.setParams(JSON.toJSONString(param));
        skuPricePushDateService.pushSkuPrice(request);
    }

    @Test
    public void bookingPolicySync() {
        String str = "{\"operateType\": 0,   \"merchantId\": 57,   \"specialBookingPolicyList\": [     {       \"startTime\": \"2025-01-28\",       \"endTime\": \"2025-02-02\",       \"thrId\": \"71\",       \"storeAll\": 0,       \"vehicleAll\": 1,       \"channel\": [         2       ],       \"maxRentTerm\": 999,       \"minRentTerm\": 1,       \"storeBookingPolicyInfoList\": [         {           \"channel\": [             2           ],           \"storeAll\": 0,           \"vehicleAll\": 1,           \"storeIdList\": [             \"MD523\",             \"430\",           ],           \"maxRentTerm\": 10,           \"minRentTerm\": 100,           \"isActive\": 1         },         {           \"channel\": [             2           ],           \"storeAll\": 0,           \"vehicleAll\": 1,           \"storeIdList\": [             \"MD54\",             \"MD67\",           ],           \"maxRentTerm\": 20,           \"minRentTerm\": 200,           \"isActive\": 1         }       ]     }   ] }";

        //str = "{\"operateType\":1,\"merchantId\":15,\"specialBookingPolicyList\":[{\"maxRentTerm\":4320,\"vehicleAll\":0,\"channel\":[2,10],\"storeBookingPolicyInfoList\":[{\"maxRentTerm\":4320,\"vehicleAll\":0,\"channel\":[2,10],\"minRentTerm\":3,\"skuBookingPolicyList\":[{\"vehicleModelId\":null,\"applyModel\":{\"thirdId\":\"1516076514985062401:7\",\"saasId\":191},\"maxRentTerm\":4001,\"channel\":[2,10],\"minRentTerm\":4}],\"applyStoreList\":[{\"saasId\":13,\"thirdId\":\"7286503298757886037\"}]}],\"storeAll\":0,\"startTime\":\"2025-01-28\",\"thrId\":\"99\",\"endTime\":\"2025-02-09\",\"minRentTerm\":3}],\"qingLuMerchantId\":\"15\"}";

        str = "{\"operateType\":1,\"specialBookingPolicyList\":[{\"maxRentTerm\":4320,\"vehicleAll\":0,\"channel\":[2,10],\"storeBookingPolicyInfoList\":[{\"maxRentTerm\":4320,\"vehicleAll\":0,\"channel\":[2,10],\"minRentTerm\":120,\"skuBookingPolicyList\":[{\"maxRentTerm\":4320,\"channel\":[2,10],\"minRentTerm\":120,\"isActive\":1,\"applyModel\":{\"thirdId\":\"1516076514985062401:7\",\"saasId\":13018}}],\"applyStoreList\":[{\"thirdId\":\"7286503298757886037\",\"saasId\":13}]}],\"storeAll\":0,\"startTime\":\"2025-01-28\",\"thrId\":\"71\",\"endTime\":\"2025-02-09\",\"minRentTerm\":120,\"isActive\":1},{\"maxRentTerm\":4320,\"vehicleAll\":0,\"channel\":[2,10],\"storeBookingPolicyInfoList\":[{\"maxRentTerm\":4320,\"vehicleAll\":0,\"channel\":[2,10],\"minRentTerm\":144,\"skuBookingPolicyList\":[{\"maxRentTerm\":4320,\"channel\":[2,10],\"minRentTerm\":144,\"isActive\":1,\"applyModel\":{\"thirdId\":\"1516076514985062401:7\",\"saasId\":13018}}],\"applyStoreList\":[{\"thirdId\":\"7286503298757886037\",\"saasId\":13}]}],\"storeAll\":0,\"startTime\":\"2025-10-01\",\"thrId\":\"99\",\"endTime\":\"2025-10-07\",\"minRentTerm\":144,\"isActive\":1}],\"merchantId\":\"15\"}";

        str = "{\"operateType\":1,\"specialBookingPolicyList\":[{\"vehicleAll\":1,\"channel\":[2,10],\"thrId\":\"17537572050653986326\",\"minRentTerm\":480,\"isActive\":1,\"storeBookingPolicyInfoList\":[{\"vehicleAll\":1,\"channel\":[2,10],\"minRentTerm\":482,\"isActive\":1,\"storeIdList\":[\"1910156975484792834\"]}],\"storeAll\":0,\"startTime\":\"2025-09-29 00:00\",\"endTime\":\"2025-10-08 00:00\"}],\"merchantId\":\"87\"}";

        //str = "{\"operateType\":1,\"specialBookingPolicyList\":[{\"vehicleAll\":1,\"channel\":[2,10],\"thrId\":\"17537572050653986326\",\"isActive\":0,\"storeBookingPolicyInfoList\":[{\"vehicleAll\":1,\"channel\":[2,10],\"minRentTerm\":240,\"isActive\":0,\"storeIdList\":[\"1910156975484792834\"]}],\"storeAll\":0}],\"merchantId\":\"87\"}";
        RentBookingPolicySyncRequest request  = JSON.parseObject(str, RentBookingPolicySyncRequest.class);
        priceOpenService.bookingPolicySync(request);
    }

    @Test
    public void getLimitPriceV2() throws Exception {
        String str = "{\"startTime\":1737734400000,\"endTime\":1740067199999,\"cityId\":71,\"vehicleModelId\":1416,\"merchantId\":45}";
        str = "{" +
            "\"dateRanges\":[" +
            "{\"startTime\":1729872000000,\"endTime\":1730303999999}," +
            "{\"startTime\":1737734400000,\"endTime\":1740067199999}" +
            "]," +
            "\"storeId\":354," +
            "\"vehicleModelIds\":[1436,1437,1443]," +
            "\"merchantId\":45" +
            "}";
        LimitPriceV2Param param = JSON.parseObject(str, LimitPriceV2Param.class);
        System.out.println(JSON.toJSONString(rentMainService.getLimitPriceV2(param)));
    }


    @Test
    public void checkPriceInfo() throws Exception {
        CheckPriceRequest param = new CheckPriceRequest();
        param.setMerchantId(58L);
        param.setStoreId("MD123");
        //param.setSaasStoreId(633L);
//        param.setStartTime(1748253998000L);
//        param.setEndTime(1750932398000L);
        //param.setVehicleModelIds(Arrays.asList("257","2","262","470"));
        //param.setSaasModelIds(Arrays.asList(2048L,2078L,2082L,2083L));
        thirdPriceService.checkPriceInfo(param);
    }

    @Test
    public void queryPriceCalendar() throws Exception {
        //String str = "{\"skuList\": [{\"storeId\": \"49174\", \"channelIdList\": [2], \"vehicleModelId\": \"38735\"}]}";
        String str = "{\"skuList\": [{\"storeId\": \"12073\", \"channelIdList\": [2], \"vehicleModelId\": \"428661\"}]}";
        OpenProductPriceCalendarRequest req = JSON.parseObject(str, OpenProductPriceCalendarRequest.class);
        //System.out.println(JSON.toJSONString(thirdPriceService.queryPriceCalendar(78L,  req.getSkuList())));

        str = "{\"skuList\": [{\"saasStoreId\": \"2927\",\"storeId\": \"storeId\", \"channelIdList\": [2], \"saasModelId\": \"11131\", \"vehicleModelId\": \"vehicleModelId\"}]}";
        req = JSON.parseObject(str, OpenProductPriceCalendarRequest.class);
        //System.out.println(JSON.toJSONString(thirdPriceService.queryPriceCalendar(78L,  req.getSkuList())));

        str = "{\"skuList\":[{\"channelIdList\":[2],\"saasModelId\":188,\"saasStoreId\":13,\"storeId\":\"7267249426349656756\",\"vehicleModelId\":\"1516086869274603521_3\"}]}";
        req = JSON.parseObject(str, OpenProductPriceCalendarRequest.class);
        //System.out.println(JSON.toJSONString(thirdPriceService.queryPriceCalendar(15L,  req.getSkuList())));

        str = "{\"skuList\":[{\"channelIdList\":[4],\"saasModelId\":13111,\"saasStoreId\":7309,\"storeId\":\"\",\"vehicleModelId\":\"\"}]}";
        req = JSON.parseObject(str, OpenProductPriceCalendarRequest.class);
        System.out.println(JSON.toJSONString(thirdPriceService.queryPriceCalendar(15L,  req.getSkuList())));

    }


    @Test
    public void pushCarRentBookingBySku(){
        List<Long> storeIds = new ArrayList<>();
        storeIds.add(355L);
        Long merchantId = 45L;
        PushCarRentBookingPolicyDetailRequest pushCarRentBookingPolicyDetailRequest = ctripStoreService.pushCarRentBookingBySku(0, merchantId, storeIds);
        System.out.println("end");
    }

    @Test
    public void calendar(){
        thirdPriceService.syncCtripFestivalHoliday();
    }

    @Test
    public void test() {
        platformBiz.priceModify(3L, 3L, 6L, 195L, ModificationBusiTypeEnum.DEPOSIT_UPDATE.getBusiType());
        platformBiz.priceModify(3L, 3L, 6L, 195L, ModificationBusiTypeEnum.MILEAGE_LIMIT.getBusiType());
        platformBiz.priceModify(3L, 3L, 6L, 195L, ModificationBusiTypeEnum.ADDED_SERVICE.getBusiType());
        platformBiz.priceModify(3L, 3L, 6L, 195L, ModificationBusiTypeEnum.INSURANCE_UPDATE.getBusiType());
//        platformBiz.circleModify(6L, 3L,  ModificationBusiTypeEnum.CIRCLE_SAVE.getBusiType(),3L);
    }

    @Test
    public void priceOpenService() throws Exception {
        String aa = "{\"skuPriceRequests\":[{\"storeId\":\"aEEqdsoiRw\",\"vehicleModelId\":\"1824349388542320640\",\"status\":1,\"priceMondaySunday\":1,\"dailyPriceWeekdayList\":[{\"dayStart\":\"1\",\"dayEnd\":\"1\",\"dayStartName\":null,\"dayEndName\":null,\"price\":10000,\"weekend\":\"0\",\"channel\":[10]},{\"dayStart\":\"2\",\"dayEnd\":\"2\",\"dayStartName\":null,\"dayEndName\":null,\"price\":10000,\"weekend\":\"0\",\"channel\":[10]},{\"dayStart\":\"3\",\"dayEnd\":\"3\",\"dayStartName\":null,\"dayEndName\":null,\"price\":10000,\"weekend\":\"0\",\"channel\":[10]},{\"dayStart\":\"4\",\"dayEnd\":\"4\",\"dayStartName\":null,\"dayEndName\":null,\"price\":20000,\"weekend\":\"0\",\"channel\":[10]},{\"dayStart\":\"5\",\"dayEnd\":\"5\",\"dayStartName\":null,\"dayEndName\":null,\"price\":20000,\"weekend\":\"0\",\"channel\":[10]},{\"dayStart\":\"6\",\"dayEnd\":\"6\",\"dayStartName\":null,\"dayEndName\":null,\"price\":30000,\"weekend\":\"0\",\"channel\":[10]},{\"dayStart\":\"7\",\"dayEnd\":\"7\",\"dayStartName\":null,\"dayEndName\":null,\"price\":40000,\"weekend\":\"1\",\"channel\":[10]}],\"dailyPriceHolidayList\":null,\"dailyPriceSpecialList\":null,\"insurancePriceList\":null,\"addProductPriceList\":null,\"illegalDeposit\":null,\"rentalDeposit\":null,\"noDepositChannelList\":null}]}";
        String a = "{\"merchantId\":57,\"skuPriceRequests\":[{\"addProductPriceList\":[{\"channel\":[2],\"feeCode\":\"2003\",\"feeName\":\"儿童座椅\",\"feeType\":\"CHILD_SEAT\",\"price\":199.88},{\"channel\":[2],\"feeCode\":\"1003\",\"feeName\":\"手续费\",\"feeType\":\"SERVICE_CHARGE\",\"price\":299.88}],\"dailyPriceHolidayList\":[{\"channel\":[2],\"dayEnd\":\"2024-02-16\",\"dayStart\":\"2024-02-09\",\"holidayCode\":\"SPRING_FESTIVAL\",\"holidayName\":\"春节\",\"limitPrice\":100,\"price\":666,\"timezone\":\"Asia/Shanghai\",\"year\":\"2024\"}],\"dailyPriceSpecialList\":[{\"channel\":[2],\"dayEnd\":\"2024-10-25\",\"dayStart\":\"2024-10-22\",\"limitPrice\":100,\"price\":888,\"timezone\":\"Asia/Shanghai\",\"year\":\"2024\"},{\"channel\":[2],\"dayEnd\":\"2024-11-25\",\"dayStart\":\"2024-11-22\",\"limitPrice\":100,\"price\":999,\"timezone\":\"Asia/Shanghai\",\"year\":\"2024\"}],\"dailyPriceWeekdayList\":[{\"dayStart\":\"1\",\"dayEnd\":\"1\",\"dayStartName\":\"MONDAY\",\"dayEndName\":\"MONDAY\",\"price\":66600,\"weekend\":\"0\",\"channelId\":2},{\"dayStart\":\"2\",\"dayEnd\":\"2\",\"dayStartName\":\"TUESDAY\",\"dayEndName\":\"TUESDAY\",\"price\":30000,\"weekend\":\"0\",\"channelId\":2},{\"dayStart\":\"3\",\"dayEnd\":\"3\",\"dayStartName\":\"WEDNESDAY\",\"dayEndName\":\"WEDNESDAY\",\"price\":30000,\"weekend\":\"0\",\"channelId\":2},{\"dayStart\":\"4\",\"dayEnd\":\"4\",\"dayStartName\":\"THURSDAY\",\"dayEndName\":\"THURSDAY\",\"price\":30000,\"weekend\":\"0\",\"channelId\":2},{\"dayStart\":\"5\",\"dayEnd\":\"5\",\"dayStartName\":\"FRIDAY\",\"dayEndName\":\"FRIDAY\",\"price\":30000,\"weekend\":\"1\",\"channelId\":2},{\"dayStart\":\"6\",\"dayEnd\":\"6\",\"dayStartName\":\"SATURDAY\",\"dayEndName\":\"SATURDAY\",\"price\":30000,\"weekend\":\"1\",\"channelId\":2},{\"dayStart\":\"7\",\"dayEnd\":\"7\",\"dayStartName\":\"SUNDAY\",\"dayEndName\":\"SUNDAY\",\"price\":30000,\"weekend\":\"1\",\"channelId\":2}],\"illegalDeposit\":{\"feeCode\":\"4004\",\"feeName\":\"违章押金\",\"feeType\":\"VIOLATION_DEPOSIT\",\"price\":2000},\"insurancePriceList\":[{\"channel\":[2],\"feeCode\":\"1002\",\"feeName\":\"基本保障服务费\",\"feeType\":\"BASIC_SERVICE_FEE\",\"onHighestPrice\":0,\"price\":110},{\"channel\":[2],\"feeCode\":\"2001\",\"feeName\":\"优享服务费\",\"feeType\":\"ADVANCED_SERVICE_FEE\",\"onHighestPrice\":0,\"price\":210.88},{\"channel\":[2],\"feeCode\":\"2011\",\"feeName\":\"无忧尊享服务费\",\"feeType\":\"PREMIUM_SERVICE_FEE\",\"onHighestPrice\":0,\"price\":310.88}],\"rentalDeposit\":{\"feeCode\":\"4003\",\"feeName\":\"租车押金\",\"feeType\":\"RENT_DEPOSIT\",\"price\":5999.88},\"rentDayPriceList\":[{\"rentDate\":\"2023-11-30\",\"channelId\":2,\"price\":9999}],\"noDepositChannelList\":[{\"channel\":2,\"status\":1}],\"storeId\":\"test57_1\",\"vehicleModelId\":\"test_model_1\"}]}";
        String c = "{\"merchantId\":57,\"skuPriceRequests\":[{\"dailyPriceSpecialList\":[{\"channel\":[2],\"dayEnd\":\"2024-10-25\",\"dayStart\":\"2024-10-22\",\"limitPrice\":100,\"price\":888,\"timezone\":\"Asia/Shanghai\",\"year\":\"2024\"},{\"channel\":[2],\"dayEnd\":\"2024-11-25\",\"dayStart\":\"2024-11-22\",\"limitPrice\":100,\"price\":999,\"timezone\":\"Asia/Shanghai\",\"year\":\"2024\",\"delete\":\"1\"}],\"storeId\":\"test57_1\",\"vehicleModelId\":\"test_model_1\",\"skuAdjustSignUps\":[{\"channelId\":2,\"status\":null}]}]}";
        OpenPriceInfoFullSyncRequest req = JSON.parseObject(c, OpenPriceInfoFullSyncRequest.class);
        priceOpenService.fullPriceSync(req);
    }

    @Test
    public void getCtripStandardFee() throws Exception {
        OpenCtripStandardFeeRequest req = new OpenCtripStandardFeeRequest();
        req.setMerchantId(58L);
        req.setStoreId("MD134");
        req.setVehicleModelIds(Arrays.asList("99999", "257", "2", "116"));
        String c = "{\"merchantId\":57,\"storeId\":\"UfhBEYtFIU\",\"vehicleModelIds\":[\"1896880964652261376\",\"1876145152964300800\"]}";
        req = JSON.parseObject(c, OpenCtripStandardFeeRequest.class);
        ResultResp<OpenCtripStandardFeeResponse> resp = priceOpenService.getCtripStandardFee(req);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void holiday() throws Exception {
        HolidayRequest holidayRequest = new HolidayRequest();
        ResultResp<HolidaySaasRes> holidayDetail1 = priceOpenService.getHolidayDetail(holidayRequest);
        holidayRequest.setStartYear(2023);
        holidayRequest.setEndYear(2025);
        ResultResp<HolidaySaasRes> holidayDetail2 = priceOpenService.getHolidayDetail(holidayRequest);
        System.out.println("end");
    }

    @Test
    public void dayPrice() throws Exception {
        DayPriceListQuery query = new DayPriceListQuery();
        query.setMerchantId(1L);
        query.setStoreId(2L);

        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date startTime = dateFormat.parse("2023-06-05");
        Date endTime = dateFormat.parse("2023-06-09");
        query.setStartTime(startTime.getTime());
        query.setEndTime(endTime.getTime());

        query.setChannelId(1L);
        query.setVehicleModelGroup(4L);
        query.setVehicleModelId(1L);

        System.out.println(JSON.toJSONString(rentMainService.dayPriceList(query, "")));
    }


    public static final String ASIA_SHANGHAI = "Asia/Shanghai";

    public static void main(String[] args) {

        OpenPriceInfoFullSyncRequest req = new OpenPriceInfoFullSyncRequest();
        req.setMerchantId(57L);
        List<OpenPriceInfoFullSyncRequest.SkuPriceRequest> skuPriceRequests = new ArrayList<>();

        //平日
        OpenPriceInfoFullSyncRequest.SkuPriceRequest request = new OpenPriceInfoFullSyncRequest.SkuPriceRequest();
        request.setStoreId("test57_1");
        request.setVehicleModelId("test_model_1");
        List<OpenPriceInfoFullSyncRequest.DailyPriceWeekdayRequest> dailyPriceWeekdayList = new ArrayList<>();
        OpenPriceInfoFullSyncRequest.DailyPriceWeekdayRequest dailyPriceWeekdayRequest = new OpenPriceInfoFullSyncRequest.DailyPriceWeekdayRequest();
        dailyPriceWeekdayRequest.setDayStart("1");
        dailyPriceWeekdayRequest.setDayEnd("1");
        dailyPriceWeekdayRequest.setDayStartName("周一");
        dailyPriceWeekdayRequest.setDayEndName("周一");
        dailyPriceWeekdayRequest.setPrice(111);
        dailyPriceWeekdayRequest.setChannel(Collections.singletonList(2L));
        dailyPriceWeekdayList.add(dailyPriceWeekdayRequest);
        OpenPriceInfoFullSyncRequest.DailyPriceWeekdayRequest dailyPriceWeekdayRequest2 = new OpenPriceInfoFullSyncRequest.DailyPriceWeekdayRequest();
        dailyPriceWeekdayRequest2.setDayStart("2");
        dailyPriceWeekdayRequest2.setDayEnd("2");
        dailyPriceWeekdayRequest2.setDayStartName("周二");
        dailyPriceWeekdayRequest2.setDayEndName("周二");
        dailyPriceWeekdayRequest2.setPrice(222);
        dailyPriceWeekdayRequest2.setChannel(Collections.singletonList(2L));
        dailyPriceWeekdayList.add(dailyPriceWeekdayRequest2);
        OpenPriceInfoFullSyncRequest.DailyPriceWeekdayRequest dailyPriceWeekdayRequest3 = new OpenPriceInfoFullSyncRequest.DailyPriceWeekdayRequest();
        dailyPriceWeekdayRequest3.setDayStart("3");
        dailyPriceWeekdayRequest3.setDayEnd("3");
        dailyPriceWeekdayRequest3.setDayStartName("周三");
        dailyPriceWeekdayRequest3.setDayEndName("周三");
        dailyPriceWeekdayRequest3.setPrice(333);
        dailyPriceWeekdayRequest3.setChannel(Collections.singletonList(2L));
        dailyPriceWeekdayList.add(dailyPriceWeekdayRequest3);

        OpenPriceInfoFullSyncRequest.DailyPriceWeekdayRequest dailyPriceWeekdayRequest4 = new OpenPriceInfoFullSyncRequest.DailyPriceWeekdayRequest();
        dailyPriceWeekdayRequest4.setDayStart("4");
        dailyPriceWeekdayRequest4.setDayEnd("4");
        dailyPriceWeekdayRequest4.setDayStartName("周四");
        dailyPriceWeekdayRequest4.setDayEndName("周四");
        dailyPriceWeekdayRequest4.setPrice(444);
        dailyPriceWeekdayRequest4.setChannel(Collections.singletonList(2L));
        dailyPriceWeekdayList.add(dailyPriceWeekdayRequest4);
        OpenPriceInfoFullSyncRequest.DailyPriceWeekdayRequest dailyPriceWeekdayRequest5 = new OpenPriceInfoFullSyncRequest.DailyPriceWeekdayRequest();
        dailyPriceWeekdayRequest5.setDayStart("5");
        dailyPriceWeekdayRequest5.setDayEnd("5");
        dailyPriceWeekdayRequest5.setDayStartName("周五");
        dailyPriceWeekdayRequest5.setDayEndName("周五");
        dailyPriceWeekdayRequest5.setPrice(555);
        dailyPriceWeekdayRequest5.setChannel(Collections.singletonList(2L));
        dailyPriceWeekdayList.add(dailyPriceWeekdayRequest5);
        OpenPriceInfoFullSyncRequest.DailyPriceWeekdayRequest dailyPriceWeekdayRequest6 = new OpenPriceInfoFullSyncRequest.DailyPriceWeekdayRequest();
        dailyPriceWeekdayRequest6.setDayStart("6");
        dailyPriceWeekdayRequest6.setDayEnd("6");
        dailyPriceWeekdayRequest6.setDayStartName("周六");
        dailyPriceWeekdayRequest6.setDayEndName("周六");
        dailyPriceWeekdayRequest6.setPrice(666);
        dailyPriceWeekdayRequest6.setChannel(Collections.singletonList(2L));
        dailyPriceWeekdayList.add(dailyPriceWeekdayRequest6);
        OpenPriceInfoFullSyncRequest.DailyPriceWeekdayRequest dailyPriceWeekdayRequest7 = new OpenPriceInfoFullSyncRequest.DailyPriceWeekdayRequest();
        dailyPriceWeekdayRequest7.setDayStart("7");
        dailyPriceWeekdayRequest7.setDayEnd("7");
        dailyPriceWeekdayRequest7.setDayStartName("周日");
        dailyPriceWeekdayRequest7.setDayEndName("周日");
        dailyPriceWeekdayRequest7.setPrice(777);
        dailyPriceWeekdayRequest7.setChannel(Collections.singletonList(2L));
        dailyPriceWeekdayList.add(dailyPriceWeekdayRequest7);
        request.setDailyPriceWeekdayList(dailyPriceWeekdayList);

        //节假日
        List<OpenPriceInfoFullSyncRequest.DailyPriceHolidayRequest> dailyPriceHolidayList = new ArrayList<>();
        OpenPriceInfoFullSyncRequest.DailyPriceHolidayRequest holidayRequest = new OpenPriceInfoFullSyncRequest.DailyPriceHolidayRequest();
        holidayRequest.setHolidayCode(CtripHolidayTypeEnum.SPRING_FESTIVAL.getValue());
        holidayRequest.setHolidayName(CtripHolidayTypeEnum.SPRING_FESTIVAL.getName());
        holidayRequest.setYear("2024");
        holidayRequest.setDayStart("2024-02-09");
        holidayRequest.setDayEnd("2024-02-16");
        holidayRequest.setTimezone(ASIA_SHANGHAI);
        holidayRequest.setPrice(876500);
        holidayRequest.setChannel(Collections.singletonList(2L));
        holidayRequest.setLimitPrice(10000);
        dailyPriceHolidayList.add(holidayRequest);
        request.setDailyPriceHolidayList(dailyPriceHolidayList);

        //特殊日
        List<OpenPriceInfoFullSyncRequest.DailyPriceSpecialRequest> dailyPriceSpecialList = new ArrayList<>();
        OpenPriceInfoFullSyncRequest.DailyPriceSpecialRequest specialRequest = new OpenPriceInfoFullSyncRequest.DailyPriceSpecialRequest();
        specialRequest.setYear("2024");
        specialRequest.setDayStart("2024-11-22");
        specialRequest.setDayEnd("2024-11-25");
        specialRequest.setTimezone(ASIA_SHANGHAI);
        specialRequest.setPrice(87600);
        specialRequest.setChannel(Collections.singletonList(2L));
        specialRequest.setLimitPrice(10000);
        dailyPriceSpecialList.add(specialRequest);
        request.setDailyPriceSpecialList(dailyPriceSpecialList);

        //保险费用
        List<OpenPriceInfoFullSyncRequest.FeePriceRequest> insurancePriceList = new ArrayList<>();
        OpenPriceInfoFullSyncRequest.FeePriceRequest feePriceRequest = new OpenPriceInfoFullSyncRequest.FeePriceRequest();
        feePriceRequest.setFeeType(FeeTypeEnum.BASIC_SERVICE_FEE.name());
        feePriceRequest.setFeeCode(FeeTypeEnum.BASIC_SERVICE_FEE.getValue());
        feePriceRequest.setFeeName(FeeTypeEnum.BASIC_SERVICE_FEE.getName());
        feePriceRequest.setChannel(Collections.singletonList(2L));
        feePriceRequest.setPrice(6655);
        insurancePriceList.add(feePriceRequest);
        OpenPriceInfoFullSyncRequest.FeePriceRequest feePriceRequest2 = new OpenPriceInfoFullSyncRequest.FeePriceRequest();
        feePriceRequest2.setFeeType(FeeTypeEnum.PREMIUM_SERVICE_FEE.name());
        feePriceRequest2.setFeeCode(FeeTypeEnum.PREMIUM_SERVICE_FEE.getValue());
        feePriceRequest2.setFeeName(FeeTypeEnum.PREMIUM_SERVICE_FEE.getName());
        feePriceRequest2.setChannel(Collections.singletonList(2L));
        feePriceRequest2.setPrice(6677);
        insurancePriceList.add(feePriceRequest2);

        OpenPriceInfoFullSyncRequest.FeePriceRequest feePriceRequest3 = new OpenPriceInfoFullSyncRequest.FeePriceRequest();
        feePriceRequest3.setFeeType(FeeTypeEnum.ADVANCED_SERVICE_FEE.name());
        feePriceRequest3.setFeeCode(FeeTypeEnum.ADVANCED_SERVICE_FEE.getValue());
        feePriceRequest3.setFeeName(FeeTypeEnum.ADVANCED_SERVICE_FEE.getName());
        feePriceRequest3.setChannel(Collections.singletonList(2L));
        feePriceRequest3.setPrice(9888);
        insurancePriceList.add(feePriceRequest3);
        request.setInsurancePriceList(insurancePriceList);

        //附加产品费用
        List<OpenPriceInfoFullSyncRequest.FeePriceRequest> addProductPriceList = new ArrayList<>();
        OpenPriceInfoFullSyncRequest.FeePriceRequest addRequest = new OpenPriceInfoFullSyncRequest.FeePriceRequest();
        addRequest.setFeeType(FeeTypeEnum.CHILD_SEAT.name());
        addRequest.setFeeCode(FeeTypeEnum.CHILD_SEAT.getValue());
        addRequest.setFeeName(FeeTypeEnum.CHILD_SEAT.getName());
        addRequest.setChannel(Collections.singletonList(2L));
        addRequest.setPrice(19988);
        addProductPriceList.add(addRequest);
        OpenPriceInfoFullSyncRequest.FeePriceRequest addRequest2 = new OpenPriceInfoFullSyncRequest.FeePriceRequest();
        addRequest2.setFeeType(FeeTypeEnum.SERVICE_CHARGE.name());
        addRequest2.setFeeCode(FeeTypeEnum.SERVICE_CHARGE.getValue());
        addRequest2.setFeeName(FeeTypeEnum.SERVICE_CHARGE.getName());
        addRequest2.setChannel(Collections.singletonList(2L));
        addRequest2.setPrice(32288);
        addProductPriceList.add(addRequest2);
        request.setAddProductPriceList(addProductPriceList);

        //租车押金
        OpenPriceInfoFullSyncRequest.FeePriceRequest rentalDeposit = new OpenPriceInfoFullSyncRequest.FeePriceRequest();
        rentalDeposit.setFeeType(FeeTypeEnum.RENT_DEPOSIT.name());
        rentalDeposit.setFeeCode(FeeTypeEnum.RENT_DEPOSIT.getValue());
        rentalDeposit.setFeeName(FeeTypeEnum.RENT_DEPOSIT.getName());
        rentalDeposit.setPrice(39988);
        rentalDeposit.setChannel(Collections.singletonList(2L));
        request.setRentalDeposit(rentalDeposit);

        //租车押金
        OpenPriceInfoFullSyncRequest.FeePriceRequest illegalDeposit = new OpenPriceInfoFullSyncRequest.FeePriceRequest();
        illegalDeposit.setFeeType(FeeTypeEnum.VIOLATION_DEPOSIT.name());
        illegalDeposit.setFeeCode(FeeTypeEnum.VIOLATION_DEPOSIT.getValue());
        illegalDeposit.setFeeName(FeeTypeEnum.VIOLATION_DEPOSIT.getName());
        illegalDeposit.setPrice(39988);
        illegalDeposit.setChannel(Collections.singletonList(2L));
        request.setIllegalDeposit(illegalDeposit);
        skuPriceRequests.add(request);
        req.setSkuPriceRequests(skuPriceRequests);
        System.out.println(JSON.toJSONString(req));
    }

    @Test
    public void getVehicleModelFeeList() throws Exception{
        StandardFeeParam param = new StandardFeeParam();
        param.setMerchantId(43L);
        param.setStoreId(12L);
        param.setVehicleModelIds(Arrays.asList(112L));
        rentMainService.getVehicleModelFeeList(param);
    }
}