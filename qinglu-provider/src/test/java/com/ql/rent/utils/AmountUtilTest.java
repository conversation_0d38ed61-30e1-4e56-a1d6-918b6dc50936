package com.ql.rent.utils;

import com.ql.rent.util.AmountUtil;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class AmountUtilTest {

    @Test
    void amountToStr_withNullAmount_returnsNull() {
        assertNull(AmountUtil.amountToStr((Long) null));
    }

    @Test
    void amountToStr_withPositiveAmount_returnsFormattedString() {
        String result = AmountUtil.amountToStr(1000L);
        assertEquals("10.00", result);
    }

    @Test
    void amountToStr_withNegativeAmount_returnsFormattedStringWithNegativeSign() {
        String s = AmountUtil.amountToStr(-1000L);
        assertEquals("-10.00", s);
    }

    @Test
    void amountToStr_withAmountLessThan10_returnsFormattedString() {
        String result = AmountUtil.amountToStr(9L);
        assertEquals("0.09", result);
    }

    @Test
    void amountToStr_withAmountLessThan100_returnsFormattedString() {
        String s = AmountUtil.amountToStr(99L);
        assertEquals("0.99", s);
    }

    @Test
    void amountToStr_withAmountLessThan100_returnsFormattedStringWithNegativeSign() {
        String s = AmountUtil.amountToStr(-99L);
        assertEquals("-0.99", s);
    }
}

