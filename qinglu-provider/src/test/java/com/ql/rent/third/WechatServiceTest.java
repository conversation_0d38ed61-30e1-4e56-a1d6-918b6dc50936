package com.ql.rent.third;

import com.ql.rent.AbstractTest;
import com.ql.rent.service.third.IEnterpriseWechatService;
import org.junit.Test;

import javax.annotation.Resource;

public class WechatServiceTest extends AbstractTest {

    @Resource
    private IEnterpriseWechatService enterpriseWechatService;

    @Test
    public void send() {
        String c = "%s-%s已在商城购买了XX种类的硬件服务，客服同学请准备后续流程    购买数量：GPS设备+50台   金额：500元   收货地址：浙江省杭州市海创园  ";

        String a = "%s-%s已在商城购买了XX种类的硬件服务，客服同学请准备后续流程\n \n 购买数量：GPS设备+50台 \n 金额：500元 \n 收货地址：浙江省杭州市海创园\n\n";


        String content = "%s-%s已在商城购买了XX种类的硬件服务，客服同学请准备后续流程\n" +
                "\n" +
                "购买数量：GPS设备+50台 \n" +
                "金额：500元 \n" +
                "收货地址：浙江省杭州市海创园\n" +
                "\n";
        String format = String.format(content, 1, "测试公司", "GPS设备", "50台");
        enterpriseWechatService.sendGroupMsg("0691ff76-f6cd-4095-9018-ecc4b3c4bd96", format);
    }

}
