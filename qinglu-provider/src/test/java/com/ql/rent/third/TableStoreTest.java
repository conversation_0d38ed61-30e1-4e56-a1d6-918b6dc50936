package com.ql.rent.third;

import com.alibaba.fastjson.JSON;
import com.alicloud.openservices.tablestore.TimeseriesClient;
import com.alicloud.openservices.tablestore.model.Row;
import com.ql.rent.AbstractTest;
import com.ql.rent.param.vehicle.DeviceQuery;
import com.ql.rent.service.tablestore.IOtsService;
import com.ql.rent.service.vehicle.IVehicleDeviceService;
import com.ql.rent.third.dto.VehicleDeviceStateDTO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

public class TableStoreTest extends AbstractTest {

    @Resource
    private IOtsService otsService;

    @Resource
    private IVehicleDeviceService iVehicleDeviceService;

    @Test
    public void testConnect(){
        final String endPoint = "https://qinglu-dev.cn-shanghai.ots.aliyuncs.com";
        String accessKeyId = "LTAI5tKGLoLTPck1C5Uto2aB"; // System.getenv("");
        String accessKeySecret = "******************************"; // System.getenv("");
        final String instanceName = "qinglu-dev";
        TimeseriesClient client = new TimeseriesClient(endPoint, accessKeyId, accessKeySecret, instanceName);

    }

    @Test
    public void deleteStore(){
        otsService.deleteTableStore();
    }

    @Test
    public void createVehicleStateTable(){
        otsService.createVehicleStateTable("device_state_data");
    }

    @Test
    public void getVehicleState(){
        Row vehicleState = otsService.getVehicleState("250012544");

    }

    @Test
    public void vehicleState(){
/*
        DeviceQuery deviceQuery = new DeviceQuery();
        deviceQuery.setSn("640030443");
        VehicleDeviceStateDTO dto = iVehicleDeviceService.vehicleState(deviceQuery);
        System.out.println(JSON.toJSON(dto));*/
    }

}
