package com.ql.rent.third;

import com.alibaba.fastjson.JSON;
import com.ql.rent.dao.trade.OrderInfoMapper;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.param.trade.OrderSettlementParam;
import com.ql.rent.service.trade.IOrderReconciliationService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.UuidUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.trade.OrderReconciliationVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
@Ignore
public class OrderReconciliationServiceTest {

    @Resource
    private IOrderReconciliationService orderReconciliationService;
    @Resource
    private OrderInfoMapper orderInfoMapper;

    @Test
    public void testBatchCreateReconciliation() {
//        OrderInfoExample orderInfoExample = new OrderInfoExample();
//        orderInfoExample.createCriteria().andMerchantIdEqualTo(1L)
//            .andOrderStatusIn(Arrays.asList(OrderStatusEnum.RETURNED.getStatus(), OrderStatusEnum.CANCELLED.getStatus()));
//        List<OrderInfo> orderInfos = orderInfoMapper.selectByExample(orderInfoExample);
//        for (OrderInfo orderInfo : orderInfos) {
//            orderReconciliationService.createReconciliation(orderInfo.getId());
//        }
        orderReconciliationService.createReconciliation(469702L);
    }

    @Test
    public void testSettleReconciliation() {
        OrderSettlementParam orderSettlementParam = new OrderSettlementParam();
//        orderSettlementParam.setSettlementAmount(200L);
//        orderSettlementParam.setSettlementAccount("settlementAccount");
        orderSettlementParam.setPaySourceType(1);
        orderSettlementParam.setRemark("备注111");
        orderSettlementParam.setOutSettlementNo(UuidUtil.getUUID());

        LoginVo loginVo = new LoginVo();
        loginVo.setUserId(1L);
        loginVo.setMerchantId(45L);

        OrderReconciliationVO reconciliation = new OrderReconciliationVO();
        reconciliation.setId(1L);
        orderSettlementParam.setReconciliation(reconciliation);
        Result<Integer> result = orderReconciliationService.settleReconciliation(orderSettlementParam, YesOrNoEnum.NO.getValue(), loginVo);
        log.info("result: {}", JSON.toJSONString(result));
        Assert.assertTrue(ResultUtil.isResultSuccess(result));
    }
}
