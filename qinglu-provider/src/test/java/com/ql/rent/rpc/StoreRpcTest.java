package com.ql.rent.rpc;

import com.ql.dto.ApiResultResp;
import com.ql.dto.store.*;
import com.ql.enums.open.ResultCodeEnum;
import com.ql.rent.service.store.IStoreInfoService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.store.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class StoreRpcTest {

    @InjectMocks
    private StoreRpc storeRpc;

    @Mock
    private IStoreInfoService storeInfoService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getStoreInfo_Success() {
        // 准备测试数据
        Long merchantId = 1L;
        Long storeId = 2L;
        Long channelId = 3L;

        StoreInfoVo mockVo = new StoreInfoVo();
        mockVo.setStoreId(storeId);
        mockVo.setMerchantId(merchantId);
        mockVo.setChannelId(channelId);
        mockVo.setStoreName("测试门店");
        
        // 设置经纬度
        LongLatVo longLatVo = new LongLatVo();
        longLatVo.setLatitude(31.2304);
        longLatVo.setLongitude(121.4737);
        mockVo.setLongLat(longLatVo);

        // 设置营业时间
        BusinessTimeV2Vo businessTimeV2Vo = new BusinessTimeV2Vo();
        businessTimeV2Vo.setBusinessPeriod("WORKDAY");
        mockVo.setBusinessTimeV2List(Collections.singletonList(businessTimeV2Vo));

        // 设置自助服务
        mockVo.setSelfServiceReturn(true);
        mockVo.setSelfServiceDistance(1000);

        // 设置联系人
        StoreContactVo contactVo = new StoreContactVo();
        contactVo.setLinkName("测试联系人");
        mockVo.setContactList(Collections.singletonList(contactVo));

        // 使用doReturn替代when
        doReturn(ResultUtil.successResult(mockVo))
            .when(storeInfoService)
            .storeInfoByStoreId(merchantId, storeId, channelId);

        // 执行测试
        ApiResultResp<StoreInfoDTO> result = storeRpc.getStoreInfo(merchantId, storeId, channelId);

        // 验证结果
        assertEquals(ApiResultResp.SUCCESS_CODE, result.getCode());
        assertNotNull(result.getData());
        StoreInfoDTO storeInfo = result.getData();
        
        assertEquals(storeId, storeInfo.getStoreId());
        assertEquals(merchantId, storeInfo.getMerchantId());
        assertEquals(channelId, storeInfo.getChannelId());
        assertEquals("测试门店", storeInfo.getStoreName());
        
        // 验证经纬度
        assertNotNull(storeInfo.getLongLat());
        assertEquals(31.2304, storeInfo.getLongLat().getLatitude());
        assertEquals(121.4737, storeInfo.getLongLat().getLongitude());
        
        // 验证营业时间
        assertNotNull(storeInfo.getBusinessTimeV2List());
        assertEquals(1, storeInfo.getBusinessTimeV2List().size());
        assertEquals("WORKDAY", storeInfo.getBusinessTimeV2List().get(0).getBusinessPeriod());
        
        // 验证自助服务
        assertTrue(storeInfo.getSelfServiceReturn());
        assertEquals(1000, storeInfo.getSelfServiceDistance());
        
        // 验证联系人
        assertNotNull(storeInfo.getContactList());
        assertEquals(1, storeInfo.getContactList().size());
        assertEquals("测试联系人", storeInfo.getContactList().get(0).getLinkName());
        
        // 验证服务调用
        verify(storeInfoService).storeInfoByStoreId(merchantId, storeId, channelId);
    }

    @Test
    void getStoreInfo_WhenServiceFail() {
        // 准备测试数据
        Long merchantId = 1L;
        Long storeId = 2L;
        Long channelId = 3L;

        doReturn(ResultUtil.failResult("查询失败"))
            .when(storeInfoService)
            .storeInfoByStoreId(merchantId, storeId, channelId);

        // 执行测试
        ApiResultResp<StoreInfoDTO> result = storeRpc.getStoreInfo(merchantId, storeId, channelId);

        // 验证结果
        assertNotEquals(ApiResultResp.SUCCESS_CODE, result.getCode());
        assertEquals(ResultCodeEnum.StoreResultCodeEnum.open_store_002.getCode(), result.getCode());
        
        // 验证服务调用
        verify(storeInfoService).storeInfoByStoreId(merchantId, storeId, channelId);
    }

    @Test
    void getStoreInfo_WhenModelIsNull() {
        // 准备测试数据
        Long merchantId = 1L;
        Long storeId = 2L;
        Long channelId = 3L;

        doReturn(ResultUtil.successResult(null))
            .when(storeInfoService)
            .storeInfoByStoreId(merchantId, storeId, channelId);

        // 执行测试
        ApiResultResp<StoreInfoDTO> result = storeRpc.getStoreInfo(merchantId, storeId, channelId);

        // 验证结果
        assertNotEquals(ApiResultResp.SUCCESS_CODE, result.getCode());
        assertEquals(ResultCodeEnum.StoreResultCodeEnum.open_store_002.getCode(), result.getCode());
        
        // 验证服务调用
        verify(storeInfoService).storeInfoByStoreId(merchantId, storeId, channelId);
    }

    @Test
    void getStoreInfo_WhenListFieldsAreNull() {
        // 准备测试数据
        Long merchantId = 1L;
        Long storeId = 2L;
        Long channelId = 3L;

        StoreInfoVo mockVo = new StoreInfoVo();
        mockVo.setStoreId(storeId);
        mockVo.setMerchantId(merchantId);
        mockVo.setChannelId(channelId);

        doReturn(ResultUtil.successResult(mockVo))
            .when(storeInfoService)
            .storeInfoByStoreId(merchantId, storeId, channelId);

        // 执行测试
        ApiResultResp<StoreInfoDTO> result = storeRpc.getStoreInfo(merchantId, storeId, channelId);

        // 验证结果
        assertEquals(ApiResultResp.SUCCESS_CODE, result.getCode());
        assertNotNull(result.getData());
        StoreInfoDTO storeInfo = result.getData();
        
        // 验证所有List字段为null
        assertNull(storeInfo.getBusinessTimeV2List());
        assertNull(storeInfo.getRestTimeV2List());
        assertNull(storeInfo.getGuidePickupList());
        assertNull(storeInfo.getGuideReturnList());
        assertNull(storeInfo.getContactList());
        assertNull(storeInfo.getHourlyChargeList());
        
        // 验证服务调用
        verify(storeInfoService).storeInfoByStoreId(merchantId, storeId, channelId);
    }

    @Test
    void testSaveStore() {
        // Mock数据
        StoreInfoDTO storeInfoDTO = new StoreInfoDTO();
        storeInfoDTO.setStoreId(1L);
        storeInfoDTO.setStoreName("测试门店");
        // 设置其他必要属性
        LongLatDTO longLatDTO = new LongLatDTO();
        longLatDTO.setLatitude(31.2304);
        longLatDTO.setLongitude(121.4737);
        storeInfoDTO.setLongLat(longLatDTO);

        // Mock服务返回
        Result<Boolean> mockResult = mock(Result.class);
        when(mockResult.isSuccess()).thenReturn(true);
        when(mockResult.getModel()).thenReturn(true);
        when(storeInfoService.storeInfoSave(any(), any())).thenReturn(mockResult);

        // 调用接口
        ApiResultResp<StoreSaveResultDTO> response = storeRpc.saveStore(storeInfoDTO);

        // 断言结果
        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals(1L, response.getData().getStoreId());
        assertEquals("0", response.getCode()); // 假设0表示成功
    }

    @Test
    void testSaveStore_WhenServiceFails() {
        // Mock数据
        StoreInfoDTO storeInfoDTO = new StoreInfoDTO();
        storeInfoDTO.setStoreId(1L);
        storeInfoDTO.setStoreName("测试门店");

        // Mock服务返回
        Result<Boolean> mockResult = mock(Result.class);
        when(mockResult.isSuccess()).thenReturn(false);
        when(mockResult.getMessage()).thenReturn("保存失败");
        when(storeInfoService.storeInfoSave(any(), any())).thenReturn(mockResult);

        // 调用接口
        ApiResultResp<StoreSaveResultDTO> response = storeRpc.saveStore(storeInfoDTO);

        // 断言结果
        assertNotNull(response);
        assertNull(response.getData());
        assertNotEquals(0, response.getCode());
    }

    @Test
    void testSaveStore_WhenModelIsNull() {
        // Mock数据
        StoreInfoDTO storeInfoDTO = new StoreInfoDTO();
        storeInfoDTO.setStoreId(1L);
        storeInfoDTO.setStoreName("测试门店");

        // Mock服务返回
        Result<Boolean> mockResult = mock(Result.class);
        when(mockResult.isSuccess()).thenReturn(true);
        when(mockResult.getModel()).thenReturn(false);
        when(storeInfoService.storeInfoSave(any(), any())).thenReturn(mockResult);

        // 调用接口
        ApiResultResp<StoreSaveResultDTO> response = storeRpc.saveStore(storeInfoDTO);

        // 断言结果
        assertNotNull(response);
        assertNull(response.getData());
        assertNotEquals(0, response.getCode());
    }
} 