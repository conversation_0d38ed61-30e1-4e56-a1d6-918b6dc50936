package com.ql.rent.store;

/**
 * <AUTHOR>
 */

import com.alibaba.fastjson.JSON;
import com.ql.dto.ResultResp;
import com.ql.dto.open.request.store.OpenAllopatryRuleSyncRequest;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.open.service.store.StoreInfoOpenServiceImpl;
import com.ql.rent.param.BaseQuery;
import com.ql.rent.param.store.ServicePolicyStoreParam;
import com.ql.rent.service.store.IServicePolicyService;
import com.ql.rent.service.store.IServicePolicyStoreService;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.store.ServicePolicyStoreListVO;
import com.ql.rent.api.aggregate.model.vo.store.ServicePolicyVO;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class IServicePolicyServiceTest {

    @Resource
    private IServicePolicyService servicePolicyService;
    @Resource
    private IServicePolicyStoreService servicePolicyStoreService;

    @Resource
    private StoreInfoOpenServiceImpl storeInfoOpenService;
    @Test
    public void signMd5HexTest() {
        // data (payload) | encryptionKey (appKey + appSecret + timestamp) = signature
        String genSign = DigestUtils.md5Hex("ecdf4c3bac224dcaa611c983a7a68654" + "f0c5dd500d02415ea2cfd7453a5b82db" + 1
                // data (payload) | encryptionKey (appKey + appSecret + timestamp) = signature
        );
        System.err.println(genSign);
        Assertions.assertEquals("c23b2ed66eedb321c5bcfb5e3724b978", genSign);
    }
    @Test
    public void testSaveServicePolicy() {
        ServicePolicyVO param = new ServicePolicyVO();
        param.setPolicyName("新增服务政策");
        param.setId(3L);
        param.setMerchantId(3L);
        param.setRentTimePolicy(this.buildRentTime());
        param.setCertificatPolicy(this.buildCertificat());
        param.setChargePolicy(this.buildCharge());
        param.setProhibitedArea(this.buildProhibitedArea());
        param.setRoadRescue(this.buildRoadRescue());
        param.setViolationHandle(this.buildViolationHandle());
        servicePolicyService.saveServicePolicy(param, 1L);
    }

    @Test
    public void testSavePolicyStore() {
        ServicePolicyStoreParam servicePolicyStoreParam = new ServicePolicyStoreParam();
        servicePolicyStoreParam.setPolicyId(3L);
        servicePolicyStoreParam.setStoreId(1L);
        servicePolicyStoreParam.setChannelId(2L);
        servicePolicyStoreService.savePolicyStore(servicePolicyStoreParam, 1L, 1L);
    }

    @Test
    public void testListServicePolicyStore() {
        Result<PageListVo<ServicePolicyStoreListVO>> pageListVoResult =
            servicePolicyStoreService.listServicePolicyStore(new BaseQuery(), 2L);
        System.out.println();
    }

    @Test
    public void testGetServicePolicy() {
        Result<ServicePolicyVO> servicePolicy = servicePolicyService.getServicePolicy(17L, 3L);
        ServicePolicyVO model = servicePolicy.getModel();
        System.out.println(JSON.toJSONString(model));
    }

    @Test
    public void listServicePolicy() {
        Result<List<ServicePolicyVO>> listResult = servicePolicyService.listServicePolicy(3L);
        System.out.println();
    }

    private ServicePolicyVO.RentTimeVO buildRentTime() {
        ServicePolicyVO.RentTimeVO rentTimePolicy = new ServicePolicyVO.RentTimeVO();
        rentTimePolicy.setAdvancePickupStatus(YesOrNoEnum.YES.getValue());
        rentTimePolicy.setMinAdvancePickupPeriod(25);
        rentTimePolicy.setHolidayAdvancePickupStatus(YesOrNoEnum.YES.getValue());
        rentTimePolicy.setHolidayMinAdvancePickupPeriod(24);
        rentTimePolicy.setAdvanceReturnStatus(YesOrNoEnum.YES.getValue());
        rentTimePolicy.setMinAdvanceReturnPeriod(24);
        rentTimePolicy.setHolidayAdvanceReturnStatus(YesOrNoEnum.YES.getValue());
        rentTimePolicy.setHolidayMinAdvanceReturnPeriod(2);
        rentTimePolicy.setMinAdvanceRerentPeriod(2);
        rentTimePolicy.setHolidayMinAdvanceRerentPeriod(9);
        rentTimePolicy.setForceRerentCountType((byte)1);
        rentTimePolicy.setForceRerentFeeTypes(Arrays.asList(2, 3));
        rentTimePolicy.setPenaltyAmountPercent(20);
        return rentTimePolicy;
    }

    private ServicePolicyVO.CertificatVO buildCertificat() {
        ServicePolicyVO.CertificatVO certificat = new ServicePolicyVO.CertificatVO();
        certificat.setMinDriveLicenseValidPeriod(3);
        certificat.setDriveLicenseYearLimit(YesOrNoEnum.YES.getValue());
        certificat.setMinLicenseYear(20);
        certificat.setDriverAgeLimit(YesOrNoEnum.YES.getValue());
        certificat.setMinDriverAge(20);
        certificat.setMaxDriverAge(80);
        certificat.setCreditCardLimit(YesOrNoEnum.YES.getValue());
        certificat.setDepositTypes(Arrays.asList(1,3));
        certificat.setMinCreditCardValidPeriod(24);
        return certificat;
    }

    private ServicePolicyVO.ChargeVO buildCharge() {
        ServicePolicyVO.ChargeVO chargePolicy = new ServicePolicyVO.ChargeVO();
        chargePolicy.setFuelFeeType((byte)1);
        chargePolicy.setFuelFeeAmount(8000);
        chargePolicy.setChargeFeeType((byte)1);
        chargePolicy.setChargeFeeAmount(10000);
        return chargePolicy;
    }

    private ServicePolicyVO.ProhibitedAreaVO buildProhibitedArea() {
        ServicePolicyVO.ProhibitedAreaVO prohibitedAreaVO
            = new ServicePolicyVO.ProhibitedAreaVO();
        prohibitedAreaVO.setProhibitIntoAreaIdList(Arrays.asList(1L, 20L));
        prohibitedAreaVO.setProhibitOutAreaIdList(Arrays.asList(3L, 40L));
        prohibitedAreaVO.setRemark("禁行区域说明");
        return prohibitedAreaVO;
    }

    private ServicePolicyVO.RoadRescueVO buildRoadRescue() {
        ServicePolicyVO.RoadRescueVO rescue = new ServicePolicyVO.RoadRescueVO();
        rescue.setRescueAmount(30000);
        rescue.setExtraRescueAmount(400000);
        return rescue;
    }

    private ServicePolicyVO.ViolationHandleVO buildViolationHandle() {
        ServicePolicyVO.ViolationHandleVO violationHandle
            = new ServicePolicyVO.ViolationHandleVO();
        violationHandle.setMaxNoticeClientPeriod(30);
        violationHandle.setMaxClientHandlePeriod(60);
        violationHandle.setMaxMerchantAuditPeriod(3);
        violationHandle.setDrivingPermitUnitPrice(30000);
        violationHandle.setUnitPenalty(20000);
        violationHandle.setUnitPointPenalty(21000);
        violationHandle.setUnitPenaltyWhenDeductPoints(21000);
        violationHandle.setPenaltyForAllPoints(350000);
        violationHandle.setUnitPenaltyWhenDeductAllPoints(50000);
        return violationHandle;
    }

    @Test
    public void syncAllopatryRuleTest(){
        //{"vendorId":"30027","skuList":[{"storeId":114784,"storeCode":"70323","vvc":"1306_24131_pupai"}]}
//        String str =  "{\"vendorId\":\"30027\",\"skuList\":[{\"storeId\":114784,\"storeCode\":\"70323\",\"vvc\":\"1306_24131_pupai\"}]}";
//        OpenAllopatryRuleSyncRequest bean = JSONUtil.toBean(str, OpenAllopatryRuleSyncRequest.class);
//        List<AllopatryRuleRequest> list = JSONUtil.toList("[{\"storeId\":114784,\"storeCode\":\"70323\",\"vvc\":\"1306_24131_pupai\"}]", AllopatryRuleRequest.class);
//        bean.setMerchantId(43L);
////        bean.setAppKey("47d31a1af48747e1b6325b3b2511e2a1");
//        bean.setAllopatryRuleList(list);

        OpenAllopatryRuleSyncRequest bean =
                JSON.parseObject("{\"allopatryRuleList\":[{\"allopatryRuleId\":\"testall1\",\"channelId\":[\"2\",\"10\"],\"delFlg\":false,\"duration\":-1,\"enabled\":1,\"permanentTag\":1,\"pickUpId\":\"310100\",\"priceKm\":0,\"returnId\":\"310100\",\"ruleType\":2,\"saasPickUpId\":310100,\"saasReturnId\":310100,\"applyVehicleModelDTO\":{\"vehicleModelIdList\":[],\"selectAllModels\":true}}],\"merchantId\":57,\"operateType\":0}",
                         OpenAllopatryRuleSyncRequest.class);
        ResultResp<Boolean> booleanResultResp = null;
        try {
            booleanResultResp = storeInfoOpenService.syncAllopatryRule(bean);
        } catch (Exception e) {
            System.out.println(e);
        }
        System.out.println(booleanResultResp);
    }
}
