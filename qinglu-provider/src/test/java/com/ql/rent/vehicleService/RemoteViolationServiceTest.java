package com.ql.rent.vehicleService;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.ql.rent.AbstractTest;
import com.ql.rent.component.RemoteViolationService;
import com.ql.rent.remote.vehicle.vo.request.*;
import com.ql.rent.remote.vehicle.vo.response.*;
import com.ql.rent.service.common.OkHttpService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.utils.MD5Util;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

public class RemoteViolationServiceTest extends AbstractTest {
    @Resource
    private RemoteViolationService remoteViolationService;
    @Resource
    private OkHttpService okHttpService;

    String urlString = "https://dalu.app:88/test";
    String appId = "2023QLKJ";
    String secretKey = "378CFC76883DAEE6B908D6571E216C65";

    @Test
    public void getIllegalTest(){
//        String requestMethod = "/cxy/enterprise/ViolationQuery";
//        Long timespan = System.currentTimeMillis();
//
//        //请求参数，设置按键排序
//        Map<String, String> requestMap = new TreeMap<>(String::compareTo);
//        requestMap.put("carType", "02");
//        requestMap.put("carNumber", "粤BL61Q9");
//        //组装加密sign
//        List<String> list = new ArrayList();
//        for (Map.Entry<String, String> entry : requestMap.entrySet()) {
//            list.add(entry.getValue());
//        }
//        String sortedStr = StringUtils.join(list, "");
//        String sign = MD5Util.MD5(timespan.toString().concat(sortedStr).concat(secretKey));
//        //发送请求
//        String url = "%s%s?appid=%s&timespan=%s&sign=%s&version=3";
//        url = String.format(url, urlString, requestMethod, appId, timespan, sign);
//        String result = HttpRequest.post(url)
//                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
//                .body(JSON.toJSONString(requestMap))
//                .execute().body();
//        System.out.println(result);

        Long timespan = System.currentTimeMillis();
        //请求参数，设置按键排序
        Map<String, String> requestMap = new TreeMap<>(String::compareTo);
        requestMap.put("carType", "02");
        requestMap.put("carNumber", "粤BL61Q9");
        //组装加密sign
        List<String> list = new ArrayList<>();
        for (Map.Entry<String, String> entry : requestMap.entrySet()) {
            list.add(entry.getValue());
        }
        String sortedStr = StringUtils.join(list, "");
        String sign = MD5Util.MD5(timespan.toString().concat(sortedStr).concat(secretKey));
        // 构建URL参数
        HttpUrl.Builder urlBuilder = Objects.requireNonNull(HttpUrl.parse(urlString + "/cxy/enterprise/ViolationQuery")).newBuilder();
        urlBuilder.addQueryParameter("appid", appId);
        urlBuilder.addQueryParameter("timespan", String.valueOf(timespan));
        urlBuilder.addQueryParameter("sign", sign);
        urlBuilder.addQueryParameter("version", String.valueOf(3));
        // 构建JSON请求体
        RequestBody requestBody = RequestBody.create(JSON.toJSONString(requestMap), MediaType.parse("application/json; charset=utf-8"));
        // 构建POST请求
        Request request = new Request.Builder()
                .url(urlBuilder.build())
                .post(requestBody)
                .build();
        // 创建调用
        OkHttpClient okHttpClient = new OkHttpClient();
        Call call = okHttpClient.newCall(request);
        try {
            // 执行请求
            Response response = call.execute();
            // 处理响应
            if (response.body() != null) {
                System.out.println(response.body().string());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void getIllegalTest2(){
        Result<VehicleIllegalResponse> vehicleIllegal = remoteViolationService.getVehicleIllegal("湘AD10111", "52");
        System.out.println(JSON.toJSONString(vehicleIllegal));
    }

    @Test
    public void abortContract(){
        AbortContractReq abortContractReq = new AbortContractReq();
        abortContractReq.setOrderId("171628471828932968");
        Result<Boolean> vehicleIllegal = remoteViolationService.abortContract(abortContractReq);
        System.out.println(JSON.toJSONString(vehicleIllegal));
    }

    @Test
    public void contractList(){
        ContractListReq contractListReq = new ContractListReq();
        contractListReq.setAccount("**************");
        Date time = new Date();
        contractListReq.setBeginTime(DateUtil.parse("2025-08-31 11:29:00"));
        contractListReq.setEndTime(DateUtil.parse("2025-08-31 11:31:00"));
        Result<List<ContractListResponse>> listResult = remoteViolationService.contractList(contractListReq);
        System.out.println(JSON.toJSONString(listResult));
    }

    @Test
    public void contractDetail(){
        ContractDetailReq contractListReq = new ContractDetailReq();
        contractListReq.setOrderId("171627356633732708");
        Result<ContractDetailResponse> contractDetailResponseResult = remoteViolationService.contractDetail(contractListReq);
        System.out.println(JSON.toJSONString(contractDetailResponseResult));
    }

    @Test
    public void getLoginQrCode(){
        LoginQrCodeReq loginQrCodeReq = new LoginQrCodeReq();
        loginQrCodeReq.setUserName("**************");
        Result<LoginQrCodeResponse> loginQrCode = remoteViolationService.getLoginQrCode(loginQrCodeReq);
        System.out.println(JSON.toJSONString(loginQrCode));
    }

    @Test
    public void getAccountList(){
        GetAccountListReq getAccountListReq = new GetAccountListReq();
        getAccountListReq.setPartnerAccount("2023QLKJ");
        getAccountListReq.setUserNames(List.of("**************"));
        Result<GetAccountListResponse> loginQrCode = remoteViolationService.getAccountList(getAccountListReq);
//        System.out.println(JSON.toJSONString(loginQrCode));
    }
}
