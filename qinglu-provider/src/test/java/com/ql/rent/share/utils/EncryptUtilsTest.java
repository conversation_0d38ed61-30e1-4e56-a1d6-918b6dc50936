package com.ql.rent.share.utils;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(SpringExtension.class)
public class EncryptUtilsTest {

    @InjectMocks
    private EncryptUtils encryptUtils;

    private static final String TEST_AES_KEY = "Kj7mP9nX2vB4yQ8s"; // 16位测试密钥
    private static final String TEST_VALUE = "2DE530AC49EA4AD7C38CC5CDD496F4605D4895A2";

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(encryptUtils, "PAY_AES_KEY", TEST_AES_KEY);
    }

    @Test
    void testAesEncryptAndDecrypt() {
        // 测试加密
        String encrypted = encryptUtils.encrypt(TEST_VALUE);
        assertNotNull(encrypted);
        assertNotEquals(TEST_VALUE, encrypted);

        // 测试解密
        String decrypted = encryptUtils.decrypt(encrypted);
        assertEquals(TEST_VALUE, decrypted);
    }

    @Test
    void testEncryptWithNullOrEmpty() {
        assertNull(encryptUtils.encrypt(""));
        assertEquals("", encryptUtils.encrypt(""));
        assertEquals("  ", encryptUtils.encrypt("  "));
    }

    @Test
    void testDecryptWithNullOrEmpty() {
        assertNull(encryptUtils.decrypt("klDLShDGglNyaq6vNIcC5zqoNtfF1WErfhN+LuMXDtiHxcSkg6cXqJAf3KCuAfVv"));
        assertEquals("", encryptUtils.decrypt(""));
        assertEquals("  ", encryptUtils.decrypt("  "));
    }

    @Test
    void testMd5() {
        String value = "test123";
        String md5Value = EncryptUtils.md5(value);
        assertNotNull(md5Value);
        assertEquals(32, md5Value.length());
        // MD5 值应该始终相同
        assertEquals(md5Value, EncryptUtils.md5(value));
    }

    @Test
    void testMd5WithNullOrEmpty() {
        assertNull(EncryptUtils.md5(null));
        assertEquals("", EncryptUtils.md5(""));
        assertEquals("  ", EncryptUtils.md5("  "));
    }

    @Test
    void testSha256() {
        String value = "test123";
        String sha256Value = EncryptUtils.sha256(value);
        assertNotNull(sha256Value);
        assertEquals(64, sha256Value.length());
        // SHA256 值应该始终相同
        assertEquals(sha256Value, EncryptUtils.sha256(value));
    }

    @Test
    void testSha256WithNullOrEmpty() {
        assertNull(EncryptUtils.sha256(null));
        assertEquals("", EncryptUtils.sha256(""));
        assertEquals("  ", EncryptUtils.sha256("  "));
    }
} 