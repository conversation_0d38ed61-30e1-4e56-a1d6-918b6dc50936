package com.ql.rent.pay;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: booker
 * @create: 2023-09-09 22:06
 **/
@Slf4j
public class CityExcelListener extends AnalysisEventListener<CityParseData> {

    /**
     * 批处理阈值
     */
    private static final int BATCH_COUNT = 20000;

    List<String> stringList = new ArrayList<>(BATCH_COUNT);

    @Override
    public void invoke(CityParseData user, AnalysisContext analysisContext) {
        log.info("解析到一条数据:{}", JSON.toJSONString(user));
        long timeMillis = System.currentTimeMillis();
        String sql = "insert into city_code_mapping_info(channel,channel_city_code,sass_city_code,deleted,create_time,op_time) values("+2+"," + user.getCtripCityCode()+ ","+ user.getSassCityCode()+ ","+0+ ","+ timeMillis + ","+timeMillis +");";
        stringList.add(sql);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        saveData();
        log.info("所有数据解析完成！");
    }

    private void saveData(){
        log.info("存储数据库成功！");
        stringList.forEach(System.out::println);
    }
}
