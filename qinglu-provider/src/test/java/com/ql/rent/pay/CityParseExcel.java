package com.ql.rent.pay;

import com.alibaba.excel.EasyExcel;

/**
 * @description:
 * @author: booker
 * @create: 2023-09-09 22:00
 **/
public class CityParseExcel {
    public static void main(String[] args) {




        //实现写操作
        //1.设置写入文件夹地址和excel文件名称
        String filename = "D:\\test.xlsx";

        //2.调用easyexcel里的方法实现写操作
        //write方法的两个参数：第一个参数是：文件路径，第二个参数是：实体类的class
        EasyExcel.read(filename, CityParseData.class,new CityExcelListener()).sheet().doRead();
    }


}
