package com.ql.rent.pay;

import com.alibaba.fastjson.JSON;
import com.ql.rent.component.AliPayClient;
import com.ql.rent.component.WechatPayClient;
import com.ql.rent.param.trade.ApplyRefundParam;
import com.ql.rent.service.trade.IRechargeService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.utils.JsonUtil;
import com.ql.rent.share.utils.UuidUtil;
import com.ql.rent.util.AesUtil;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.AmountReq;
import com.wechat.pay.java.service.refund.model.CreateRequest;
import com.wechat.pay.java.service.refund.model.Refund;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class AliPayTest {

    @Resource
    private AliPayClient aliPayClient;

    @Resource
    private IRechargeService rechargeService;


    //支付下单
    @Test
    public  void tradePay(){
        int a=1;
        BigDecimal aa = BigDecimal.valueOf(a).divide(BigDecimal.valueOf(100));
        String outTradeNo = UuidUtil.getUUID().toString();
        BigDecimal totalAmount = BigDecimal.valueOf(0.01);
        Result result = aliPayClient.tradePay("158",aa);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public  void applyWechatPayRefund(){

        ApplyRefundParam param = new ApplyRefundParam();
        param.setMerchantId(1L);
        param.setId(10L);
        param.setPrice(1);
        Result result = rechargeService.applyWechatPayRefund(param);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public  void applyAliPayRefund(){

        ApplyRefundParam param = new ApplyRefundParam();
        param.setMerchantId(1L);
        param.setId(205L);
        param.setPrice(1);
        Result result = rechargeService.applyAliPayRefund(param);
        System.out.println(JSON.toJSONString(result));
    }



}
