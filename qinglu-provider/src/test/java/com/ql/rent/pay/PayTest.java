package com.ql.rent.pay;

import com.alibaba.fastjson.JSON;
import com.ql.rent.component.AliPayClient;
import com.ql.rent.component.WechatPayClient;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.utils.JsonUtil;
import com.ql.rent.share.utils.UuidUtil;
import com.ql.rent.util.AesUtil;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.AmountReq;
import com.wechat.pay.java.service.refund.model.CreateRequest;
import com.wechat.pay.java.service.refund.model.Refund;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class PayTest {

    @Resource
    private WechatPayClient wechatPayClient;

    @Resource
    private AliPayClient aliPayClient;


    //支付下单
    @Test
    public  void tradePay(){

        String outTradeNo = UuidUtil.getUUID().toString();
        Integer totalAmount = 1;
        Result result = wechatPayClient.tradePay(outTradeNo,totalAmount);
        System.out.println(JSON.toJSONString(result));
    }


    //支付回调
    @Test
    public  void callback() throws Exception {
//        String json = "{\"id\":\"c64a0f0a-60a8-5c16-afb2-501ee7d41b0b\",\"create_time\":\"2023-02-07T11:34:56+08:00\",\"resource_type\":\"encrypt-resource\",\"event_type\":\"TRANSACTION.SUCCESS\",\"summary\":\"????\",\"resource\":{\"original_type\":\"transaction\",\"algorithm\":\"AEAD_AES_256_GCM\",\"ciphertext\":\"lvqATb5V+91TzGfwIY0zAiRVeSR4REGPNWQSpnSwnvzI7IG47PFsXKxmxjquOCqLxS0AIUaS45GmB1mL9qhp4t7paANltI3tm/+VcvR4ZX33YGGRcfcCOWxdCT0hgA2D6j4CXFVGKLSUplMWZqaIzBBfQnXjR56JbBVKFk+VObLEmfvHt0puFDgz7Mefk5cGeo4SRlt/tLiFSKFwJx4/Fj3ysrrGE/Tgocpb03GktzMwt0d0bNuPrf1zQ8uT71xCFIJxt6LHN0+QNFhGnLb19QrbpC+Vi/+sRrg9xkKyuUXfbCk8rvWuvjvwTVw65TuKa+gfoNkXv1/ZY/dJFvnrdmwO5f1aHFJwyFmUxJ9rrK7W2WsKpnwoY5DumY39lOch5BR7spmTEz2pUtje8pBUCOHNokqmMQBl/Ec72so8BDO6T6yt+xpEVRP9wXsSTchIOH57/VENAGCGhpvrM9mB/ckCVUJ0Umf3VseDcL0Y9qEhVbAvvTJiBX9YGCse+4l22Ctre4dQX7kZn4s1zuDS7RtS043va6j+pdMLQGaTNYY6N3BVK41F4uaOEVv1YhIToQmQsE2TYJ1TznEeCswz4A==\",\"associated_data\":\"transaction\",\"nonce\":\"m7esV9xRIekb\"}}";

        String json = "{\"id\":\"f8b2e17b-21c1-517a-be2f-4ea24cc55211\",\"create_time\":\"2023-02-07T14:40:38+08:00\",\"resource_type\":\"encrypt-resource\",\"event_type\":\"REFUND.SUCCESS\",\"summary\":\"????\",\"resource\":{\"original_type\":\"refund\",\"algorithm\":\"AEAD_AES_256_GCM\",\"ciphertext\":\"60feLA2fb9VpRjs7IJNNByC3tWWOz6evx415L4adb10GiHb4qGPMqqVtxsMMZ81V/RpZncJ/BeD81u4Qn5zWRvumKjrRTBPULt8PmEh18IRascozcD5Yh5HUObB68UqzzzA01iUeOTg60OhWaU398ZvXic40RZPn0TSUtI+zTIkgguy4v08O1DXgyS/6UrF6W+2FdYJx79uW/myNslil9i9JzgD1o5nS99QIyAnqMQcYMWhmDXnaIj/xImvHZmQkf65efyAVhz1l36Em94Ov0Gz5QYQeoBOPAjVT13IfkndkxHIZoziQ6woiGbWQB9b6GSiJwQaWuthfoGKgHtKVS6hX5kZ8CfYIGy9oBEwM0ABZeP3fjs6R8MqmMTHWrHQ7kETef5Qs/bsMmhSEaGtvHV272IlULtBbX64hT28PKiBsvsWH9VY3E5ADTIrdwFqGLAzzVMYdffUZq2oX86a3VHkFUkKdZ7zP5i/K32hea8soOxznpGIkHOMJZsEVlQRRmdbXBagnXBQRubVMwwekkj8liMEn8IK0Iaw=\",\"associated_data\":\"refund\",\"nonce\":\"7b3sRuHTHONO\"}}";

        Map<String,Object> map = (Map<String, Object>) JsonUtil.jsonToBean(json,Map.class);
        Map<String,String> resourceMap = (Map<String,String>)map.get("resource");

        String associatedData = resourceMap.get("associated_data");
        String nonce = resourceMap.get("nonce");
        String ciphertext = resourceMap.get("ciphertext");


        AesUtil aesUtil = new AesUtil("ff992322f2f3ca05ee3a6bedfe6833b6".getBytes(StandardCharsets.UTF_8));
        String text = aesUtil.decryptToString(associatedData.getBytes(StandardCharsets.UTF_8),nonce.getBytes(StandardCharsets.UTF_8),ciphertext);
        System.out.println(text);
    }


    //支付退款
    @Test
    public  void tradeRefund(){
        Config config =
                new RSAAutoCertificateConfig.Builder()
                        .merchantId("1637115162")
                        .privateKeyFromPath("D:\\work\\WXCertUtil\\cert\\apiclient_key.pem")
                        .merchantSerialNumber("56365CB93A2286319A00C8815CA775BFE3CA6A94")
                        .apiV3Key("ff992322f2f3ca05ee3a6bedfe6833b6")
                        .build();
            RefundService service = new RefundService.Builder().config(config).build();
            CreateRequest request = new CreateRequest();
            AmountReq amount = new AmountReq();
            amount.setTotal(1L);
            amount.setRefund(1L);
            amount.setCurrency("CNY");
            request.setAmount(amount);
            request.setNotifyUrl("https://s2.qinglusaas-dev.com/api/wechatPay/v1/callback");
            request.setOutRefundNo("72c0e41b3c7946b9b62a4225519d75a8");
            request.setOutTradeNo("72c0e41b3c7946b9b62a4225519d75a7");

            Refund response = service.create(request);
            System.out.println(JSON.toJSONString(response));
    }


}
