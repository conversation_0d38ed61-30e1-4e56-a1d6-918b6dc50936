package com.ql.rent.service.trade;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JavaType;
import com.google.common.collect.Lists;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.request.PushCtripDiffRuleRequest;
import com.ql.rent.api.aggregate.model.request.ValidateCreateOrderReq;
import com.ql.rent.bizdata.service.IDiffRulePushDataService;
import com.ql.rent.enums.vehicle.VehicleBusyEnum;
import com.ql.rent.param.sync.CtripDiffRuleParam;
import com.ql.rent.service.common.IPushMsgService;
import com.ql.rent.service.store.ICtripStoreService;
import com.ql.rent.service.vehicle.IThirdVehicleService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.utils.JsonUtil;
import com.ql.rent.vo.store.AllopatryRuleVO;
import com.ql.rent.vo.trade.MarketVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @desc
 * @time 2023-03-01 20:19
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class OrderTest {

    @Resource
    private IVehiclePickReturnService vehiclePickReturnService;
    @Resource
    private IPushMsgService pushMsgService;

    @Resource
    private IMarketService iMarketService;
    @Resource
    private IThirdVehicleService thirdVehicleService;

    @Test
    public void testSendCloseService() {
//        vehiclePickReturnService.listOrderAvailableVehicle(2491L, 29L);
    }

    @Test
    public void testMarket(){
        Result<List<MarketVO>> listResult = iMarketService.marketList(1L, Lists.newArrayList(2L, 28L, 105L, 110L), 2L, new Date(), null, null);
        System.out.println(JSON.toJSON(listResult));
    }

    @Resource
    private ICtripStoreService iCtripStoreService;

    @Test
    public void testPushDiffRule(){

        String str = "{\"merchantId\":45,\"vos\":[{\"channelId\":[2],\"deleted\":0,\"duration\":12,\"effectiveEndTime\":-1,\"effectiveStartTime\":-1,\"enabled\":1,\"fixedPrice\":1200,\"id\":2301,\"permanentTag\":1,\"pickUpId\":354,\"priceKm\":-1,\"returnId\":900,\"ruleType\":1,\"vehicleModelId\":[0,0]}]}";
        CtripDiffRuleParam param = JSON.parseObject(str, CtripDiffRuleParam.class);
        JavaType collectionType = JsonUtil.getCollectionType(ArrayList.class, AllopatryRuleVO.class);
        CtripDiffRuleParam diffRuleParam = new CtripDiffRuleParam();
       /* try {
            diffRuleParam.setMerchantId(45L);
            diffRuleParam.setVos(JsonUtil.jsonToBean(params, collectionType));
        } catch (Exception e) {
            log.error("异门店推送error:{}", JSON.toJSONString(param));
        }*/
        PushCtripDiffRuleRequest request = iCtripStoreService.pushDiffStoreRule(param, 45L);
    }

    @Resource
    private IThirdOrderService iThirdOrderService;

    @Test
    public void checkOrderStock(){
        String str = "{\"channelId\":6,\"dropOffStore\":{\"cityId\":0,\"datetime\":\"2024-05-13 11:18:49\",\"storeId\":633},\"originReq\":{\"wk_cartype_id\":\"BUICKGL82022SUV_B45GASOLINEAT3000\",\"start_time\":\"2024-05-11 11:18:49\",\"end_time\":\"2024-05-13 11:18:49\",\"take_site_id\":\"3100001345\",\"return_site_id\":\"3100001345\",\"city_code\":\"310000\",\"user_name\":\"林晨\",\"moblie\":\"13166669901\",\"id_card_type\":0,\"id_card_no\":\"******************\",\"member_name\":\"林晨\",\"member_moblie\":\"13166669901\",\"member_id_card_no\":\"******************\"},\"partnerUser\":{\"idNo\":\"******************\",\"idType\":1,\"mobile\":\"13166669901\",\"name\":\"林晨\",\"pickUpMobile\":\"13166669901\",\"pickUpName\":\"林晨\"},\"payMode\":1,\"pickUpStore\":{\"cityId\":0,\"datetime\":\"2024-05-11 11:18:49\",\"storeId\":633},\"selfPickupReturnOrder\":false,\"vehicleModelId\":3737}";
        ValidateCreateOrderReq validateCreateOrderReq = JSON.parseObject(str, ValidateCreateOrderReq.class);
        String license = iThirdOrderService.checkOrderStock(2L, 58L, validateCreateOrderReq, VehicleBusyEnum.ORDER.getValue());
    }

    @Test
    public void TestSaveVehicleModelLimitPriceUpdate(){
        String str = "{\"updateTime\":\"2024-10-10 04:00:00\",\"updateType\":3,\"vendorId\":15000558,\"updateDetail\":[{\"storeId\":162252,\"storeCode\":\"162252\",\"skuId\":44484224,\"vvc\":\"11532_4449_pupai\",\"priceDetail\":[{\"feeCode\":\"2001\",\"beforePrice\":80.0,\"afterPrice\":85.0,\"channel\":\"ctrip_channel\",\"beforePriceLimit\":{\"priceFloor\":80,\"priceCeiling\":90},\"afterPriceLimit\":{\"priceFloor\":85,\"priceCeiling\":90}}]}]}";

        str = "{\"updateTime\":\"2024-10-15 15:09:09\",\"updateType\":1,\"vendorId\":37573,\"updateDetail\":[{\"storeId\":1167713,\"storeCode\":\"\",\"skuId\":64094437,\"vvc\":\"1433_4844_pupai\",\"priceDetail\":[{\"feeCode\":\"1002\",\"beforePrice\":35.00,\"afterPrice\":50,\"channel\":\"ctrip_channel\",\"beforePriceLimit\":{\"priceFloor\":30,\"priceCeiling\":80},\"afterPriceLimit\":{\"priceFloor\":50,\"priceCeiling\":100}},{\"feeCode\":\"1002\",\"beforePrice\":35.00,\"afterPrice\":60,\"channel\":\"others_channel\",\"beforePriceLimit\":{\"priceFloor\":0,\"priceCeiling\":200},\"afterPriceLimit\":{\"priceFloor\":60,\"priceCeiling\":110}}]},{\"storeId\":106890,\"storeCode\":\"\",\"skuId\":11898062,\"vvc\":\"1433_4844_pupai\",\"priceDetail\":[{\"feeCode\":\"1002\",\"beforePrice\":35.00,\"afterPrice\":50,\"channel\":\"ctrip_channel\",\"beforePriceLimit\":{\"priceFloor\":30,\"priceCeiling\":80},\"afterPriceLimit\":{\"priceFloor\":50,\"priceCeiling\":100}},{\"feeCode\":\"1002\",\"beforePrice\":35.00,\"afterPrice\":60,\"channel\":\"others_channel\",\"beforePriceLimit\":{\"priceFloor\":0,\"priceCeiling\":200},\"afterPriceLimit\":{\"priceFloor\":60,\"priceCeiling\":110}}]},{\"storeId\":106896,\"storeCode\":\"74399\",\"skuId\":10924698,\"vvc\":\"1433_4844_pupai\",\"priceDetail\":[{\"feeCode\":\"1002\",\"beforePrice\":40.00,\"afterPrice\":50,\"channel\":\"ctrip_channel\",\"beforePriceLimit\":{\"priceFloor\":30,\"priceCeiling\":80},\"afterPriceLimit\":{\"priceFloor\":50,\"priceCeiling\":100}},{\"feeCode\":\"1002\",\"beforePrice\":40.00,\"afterPrice\":60,\"channel\":\"others_channel\",\"beforePriceLimit\":{\"priceFloor\":0,\"priceCeiling\":200},\"afterPriceLimit\":{\"priceFloor\":60,\"priceCeiling\":110}}]}]}";
        thirdVehicleService.saveVehicleModelLimitPriceUpdate(str);
        System.out.println();
    }



}
