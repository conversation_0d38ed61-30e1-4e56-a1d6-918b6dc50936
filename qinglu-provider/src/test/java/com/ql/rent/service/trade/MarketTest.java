package com.ql.rent.service.trade;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.databind.JavaType;
import com.ql.rent.AbstractTest;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.utils.JsonUtil;
import com.ql.rent.vo.trade.MarketCostVO;
import com.ql.rent.vo.trade.MarketVO;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

public class MarketTest extends AbstractTest {

    @Resource
    private IMarketService marketService;

    @Test
    public void test(){
        String str = "[{\"channelId\":7,\"costParty\":2,\"costPartyType\":0,\"dimension\":2,\"discount\":100,\"discountMode\":2,\"discountSubMode\":2,\"id\":630,\"marketCode\":\"DM630\",\"marketName\":\"无门槛减40元\",\"marketStoreModelGroup\":{\"storeId\":25,\"vehicleModelIds\":[2173]},\"marketType\":1,\"maxLeaseTerm\":0,\"maxMoney\":-1,\"merchantCostPartyValue\":0,\"merchantId\":29,\"minLeaseTerm\":0,\"pickEnd\":1719503999000,\"pickStart\":1717430400000,\"placeOrderEnd\":1719503999000,\"placeOrderStart\":1717430400000,\"platformCostPartyValue\":0,\"returnEnd\":1719503999000,\"returnStart\":1717430400000,\"satisfy\":1,\"status\":1},{\"channelId\":7,\"costParty\":2,\"costPartyType\":0,\"dimension\":2,\"discount\":200,\"discountMode\":1,\"discountSubMode\":0,\"id\":631,\"marketCode\":\"DM631\",\"marketName\":\"无门槛优惠力度最高\",\"marketStoreModelGroup\":{\"storeId\":25,\"vehicleModelIds\":[2173]},\"marketType\":1,\"maxLeaseTerm\":0,\"maxMoney\":-1,\"merchantCostPartyValue\":0,\"merchantId\":29,\"minLeaseTerm\":0,\"pickEnd\":1719763199000,\"pickStart\":1717430400000,\"placeOrderEnd\":1719763199000,\"placeOrderStart\":1717430400000,\"platformCostPartyValue\":0,\"returnEnd\":1719763199000,\"returnStart\":1717430400000,\"satisfy\":100,\"status\":1}]";

        JavaType collectionType = JsonUtil.getCollectionType(ArrayList.class, MarketVO.class);

        try {
            List<MarketVO> marketList = JsonUtil.jsonToBean(str, collectionType);
            Result<MarketCostVO> marketCostResult = marketService.calculateOptimumMarket(2173L, 0.0, 300, 100,
                    100, marketList);
            MarketCostVO model = marketCostResult.getModel();
            System.out.println(JSON.toJSONString(model));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
