package com.ql.rent.service.trade;

import com.ql.rent.entity.trade.MerchantAccount;
import com.ql.enums.SmsTemplateEnum;
import com.ql.rent.enums.WxMpTemplateEnum;
import com.ql.rent.enums.trade.MerchantFollowingEnum;
import com.ql.rent.param.trade.UpdMerchantAmountParam;
import com.ql.rent.provider.trade.MerchantAccountServiceImpl;
import com.ql.rent.service.common.IPushMsgService;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.vo.common.PushVO;
import com.ql.rent.vo.login.WxMsgVo;
import com.ql.rent.vo.merchant.MerchantInfoVo;
import com.ql.rent.vo.merchant.SysUserVo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @desc
 * @time 2023-03-01 20:19
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class IMerchantAccountServiceTest {

    @Resource
    private MerchantAccountServiceImpl merchantAccountService;
    @Resource
    private IPushMsgService pushMsgService;

    @Test
    public void testUpdateAmount() {
        UpdMerchantAmountParam updMerchantAmountParam = UpdMerchantAmountParam.builder()
            .merchantId(11L)
            .amount(Long.MIN_VALUE)
            .busiId(System.currentTimeMillis())
            .scene(MerchantFollowingEnum.ORDER_BILL.getScene().intValue())
            .opUserId(-1L).build();
        merchantAccountService.updateAmount(updMerchantAmountParam);
    }

    @Test
    public void testSendCloseService() {
        MerchantInfoVo merchantInfo = new MerchantInfoVo();
        merchantInfo.setId(0L);
        merchantInfo.setName("商家测试");
        SysUserVo sysUserVo = new SysUserVo();
        sysUserVo.setId(3L);
        MerchantAccount account = new MerchantAccount();
        account.setLastArrearsTime(0L);
//        merchantAccountService.noticeForAmountLessZero(merchantInfo, sysUserVo, account);

        PushVO pushVo = new PushVO();
        pushVo.setUserIds(Collections.singletonList(sysUserVo.getId()));
        // 服务关停时间未到，短信、微信 提醒内容：您的账号{%s}额度已用完，由于您的信用良好，已为您将服务延长至{%s}，请尽快登陆PC端完成充值，否则将无法正常接收订单

        //
        // 短信参数
        Map<String, Object> smsPushObj = new HashMap<>(3);
        smsPushObj.put("account", merchantInfo.getName());
        pushVo.setSmsPushObj(smsPushObj);
        pushVo.setSmsTemplate(SmsTemplateEnum.MERCHANT_ACCOUNT_SERVICE_CLOSE);
        // 公众号
        Map<String, WxMsgVo.Template> data = new HashMap<>();
        data.put("first", new WxMsgVo.Template(
            String.format("您的账号{%s}额度已用完，已被关停服务", merchantInfo.getName())));
        data.put("keyword1", new WxMsgVo.Template(merchantInfo.getName()));
        data.put("remark", new WxMsgVo.Template("点击此处立即充值。"));
        pushVo.setMpTemplate(WxMpTemplateEnum.MERCHANT_SERVICE_CLOSE.getTemplateId());
        pushVo.setMpPushObj(data);
        pushMsgService.push(pushVo);
    }

    @Test
    public void testSendUnder0() {
        MerchantInfoVo merchantInfo = new MerchantInfoVo();
        merchantInfo.setId(0L);
        merchantInfo.setName("商家测试");
        SysUserVo sysUserVo = new SysUserVo();
        sysUserVo.setId(3L);
        MerchantAccount account = new MerchantAccount();
        account.setLastArrearsTime(0L);

        PushVO pushVo = new PushVO();
        pushVo.setUserIds(Collections.singletonList(sysUserVo.getId()));
        String closeTimeStr = DateUtil.getFormatDateStr(new Date(), DateUtil.yyyyMMddHHmmss);
        // 服务关停时间未到，短信、微信 提醒内容：您的账号{%s}额度已用完，由于您的信用良好，已为您将服务延长至{%s}，请尽快登陆PC端完成充值，否则将无法正常接收订单

        // 短信参数
        Map<String, Object> smsPushObj = new HashMap<>(3);
        smsPushObj.put("account", merchantInfo.getName());
        smsPushObj.put("time", closeTimeStr);
        pushVo.setSmsPushObj(smsPushObj);
        pushVo.setSmsTemplate(SmsTemplateEnum.MERCHANT_ACCOUNT_ARREARS);
        // 公众号
        Map<String, WxMsgVo.Template> data = new HashMap<>();
        data.put("first", new WxMsgVo.Template(
            String.format("您的账号{%s}额度已用完，由于您的信用良好，已为您将服务延长至{%s}",
                merchantInfo.getName(), closeTimeStr)));
        data.put("keyword1", new WxMsgVo.Template(merchantInfo.getName()));
        data.put("keyword2", new WxMsgVo.Template(closeTimeStr));
        data.put("remark", new WxMsgVo.Template("请尽快完成充值，否则将无法正常接收订单"));
        pushVo.setMpTemplate(WxMpTemplateEnum.MERCHANT_ACCOUNT_LESS_THAN_ZERO.getTemplateId());
        pushVo.setMpPushObj(data);
        pushMsgService.push(pushVo);
    }

    @Test
    public void testSendUnder200() {
        MerchantInfoVo merchantInfo = new MerchantInfoVo();
        merchantInfo.setId(0L);
        merchantInfo.setName("商家测试");
        SysUserVo sysUserVo = new SysUserVo();
        sysUserVo.setId(3L);
        MerchantAccount account = new MerchantAccount();
        account.setLastArrearsTime(0L);

        PushVO pushVo = new PushVO();
        pushVo.setUserIds(Collections.singletonList(sysUserVo.getId()));

        // 短信参数
        Map<String, Object> smsPushObj = new HashMap<>(3);
        smsPushObj.put("account", merchantInfo.getName());
        smsPushObj.put("amount", "200");
        pushVo.setSmsPushObj(smsPushObj);
        pushVo.setSmsTemplate(SmsTemplateEnum.MERCHANT_ACCOUNT_LESS);
        // 公众号
        Map<String, WxMsgVo.Template> data = new HashMap<>();
        data.put("first", new WxMsgVo.Template(String.format("您的账号{%s}额度已不足200元", merchantInfo.getName())));
        data.put("keyword1", new WxMsgVo.Template(merchantInfo.getName()));
        data.put("keyword2", new WxMsgVo.Template("200"));
        data.put("remark", new WxMsgVo.Template("请尽快完成充值，否则将无法正常接收订单"));
        pushVo.setMpTemplate(WxMpTemplateEnum.MERCHANT_ACCOUNT_LESS_THAN.getTemplateId());
        pushVo.setMpPushObj(data);
        pushMsgService.push(pushVo);
    }
}
