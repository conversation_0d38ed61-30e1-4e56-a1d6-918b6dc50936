//package com.ql.rent.service.trade;
//
//import com.alibaba.fastjson.JSON;
//import com.ql.rent.param.trade.BillRefundAuditParam;
//import com.ql.rent.param.trade.BillRefundParam;
//import com.ql.rent.share.result.Result;
//import com.ql.rent.vo.LoginVo;
//import com.ql.rent.vo.trade.OrderBillRefundApplyVO;
//import org.junit.jupiter.api.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//import java.util.Arrays;
//
///**
// * <AUTHOR>
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest
//class IOrderBillRefundApplyServiceTest {
//
//    @Resource
//    private IOrderBillRefundApplyService orderBillRefundApplyService;
//
//    @Test
//    void applyRefund() {
//        BillRefundParam refundParam = new BillRefundParam();
//        refundParam.setBillId(2L);
//        refundParam.setRefundReason("扣多了，退款1");
//        refundParam.setProofUrlList(Arrays.asList("1.jpg", "2.png"));
//
//        LoginVo loginVo = new LoginVo();
//        loginVo.setMerchantId(1L);
//        loginVo.setUserId(1L);
//        orderBillRefundApplyService.applyRefund(refundParam, loginVo);
//    }
//
//    @Test
//    void getActiveApplyByBillId() {
//        Result<OrderBillRefundApplyVO> activeApplyByBillId = orderBillRefundApplyService.getActiveApplyByBillId(2L);
//        System.out.println(JSON.toJSONString(activeApplyByBillId));
//    }
//
//    @Test
//    void auditOrderBillRefundFailed() {
//        BillRefundAuditParam auditParam = new BillRefundAuditParam();
//        auditParam.setAuditStatus((byte)2);
//        auditParam.setRemark("并没有扣多了");
//        auditParam.setId(3L);
//        orderBillRefundApplyService.auditOrderBillRefund(auditParam, 1L);
//    }
//
//    @Test
//    void auditOrderBillRefundPassed() {
//        BillRefundAuditParam auditParam = new BillRefundAuditParam();
//        auditParam.setAuditStatus((byte)1);
//        auditParam.setActualAmount(2000L);
//        auditParam.setId(4L);
//        orderBillRefundApplyService.auditOrderBillRefund(auditParam, 1L);
//    }
//}