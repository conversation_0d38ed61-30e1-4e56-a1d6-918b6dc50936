//package com.ql.rent.service.trade;
//
//import com.alibaba.fastjson.JSON;
//import com.ql.rent.param.trade.DriverDashboardParam;
//import com.ql.rent.share.result.Result;
//import com.ql.rent.vo.trade.DriverDashboardVO;
//import org.junit.jupiter.api.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//
//import java.util.List;
//
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//class IVehiclePickDriverServiceTest {
//
//    @Resource
//    private IVehiclePickDriverService vehiclePickDriverService;
//
//    @Test
//    void listDriverDashboard() {
//        DriverDashboardParam param = new DriverDashboardParam();
//        param.setStoreId(3L);
//        param.setEndTime(System.currentTimeMillis());
//        param.setStartTime(1672559223000L);
//        param.setMerchantId(2L);
//        Result<List<DriverDashboardVO>> listResult = vehiclePickDriverService.listDriverDashboard(param);
//        System.out.println(JSON.toJSONString(listResult.getModel()));
//    }
//
//    @Test
//    void listUnArrangeOrder() {
//    }
//}