package com.ql.rent.service.merchant;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.ql.rent.dao.merchant.ConfigValueMapper;
import com.ql.rent.provider.merchant.ConfigValueServiceImpl;

@ExtendWith(MockitoExtension.class)
public class ConfigValueServiceTest {

    @Mock
    private ConfigValueMapper configValueMapper;

    @InjectMocks
    private ConfigValueServiceImpl configValueService;

    @Test
    public void testGetApiV3KeyBySerial() {
        // 准备测试数据
        String serial = "PUB_KEY_ID_0117065817042025021300326400001551";
        String expectedResult = "PUB_KEY_ID_0117065817042025021300326400001551";

        // 配置mock行为
        when(configValueService.getApiV3KeyBySerial(serial)).thenReturn(expectedResult);

        // 执行测试
        String actualResult = configValueService.getApiV3KeyBySerial(serial);

        // 验证结果
        assertEquals(expectedResult, actualResult, "API V3 Key应该匹配预期结果");
    }
} 