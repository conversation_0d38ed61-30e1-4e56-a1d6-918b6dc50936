## qinglu-provider 模块架构文档

### 1. 架构概述

- **系统定位**: `qinglu-provider` 为应用服务层，提供租车业务的核心服务能力与对外 REST 接口（控制器）、任务调度（Quartz）、WebSocket 实时推送、第三方平台集成（支付/短信/开放平台/地图/携程/滴滴等）。本模块依赖 `qinglu-client` 与 `qinglu-model` 提供的契约与数据模型，整合多数据源并通过 MyBatis 访问数据库。
- **构建与运行时**: Maven（JDK 11）。以可执行 JAR 方式构建，并使用 `maven-assembly-plugin` 打包出包含依赖与配置的 `tar.gz` 发行物。
- **关键依赖/技术栈（节选）**:
  - Web 与容器: Spring Boot（主类 `com.ql.rent.StartSpringBootApplication`），Servlet 组件扫描，OpenFeign，Spring WebSocket
  - 持久化: MyBatis + 多数据源（store/common/trade/vehicle/bill 及各从库），PageHelper 分页
  - 任务调度: Quartz（DB 持久化、集群）
  - 配置与环境: Profiles + 资源过滤（`src/main/filter/*.properties`），外置 `conf/` 配置
  - 可观测性: Micrometer + OTLP（Metrics），OpenTelemetry（日志上下文 & Trace 透传）
  - 缓存: Spring Data Redis（Lettuce）
  - 日志: Log4j2（OpenTelemetry Appender，RollingFile）
  - 其他: MapStruct、Hutool、Fastjson/fastjson2、OkHttp、JPush、Ali OCR/SMS、ZXing

```xml
<!-- 关键依赖（节选，自 qinglu-provider/pom.xml） -->
<dependency>com.ql:qinglu-client:1.0.0-SNAPSHOT</dependency>
<dependency>com.ql:qinglu-model:${qinglu.model.version}</dependency>
<dependency>com.ql:qinglu-rpc:${qinglu.rpc.version}</dependency>
<dependency>com.ql:qinglu-api:1.0.0-SNAPSHOT</dependency>
<dependency>com.ql:qinglu-api-aggregate:1.0.0-SNAPSHOT</dependency>
<dependency>com.ql:qinglu-open-provider:1.0.0-SNAPSHOT</dependency>
<dependency>org.mybatis.spring.boot:mybatis-spring-boot-starter</dependency>
<dependency>com.github.pagehelper:pagehelper-spring-boot-starter:1.3.0</dependency>
<dependency>org.springframework.cloud:spring-cloud-starter-openfeign</dependency>
<dependency>org.springframework.boot:spring-boot-starter-quartz</dependency>
<dependency>io.micrometer:micrometer-registry-otlp</dependency>
<dependency>io.micrometer:micrometer-registry-prometheus</dependency>
<dependency>com.alibaba:fastjson:1.2.70</dependency>
<dependency>com.alibaba.fastjson2:fastjson2:2.0.7</dependency>
<dependency>cn.hutool:hutool-all:5.7.22</dependency>
<dependency>org.mapstruct:mapstruct:1.5.5.Final</dependency>
```

### 1.3 环境拓扑图（PlantUML）

```plantuml
@startuml
title qinglu-provider 在整体系统中的部署与依赖关系

actor Client as C
package "应用服务层" {
  [qinglu-provider]
}
package "契约与模型层" {
  [qinglu-client]
  [qinglu-model]
}
package "开放能力" {
  [qinglu-open-provider]
}
cloud "外部平台" as EXT
database "MySQL(多数据源)" as DB
queue "Redis" as REDIS
node "OTel Collector" as OTEL

C --> [qinglu-provider] : REST/WebSocket
[qinglu-provider] ..> [qinglu-client]
[qinglu-provider] ..> [qinglu-model]
[qinglu-provider] --> DB : MyBatis / Mapper XML
[qinglu-provider] --> REDIS : Cache
[qinglu-provider] ..> OTEL : Metrics(Log4j2 OTel Appender)
[qinglu-provider] --> [qinglu-open-provider] : Feign
[qinglu-open-provider] --> EXT

note right of DB
store/common/trade/vehicle/bill
含从库与 Quartz 表
end note
@enduml
```

---

### 2. 核心模块解剖

- **启动与装配**
  - `StartSpringBootApplication`：`@SpringBootApplication`、`@ServletComponentScan`、`@MapperScan("com.ql.rent.dao")`、`@EnableFeignClients`、`@ImportResource("spring-content.xml")`、`@PropertySource` 多配置装载，开启 Swagger OpenAPI 注解。
  - `spring-content.xml`：额外扫描 `com.qinglusaas.logutil` 切面组件，增强日志域与 MDC。

- **Web 层（Controller/Provider）**
  - 包路径 `com.ql.rent.provider.*`（trade/price/merchant/pay/vehicle/store 等子域）对外暴露 REST 接口。
  - `websocket`：`WebsocketConfig` 注册 `ws` 端点，`WebSocketInterceptor` + `ServerWebSocketHandler` 实现连接鉴权与消息下发。

- **拦截与过滤**
  - `PricePermissionFilter`（`@WebFilter`）：包装请求为可重复读取的 `MultiReadableHttpServletRequest`，拦截 `/price/*` 与 `/price/batch/v1/*`。
  - `InterceptorConfig`：注册 `PricePermissionInterceptor`，对 `/price/**` 进行细粒度鉴权（基于登录用户可访问门店列表）。

```plantuml
@startuml
title 价格权限校验（Filter + Interceptor）

participant Client
participant Filter as F
participant Interceptor as I
participant TokenService as T
participant StoreService as S
Client -> F : HTTP /api/price/...
F -> I : 继续调用
I -> T : 获取登录用户(LoginVo)
I -> S : 查询可访问门店列表
I --> Client : 未登录/无权限 -> 403(JSON)
I --> Client : 有权限 -> 放行
@enduml
```

- **配置与基础设施**
  - `application.yml`：
    - `spring.profiles.active=${profile.env}` + `include: default,jdbc,redis,api`，集中控制环境；
    - Ribbon 直连三方或内部服务地址：`api`/`ctrip-api`/`didi-api`/`open-api` 等；
    - OTEL 资源与导出配置（`otlp.enabled`、`otlp.endpoint`）。
  - `application-jdbc.yml`：MyBatis、分页插件及 Druid 连接池基础参数。
  - `multipleDataSourceConfig.properties`：定义 `store/common/trade/vehicle/bill` 及从库数据源连接串与 Mapper 路径。
  - `application-redis.yml`：Redis 连接与连接池参数（Lettuce）。
  - `log4j2.xml`：OpenTelemetry Appender 输出 Trace 上下文，RollingFile 分级日志。
  - `src/main/filter/*.properties`：按 Profile 注入 `env` 变量以参与资源过滤与打包。

- **数据访问层**
  - `entity/*` + `dao/*` + `resources/mapper/**/**/*.xml`：按业务域（bill/common/merchant/store/trade/vehicle/slave）分包，MyBatis XML Mapper 丰富，覆盖读写与统计查询。
  - PageHelper 提供分页能力；`typeAliasesPackage: com.ql.rent.entity.**` 简化映射。

- **任务调度（Quartz）**
  - `schedule/*`：Runner/Task/Config 定时任务注册与工厂。
  - `quartz.properties`：DB JobStore、集群、线程池与 Druid 数据源（`DruidConnectionProvider`）。
  - 任务与执行记录持久化到 `QRTZ_` 前缀表，支持水平伸缩。

- **异步线程池**
  - `ExecutorConfig`：提供 `asyncPromiseExecutor`、`msgPromiseExecutor`、`orderPromiseExecutor` 三类线程池，使用 `OpenTelemetryContextDecorator` 与自定义 `ContextAwareExecutor` 透传 `Span` 与 `MDC`，避免异步丢失链路信息。

- **可观测性与日志**
  - `OpenTelemetryConfig`：`ParameterNameDiscoverer` 覆盖，优化方法参数名获取；
  - Log4j2 + OTEL Appender 将日志附带 `trace_id/span_id` 输出；Micrometer OTLP 导出业务指标。

---

### 3. 关键设计决策

- **多数据源分域**：将门店、通用、订单、车辆、支付账单拆分为独立数据源，并配置从库读请求，提升读写隔离与可扩展性。
- **权限前置与最小化授权**：`PricePermissionInterceptor` 基于用户可访问门店清单进行抑制型校验，保证价格相关接口的资源级授权。
- **异步链路可观测**：在自定义线程池中透传 OTel 上下文与 MDC，确保异步任务的 Trace/日志可关联。
- **定时任务可水平伸缩**：Quartz 使用 DB 集群模式，结合 `JobStoreTX` 与 `AUTO` 实例 ID；通过数据库互斥保证单次任务仅被一个实例执行。
- **配置外置与按环境打包**：借助 Maven 资源过滤与 `src/main/filter/${env}.properties`，在打包阶段注入环境特定配置到 `/conf`，避免硬编码。

---

### 4. 部署架构

- **打包与产物**
  - `maven-assembly-plugin` 生成 `carrent-provider.tar.gz`，结构包含：
    - 根目录：可执行 `jar`
    - `/lib`：三方依赖
    - `/conf`：`*.yml/*.xml/*.properties` 配置
    - `/env.conf`：环境变量入口
- **运行参数**
  - 通过 `-Dprofile.env=<env>` 指定环境（影响 `spring.profiles.active` 与资源装载）。
  - OTLP 导出与 Ribbon 直连地址通过外置 `conf` 控制。
- **日志与指标**
  - 日志：`logs/` 目录分级滚动；同时经 OTel Appender 送至采集器。
  - 指标：Micrometer 通过 OTLP 上报至 `otel-collector`。

---

### 5. 配置总览（要点）

- `application.yml`
  - `server.servlet.context-path: /api`，Tomcat `max-threads/accept-count` 调优；
  - `spring.profiles.include: default,jdbc,redis,api`；
  - `otel.exporter.otlp.endpoint`、`management.otlp.metrics.export.url`；
  - Ribbon `*.ribbon.listOfServers`：`api/ctrip-api/open-api/didi-api/...`。
- `application-jdbc.yml`: Druid 连接池通用参数；MyBatis 日志实现、分页参数。
- `multipleDataSourceConfig.properties`: 各域 `url/username/password` 与 `mapper-locations`。
- `quartz.properties`: `org.quartz.jobStore.isClustered=true`、`JobStoreTX`、`qzDS` 数据源。
- `application-redis.yml`: `host/port/password/database` 与连接池参数。

---

### 6. 技术栈矩阵（节选）

| 组件 | 版本 | 使用场景 |
|---|---|---|
| JDK | 11 | 运行时 |
| Spring Boot | 2.x（父 POM 管理） | 应用容器与自动配置 |
| MyBatis | 3.x | ORM 映射/DAO |
| PageHelper | 1.3.0 | 分页 |
| Quartz | - | 定时任务集群/持久化 |
| Spring WebSocket | 2.1.3.RELEASE | 实时推送通道 |
| Spring Cloud OpenFeign | - | 内部/外部服务调用 |
| Redis(Lettuce) | - | 缓存/会话 |
| Micrometer OTLP | - | 指标上报 |
| OpenTelemetry(Log4j2) | - | 日志 Trace 关联 |
| MapStruct | 1.5.5.Final | Bean 映射 |
| Hutool | 5.7.22 | 工具集 |
| fastjson | 1.2.70 | JSON 处理 |

---

### 7. 风险点与技术债务

- ⚠️ `fastjson 1.2.70`、`xstream 1.4.9`、`commons-codec 1.6`、`spring-boot-starter-websocket 2.1.3.RELEASE` 等依赖版本较老，存在已知 CVE 或维护风险，建议评估升级（或以 `fastjson2`/Jackson 替换部分场景）。
- ⚠️ 多 `ThreadLocal`/MDC 上下文在异步中的传递需保持一致性，已有 `ContextAwareExecutor` 与 `TaskDecorator`，使用新线程池时需复用相关装饰器。
- ⚠️ `allow-circular-references: true`（`application.yml`）可能掩盖设计问题，建议限制使用并通过重构消除循环依赖。
- ⚠️ Swagger（`@EnableOpenApi` via springfox）与新版 Spring Boot 兼容性需关注，建议后续迁移至 springdoc-openapi。

---

### 8. UML：API 请求到数据访问（示例时序）

```plantuml
@startuml
title Controller -> Service -> DAO -> DB (MyBatis)

participant Controller as C
participant Service as S
participant Mapper as M
database DB

C -> S : 处理业务请求(Param)
S -> M : 调用 MyBatis Mapper
M -> DB : 执行 SQL(Mapper XML)
DB --> M : 结果集
M --> S : 实体/VO
S --> C : Result
@enduml
```

---

### 9. 扩展与约定

- 新增接口：在 `provider/*` 中按业务域创建 Controller；安全敏感接口优先复用或扩展拦截器能力。
- 新增任务：统一在 `schedule/runner` 或 `schedule/task` 注册，遵循 Quartz 命名与分组约定，避免并发冲突。
- 新增数据访问：按域在 `dao/entity/resources/mapper` 中成对新增，并补充从库只读 Mapper（如适用）。
- 异步任务：统一使用已定义的线程池或复用 `TaskDecorator`，确保 Trace 传播。

---

### 10. 变更记录（模板）

| 版本 | 日期 | 变更人 | 变更项 | 说明 |
|---|---|---|---|---|
| 1.0.0 | 2025-xx-xx | - | 初版 | 基于代码扫描与依赖清单生成 |

---

附：参考源文件（便于定位）

- 启动类：`com.ql.rent.StartSpringBootApplication`
- 配置：`application.yml`、`application-jdbc.yml`、`application-redis.yml`、`multipleDataSourceConfig.properties`、`quartz.properties`、`log4j2.xml`
- Web：`com.ql.rent.provider.*`、`com.ql.rent.config.WebsocketConfig`
- 拦截/过滤：`com.ql.rent.config.InterceptorConfig`、`com.ql.rent.interceptor.PricePermissionInterceptor`、`com.ql.rent.filter.PricePermissionFilter`
- 异步：`com.ql.rent.config.ExecutorConfig`
- 可观测：`com.ql.rent.trace.OpenTelemetryConfig`
- 数据访问：`com.ql.rent.dao.*`、`com.ql.rent.entity.*`、`src/main/resources/mapper/**/**/*.xml`


