package com.ql.rent.api.aggregate.remote.platform.vo.request;

import lombok.Data;

/**
 * 预授权交易关闭请求参数
 *
 * <AUTHOR>
 */
@Data
public class ThirdFundAuthCloseReq {

    /**
     * 第三方资金授权来源
     */
    private Byte thirdFundAuthSource;

    /**
     * 商户授权资金订单号
     */
    private String payNo;

    /**
     * 操作人ID
     */
    private Long opUserId;

    /**
     * 支付主体：1-代商家调用，0-擎路调用
     */
    private Integer merchantPay;

    /**
     * 商户ID
     */
    private Long merchantId;
}
