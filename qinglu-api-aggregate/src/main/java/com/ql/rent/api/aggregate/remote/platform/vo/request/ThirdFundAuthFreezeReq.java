package com.ql.rent.api.aggregate.remote.platform.vo.request;

import lombok.Data;

/**
 * 资金授权冻结请求参数
 *
 * <AUTHOR>
 */
@Data
public class ThirdFundAuthFreezeReq {

    /**
     * 第三方资金授权来源
     */
    private Byte thirdFundAuthSource;

    /**
     * SAAS订单ID
     */
    private Long orderId;

    /**
     * 商户授权资金订单号
     */
    private String orderNo;

    /**
     * 资金操作请求流水号
     */
    private String requestNo;

    /**
     * 订单标题
     */
    private String orderTitle;

    /**
     * 冻结金额（单位：分）
     */
    private Long amount;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 预授权订单相对超时时间（分钟）
     */
    private Long timeoutExpress;

    /**
     * 操作人ID
     */
    private Long opUserId;

    /**
     * 支付主体：1-代商家调用，0-擎路调用
     */
    private Integer merchantPay;

    /**
     * 商户ID
     */
    private Long merchantId;
}
