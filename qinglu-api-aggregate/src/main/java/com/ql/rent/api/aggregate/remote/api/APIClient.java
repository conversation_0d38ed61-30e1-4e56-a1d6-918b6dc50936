package com.ql.rent.api.aggregate.remote.api;

import com.ql.dto.open.request.store.TrafficPolicyInfoRequest;
import com.ql.dto.vehicle.VehicleInfoDTO;
import com.ql.rent.api.aggregate.remote.collector.vo.dto.*;
import com.ql.rent.api.aggregate.remote.vo.request.*;
import com.ql.dto.ApiResultResp;
import com.ql.dto.open.request.PlatformBaseRequest;
import com.ql.rent.api.aggregate.remote.vo.response.*;
import com.ql.rent.api.aggregate.web.security.filter.FeignClientErrorDecoder;
import com.ql.rent.api.aggregate.web.security.filter.FeignSignRequestInterceptor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/10/27, Thursday
 **/
@FeignClient(name = "remote-api",
        configuration = {FeignSignRequestInterceptor.class, FeignClientErrorDecoder.class},
        url = "${api.ribbon.listOfServers}")
public interface APIClient {

    /**
     * 供应商传递扣费明细接口
     *
     * @param deductionDetailNotifyReq
     * @return
     */
    @PostMapping("/saas/ctrip-api/v1/deductionDetailNotify")
    ApiResultResp deductionDetailNotify(PlatformBaseRequest<DeductionDetailNotifyReq> deductionDetailNotifyReq);

    /**
     * 免押扣款接口
     *
     * @return
     */
    @RequestMapping(path = "/saas/api/platform/v1/freeDepositDeduction", method = {RequestMethod.POST})
    ApiResultResp<FreeDepositDeductionResp> freeDepositDeduction(
            @RequestBody PlatformBaseRequest<FreeDepositDeductionReq> request);

    /**
     * 免押扣款撤销接口
     *
     * @return
     */
    @RequestMapping(path = "/saas/api/platform/v1/cancelFreeDepositDeduction", method = {RequestMethod.POST})
    ApiResultResp cancelFreeDepositDeduction(@RequestBody PlatformBaseRequest<CancelFreeDepositDeductionReq> request);

    /**
     * 免押退款接口
     *
     * @return
     */
    @RequestMapping(path = "/saas/api/platform/v1/freeDepositRefund", method = {RequestMethod.POST})
    ApiResultResp freeDepositRefund(@RequestBody PlatformBaseRequest<FreeDepositRefundReq> request);

    /**
     * 订单状态回调 订单完成
     *
     * @param finishedReq
     * @return
     */
    @PostMapping("/saas/ctrip-api/v1/orderInfoCallbackV2/finished")
    OrderInfoCallbackV2Resp finished(@RequestBody PlatformBaseRequest<OrderInfoStatusCallBackReq> finishedReq);

    /**
     * 订单状态回调 押金退换状态 （已废弃）
     *
     * @param depositRefundReq
     * @return
     */
    @PostMapping("/saas/ctrip-api/v1/orderInfoCallbackV2/depositRefund")
    OrderInfoCallbackV2Resp depositRefund(
            @RequestBody PlatformBaseRequest<OrderInfoCallbackStatusDepositRefundReq> depositRefundReq);

    /**
     * 订单状态回调  未到店状态
     *
     * @param notReachedReq
     * @return
     */
    @PostMapping("/saas/ctrip-api/v1/orderInfoCallbackV2/notReached")
    OrderInfoCallbackV2Resp notReached(
            @RequestBody PlatformBaseRequest<OrderInfoCallbackStatusNotReachedReq> notReachedReq);

    /**
     * 订单状态回调 延迟换车状态
     *
     * @param delayedReturnReq
     * @return
     */
    @PostMapping("/saas/ctrip-api/v1/orderInfoCallbackV2/delayedReturn")
    OrderInfoCallbackV2Resp delayedReturn(
            @RequestBody PlatformBaseRequest<OrderInfoCallbackStatusDelayedReturnReq> delayedReturnReq);

    /**
     * 订单状态回调 已取车状态
     *
     * @param pickedUpReq
     * @return
     */
    @PostMapping("/saas/ctrip-api/v1/orderInfoCallbackV2/pickedUp")
    OrderInfoCallbackV2Resp pickedUp(@RequestBody PlatformBaseRequest<OrderInfoStatusCallBackReq> pickedUpReq);

    /**
     * 订单状态回调 自助取还已排车
     *
     * @param pickedUpReq
     * @return
     */
    @PostMapping("/saas/ctrip-api/v1/orderInfoCallbackV2/scheduled")
    OrderInfoCallbackV2Resp scheduled(@RequestBody PlatformBaseRequest<OrderInfoCallbackScheduledReq> pickedUpReq);


    /**
     * 订单状态回调 行中换车
     *
     * @param callbackChangeCarReq
     * @return
     */
    @PostMapping("/saas/ctrip-api/v1/orderInfoCallbackV2/changeCar")
    OrderInfoCallbackV2Resp changeCar(@RequestBody PlatformBaseRequest<OrderInfoCallbackScheduledReq> callbackChangeCarReq);

    /**
     * hello 订单状态回调
     *
     * @param callbackOrderStatusReq
     * @return
     */
    @PostMapping("/saas/hello-api/v1/callback/order-status")
    ApiResultResp helloCallbackOrderStatus(PlatformBaseRequest<OrderInfoStatusCallBackReq> callbackOrderStatusReq);


    /**
     * 获取哈啰门店列表数据
     *
     * @param request 平台请求参数，包含门店ID
     * @return 门店列表数据
     */
    @PostMapping("/saas/hello-api/v1/store/list")
    ApiResultResp<List<Store>> getStoreList(PlatformBaseRequest<Long> request);

    /**
     * 哈啰初始化过程-拉取哈啰的全部车辆
     */
    @PostMapping("/saas/hello-api/v1/vehicle/all_license")
    ApiResultResp<List<Vehicle>> getAllHelloLicense(PlatformBaseRequest<List<String>> request);

    /**
     * 订单状态回调(适用于：哈喽、飞猪)
     *
     * @param request
     * @return
     */
    @PostMapping(path = "/saas/api/platform/v1/order-status/callback")
    ApiResultResp orderStatusCallback(
            @RequestBody PlatformBaseRequest<OrderInfoStatusCallBackReq> request);

    /**
     * 渠道车型匹配
     *
     * @param request
     * @return
     */
    @PostMapping(path = "/saas/api/platform/v1/channel-vehicle-model/match")
    ApiResultResp<ChannelVehicleModelMatchResp> channelVehicleModelMatch(
            @RequestBody PlatformBaseRequest<ChannelVehicleModelMatchReq> request);

    /**
     * 携程 推送禁行政策
     * @param deductionReq
     * @return
     */
    @PostMapping(path = "/saas/api/platform/v1/trafficPolicyNotify")
    ApiResultResp pushServiceData(@RequestBody PlatformBaseRequest<List<TrafficPolicyInfoRequest>> deductionReq);

    /**
     * 查询平台车辆详情
     *
     * @param request
     * @return
     */

    @PostMapping(path = "/saas/api/platform/v1/vehicle/detail")
    ApiResultResp<VehicleInfoDTO> vehicleDetail(@RequestBody PlatformBaseRequest<Long> request);

    /**
     * hello 订单列表
     *
     * @param orderListReq
     * @return
     */
    @PostMapping("/saas/hello-api/v1/order/list")
    ApiResultResp<OrderListResp> orderList(PlatformBaseRequest<OrderListReq> orderListReq);

    /**
     * hello 订单详细
     *
     * @param orderDetailReq
     * @return
     */
    @PostMapping("/saas/hello-api/v1//order/detail")
    ApiResultResp<OrderDetailResp> orderDetail(PlatformBaseRequest<OrderDetailReq> orderDetailReq);

    /**
     * 哈啰门店下架
     *
     * @param req 门店编号
     * @return 操作结果
     */
    @PostMapping("/saas/hello-api/v1/store/down")
    ApiResultResp helloStoreDown(@RequestBody PlatformBaseRequest<String> req);

    /**
     * 初始化哈啰车辆数据
     *
     * @param req 车辆初始化请求参数
     * @return 操作结果
     */
    @PostMapping("/saas/hello-api/v1/vehicle/model/init")
    ApiResultResp helloInitVehicleModel(@RequestBody PlatformBaseRequest req);

    /**
     * 初始化哈啰车辆数据
     *
     * @param req 车辆初始化请求参数
     * @return 操作结果
     */
    @PostMapping("/saas/hello-api/v1/vehicle/info/init")
    ApiResultResp helloInitVehicleData(@RequestBody PlatformBaseRequest<VehicleInitReq> req);

    /**
     * 初始化哈啰门店数据
     *
     * @param req 初始化哈啰门店数据
     * @return 操作结果
     */
    @PostMapping("/saas/hello-api/v1/store/info/init")
    ApiResultResp helloInitStoreData(@RequestBody PlatformBaseRequest<StoreInitReq> req);


    @PostMapping("/saas/hello-api/v1/vehicle/price/init")
    ApiResultResp initHelloPriceData(@RequestBody PlatformBaseRequest<VehicleInitReq> req);

    @PostMapping("/saas/hello-api/v1/service-circle/info/init")
    ApiResultResp initServiceCircle(@RequestBody PlatformBaseRequest<ServiceCircleInitReq> req);

    @PostMapping("/saas/hello-api/v1/stock/info/init")
    ApiResultResp initStock(@RequestBody PlatformBaseRequest<VehicleInitReq> req);

    /**
     * 哈啰排车
     * @param req
     * @return
     */
    @PostMapping("/saas/hello-api/v1/vehicle/settle")
    ApiResultResp vehicleSettle(@RequestBody PlatformBaseRequest<VehicleSettleReq> req);

    /**
     * 查询订单库存占用
     * @param req
     * @return
     */
    @PostMapping("/saas/hello-api/v1/order/inventory/occupy/query")
    ApiResultResp<OrderInventoryOccupyQueryResp> orderInventoryOccupyQuery(@RequestBody PlatformBaseRequest<OrderInventoryOccupyQueryRequest> req);

    // ==================== 携程履约项目raptor接口 ====================

    /**
     * 查询虚拟号接口
     * 查询携程订单的虚拟联系号码
     * 
     * @param request 查询虚拟号请求参数
     * @return 虚拟号信息
     */
    @PostMapping("/saas/ctrip-api/v1/query-virtual-phone")
    ApiResultResp<QueryVirtualPhoneResp> queryVirtualPhone(
                    @RequestBody PlatformBaseRequest<QueryVirtualPhoneReq> request);

    /**
     * 送车上门-点位上传接口
     * 送车上门时上传车辆点位信息 (messageType: 3)
     * 
     * @param request 点位上传请求参数
     * @return 操作结果
     */
    @PostMapping("/saas/ctrip-api/v1/on-door-location-upload")
    ApiResultResp onDoorLocationUpload(@RequestBody PlatformBaseRequest<OnDoorLocationUploadReq> request);

    /**
     * 送车上门-轨迹生成接口
     * 送车上门时生成车辆轨迹 (messageType: 4)
     * 
     * @param request 轨迹生成请求参数
     * @return 操作结果
     */
    @PostMapping("/saas/ctrip-api/v1/on-door-track-generate")
    ApiResultResp onDoorTrackGenerate(@RequestBody PlatformBaseRequest<OnDoorTrackGenerateReq> request);

    /**
     * OCR履约实际车牌上传接口
     * 上传OCR识别的实际车牌信息 (messageType: 5)
     * 
     * @param request OCR车牌上传请求参数
     * @return 操作结果
     */
    @PostMapping("/saas/ctrip-api/v1/ocr-license-plate-upload")
    ApiResultResp<UpdateOrderImplInfoResponse> ocrLicensePlateUpload(@RequestBody PlatformBaseRequest<OcrLicensePlateUploadReq> request);

    /**
     * 供应商修改订单接口
     * 供应商发起的订单修改请求
     * 
     * @param request 修改订单请求参数
     * @return 操作结果
     */
    @PostMapping("/saas/ctrip-api/v1/vendor-modify-order")
    ApiResultResp vendorModifyOrder(@RequestBody PlatformBaseRequest<VendorModifyOrderReq> request);

    /**
     * 供应商修改订单回调接口
     * 供应商修改订单的结果回调
     * 
     * @param request 修改订单回调请求参数
     * @return 操作结果
     */
    @PostMapping("/saas/ctrip-api/v1/modify-order-cancel")
    ApiResultResp vendorModifyOrderCallback(@RequestBody PlatformBaseRequest<VendorModifyOrderCallbackReq> request);

    /**
     * 供应商修改订单回调接口
     * 供应商修改订单的结果回调
     *
     * @param request 修改订单回调请求参数
     * @return 操作结果
     */
    @PostMapping("/saas/ctrip-api/v1/query-vendor-modify-order")
    ApiResultResp queryVendorModify(@RequestBody PlatformBaseRequest<VendorModifyOrderCallbackReq> request);

    /**
     * 供应商修改订单催促确认接口
     * 催促供应商确认订单修改
     * 
     * @param request 催促确认请求参数
     * @return 操作结果
     */
    @PostMapping("/saas/ctrip-api/v1/vendor-modify-order-urge-confirm")
    ApiResultResp urgeConfirmModifyOrder(
                    @RequestBody PlatformBaseRequest<VendorModifyOrderUrgeConfirmReq> request);

    /**
     * 已排司机状态回调接口
     * 通知携程订单已安排司机
     * 
     * @param request 已排司机状态回调请求参数
     * @return 操作结果
     */
    @PostMapping("/saas/ctrip-api/v1/orderInfoCallbackV2/driverAssigned")
    ApiResultResp driverAssigned(@RequestBody PlatformBaseRequest<OrderInfoCallbackDriverAssignedReq> request);

    /**
     * 查询携程的判责标签
     *
     * @param request 已排司机状态回调请求参数
     * @return 操作结果
     */
    @PostMapping("/saas/ctrip-api/v1/query-vendor-response-tag")
    ApiResultResp queryOrderTag(@RequestBody PlatformBaseRequest<OrderInfoCallbackDriverAssignedReq> request);
}

