package com.ql.rent.api.aggregate.remote.etc.api;

import com.ql.dto.ApiResultResp;
import com.ql.rent.api.aggregate.remote.etc.api.dto.EtcEndOrderRequest;
import com.ql.rent.api.aggregate.remote.etc.api.dto.EtcVehicleInfo;
import com.ql.rent.api.aggregate.web.security.filter.FeignClientErrorDecoder;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
    name = "saas-self-etc-api",
    configuration = { FeignClientErrorDecoder.class},
    url = "${saas-self-etc-api.ribbon.listOfServers}")
public interface SaasSelfEtcApiClient {

    @PostMapping("/order/end")
    ApiResultResp endOrder(@RequestBody EtcEndOrderRequest etcEndOrderRequest);

    @GetMapping("/alipay/etc/vehicle")
    ApiResultResp<EtcVehicleInfo> etcVehicle(@RequestParam String thirdVehicleId, @RequestParam String license, @RequestParam Integer plateColor, @RequestParam  String etcOrderNo);

}
