package com.ql.rent.api.aggregate.remote.platform.vo.response;

import lombok.Data;
import java.util.Date;

/**
 * 资金授权解冻响应结果
 *
 * <AUTHOR>
 */
@Data
public class ThirdFundAuthUnfreezeResp {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 失败原因
     */
    private String sourceFailedReason;

    /**
     * 微信资金操作流水号
     */
    private String operationId;

    /**
     * 解冻金额（分）
     */
    private Long amount;

    /**
     * 操作状态
     */
    private String status;

    /**
     * 解冻成功时间
     */
    private Date gmtTrans;
}
