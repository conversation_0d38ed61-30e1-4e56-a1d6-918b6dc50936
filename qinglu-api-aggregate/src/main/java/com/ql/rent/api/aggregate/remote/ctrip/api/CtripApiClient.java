package com.ql.rent.api.aggregate.remote.ctrip.api;

import com.ql.rent.api.aggregate.model.remote.ctrip.dto.VehicleInfoDTO;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.SignRequest;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.response.*;
import com.ql.rent.api.aggregate.web.security.filter.FeignClientErrorDecoder;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @desc
 */
@FeignClient(
        name = "ctrip-client",
        configuration = {FeignClientErrorDecoder.class},
        url = "${ctrip-api.ribbon.listOfServers}")
public interface CtripApiClient {

    /**
     * 拉取门店信息
     */
    @PostMapping("/getStoreListForSaas")
    GetStoreListForSaasResponse pullStoreListForSaas(SignRequest request);

    /**
     * 拉取异门店信息
     */
    @PostMapping("/getDiffStoreConfigListForSaas")
    GetDiffStoreConfigListForSaas pullDiffStoreListForSaas(SignRequest request);

    /**
     * 拉取零散小时规则信息
     */
    @PostMapping("/getHourlyChargeForSaas")
    GetHourlyChargeForSaasResponse pullHourlyChargeForSaas(SignRequest request);

    /**
     * 拉取商品sku信息
     *
     * @param request
     * @return
     */
    @PostMapping("/getSkuPrice")
    //CtripSkuPriceGetResponse pullSkuPriceForSaas(SignRequest request);
    CtripSkuPriceGetResponse pullSkuPriceForSaas(SignRequest request);


    /**
     * 限价查询接口
     *
     * @param request
     * @return
     */
    @PostMapping("/getPriceFilterSaas")
    CtripSkuPriceFilterGetResponse pullPriceFilterSaas(SignRequest request);

    /**
     * 拉取库存
     *
     * @param request
     * @return
     * @see com.ql.rent.api.aggregate.remote.ctrip.vo.request.GetUsageInfoRequest
     */
    @PostMapping("/getUsageInfo")
    GetUsageInfoResp getUsageInfo(SignRequest request);

    /**
     * 拉取携程子车系
     *
     * @param request
     * @return 对应结果
     */
    @GetMapping("/getSubSeries")
    PageResp<CtripVehicleSubSeriesResp> pullCtripVehicleSubSeries(SignRequest request);

    /**
     * 拉取携程车辆信息列表
     *
     * @param baseReq 必须上传渠道id+商户id参数+起始id
     * @return 对应结果
     */
    @PostMapping("/getCarInfo")
    PageResp<VehicleInfoDTO> pullCtripVehicle(SignRequest baseReq);


    /**
     * 拉取携程sku多媒体信息
     */
    @PostMapping("/getSkuMedia")
    GetVehicleMediaResp pullVehicleMedia(SignRequest request);

    /**
     * 推送携程消息
     *
     * @param request
     * @return
     */
    @PostMapping("/pushNotice")
    BaseCtripResponse pushNotice(SignRequest request);

    /**
     * 推送携程子车系绑定
     * @param request
     * @return
     */
    @PostMapping("/createSku")
    SkuResponse createSku(SignRequest request);

    /**
     * 删除携程子车系绑定
     * @param request
     * @return
     */
    @PostMapping("/deleteSku")
    SkuResponse deleteSku(SignRequest request);


    /**
     * 拉取携程车型
     * @param request
     * @return response
     */
    @PostMapping("/getStandardProductAndSubSeries")
    PageResp<StandardProductAndSubSeriesDto> getStandardProductAndSubSeries(SignRequest request);

    /**
     * 推送车辆标签
     * @param request
     * @return response
     */
    @PostMapping("/pushCarLableToTrip")
    BaseCtripResponse pushTag(SignRequest request);

    /**
     * 推送车辆数据
     * @param request
     * @return response
     */
    @PostMapping("/pushCarBaseInfoToTrip")
    BaseCtripResponse pushVehicle(SignRequest request);

    /**
     * 推送上线状态
     */
    @PostMapping("/pushCarStatusToTrip")
    BaseCtripResponse pushVehicleStatus(SignRequest request);

    /**
     * 推送零散小时
     */
    @PostMapping("/pushHourlyChargeToTrip")
    CtripHourlyChargeResponse pushHourlyCharge(SignRequest request);

    /**
     * 推送库存
     */
    @PostMapping("/pushCarStockUsageToTrip")
    BaseCtripResponse pushStock(SignRequest request);

    /**
     * 推送异门店规则
     */
    @PostMapping("/pushDiffStoreRuleToTrip")
    BaseCtripResponse pushDiffRule(SignRequest request);

    /**
     * 推送订单时间间隔
     */
    @PostMapping("/pushOrderIntervalToTrip")
    CtripOrderIntervalToSaasResponse pushOrderInterval(SignRequest request);

    /**
     * 推送sku数据
     */
    @PostMapping("/pushSkuPriceInfoToTrip")
    SkuResponse pushSkuInfo(SignRequest request);

    /**
     * 推送双免数据
     */
    @PostMapping("/pushDepositFreeToTrip")
    BaseCtripResponse pushDepositFree(SignRequest request);

//    /**
//     * 推送门店和服务圈信息
//     */
//    @PostMapping("/json/syncStoreInfo")
//    BaseCtripResponse pushStoreInfo(SignRequest request);

    /**
     * 推送携提前预定期（服务规则域）
     */
    @PostMapping("/pushServiceRuleToTrip")
    BaseCtripResponse pushServiceRule(SignRequest request);

    /**
     * 推送服务商预定规则（商品-套餐域）
     */
    @PostMapping("/pushCarRentBookingPolicyDetailToTrip")
    BaseCtripResponse pushCarRentBookingPolicyDetail(SignRequest request);

    /**
     * 推送sku下线接口
     */
    @PostMapping("/pushSkuStatusToTrip")
    BaseCtripResponse pushSkuStatusToTrip(SignRequest request);

    /**
     * 儿童座椅库存推送
     */
    @PostMapping("/pushPurchasedAddProductToTrip")
    BaseCtripResponse pushPurchasedAddProductToTrip(SignRequest request);
    /**
     * 拉取节假日数据
     */
    @PostMapping("/getAnnualHolidayInfoFromTrip")
    CtripHolidayResp getAnnualHolidayInfoFromTrip(SignRequest request);
    /**
     * 拉取携程价格信息
     */
    @PostMapping("/getSkuPriceInfoFromTrip")
    String getSkuPriceInfoFromTrip(SignRequest request);

    @PostMapping("/toTripCommonService")
    BaseCtripResponse toTripCommonService(SignRequest request);

}