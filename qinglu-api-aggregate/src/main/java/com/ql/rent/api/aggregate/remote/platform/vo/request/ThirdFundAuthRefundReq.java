package com.ql.rent.api.aggregate.remote.platform.vo.request;

import lombok.Data;

/**
 * 预授权退款请求参数
 *
 * <AUTHOR>
 */
@Data
public class ThirdFundAuthRefundReq {

    /**
     * 第三方资金授权来源
     */
    private Byte thirdFundAuthSource;

    /**
     * 退款金额（分）
     */
    private Long amount;

    /**
     * 预授权支付记录ID
     */
    private Long authPayId;

    /**
     * 商户订单号
     */
    private String tradeNo;

    /**
     * 退款原因说明
     */
    private String refundReason;

    /**
     * 退款请求号
     */
    private String refundNo;

    /**
     * 操作人ID
     */
    private Long opUserId;

    /**
     * 微信支付订单号
     */
    private String thirdSouceNo;

    /**
     * 支付主体：1-代商家调用，0-擎路调用
     */
    private Integer merchantPay;

    /**
     * 商户ID
     */
    private Long merchantId;
}
