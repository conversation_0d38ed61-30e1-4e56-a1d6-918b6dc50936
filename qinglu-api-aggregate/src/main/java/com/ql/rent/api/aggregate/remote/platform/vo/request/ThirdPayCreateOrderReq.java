package com.ql.rent.api.aggregate.remote.platform.vo.request;

import lombok.Data;

/**
 * 创建微信支付订单请求参数
 *
 * <AUTHOR>
 */
@Data
public class ThirdPayCreateOrderReq {

    /**
     * 第三方支付来源标识
     */
    private Integer thirdPaySource;

    /**
     * 支付金额（单位：分）
     */
    private Long amount;

    /**
     * 业务关联ID
     */
    private Long relationId;

    /**
     * 业务类型
     */
    private Integer relationType;

    /**
     * 操作人ID
     */
    private Long opUserId;

    /**
     * 支付订单号
     */
    private String payNo;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 下单数量，默认1
     */
    private Integer quantity;

    /**
     * 订单标题
     */
    private String subject;

    /**
     * 买家openId
     */
    private String openId;

    /**
     * 支付主体：1-代商家调用，0-擎路调用
     */
    private Integer merchantPay;
}
