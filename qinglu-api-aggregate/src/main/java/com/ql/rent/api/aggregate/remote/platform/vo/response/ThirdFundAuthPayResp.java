package com.ql.rent.api.aggregate.remote.platform.vo.response;

import lombok.Data;

/**
 * 预授权支付响应结果
 *
 * <AUTHOR>
 */
@Data
public class ThirdFundAuthPayResp {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 失败原因
     */
    private String sourceFailedReason;

    /**
     * 商家订单号
     */
    private String tradeNo;

    /**
     * 三方的资金授权订单号
     */
    private String thirdTradeNO;
}
