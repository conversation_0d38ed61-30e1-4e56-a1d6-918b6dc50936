package com.ql.rent.api.aggregate.remote.platform.vo.request;

import lombok.Data;

/**
 * 支付退款请求参数
 *
 * <AUTHOR>
 */
@Data
public class ThirdPayRefundReq {

    /**
     * 第三方支付来源标识
     */
    private Integer thirdPaySource;

    /**
     * 退款金额（单位：分）
     */
    private Long amount;

    /**
     * 业务关联ID
     */
    private Long relationId;

    /**
     * 业务类型
     */
    private Integer relationType;

    /**
     * 支付记录ID
     */
    private Long payId;

    /**
     * 退款请求号
     */
    private String refundNo;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 操作人ID
     */
    private Long opUserId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 微信支付单号
     */
    private String thirdSourceNo;

    /**
     * 支付主体：1-代商家调用，0-擎路调用
     */
    private Integer merchantPay;
}
