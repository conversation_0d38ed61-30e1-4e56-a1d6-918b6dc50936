package com.ql.rent.api.aggregate.remote.platform.vo.request;

import lombok.Data;

/**
 * 资金授权解冻请求参数
 *
 * <AUTHOR>
 */
@Data
public class ThirdFundAuthUnfreezeReq {

    /**
     * 第三方资金授权来源
     */
    private Byte thirdFundAuthSource;

    /**
     * SAAS订单ID
     */
    private Long orderId;

    /**
     * 微信资金授权订单号
     */
    private String thirdAuthNo;

    /**
     * 解冻请求流水号
     */
    private String requestNo;

    /**
     * 解冻金额（单位：分）
     */
    private Long amount;

    /**
     * 解冻操作附言描述
     */
    private String remark;

    /**
     * 是否履约完结
     */
    private Boolean bizComplete;

    /**
     * 操作人ID
     */
    private Long opUserId;

    /**
     * 支付主体：1-代商家调用，0-擎路调用
     */
    private Integer merchantPay;

    /**
     * 商户ID
     */
    private Long merchantId;
}
