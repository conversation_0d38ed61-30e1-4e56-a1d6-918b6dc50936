package com.ql.rent.api.aggregate.web.security.filter;

import com.ql.rent.api.aggregate.web.security.exception.BadRequestException;
import com.ql.rent.api.aggregate.web.security.exception.InternalServerErrorException;
import feign.Response;
import feign.Util;
import feign.codec.ErrorDecoder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import static feign.FeignException.errorStatus;


/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/1/8, Sunday
 **/
//@Configuration
public class FeignClientErrorDecoder implements ErrorDecoder {
    private Logger logger = LoggerFactory.getLogger(FeignClientErrorDecoder.class);

    @Override
    public Exception decode(String methodKey, Response response) {
        Map<String, Object> jsonBody = new HashMap<>();
        jsonBody.put("message", "Internal server error");
        try {
            if (null != response.body()) {
                String body = Util.toString(response.body().asReader(StandardCharsets.UTF_8));
                if (StringUtils.isNotBlank(body)) {
                    jsonBody.put("message", body);
                }
                logger.error("feign client response body {}", body);
            }
        } catch (IOException e) {
            logger.error("feign.IOException", e);
        }
        assert jsonBody != null;
        if (response.status() >= 400 && response.status() < 500) {
            logger.error("feign Exception {}", response);
            throw new BadRequestException(jsonBody.get("message").toString());
        }

        if (response.status() >= 500) {
            throw new InternalServerErrorException(jsonBody.get("message").toString());
        }

        return errorStatus(methodKey, response);
    }

}