package com.ql.rent.api.aggregate.remote.platform.vo.response;

import lombok.Data;

/**
 * 创建微信支付订单响应结果
 *
 * <AUTHOR>
 */
@Data
public class ThirdPayCreateOrderResp {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 支付状态
     */
    private Byte payStatus;

    /**
     * 实际支付金额（分）
     */
    private Long actualPayAmount;

    /**
     * 支付订单号
     */
    private Long payNo;

    /**
     * 外部支付单号
     */
    private String thirdSourceNo;

    /**
     * 外部失败原因
     */
    private String sourceFailedReason;

    /**
     * 订单支付码
     */
    private String qrCode;

    /**
     * 预支付交易会话标识
     */
    private String prepay_id;
}
