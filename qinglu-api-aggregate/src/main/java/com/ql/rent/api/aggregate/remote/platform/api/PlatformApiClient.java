package com.ql.rent.api.aggregate.remote.platform.api;

import com.ql.dto.ApiResultResp;
import com.ql.rent.api.aggregate.remote.platform.vo.request.*;
import com.ql.rent.api.aggregate.remote.platform.vo.response.*;
import com.ql.rent.api.aggregate.web.security.filter.FeignClientErrorDecoder;
import com.ql.rent.api.aggregate.web.security.filter.FeignSignRequestInterceptor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Platform API客户端
 * 用于调用platform服务的微信支付和预授权相关接口
 *
 * <AUTHOR>
 */
@FeignClient(name = "platform-api",
        configuration = {FeignClientErrorDecoder.class},
        url = "${platform-api.ribbon.listOfServers}")
public interface PlatformApiClient {

    // ==================== 支付相关接口 ====================

    /**
     * 创建微信支付订单
     *
     * @param request 创建支付订单请求参数
     * @return 支付订单结果
     */
    @PostMapping("/third-pay/create-order")
    ApiResultResp<ThirdPayCreateOrderResp> createThirdPayOrder(@RequestBody ThirdPayCreateOrderReq request);

    /**
     * 支付退款
     *
     * @param request 退款请求参数
     * @return 退款结果
     */
    @PostMapping("/third-pay/refund")
    ApiResultResp<ThirdPayRefundResp> thirdPayRefund(@RequestBody ThirdPayRefundReq request);

    // ==================== 预授权相关接口 ====================

    /**
     * 资金授权冻结
     *
     * @param request 冻结请求参数
     * @return 冻结结果
     */
    @PostMapping("/third-fundauth/freeze")
    ApiResultResp<ThirdFundAuthFreezeResp> thirdFundAuthFreeze(@RequestBody ThirdFundAuthFreezeReq request);

    /**
     * 资金授权解冻
     *
     * @param request 解冻请求参数
     * @return 解冻结果
     */
    @PostMapping("/third-fundauth/unfreeze")
    ApiResultResp<ThirdFundAuthUnfreezeResp> thirdFundAuthUnfreeze(@RequestBody ThirdFundAuthUnfreezeReq request);

    /**
     * 预授权支付
     *
     * @param request 预授权支付请求参数
     * @return 预授权支付结果
     */
    @PostMapping("/third-fundauth/pay")
    ApiResultResp<ThirdFundAuthPayResp> thirdFundAuthPay(@RequestBody ThirdFundAuthPayReq request);

    /**
     * 预授权退款
     *
     * @param request 预授权退款请求参数
     * @return 预授权退款结果
     */
    @PostMapping("/third-fundauth/refund")
    ApiResultResp<ThirdFundAuthRefundResp> thirdFundAuthRefund(@RequestBody ThirdFundAuthRefundReq request);

    /**
     * 预授权交易关闭
     *
     * @param request 关闭交易请求参数
     * @return 关闭结果
     */
    @PostMapping("/third-fundauth/close")
    ApiResultResp<ThirdFundAuthCloseResp> thirdFundAuthClose(@RequestBody ThirdFundAuthCloseReq request);
}
