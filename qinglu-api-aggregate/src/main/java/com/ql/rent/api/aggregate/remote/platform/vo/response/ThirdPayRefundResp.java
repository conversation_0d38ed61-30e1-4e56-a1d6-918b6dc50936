package com.ql.rent.api.aggregate.remote.platform.vo.response;

import lombok.Data;

/**
 * 支付退款响应结果
 *
 * <AUTHOR>
 */
@Data
public class ThirdPayRefundResp {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 实际退款金额（分）
     */
    private Long actualAmount;

    /**
     * 资金是否发生变动
     */
    private Boolean fundChange;

    /**
     * 失败原因
     */
    private String sourceFailedReason;
}
