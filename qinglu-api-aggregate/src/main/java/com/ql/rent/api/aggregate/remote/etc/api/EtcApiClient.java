package com.ql.rent.api.aggregate.remote.etc.api;

import com.ql.dto.ApiResultResp;
import com.ql.dto.open.response.EtcOrderRespDetail;
import com.ql.dto.open.response.EtcRespDetail;
import com.ql.dto.store.OrganCreate;
import com.ql.dto.vehicle.VehicleUnmountReq;
import com.ql.rent.api.aggregate.model.request.etc.CancellationOrder;
import com.ql.rent.api.aggregate.web.security.filter.FeignClientErrorDecoder;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(
    name = "etc-api",
    configuration = { FeignClientErrorDecoder.class},
    url = "${api.ribbon.listOfServers}")
public interface EtcApiClient {
    /**
     * 商家入驻好人好车
     */
    @PostMapping("/saas/api/platform/v1/etcOrganCreate")
    ApiResultResp<EtcRespDetail> organCreate(OrganCreate request);
    /**
     *
     */
    @PostMapping("/saas/api/platform/v1/etcBranchCreate")
    ApiResultResp<EtcRespDetail> updateOrgan(OrganCreate request);
    //
    @PostMapping("/saas/api/platform/v1/etcVehicleCreate")
    ApiResultResp<EtcRespDetail> vehicleOpcontrol(VehicleUnmountReq request);
    //订单取消
    @PostMapping("/saas/api/platform/v1/complete")
    ApiResultResp<EtcOrderRespDetail> etcOrderComplete(CancellationOrder request);
}
