package com.ql.rent.api.aggregate.remote.platform.vo.request;

import lombok.Data;

/**
 * 预授权支付请求参数
 *
 * <AUTHOR>
 */
@Data
public class ThirdFundAuthPayReq {

    /**
     * 第三方资金授权来源
     */
    private Byte thirdFundAuthSource;

    /**
     * SAAS订单ID
     */
    private Long orderId;

    /**
     * 商户授权资金订单号
     */
    private String payNo;

    /**
     * 订单总金额（分）
     */
    private Long totalAmount;

    /**
     * 订单标题
     */
    private String subject;

    /**
     * 资金预授权单号
     */
    private String authNo;

    /**
     * 交易是否完成
     */
    private Boolean done;

    /**
     * 操作人ID
     */
    private Long opUserId;

    /**
     * 支付主体：1-代商家调用，0-擎路调用
     */
    private Integer merchantPay;

    /**
     * 商户ID
     */
    private Long merchantId;
}
